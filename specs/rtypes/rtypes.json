{"name": "Runtime Type System", "description": "A categorical approach to runtime type validation in the SpiceTime architecture", "version": "0.1.0", "api": {"RTYPE": {"description": "Root functor for the Runtime Type System", "methods": {"create": {"description": "Creates a new rt instance", "parameters": [], "returns": "RTInstance", "example": "const rt = RTYPE.create()"}, "createWithAdapter": {"description": "Creates a new rt instance with a specific adapter", "parameters": [{"name": "adapter", "type": "TypeAdapter", "description": "The adapter to use"}], "returns": "RTInstance", "example": "const rt = RTYPE.createWithAdapter(zodAdapter)"}, "setDefaultAdapter": {"description": "Sets the default adapter for all rt instances", "parameters": [{"name": "adapter", "type": "TypeAdapter", "description": "The adapter to use"}], "returns": "void", "example": "RTYPE.setDefaultAdapter(zodAdapter)"}}}, "RTInstance": {"description": "Instance of the Runtime Type System", "primitiveTypes": {"string": {"description": "Creates a string type", "parameters": [], "returns": "StringType", "example": "rt.string()"}, "number": {"description": "Creates a number type", "parameters": [], "returns": "NumberType", "example": "rt.number()"}, "boolean": {"description": "Creates a boolean type", "parameters": [], "returns": "BooleanType", "example": "rt.boolean()"}, "literal": {"description": "Creates a literal type", "parameters": [{"name": "value", "type": "any", "description": "The literal value"}], "returns": "LiteralType", "example": "rt.literal('active')"}, "any": {"description": "Creates an any type", "parameters": [], "returns": "AnyType", "example": "rt.any()"}, "unknown": {"description": "Creates an unknown type", "parameters": [], "returns": "UnknownType", "example": "rt.unknown()"}, "null": {"description": "Creates a null type", "parameters": [], "returns": "NullType", "example": "rt.null()"}, "undefined": {"description": "Creates an undefined type", "parameters": [], "returns": "UndefinedType", "example": "rt.undefined()"}}, "complexTypes": {"object": {"description": "Creates an object type", "parameters": [{"name": "shape", "type": "Record<string, Type>", "description": "The shape of the object"}], "returns": "ObjectType", "example": "rt.object({ name: rt.string(), age: rt.number() })"}, "array": {"description": "Creates an array type", "parameters": [{"name": "itemType", "type": "Type", "description": "The type of array items"}], "returns": "ArrayType", "example": "rt.array(rt.string())"}, "union": {"description": "Creates a union type", "parameters": [{"name": "types", "type": "Type[]", "description": "The types in the union"}], "returns": "UnionType", "example": "rt.union([rt.string(), rt.number()])"}, "intersection": {"description": "Creates an intersection type", "parameters": [{"name": "types", "type": "Type[]", "description": "The types in the intersection"}], "returns": "IntersectionType", "example": "rt.intersection([rt.object({ name: rt.string() }), rt.object({ age: rt.number() })])"}, "record": {"description": "Creates a record type", "parameters": [{"name": "keyType", "type": "Type", "description": "The type of record keys"}, {"name": "valueType", "type": "Type", "description": "The type of record values"}], "returns": "RecordType", "example": "rt.record(rt.string(), rt.number())"}, "tuple": {"description": "Creates a tuple type", "parameters": [{"name": "types", "type": "Type[]", "description": "The types in the tuple"}], "returns": "TupleType", "example": "rt.tuple([rt.string(), rt.number(), rt.boolean()])"}, "enum": {"description": "Creates an enum type", "parameters": [{"name": "values", "type": "Record<string, string | number>", "description": "The enum values"}], "returns": "EnumType", "example": "rt.enum({ RED: 'red', GREEN: 'green', BLUE: 'blue' })"}}, "modifiedTypes": {"optional": {"description": "Creates an optional type", "parameters": [{"name": "type", "type": "Type", "description": "The base type"}], "returns": "OptionalType", "example": "rt.optional(rt.string())"}, "nullable": {"description": "Creates a nullable type", "parameters": [{"name": "type", "type": "Type", "description": "The base type"}], "returns": "NullableType", "example": "rt.nullable(rt.string())"}, "default": {"description": "Creates a type with a default value", "parameters": [{"name": "type", "type": "Type", "description": "The base type"}, {"name": "defaultValue", "type": "any", "description": "The default value"}], "returns": "DefaultType", "example": "rt.default(rt.string(), '')"}, "lazy": {"description": "Creates a lazy type for recursive definitions", "parameters": [{"name": "typeFn", "type": "() => Type", "description": "Function that returns a type"}], "returns": "LazyType", "example": "rt.lazy(() => rt.object({ next: rt.optional(nodeType) }))"}}, "extensionMethods": {"evolve": {"description": "Extends the rt instance with new types", "parameters": [{"name": "types", "type": "Record<string, Type>", "description": "The types to add"}], "returns": "RTInstance", "example": "rt.evolve({ User: rt.object({ name: rt.string() }) })"}}}, "Type": {"description": "Base type for all runtime types", "methods": {"is": {"description": "Type guard function", "parameters": [{"name": "value", "type": "unknown", "description": "The value to check"}], "returns": "boolean", "example": "if (type.is(value)) { /* value is of the type */ }"}, "validate": {"description": "Deep validation function", "parameters": [{"name": "value", "type": "unknown", "description": "The value to validate"}], "returns": "boolean", "example": "if (type.validate(value)) { /* value is valid */ }"}, "safeParse": {"description": "Parses a value with error handling", "parameters": [{"name": "value", "type": "unknown", "description": "The value to parse"}], "returns": "Result", "example": "const result = type.safeParse(value)"}, "parse": {"description": "Parses a value, throwing on error", "parameters": [{"name": "value", "type": "unknown", "description": "The value to parse"}], "returns": "any", "example": "const parsed = type.parse(value)"}, "create": {"description": "Creates a value with defaults", "parameters": [{"name": "partial", "type": "any", "description": "Partial value", "optional": true}], "returns": "any", "example": "const value = type.create({ name: '<PERSON>' })"}, "refine": {"description": "Adds custom validation", "parameters": [{"name": "predicate", "type": "(value: any) => boolean", "description": "Custom validation function"}, {"name": "message", "type": "string", "description": "Error message", "optional": true}], "returns": "Type", "example": "type.refine(value => value.length > 0, 'Cannot be empty')"}, "transform": {"description": "Transforms a value", "parameters": [{"name": "fn", "type": "(value: any) => any", "description": "Transformation function"}], "returns": "Type", "example": "type.transform(value => value.trim())"}}}}, "adapters": {"TypeAdapter": {"description": "Adapter interface for validation libraries", "methods": {"validate": {"description": "Validates a value against a schema", "parameters": [{"name": "schema", "type": "any", "description": "The schema to validate against"}, {"name": "value", "type": "unknown", "description": "The value to validate"}], "returns": "boolean", "example": "adapter.validate(schema, value)"}, "safeParse": {"description": "Parses a value with error handling", "parameters": [{"name": "schema", "type": "any", "description": "The schema to validate against"}, {"name": "value", "type": "unknown", "description": "The value to parse"}], "returns": "Result", "example": "adapter.safeParse(schema, value)"}, "parse": {"description": "Parses a value, throwing on error", "parameters": [{"name": "schema", "type": "any", "description": "The schema to validate against"}, {"name": "value", "type": "unknown", "description": "The value to parse"}], "returns": "any", "example": "adapter.parse(schema, value)"}, "create": {"description": "Creates a value with defaults", "parameters": [{"name": "schema", "type": "any", "description": "The schema to create from"}, {"name": "partial", "type": "any", "description": "Partial value", "optional": true}], "returns": "any", "example": "adapter.create(schema, { name: '<PERSON>' })"}}}, "implementations": {"ZodAdapter": {"description": "Adapter for Zod validation library", "example": "RTYPE.createWithAdapter(zodAdapter)"}, "IoTsAdapter": {"description": "Adapter for io-ts validation library", "example": "RTYPE.createWithAdapter(ioTsAdapter)"}, "RuntypesAdapter": {"description": "Adapter for Runtypes validation library", "example": "RTYPE.createWithAdapter(runtypesAdapter)"}, "CustomAdapter": {"description": "Custom adapter implementation", "example": "RTYPE.createWithAdapter(customAdapter)"}}}, "integration": {"stPragma": {"description": "Integration with stPragma", "components": {"typeFiles": {"description": "Type definition files", "examples": [{"name": "_.pragma.type.ts", "description": "Private type definitions", "content": "// Static type definitions\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n}\n\n// Runtime type definitions\nexport const User = rt.object({\n  id: rt.string().uuid(),\n  name: rt.string().min(1),\n  email: rt.string().email()\n});"}, {"name": ".pragma.type.ts", "description": "Public type definitions", "content": "// Static type definitions\nexport interface Component {\n  name: string;\n  props: Record<string, any>;\n  render: () => any;\n}\n\n// Runtime type definitions\nexport const Component = rt.object({\n  name: rt.string().min(1),\n  props: rt.record(rt.string(), rt.any()),\n  render: rt.function()\n});"}]}, "scopeIntegration": {"description": "Integration with scope", "example": "import { t, rt } from './scope';\n\n// Use static types\nconst user: t.User = {\n  id: '123',\n  name: '<PERSON>',\n  email: '<EMAIL>'\n};\n\n// Use runtime types\nif (rt.User.validate(data)) {\n  // Use validated data\n}"}}}, "categoryTheory": {"description": "Integration with category theory", "components": {"typesAsFunctors": {"description": "Types implemented as functors", "example": "// Type as a functor\nclass StringType extends Type {\n  // Functor mapping\n  map<T>(f: (value: string) => T): Type<T> {\n    return new TransformedType(this, f);\n  }\n}"}, "categoricalLaws": {"description": "Enforcement of categorical laws", "examples": [{"name": "Identity Law", "description": "F(id_A) = id_F(A)", "example": "// Identity law for types\nconst type = rt.string();\nconst identity = (x: string) => x;\nconst mapped = type.map(identity);\nassert(mapped.equals(type));"}, {"name": "Composition Law", "description": "F(g ∘ f) = F(g) ∘ F(f)", "example": "// Composition law for types\nconst type = rt.string();\nconst f = (x: string) => x.length;\nconst g = (x: number) => x > 5;\nconst composed = type.map(x => g(f(x)));\nconst sequenced = type.map(f).map(g);\nassert(composed.equals(sequenced));"}]}}}}, "examples": [{"name": "Basic Usage", "description": "Basic usage of the Runtime Type System", "code": "// Create an rt instance\nconst rt = RTYPE.create();\n\n// Define a User type\nconst User = rt.object({\n  id: rt.string().uuid(),\n  name: rt.string().min(1),\n  email: rt.string().email(),\n  age: rt.optional(rt.number().min(0)),\n  status: rt.union([\n    rt.literal('active'),\n    rt.literal('inactive'),\n    rt.literal('pending')\n  ])\n});\n\n// Validate a user\nconst user = {\n  id: '123e4567-e89b-12d3-a456-************',\n  name: '<PERSON>',\n  email: '<EMAIL>',\n  status: 'active'\n};\n\nif (User.validate(user)) {\n  console.log('Valid user');\n} else {\n  console.log('Invalid user');\n}\n\n// Create a user with defaults\nconst newUser = User.create({\n  name: '<PERSON>',\n  email: '<EMAIL>'\n});\n\nconsole.log(newUser);"}, {"name": "Type Extension", "description": "Extending types with additional properties", "code": "// Define a base Person type\nconst Person = rt.object({\n  name: rt.string().min(1),\n  age: rt.number().min(0)\n});\n\n// Extend to create an Employee type\nconst Employee = Person.extend({\n  employeeId: rt.string().min(1),\n  department: rt.string(),\n  salary: rt.number().positive()\n});\n\n// Create an employee\nconst employee = Employee.create({\n  name: '<PERSON>',\n  age: 30,\n  employeeId: 'E12345',\n  department: 'Engineering'\n});\n\nconsole.log(employee);"}, {"name": "Custom Validation", "description": "Adding custom validation to types", "code": "// Define a Password type with custom validation\nconst Password = rt.string()\n  .min(8)\n  .refine(\n    value => /[A-Z]/.test(value),\n    'Password must contain at least one uppercase letter'\n  )\n  .refine(\n    value => /[a-z]/.test(value),\n    'Password must contain at least one lowercase letter'\n  )\n  .refine(\n    value => /[0-9]/.test(value),\n    'Password must contain at least one number'\n  )\n  .refine(\n    value => /[^A-Za-z0-9]/.test(value),\n    'Password must contain at least one special character'\n  );\n\n// Validate a password\nconst result = Password.safeParse('Passw0rd!');\n\nif (result.success) {\n  console.log('Valid password');\n} else {\n  console.log('Invalid password:', result.error);\n}"}]}