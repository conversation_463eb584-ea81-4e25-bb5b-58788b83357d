Feature: Runtime Type System Tests
  As a developer using the SpiceTime architecture
  I want to test the Runtime Type System thoroughly
  So that I can ensure it works correctly in all scenarios

  Background:
    Given I have imported the RTYPE factory
    And I have created an rt instance

  # RTYPE Factory Tests
  Scenario: Creating an rt instance
    When I call RTYPE.create()
    Then I should get an rt instance
    And the rt instance should have primitive type factories
    And the rt instance should have complex type factories
    And the rt instance should have modified type factories

  Scenario: Creating an rt instance with an adapter
    Given I have a Zod adapter
    When I call RTYPE.createWithAdapter(zodAdapter)
    Then I should get an rt instance
    And the rt instance should use the Zod adapter for validation

  Scenario: Setting the default adapter
    Given I have a Zod adapter
    When I call RTYPE.setDefaultAdapter(zodAdapter)
    And I create a new rt instance
    Then the new rt instance should use the Zod adapter for validation

  # Primitive Type Tests
  Scenario Outline: Validating primitive types
    When I create a <type> type
    Then it should validate <valid_value> as valid
    And it should validate <invalid_value> as invalid

    Examples:
      | type     | valid_value | invalid_value |
      | string   | "test"      | 123           |
      | number   | 123         | "test"        |
      | boolean  | true        | "true"        |
      | literal  | "active"    | "inactive"    |
      | null     | null        | undefined     |
      | undefined| undefined   | null          |

  Scenario: String type with constraints
    When I create a string type with minimum length 3
    Then it should validate "test" as valid
    And it should validate "ab" as invalid
    And it should validate 123 as invalid

  Scenario: Number type with constraints
    When I create a number type with minimum value 0
    Then it should validate 10 as valid
    And it should validate -5 as invalid
    And it should validate "10" as invalid

  Scenario: Email validation
    When I create a string type with email validation
    Then it should validate "<EMAIL>" as valid
    And it should validate "invalid-email" as invalid

  # Complex Type Tests
  Scenario: Object type validation
    When I create an object type with properties:
      | property | type   | constraints |
      | name     | string | min(1)      |
      | age      | number | min(0)      |
    Then it should validate { "name": "John", "age": 30 } as valid
    And it should validate { "name": "", "age": 30 } as invalid
    And it should validate { "name": "John", "age": -5 } as invalid
    And it should validate { "name": "John" } as invalid
    And it should validate { "age": 30 } as invalid

  Scenario: Array type validation
    When I create an array type of strings
    Then it should validate ["a", "b", "c"] as valid
    And it should validate ["a", 1, "c"] as invalid
    And it should validate "not an array" as invalid

  Scenario: Union type validation
    When I create a union type of string and number
    Then it should validate "test" as valid
    And it should validate 123 as valid
    And it should validate true as invalid

  Scenario: Optional property validation
    When I create an object type with an optional property:
      | property | type   | optional |
      | name     | string | false    |
      | age      | number | true     |
    Then it should validate { "name": "John", "age": 30 } as valid
    And it should validate { "name": "John" } as valid
    And it should validate { "age": 30 } as invalid

  # Type Creation Tests
  Scenario: Creating values with defaults
    When I create an object type with defaults:
      | property | type   | default   |
      | name     | string | "Unknown" |
      | age      | number | 0         |
    And I call create() with no arguments
    Then I should get { "name": "Unknown", "age": 0 }
    When I call create() with { "name": "John" }
    Then I should get { "name": "John", "age": 0 }

  # Type Refinement Tests
  Scenario: Refining a type with custom validation
    When I create a number type
    And I refine it with a predicate that checks if the number is even
    Then it should validate 2 as valid
    And it should validate 3 as invalid

  Scenario: Transforming a type
    When I create a string type
    And I transform it to convert the string to uppercase
    And I call parse() with "hello"
    Then I should get "HELLO"

  # Type Extension Tests
  Scenario: Extending an object type
    When I create a Person type with name and age properties
    And I extend it to create an Employee type with employeeId property
    Then the Employee type should validate { "name": "John", "age": 30, "employeeId": "E12345" } as valid
    And the Employee type should validate { "name": "John", "age": 30 } as invalid
    And the Person type should validate { "name": "John", "age": 30 } as valid
    And the Person type should validate { "name": "John", "age": 30, "employeeId": "E12345" } as valid

  # Integration Tests
  Scenario: Integration with stPragma
    Given I have a stPragma module
    When I define a User type in _.pragma.type.ts
    Then the User type should be available in the t namespace
    And the runtime User type should be available in the rt namespace
    And I should be able to validate user objects with rt.User.validate()
    And I should be able to create user objects with rt.User.create()

  Scenario: Type safety across component boundaries
    Given I have a component that accepts a User
    When I pass a valid user object to the component
    Then the component should process the user
    When I pass an invalid user object to the component
    Then the component should reject the user with an error message
