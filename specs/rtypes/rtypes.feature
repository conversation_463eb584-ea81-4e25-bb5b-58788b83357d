Feature: Runtime Type System
  As a developer using the SpiceTime architecture
  I want a categorical approach to runtime type validation
  So that I can ensure type safety across component boundaries

  Background:
    Given the Runtime Type System is implemented as a functor from cat_types
    And each node extends its parent's class and adds its own prototype layer
    And the system integrates with stPragma

  Scenario: Creating a basic runtime type instance
    When I create a new runtime type instance
    Then I should have access to primitive type factories
    And I should be able to define custom types
    And I should be able to validate values against types
    And I should be able to create values from types

  Scenario: Validating values with primitive types
    Given I have a runtime type instance
    When I validate a string value with rt.string()
    Then the validation should succeed for valid strings
    And the validation should fail for non-string values
    
  Scenario: Creating complex object types
    Given I have a runtime type instance
    When I define a User type with required and optional fields
    Then I should be able to validate user objects
    And I should be able to create user objects with default values
    And invalid user objects should fail validation

  Scenario: Type refinement with custom validation
    Given I have a runtime type instance
    When I refine a string type with a custom validation
    Then the validation should succeed for values that pass the custom validation
    And the validation should fail for values that don't pass the custom validation

  Scenario: Type transformation
    Given I have a runtime type instance
    When I define a type with a transformation
    Then the transformation should be applied when parsing values
    And the original value should be preserved when validating

  Scenario: Type extension and inheritance
    Given I have a parent runtime type instance
    When I create a child runtime type instance
    Then the child should inherit all types from the parent
    And I should be able to add new types to the child
    And the parent types should remain unchanged

  Scenario: Integration with stPragma
    Given I have a stPragma module
    When I define types in a .type.ts file
    Then the types should be available in the t namespace
    And the runtime types should be available in the rt namespace
    And the types should follow the same visibility rules as other pragmas

  Scenario: Adapter pattern for validation libraries
    Given I have the RTYPE factory
    When I create an rt instance with a specific adapter
    Then the adapter should be used for validation
    And I should be able to switch adapters without changing the API

  Scenario: Type safety across component boundaries
    Given I have components that exchange data
    When I validate data at component boundaries
    Then I should be able to ensure type safety
    And I should get detailed error messages for invalid data
