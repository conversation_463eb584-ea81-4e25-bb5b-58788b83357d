name: Runtime Type System
description: A categorical approach to runtime type validation in the SpiceTime architecture
version: 0.1.0

components:
  RTYPE:
    description: Root functor for the Runtime Type System
    type: class
    extends: TreeFunctor
    methods:
      create:
        description: Creates a new rt instance
        returns: RTInstance
        example: const rt = RTYPE.create()
      createWithAdapter:
        description: Creates a new rt instance with a specific adapter
        parameters:
          adapter:
            description: The adapter to use
            type: TypeAdapter
        returns: RTInstance
        example: const rt = RTYPE.createWithAdapter(zodAdapter)
      setDefaultAdapter:
        description: Sets the default adapter for all rt instances
        parameters:
          adapter:
            description: The adapter to use
            type: TypeAdapter
        returns: void
        example: RTYPE.setDefaultAdapter(zodAdapter)

  RTInstance:
    description: Instance of the Runtime Type System
    type: object
    properties:
      # Primitive Types
      string:
        description: Creates a string type
        type: function
        returns: StringType
        example: rt.string()
      number:
        description: Creates a number type
        type: function
        returns: NumberType
        example: rt.number()
      boolean:
        description: Creates a boolean type
        type: function
        returns: BooleanType
        example: rt.boolean()
      literal:
        description: Creates a literal type
        parameters:
          value:
            description: The literal value
            type: any
        returns: LiteralType
        example: rt.literal('active')
      
      # Complex Types
      object:
        description: Creates an object type
        parameters:
          shape:
            description: The shape of the object
            type: Record<string, Type>
        returns: ObjectType
        example: rt.object({ name: rt.string(), age: rt.number() })
      array:
        description: Creates an array type
        parameters:
          itemType:
            description: The type of array items
            type: Type
        returns: ArrayType
        example: rt.array(rt.string())
      union:
        description: Creates a union type
        parameters:
          types:
            description: The types in the union
            type: Type[]
        returns: UnionType
        example: rt.union([rt.string(), rt.number()])
      
      # Modified Types
      optional:
        description: Creates an optional type
        parameters:
          type:
            description: The base type
            type: Type
        returns: OptionalType
        example: rt.optional(rt.string())
      nullable:
        description: Creates a nullable type
        parameters:
          type:
            description: The base type
            type: Type
        returns: NullableType
        example: rt.nullable(rt.string())
      
      # Extension Methods
      evolve:
        description: Extends the rt instance with new types
        parameters:
          types:
            description: The types to add
            type: Record<string, Type>
        returns: RTInstance
        example: rt.evolve({ User: rt.object({ name: rt.string() }) })

types:
  Type:
    description: Base type for all runtime types
    type: class
    methods:
      is:
        description: Type guard function
        parameters:
          value:
            description: The value to check
            type: unknown
        returns: boolean
        example: if (type.is(value)) { /* value is of the type */ }
      validate:
        description: Deep validation function
        parameters:
          value:
            description: The value to validate
            type: unknown
        returns: boolean
        example: if (type.validate(value)) { /* value is valid */ }
      safeParse:
        description: Parses a value with error handling
        parameters:
          value:
            description: The value to parse
            type: unknown
        returns: Result
        example: const result = type.safeParse(value)
      parse:
        description: Parses a value, throwing on error
        parameters:
          value:
            description: The value to parse
            type: unknown
        returns: any
        example: const parsed = type.parse(value)
      create:
        description: Creates a value with defaults
        parameters:
          partial:
            description: Partial value
            type: any
            optional: true
        returns: any
        example: const value = type.create({ name: 'John' })
      refine:
        description: Adds custom validation
        parameters:
          predicate:
            description: Custom validation function
            type: (value: any) => boolean
          message:
            description: Error message
            type: string
            optional: true
        returns: Type
        example: type.refine(value => value.length > 0, 'Cannot be empty')
      transform:
        description: Transforms a value
        parameters:
          fn:
            description: Transformation function
            type: (value: any) => any
        returns: Type
        example: type.transform(value => value.trim())

  StringType:
    description: String type
    extends: Type
    methods:
      min:
        description: Sets minimum length
        parameters:
          length:
            description: Minimum length
            type: number
        returns: StringType
        example: rt.string().min(1)
      max:
        description: Sets maximum length
        parameters:
          length:
            description: Maximum length
            type: number
        returns: StringType
        example: rt.string().max(100)
      email:
        description: Validates as email
        returns: StringType
        example: rt.string().email()
      url:
        description: Validates as URL
        returns: StringType
        example: rt.string().url()
      uuid:
        description: Validates as UUID
        returns: StringType
        example: rt.string().uuid()
      regex:
        description: Validates against regex
        parameters:
          pattern:
            description: Regex pattern
            type: RegExp
        returns: StringType
        example: rt.string().regex(/^[a-z]+$/)

  NumberType:
    description: Number type
    extends: Type
    methods:
      min:
        description: Sets minimum value
        parameters:
          value:
            description: Minimum value
            type: number
        returns: NumberType
        example: rt.number().min(0)
      max:
        description: Sets maximum value
        parameters:
          value:
            description: Maximum value
            type: number
        returns: NumberType
        example: rt.number().max(100)
      int:
        description: Validates as integer
        returns: NumberType
        example: rt.number().int()
      positive:
        description: Validates as positive
        returns: NumberType
        example: rt.number().positive()
      negative:
        description: Validates as negative
        returns: NumberType
        example: rt.number().negative()

  ObjectType:
    description: Object type
    extends: Type
    methods:
      strict:
        description: Disallows unknown properties
        returns: ObjectType
        example: rt.object({ name: rt.string() }).strict()
      extend:
        description: Extends with additional properties
        parameters:
          shape:
            description: Additional properties
            type: Record<string, Type>
        returns: ObjectType
        example: userType.extend({ age: rt.number() })
      pick:
        description: Creates a type with only the specified properties
        parameters:
          keys:
            description: Keys to pick
            type: string[]
        returns: ObjectType
        example: userType.pick(['name', 'email'])
      omit:
        description: Creates a type without the specified properties
        parameters:
          keys:
            description: Keys to omit
            type: string[]
        returns: ObjectType
        example: userType.omit(['password'])
      partial:
        description: Makes all properties optional
        returns: ObjectType
        example: userType.partial()
      required:
        description: Makes all properties required
        returns: ObjectType
        example: userType.required()

  ArrayType:
    description: Array type
    extends: Type
    methods:
      min:
        description: Sets minimum length
        parameters:
          length:
            description: Minimum length
            type: number
        returns: ArrayType
        example: rt.array(rt.string()).min(1)
      max:
        description: Sets maximum length
        parameters:
          length:
            description: Maximum length
            type: number
        returns: ArrayType
        example: rt.array(rt.string()).max(10)
      nonempty:
        description: Requires non-empty array
        returns: ArrayType
        example: rt.array(rt.string()).nonempty()
      length:
        description: Sets exact length
        parameters:
          length:
            description: Exact length
            type: number
        returns: ArrayType
        example: rt.array(rt.string()).length(3)

adapters:
  TypeAdapter:
    description: Adapter interface for validation libraries
    type: interface
    methods:
      validate:
        description: Validates a value against a schema
        parameters:
          schema:
            description: The schema to validate against
            type: any
          value:
            description: The value to validate
            type: unknown
        returns: boolean
        example: adapter.validate(schema, value)
      safeParse:
        description: Parses a value with error handling
        parameters:
          schema:
            description: The schema to validate against
            type: any
          value:
            description: The value to parse
            type: unknown
        returns: Result
        example: adapter.safeParse(schema, value)
      parse:
        description: Parses a value, throwing on error
        parameters:
          schema:
            description: The schema to validate against
            type: any
          value:
            description: The value to parse
            type: unknown
        returns: any
        example: adapter.parse(schema, value)
      create:
        description: Creates a value with defaults
        parameters:
          schema:
            description: The schema to create from
            type: any
          partial:
            description: Partial value
            type: any
            optional: true
        returns: any
        example: adapter.create(schema, { name: 'John' })

  ZodAdapter:
    description: Adapter for Zod validation library
    implements: TypeAdapter
    example: RTYPE.createWithAdapter(zodAdapter)

  IoTsAdapter:
    description: Adapter for io-ts validation library
    implements: TypeAdapter
    example: RTYPE.createWithAdapter(ioTsAdapter)

  RuntypesAdapter:
    description: Adapter for Runtypes validation library
    implements: TypeAdapter
    example: RTYPE.createWithAdapter(runtypesAdapter)

examples:
  - name: Basic Usage
    description: Basic usage of the Runtime Type System
    code: |
      // Create an rt instance
      const rt = RTYPE.create();
      
      // Define a User type
      const User = rt.object({
        id: rt.string().uuid(),
        name: rt.string().min(1),
        email: rt.string().email(),
        age: rt.optional(rt.number().min(0)),
        status: rt.union([
          rt.literal('active'),
          rt.literal('inactive'),
          rt.literal('pending')
        ])
      });
      
      // Validate a user
      const user = {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'John Doe',
        email: '<EMAIL>',
        status: 'active'
      };
      
      if (User.validate(user)) {
        console.log('Valid user');
      } else {
        console.log('Invalid user');
      }
      
      // Create a user with defaults
      const newUser = User.create({
        name: 'Jane Doe',
        email: '<EMAIL>'
      });
      
      console.log(newUser);
      
  - name: Type Extension
    description: Extending types with additional properties
    code: |
      // Define a base Person type
      const Person = rt.object({
        name: rt.string().min(1),
        age: rt.number().min(0)
      });
      
      // Extend to create an Employee type
      const Employee = Person.extend({
        employeeId: rt.string().min(1),
        department: rt.string(),
        salary: rt.number().positive()
      });
      
      // Create an employee
      const employee = Employee.create({
        name: 'John Doe',
        age: 30,
        employeeId: 'E12345',
        department: 'Engineering'
      });
      
      console.log(employee);
      
  - name: Custom Validation
    description: Adding custom validation to types
    code: |
      // Define a Password type with custom validation
      const Password = rt.string()
        .min(8)
        .refine(
          value => /[A-Z]/.test(value),
          'Password must contain at least one uppercase letter'
        )
        .refine(
          value => /[a-z]/.test(value),
          'Password must contain at least one lowercase letter'
        )
        .refine(
          value => /[0-9]/.test(value),
          'Password must contain at least one number'
        )
        .refine(
          value => /[^A-Za-z0-9]/.test(value),
          'Password must contain at least one special character'
        );
      
      // Validate a password
      const result = Password.safeParse('Passw0rd!');
      
      if (result.success) {
        console.log('Valid password');
      } else {
        console.log('Invalid password:', result.error);
      }
      
  - name: Integration with stPragma
    description: Using the Runtime Type System with stPragma
    code: |
      // In _.pragma.type.ts
      // Static type definitions (t namespace)
      export interface User {
        id: string;
        name: string;
        email: string;
        age?: number;
        status: 'active' | 'inactive' | 'pending';
      }
      
      // Runtime type definitions (rt namespace)
      export const User = rt.object({
        id: rt.string().uuid(),
        name: rt.string().min(1),
        email: rt.string().email(),
        age: rt.optional(rt.number().min(0)),
        status: rt.union([
          rt.literal('active'),
          rt.literal('inactive'),
          rt.literal('pending')
        ])
      });
      
      // In a component file
      import { t, rt } from './scope';
      
      // Create a user
      const user = rt.User.create({
        name: 'John Doe',
        email: '<EMAIL>',
        status: 'active'
      });
      
      // Validate external data
      const data = await fetchData();
      if (rt.User.validate(data)) {
        // Use validated data
        processUser(data);
      } else {
        // Handle invalid data
        handleError('Invalid user data');
      }
      
      // Type guard
      function processData(data: unknown) {
        if (rt.User.is(data)) {
          // TypeScript knows data is a User
          processUser(data);
        } else if (rt.Post.is(data)) {
          // TypeScript knows data is a Post
          processPost(data);
        }
      }

tests:
  - name: RTYPE Factory Tests
    description: Tests for the RTYPE factory
    scenarios:
      - name: Creating an rt instance
        steps:
          - description: Create an rt instance
            code: const rt = RTYPE.create()
          - description: Check that rt has primitive type factories
            code: expect(rt.string).toBeFunction()
          - description: Check that rt has complex type factories
            code: expect(rt.object).toBeFunction()
      
      - name: Creating an rt instance with an adapter
        steps:
          - description: Create an rt instance with a Zod adapter
            code: const rt = RTYPE.createWithAdapter(zodAdapter)
          - description: Validate a value using the adapter
            code: expect(rt.string().validate('test')).toBe(true)
  
  - name: Primitive Type Tests
    description: Tests for primitive types
    scenarios:
      - name: String type validation
        steps:
          - description: Create a string type
            code: const stringType = rt.string()
          - description: Validate a string value
            code: expect(stringType.validate('test')).toBe(true)
          - description: Validate a non-string value
            code: expect(stringType.validate(123)).toBe(false)
      
      - name: Number type validation
        steps:
          - description: Create a number type
            code: const numberType = rt.number()
          - description: Validate a number value
            code: expect(numberType.validate(123)).toBe(true)
          - description: Validate a non-number value
            code: expect(numberType.validate('test')).toBe(false)
  
  - name: Complex Type Tests
    description: Tests for complex types
    scenarios:
      - name: Object type validation
        steps:
          - description: Create an object type
            code: |
              const userType = rt.object({
                name: rt.string(),
                age: rt.number()
              })
          - description: Validate a valid object
            code: |
              expect(userType.validate({
                name: 'John',
                age: 30
              })).toBe(true)
          - description: Validate an invalid object
            code: |
              expect(userType.validate({
                name: 'John',
                age: 'thirty'
              })).toBe(false)
      
      - name: Array type validation
        steps:
          - description: Create an array type
            code: const stringArrayType = rt.array(rt.string())
          - description: Validate a valid array
            code: expect(stringArrayType.validate(['a', 'b', 'c'])).toBe(true)
          - description: Validate an invalid array
            code: expect(stringArrayType.validate(['a', 1, 'c'])).toBe(false)
  
  - name: Type Extension Tests
    description: Tests for type extension
    scenarios:
      - name: Extending an object type
        steps:
          - description: Create a base type
            code: |
              const personType = rt.object({
                name: rt.string(),
                age: rt.number()
              })
          - description: Extend the base type
            code: |
              const employeeType = personType.extend({
                employeeId: rt.string()
              })
          - description: Validate a valid extended object
            code: |
              expect(employeeType.validate({
                name: 'John',
                age: 30,
                employeeId: 'E12345'
              })).toBe(true)
          - description: Validate an object missing extended properties
            code: |
              expect(employeeType.validate({
                name: 'John',
                age: 30
              })).toBe(false)
  
  - name: Custom Validation Tests
    description: Tests for custom validation
    scenarios:
      - name: Refining a type with custom validation
        steps:
          - description: Create a refined type
            code: |
              const positiveEvenNumber = rt.number()
                .positive()
                .refine(n => n % 2 === 0, 'Must be even')
          - description: Validate a valid value
            code: expect(positiveEvenNumber.validate(2)).toBe(true)
          - description: Validate an invalid value (odd)
            code: expect(positiveEvenNumber.validate(3)).toBe(false)
          - description: Validate an invalid value (negative)
            code: expect(positiveEvenNumber.validate(-2)).toBe(false)
