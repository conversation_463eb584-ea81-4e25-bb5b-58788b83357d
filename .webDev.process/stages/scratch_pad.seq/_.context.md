this is description of what scratch_pad is
and what for

this is for the dev
to express his thoughts and ideas
in real time
without distructions
of organizing in structure
even as simple as an order of concepts

concepts are not to be a random stream of disjointed and confusing thoughts
thats what scratch_pad is for
it allows recording of random thoughts
to be processed into ideation concepts
by an AI assistant

thru some interaction with the dev
but more automated later

all files inside scratch_pad folder are to be processed in chrono order
they can be indexed by a numerical prefix, as 1.something.md
if, not, time tags should be used

after scratch pad is processed into ideation concepts
it is to be purged
and all concepts are to be moved into their final structure
after they are fully developed