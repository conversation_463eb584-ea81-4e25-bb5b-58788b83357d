# Primitive Types - Specification

## Overview

Primitive types are the building blocks of the Runtime Type System, representing the basic JavaScript types: string, number, boolean, and literal values. They provide methods for validation, refinement, and creation of primitive values.

## String Type

The String type represents string values and provides methods for validating and refining strings.

### Creation

- **rt.string()**
  - Creates a string type
  - Example: `const stringType = rt.string()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is a string
  - Example: `if (rt.string().is(value)) { /* value is string */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.string().validate(value)) { /* value is valid string */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.string().safeParse(value); if (result.success) { /* use result.data */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const str = rt.string().parse(value); } catch (error) { /* handle error */ }`

### Refinement Methods

- **min(length)**
  - Minimum length constraint
  - Returns a new string type
  - Example: `rt.string().min(1)`

- **max(length)**
  - Maximum length constraint
  - Returns a new string type
  - Example: `rt.string().max(100)`

- **length(length)**
  - Exact length constraint
  - Returns a new string type
  - Example: `rt.string().length(10)`

- **pattern(regex)**
  - Regular expression constraint
  - Returns a new string type
  - Example: `rt.string().pattern(/^[a-z]+$/)`

- **email()**
  - Email format constraint
  - Returns a new string type
  - Example: `rt.string().email()`

- **url()**
  - URL format constraint
  - Returns a new string type
  - Example: `rt.string().url()`

- **uuid()**
  - UUID format constraint
  - Returns a new string type
  - Example: `rt.string().uuid()`

- **cuid()**
  - CUID format constraint
  - Returns a new string type
  - Example: `rt.string().cuid()`

- **startsWith(prefix)**
  - Starts with constraint
  - Returns a new string type
  - Example: `rt.string().startsWith('https://')`

- **endsWith(suffix)**
  - Ends with constraint
  - Returns a new string type
  - Example: `rt.string().endsWith('.com')`

- **includes(substring)**
  - Includes constraint
  - Returns a new string type
  - Example: `rt.string().includes('@')`

- **trim()**
  - Trims whitespace during validation
  - Returns a new string type
  - Example: `rt.string().trim()`

- **toLowerCase()**
  - Converts to lowercase during validation
  - Returns a new string type
  - Example: `rt.string().toLowerCase()`

- **toUpperCase()**
  - Converts to uppercase during validation
  - Returns a new string type
  - Example: `rt.string().toUpperCase()`

### Creation Methods

- **default(value)**
  - Sets a default value
  - Returns a new string type
  - Example: `rt.string().default('unknown')`

## Number Type

The Number type represents number values and provides methods for validating and refining numbers.

### Creation

- **rt.number()**
  - Creates a number type
  - Example: `const numberType = rt.number()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is a number
  - Example: `if (rt.number().is(value)) { /* value is number */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.number().validate(value)) { /* value is valid number */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.number().safeParse(value); if (result.success) { /* use result.data */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const num = rt.number().parse(value); } catch (error) { /* handle error */ }`

### Refinement Methods

- **min(value)**
  - Minimum value constraint
  - Returns a new number type
  - Example: `rt.number().min(0)`

- **max(value)**
  - Maximum value constraint
  - Returns a new number type
  - Example: `rt.number().max(100)`

- **int()**
  - Integer constraint
  - Returns a new number type
  - Example: `rt.number().int()`

- **positive()**
  - Positive number constraint
  - Returns a new number type
  - Example: `rt.number().positive()`

- **negative()**
  - Negative number constraint
  - Returns a new number type
  - Example: `rt.number().negative()`

- **nonpositive()**
  - Non-positive number constraint
  - Returns a new number type
  - Example: `rt.number().nonpositive()`

- **nonnegative()**
  - Non-negative number constraint
  - Returns a new number type
  - Example: `rt.number().nonnegative()`

- **multipleOf(value)**
  - Multiple of constraint
  - Returns a new number type
  - Example: `rt.number().multipleOf(5)`

- **finite()**
  - Finite number constraint
  - Returns a new number type
  - Example: `rt.number().finite()`

- **safe()**
  - Safe integer constraint
  - Returns a new number type
  - Example: `rt.number().safe()`

### Creation Methods

- **default(value)**
  - Sets a default value
  - Returns a new number type
  - Example: `rt.number().default(0)`

## Boolean Type

The Boolean type represents boolean values and provides methods for validating and refining booleans.

### Creation

- **rt.boolean()**
  - Creates a boolean type
  - Example: `const booleanType = rt.boolean()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is a boolean
  - Example: `if (rt.boolean().is(value)) { /* value is boolean */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks all constraints
  - Example: `if (rt.boolean().validate(value)) { /* value is valid boolean */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.boolean().safeParse(value); if (result.success) { /* use result.data */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const bool = rt.boolean().parse(value); } catch (error) { /* handle error */ }`

### Creation Methods

- **default(value)**
  - Sets a default value
  - Returns a new boolean type
  - Example: `rt.boolean().default(false)`

## Literal Type

The Literal type represents literal values and provides methods for validating and refining literals.

### Creation

- **rt.literal(value)**
  - Creates a literal type
  - Example: `const literalType = rt.literal('admin')`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is the literal value
  - Example: `if (rt.literal('admin').is(value)) { /* value is 'admin' */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is the literal value
  - Example: `if (rt.literal('admin').validate(value)) { /* value is 'admin' */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.literal('admin').safeParse(value); if (result.success) { /* value is 'admin' */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const lit = rt.literal('admin').parse(value); } catch (error) { /* handle error */ }`

## Null Type

The Null type represents null values and provides methods for validating and refining null.

### Creation

- **rt.null()**
  - Creates a null type
  - Example: `const nullType = rt.null()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is null
  - Example: `if (rt.null().is(value)) { /* value is null */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is null
  - Example: `if (rt.null().validate(value)) { /* value is null */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.null().safeParse(value); if (result.success) { /* value is null */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const n = rt.null().parse(value); } catch (error) { /* handle error */ }`

## Undefined Type

The Undefined type represents undefined values and provides methods for validating and refining undefined.

### Creation

- **rt.undefined()**
  - Creates an undefined type
  - Example: `const undefinedType = rt.undefined()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Returns true if the value is undefined
  - Example: `if (rt.undefined().is(value)) { /* value is undefined */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Checks if the value is undefined
  - Example: `if (rt.undefined().validate(value)) { /* value is undefined */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Provides detailed error information
  - Example: `const result = rt.undefined().safeParse(value); if (result.success) { /* value is undefined */ }`

- **parse(value)**
  - Throws on error
  - Returns the value if valid
  - Example: `try { const u = rt.undefined().parse(value); } catch (error) { /* handle error */ }`

## Any Type

The Any type represents any value and provides methods for validating and refining any value.

### Creation

- **rt.any()**
  - Creates an any type
  - Example: `const anyType = rt.any()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Always returns true
  - Example: `if (rt.any().is(value)) { /* always true */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Always returns true
  - Example: `if (rt.any().validate(value)) { /* always true */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Always succeeds
  - Example: `const result = rt.any().safeParse(value); /* result.success is always true */ `

- **parse(value)**
  - Never throws
  - Returns the value
  - Example: `const a = rt.any().parse(value); /* always returns value */ `

## Unknown Type

The Unknown type represents unknown values and provides methods for validating and refining unknown values.

### Creation

- **rt.unknown()**
  - Creates an unknown type
  - Example: `const unknownType = rt.unknown()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Always returns true
  - Example: `if (rt.unknown().is(value)) { /* always true */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Always returns true
  - Example: `if (rt.unknown().validate(value)) { /* always true */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Always succeeds
  - Example: `const result = rt.unknown().safeParse(value); /* result.success is always true */ `

- **parse(value)**
  - Never throws
  - Returns the value
  - Example: `const u = rt.unknown().parse(value); /* always returns value */ `

## Never Type

The Never type represents values that should never occur and provides methods for validating and refining never values.

### Creation

- **rt.never()**
  - Creates a never type
  - Example: `const neverType = rt.never()`

### Validation Methods

- **is(value)**
  - Type guard function
  - Always returns false
  - Example: `if (rt.never().is(value)) { /* never true */ }`

- **validate(value)**
  - Deep validation returning boolean
  - Always returns false
  - Example: `if (rt.never().validate(value)) { /* never true */ }`

- **safeParse(value)**
  - Returns result object with success/error
  - Always fails
  - Example: `const result = rt.never().safeParse(value); /* result.success is always false */ `

- **parse(value)**
  - Always throws
  - Example: `try { const n = rt.never().parse(value); } catch (error) { /* always throws */ }`

## Test Specifications

The following test specifications define the expected behavior of the primitive types:

### String Type Tests

- **Basic Validation**
  - Test basic string validation
  - Example:
    ```typescript
    const type = rt.string();
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('')).toBe(true);
    expect(type.validate(123)).toBe(false);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(undefined)).toBe(false);
    ```

- **Min Length**
  - Test minimum length constraint
  - Example:
    ```typescript
    const type = rt.string().min(3);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('te')).toBe(false);
    ```

- **Max Length**
  - Test maximum length constraint
  - Example:
    ```typescript
    const type = rt.string().max(5);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('testing')).toBe(false);
    ```

- **Exact Length**
  - Test exact length constraint
  - Example:
    ```typescript
    const type = rt.string().length(4);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('tes')).toBe(false);
    expect(type.validate('tests')).toBe(false);
    ```

- **Pattern**
  - Test pattern constraint
  - Example:
    ```typescript
    const type = rt.string().pattern(/^[a-z]+$/);
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate('Test')).toBe(false);
    expect(type.validate('test123')).toBe(false);
    ```

- **Email**
  - Test email constraint
  - Example:
    ```typescript
    const type = rt.string().email();
    
    expect(type.validate('<EMAIL>')).toBe(true);
    expect(type.validate('test')).toBe(false);
    expect(type.validate('test@')).toBe(false);
    expect(type.validate('@example.com')).toBe(false);
    ```

- **URL**
  - Test URL constraint
  - Example:
    ```typescript
    const type = rt.string().url();
    
    expect(type.validate('https://example.com')).toBe(true);
    expect(type.validate('example.com')).toBe(false);
    expect(type.validate('test')).toBe(false);
    ```

- **Transformation**
  - Test transformation methods
  - Example:
    ```typescript
    const type = rt.string().trim().toLowerCase();
    
    expect(type.parse('  TEST  ')).toBe('test');
    ```

### Number Type Tests

- **Basic Validation**
  - Test basic number validation
  - Example:
    ```typescript
    const type = rt.number();
    
    expect(type.validate(123)).toBe(true);
    expect(type.validate(0)).toBe(true);
    expect(type.validate(-123)).toBe(true);
    expect(type.validate(123.45)).toBe(true);
    expect(type.validate('123')).toBe(false);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(undefined)).toBe(false);
    ```

- **Min Value**
  - Test minimum value constraint
  - Example:
    ```typescript
    const type = rt.number().min(0);
    
    expect(type.validate(0)).toBe(true);
    expect(type.validate(123)).toBe(true);
    expect(type.validate(-123)).toBe(false);
    ```

- **Max Value**
  - Test maximum value constraint
  - Example:
    ```typescript
    const type = rt.number().max(100);
    
    expect(type.validate(100)).toBe(true);
    expect(type.validate(50)).toBe(true);
    expect(type.validate(200)).toBe(false);
    ```

- **Integer**
  - Test integer constraint
  - Example:
    ```typescript
    const type = rt.number().int();
    
    expect(type.validate(123)).toBe(true);
    expect(type.validate(0)).toBe(true);
    expect(type.validate(-123)).toBe(true);
    expect(type.validate(123.45)).toBe(false);
    ```

- **Positive**
  - Test positive constraint
  - Example:
    ```typescript
    const type = rt.number().positive();
    
    expect(type.validate(123)).toBe(true);
    expect(type.validate(0)).toBe(false);
    expect(type.validate(-123)).toBe(false);
    ```

- **Negative**
  - Test negative constraint
  - Example:
    ```typescript
    const type = rt.number().negative();
    
    expect(type.validate(-123)).toBe(true);
    expect(type.validate(0)).toBe(false);
    expect(type.validate(123)).toBe(false);
    ```

- **Multiple Of**
  - Test multiple of constraint
  - Example:
    ```typescript
    const type = rt.number().multipleOf(5);
    
    expect(type.validate(5)).toBe(true);
    expect(type.validate(10)).toBe(true);
    expect(type.validate(7)).toBe(false);
    ```

### Boolean Type Tests

- **Basic Validation**
  - Test basic boolean validation
  - Example:
    ```typescript
    const type = rt.boolean();
    
    expect(type.validate(true)).toBe(true);
    expect(type.validate(false)).toBe(true);
    expect(type.validate('true')).toBe(false);
    expect(type.validate(1)).toBe(false);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(undefined)).toBe(false);
    ```

### Literal Type Tests

- **Basic Validation**
  - Test basic literal validation
  - Example:
    ```typescript
    const type = rt.literal('admin');
    
    expect(type.validate('admin')).toBe(true);
    expect(type.validate('user')).toBe(false);
    expect(type.validate(123)).toBe(false);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(undefined)).toBe(false);
    ```

- **Number Literal**
  - Test number literal validation
  - Example:
    ```typescript
    const type = rt.literal(123);
    
    expect(type.validate(123)).toBe(true);
    expect(type.validate(456)).toBe(false);
    expect(type.validate('123')).toBe(false);
    ```

- **Boolean Literal**
  - Test boolean literal validation
  - Example:
    ```typescript
    const type = rt.literal(true);
    
    expect(type.validate(true)).toBe(true);
    expect(type.validate(false)).toBe(false);
    expect(type.validate('true')).toBe(false);
    ```

### Null Type Tests

- **Basic Validation**
  - Test basic null validation
  - Example:
    ```typescript
    const type = rt.null();
    
    expect(type.validate(null)).toBe(true);
    expect(type.validate(undefined)).toBe(false);
    expect(type.validate(0)).toBe(false);
    expect(type.validate('')).toBe(false);
    expect(type.validate(false)).toBe(false);
    ```

### Undefined Type Tests

- **Basic Validation**
  - Test basic undefined validation
  - Example:
    ```typescript
    const type = rt.undefined();
    
    expect(type.validate(undefined)).toBe(true);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(0)).toBe(false);
    expect(type.validate('')).toBe(false);
    expect(type.validate(false)).toBe(false);
    ```

### Any Type Tests

- **Basic Validation**
  - Test basic any validation
  - Example:
    ```typescript
    const type = rt.any();
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate(123)).toBe(true);
    expect(type.validate(true)).toBe(true);
    expect(type.validate(null)).toBe(true);
    expect(type.validate(undefined)).toBe(true);
    expect(type.validate({})).toBe(true);
    expect(type.validate([])).toBe(true);
    ```

### Unknown Type Tests

- **Basic Validation**
  - Test basic unknown validation
  - Example:
    ```typescript
    const type = rt.unknown();
    
    expect(type.validate('test')).toBe(true);
    expect(type.validate(123)).toBe(true);
    expect(type.validate(true)).toBe(true);
    expect(type.validate(null)).toBe(true);
    expect(type.validate(undefined)).toBe(true);
    expect(type.validate({})).toBe(true);
    expect(type.validate([])).toBe(true);
    ```

### Never Type Tests

- **Basic Validation**
  - Test basic never validation
  - Example:
    ```typescript
    const type = rt.never();
    
    expect(type.validate('test')).toBe(false);
    expect(type.validate(123)).toBe(false);
    expect(type.validate(true)).toBe(false);
    expect(type.validate(null)).toBe(false);
    expect(type.validate(undefined)).toBe(false);
    expect(type.validate({})).toBe(false);
    expect(type.validate([])).toBe(false);
    ```

## Implementation Approach

The primitive types will be implemented as classes that extend a base Type class, with methods for validation, refinement, and creation. The implementation will follow a functional approach with immutable data structures and pure functions.

```typescript
// Base Type
class Type {
  is(value: unknown): boolean { /* ... */ }
  validate(value: unknown): boolean { /* ... */ }
  safeParse(value: unknown): Result { /* ... */ }
  parse(value: unknown): any { /* ... */ }
  create(partial?: any): any { /* ... */ }
}

// String Type
class StringType extends Type {
  min(length: number): StringType { /* ... */ }
  max(length: number): StringType { /* ... */ }
  length(length: number): StringType { /* ... */ }
  pattern(regex: RegExp): StringType { /* ... */ }
  email(): StringType { /* ... */ }
  url(): StringType { /* ... */ }
  trim(): StringType { /* ... */ }
  toLowerCase(): StringType { /* ... */ }
  toUpperCase(): StringType { /* ... */ }
  default(value: string): StringType { /* ... */ }
}

// Number Type
class NumberType extends Type {
  min(value: number): NumberType { /* ... */ }
  max(value: number): NumberType { /* ... */ }
  int(): NumberType { /* ... */ }
  positive(): NumberType { /* ... */ }
  negative(): NumberType { /* ... */ }
  multipleOf(value: number): NumberType { /* ... */ }
  default(value: number): NumberType { /* ... */ }
}

// Boolean Type
class BooleanType extends Type {
  default(value: boolean): BooleanType { /* ... */ }
}

// Literal Type
class LiteralType extends Type {
  constructor(private readonly value: any) {
    super();
  }
}

// Null Type
class NullType extends Type {
  // Implementation
}

// Undefined Type
class UndefinedType extends Type {
  // Implementation
}

// Any Type
class AnyType extends Type {
  // Implementation
}

// Unknown Type
class UnknownType extends Type {
  // Implementation
}

// Never Type
class NeverType extends Type {
  // Implementation
}
```

The primitive types will be the foundation of the Runtime Type System, providing the basic building blocks for more complex types.
