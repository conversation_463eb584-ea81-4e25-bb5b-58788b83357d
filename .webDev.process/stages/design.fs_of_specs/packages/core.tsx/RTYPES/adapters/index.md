# Adapters System - Specification

## Overview

The Adapters System provides a pluggable architecture for the Runtime Type System, allowing different validation libraries to be used under the hood while maintaining a consistent API. It follows the adapter pattern, where adapters translate between the RTYPES API and the underlying validation libraries.

## Adapter Interface

The Adapter Interface defines the contract that all adapters must implement:

- **validate(schema, value)**
  - Validates a value against a schema
  - Returns a boolean indicating if the value is valid
  - Example: `adapter.validate(schema, value)`

- **parse(schema, value)**
  - Parses a value according to a schema
  - Returns the parsed value if valid, throws if invalid
  - Example: `adapter.parse(schema, value)`

- **safeParse(schema, value)**
  - Safely parses a value according to a schema
  - Returns a result object with success/error
  - Example: `adapter.safeParse(schema, value)`

- **create(schema, partial)**
  - Creates a value from a partial input
  - Returns a new value with defaults filled in
  - Example: `adapter.create(schema, partial)`

- **extend(schema, extension)**
  - Extends a schema with additional properties
  - Returns a new schema
  - Example: `adapter.extend(schema, extension)`

## Built-in Adapters

The Adapters System provides the following built-in adapters:

- **ZodAdapter**
  - Adapter for the Zod validation library
  - Provides schema validation using Zod
  - Example: `RTYPE.createWithAdapter(zodAdapter)`

- **IoTsAdapter**
  - Adapter for the io-ts validation library
  - Provides schema validation using io-ts
  - Example: `RTYPE.createWithAdapter(ioTsAdapter)`

- **RuntypesAdapter**
  - Adapter for the Runtypes validation library
  - Provides schema validation using Runtypes
  - Example: `RTYPE.createWithAdapter(runtypesAdapter)`

- **CustomAdapter**
  - Custom adapter implementation
  - Provides schema validation using custom logic
  - Example: `RTYPE.createWithAdapter(customAdapter)`

## Adapter Factory

The Adapter Factory provides methods for creating adapters:

- **createAdapter(implementation)**
  - Creates an adapter from an implementation
  - Returns a new adapter
  - Example: `createAdapter({ validate: (schema, value) => { /* ... */ } })`

- **createZodAdapter()**
  - Creates a Zod adapter
  - Returns a new Zod adapter
  - Example: `createZodAdapter()`

- **createIoTsAdapter()**
  - Creates an io-ts adapter
  - Returns a new io-ts adapter
  - Example: `createIoTsAdapter()`

- **createRuntypesAdapter()**
  - Creates a Runtypes adapter
  - Returns a new Runtypes adapter
  - Example: `createRuntypesAdapter()`

## Adapter Selection

The Adapters System provides methods for selecting adapters:

- **RTYPE.createWithAdapter(adapter)**
  - Creates an rt instance with a specific adapter
  - Returns a new rt instance
  - Example: `RTYPE.createWithAdapter(zodAdapter)`

- **RTYPE.create()**
  - Creates an rt instance with the default adapter
  - Returns a new rt instance
  - Example: `RTYPE.create()`

- **RTYPE.setDefaultAdapter(adapter)**
  - Sets the default adapter for all rt instances
  - Returns void
  - Example: `RTYPE.setDefaultAdapter(zodAdapter)`

## Test Specifications

The following test specifications define the expected behavior of the Adapters System:

### Adapter Interface Tests

- **should define a consistent interface for all adapters**
  - Creates adapters for different validation libraries
  - Verifies that all adapters implement the same interface
  - Verifies that all adapters provide the required methods

- **should validate values against schemas**
  - Creates an adapter
  - Validates values against schemas
  - Verifies that valid values pass validation
  - Verifies that invalid values fail validation

- **should parse values according to schemas**
  - Creates an adapter
  - Parses values according to schemas
  - Verifies that valid values are parsed correctly
  - Verifies that invalid values throw errors

- **should safely parse values according to schemas**
  - Creates an adapter
  - Safely parses values according to schemas
  - Verifies that valid values return success results
  - Verifies that invalid values return error results

- **should create values from partial inputs**
  - Creates an adapter
  - Creates values from partial inputs
  - Verifies that missing properties are filled with default values

- **should extend schemas with additional properties**
  - Creates an adapter
  - Extends schemas with additional properties
  - Verifies that the extended schemas include the additional properties

### Built-in Adapters Tests

- **should provide a Zod adapter**
  - Creates a Zod adapter
  - Validates values using the Zod adapter
  - Verifies that the adapter correctly uses Zod for validation

- **should provide an io-ts adapter**
  - Creates an io-ts adapter
  - Validates values using the io-ts adapter
  - Verifies that the adapter correctly uses io-ts for validation

- **should provide a Runtypes adapter**
  - Creates a Runtypes adapter
  - Validates values using the Runtypes adapter
  - Verifies that the adapter correctly uses Runtypes for validation

- **should provide a custom adapter**
  - Creates a custom adapter
  - Validates values using the custom adapter
  - Verifies that the adapter correctly uses custom logic for validation

### Adapter Factory Tests

- **should create adapters from implementations**
  - Creates an adapter from an implementation
  - Validates values using the adapter
  - Verifies that the adapter correctly uses the implementation for validation

- **should create Zod adapters**
  - Creates a Zod adapter using the factory
  - Validates values using the adapter
  - Verifies that the adapter correctly uses Zod for validation

- **should create io-ts adapters**
  - Creates an io-ts adapter using the factory
  - Validates values using the adapter
  - Verifies that the adapter correctly uses io-ts for validation

- **should create Runtypes adapters**
  - Creates a Runtypes adapter using the factory
  - Validates values using the adapter
  - Verifies that the adapter correctly uses Runtypes for validation

### Adapter Selection Tests

- **should create rt instances with specific adapters**
  - Creates an rt instance with a specific adapter
  - Validates values using the rt instance
  - Verifies that the rt instance correctly uses the adapter for validation

- **should create rt instances with the default adapter**
  - Creates an rt instance without specifying an adapter
  - Validates values using the rt instance
  - Verifies that the rt instance correctly uses the default adapter for validation

- **should set the default adapter for all rt instances**
  - Sets the default adapter
  - Creates an rt instance without specifying an adapter
  - Validates values using the rt instance
  - Verifies that the rt instance correctly uses the default adapter for validation
