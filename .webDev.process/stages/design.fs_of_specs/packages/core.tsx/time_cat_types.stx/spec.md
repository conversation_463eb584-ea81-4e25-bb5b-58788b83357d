# Time Categorical Type System Specification

## Overview

This specification defines the categorical type system for time-related concepts in the SpiceTime architecture. It leverages category theory to model temporal structures, events, timelines, and temporal relationships as categorical objects and morphisms.

The time_cat_types package acts as a Higher-Order Component (HOC) that extends the cat_types component by extending its context with time-specific categorical types, functors, and operations. This allows for seamless integration of temporal concepts into the broader categorical framework of the SpiceTime architecture.

The package follows the Context Scoping Tree pattern described in concept #56, establishing bidirectional inheritance where it extends the cat_types context while simultaneously exposing its public API as a namespace within the parent context. It leverages the extensions defining namespaces approach from concept #57 to organize its API, and uses pragmas to compose extensions as needed.

## 1. Core Categorical Types

### 1.1 TimelineCategory

The primary category representing timelines and temporal structures:

```typescript
interface TimelineCategory<E extends TimelineEvent> extends Category<TimelineObject<E>, TimelineMorphism<E>> {
  // Timeline-specific category operations
  getTimeline(id: string): TimelineObject<E>;
  addEvent(timelineId: string, event: E): void;
  removeEvent(timelineId: string, eventId: string): void;
  getEventsByTimeRange(timelineId: string, start: number, end: number): E[];
}
```

### 1.2 TimelineObject

Objects representing timelines in the category:

```typescript
interface TimelineObject<E extends TimelineEvent> extends CatObject {
  events: E[];
  startTime: number;
  endTime: number;
  resolution: number;
  isLinear: boolean;
  branchingFactor: number;
  metadata: TimelineMetadata;
}
```

### 1.3 TimelineEvent

Interface for events that occur on timelines:

```typescript
interface TimelineEvent extends CatObject {
  timestamp: number;
  duration: number;
  type: string;
  data: any;
  causedBy?: string[]; // IDs of causal events
  affects?: string[]; // IDs of affected events
}
```

### 1.4 TimelineMorphism

Morphisms representing transformations between timelines:

```typescript
interface TimelineMorphism<E extends TimelineEvent> extends Morphism<TimelineObject<E>> {
  // Specific transformation types
  type: 'stretch' | 'compress' | 'shift' | 'merge' | 'split' | 'filter';

  // Apply the transformation with temporal context
  applyWithContext(source: TimelineObject<E>, context: TemporalContext): TimelineObject<E>;
}
```

## 2. Functors and Natural Transformations

### 2.1 TemporalFunctor

A functor mapping between different temporal categories:

```typescript
interface TemporalFunctor<E1 extends TimelineEvent, E2 extends TimelineEvent>
  extends Functor<TimelineCategory<E1>, TimelineCategory<E2>> {

  // Maps a timeline to another timeline
  mapObject(timeline: TimelineObject<E1>): TimelineObject<E2>;

  // Maps timeline transformations
  mapMorphism(morphism: TimelineMorphism<E1>): TimelineMorphism<E2>;
}
```

### 2.2 TimeScaleTransformation

A natural transformation between different time scales:

```typescript
interface TimeScaleTransformation<E extends TimelineEvent> extends NaturalTransformation<
  MicroTimeFunctor<E>,
  MacroTimeFunctor<E>
> {
  // Component at a specific timeline
  componentAt(timeline: TimelineObject<E>): TimelineMorphism<E>;

  // Scale factor between micro and macro time
  scaleFactor: number;
}
```

## 3. Monads and Computational Effects

### 3.1 UncertaintyMonad

A monad representing uncertainty in temporal measurements:

```typescript
interface UncertaintyMonad<E extends TimelineEvent> extends Monad<TimelineCategory<E>> {
  // Wrap a timeline with uncertainty
  unit(timeline: TimelineObject<E>): UncertainTimeline<E>;

  // Apply a function that produces uncertain results
  bind<F extends TimelineEvent>(
    uncertainTimeline: UncertainTimeline<E>,
    f: (timeline: TimelineObject<E>) => UncertainTimeline<F>
  ): UncertainTimeline<F>;
}
```

### 3.2 BranchingMonad

A monad for handling branching timelines:

```typescript
interface BranchingMonad<E extends TimelineEvent> extends Monad<TimelineCategory<E>> {
  // Create a branching timeline from a linear timeline
  unit(timeline: TimelineObject<E>): BranchingTimeline<E>;

  // Apply a function that produces branching timelines
  bind<F extends TimelineEvent>(
    branchingTimeline: BranchingTimeline<E>,
    f: (timeline: TimelineObject<E>) => BranchingTimeline<F>
  ): BranchingTimeline<F>;

  // Get all possible branches
  getBranches(branchingTimeline: BranchingTimeline<E>): TimelineObject<E>[];

  // Collapse to most probable branch
  collapse(branchingTimeline: BranchingTimeline<E>): TimelineObject<E>;
}
```

## 4. Integration with Other Categorical Types

The time categorical type system integrates with other categorical type systems through:

### 4.1 SpacetimeFunctor

A functor mapping between time categories and space categories:

```typescript
interface SpacetimeFunctor<E extends TimelineEvent, S extends SpatialObject>
  extends Functor<TimelineCategory<E>, SpatialCategory<S>> {

  // Map a timeline to a spatial trajectory
  mapObject(timeline: TimelineObject<E>): SpatialObject<S>;

  // Map timeline transformations to spatial transformations
  mapMorphism(morphism: TimelineMorphism<E>): SpatialMorphism<S>;
}
```

### 4.2 DomainTimelineFunctor

A generic functor for mapping domain-specific categories to timeline categories:

```typescript
interface DomainTimelineFunctor<D extends CatObject, E extends TimelineEvent>
  extends Functor<DomainCategory<D>, TimelineCategory<E>> {

  // Map a domain object to a timeline
  mapObject(domainObject: D): TimelineObject<E>;

  // Map domain transformations to timeline transformations
  mapMorphism(morphism: DomainMorphism<D>): TimelineMorphism<E>;
}
```

## 5. Implementation Requirements

Implementations of this categorical type system must:

1. Preserve categorical laws (identity, associativity)
2. Maintain causal relationships between events
3. Support both linear and branching timelines
4. Integrate with other categorical type systems
5. Provide serialization/deserialization for WebAssembly integration

## 6. WebAssembly Integration

The time categorical types should support WebAssembly integration:

```typescript
interface WasmTimelineType<E extends TimelineEvent> {
  // Convert to WebAssembly representation
  toWasm(timeline: TimelineObject<E>): number;

  // Create from WebAssembly representation
  fromWasm(ptr: number): TimelineObject<E>;

  // Apply transformations in WebAssembly
  applyTransformWasm(sourcePtr: number, transformPtr: number): number;
}
```

## 7. Usage Examples

### 7.1 Creating a Timeline Category

```typescript
// Create a timeline category
const timelineCategory = new ConcreteTimelineCategory<SimpleEvent>();

// Create timeline objects
const timeline1 = createTimelineObject<SimpleEvent>({
  events: [],
  startTime: 0,
  endTime: 100,
  resolution: 0.1,
  isLinear: true,
  branchingFactor: 0,
  metadata: { name: 'Main Timeline', description: 'Primary sequence of events' }
});

// Add timeline to the category
timelineCategory.addObject(timeline1);

// Add events to the timeline
timelineCategory.addEvent(timeline1.id, {
  id: 'event1',
  timestamp: 10,
  duration: 2,
  type: 'start',
  data: { message: 'Process started' }
});

timelineCategory.addEvent(timeline1.id, {
  id: 'event2',
  timestamp: 15,
  duration: 5,
  type: 'processing',
  data: { progress: 0.5 },
  causedBy: ['event1']
});
```

### 7.2 Applying Timeline Transformations

```typescript
// Create a timeline shift morphism
const shiftMorphism = new TimelineShiftMorphism<SimpleEvent>(
  timeline1,
  timeline1,
  { shiftAmount: 5 }
);

// Add morphism to the category
timelineCategory.addMorphism(shiftMorphism);

// Apply the morphism
const shiftedTimeline = shiftMorphism.apply(timeline1);

// Get events in a time range
const events = timelineCategory.getEventsByTimeRange(
  shiftedTimeline.id,
  15,
  25
);
```

### 7.3 Using the Branching Monad

```typescript
// Create a branching monad
const branchingMonad = new ConcreteBranchingMonad<SimpleEvent>();

// Create a branching timeline
const branchingTimeline = branchingMonad.unit(timeline1);

// Define a branching function
const branchingFunction = (timeline: TimelineObject<SimpleEvent>): BranchingTimeline<SimpleEvent> => {
  // Create multiple branches based on different event outcomes
  // ...
  return new ConcreteBranchingTimeline<SimpleEvent>(branches);
};

// Apply the branching function
const resultBranching = branchingMonad.bind(branchingTimeline, branchingFunction);

// Get all possible branches
const allBranches = branchingMonad.getBranches(resultBranching);

// Collapse to most probable branch
const mostProbableBranch = branchingMonad.collapse(resultBranching);
```

## 8. Higher-Order Component Implementation

### 8.1 TimeCatTypesProvider

The time_cat_types package provides a TimeCatTypesProvider component that extends the CatTypesProvider from the cat_types package:

```typescript
/**
 * Props for the TimeCatTypesProvider component
 */
interface TimeCatTypesProviderProps {
  /**
   * Child components
   */
  children: React.ReactNode;

  /**
   * Initial timeline configuration
   */
  initialTimeline?: TimelineConfig;

  /**
   * Treenity node for storing timeline state
   */
  treenityNode?: TreenityNode;
}

/**
 * TimeCatTypesProvider component
 */
const TimeCatTypesProvider: React.FC<TimeCatTypesProviderProps> = ({
  children,
  initialTimeline,
  treenityNode
}) => {
  // Create or use provided Treenity node
  const node = useMemo(() => {
    if (treenityNode) return treenityNode;

    return new TreenityNode({
      path: ['temporal', 'timeline'],
      initialState: initialTimeline || {
        events: [],
        startTime: 0,
        endTime: 100,
        resolution: 0.1,
        isLinear: true,
        branchingFactor: 0,
        metadata: { name: 'Main Timeline' }
      }
    });
  }, [treenityNode, initialTimeline]);

  // Create time context extensions
  const timeExtensions = useMemo(() => ({
    // Timeline category operations
    createTimelineCategory: <E extends TimelineEvent>() => new ConcreteTimelineCategory<E>(),
    createTimelineObject: <E extends TimelineEvent>(config: TimelineObjectConfig<E>) =>
      createTimelineObject<E>(config),
    createTimelineMorphism: <E extends TimelineEvent>(
      source: TimelineObject<E>,
      target: TimelineObject<E>,
      type: string
    ) => new TimelineMorphism<E>(source, target, type),

    // Time-specific functors
    createTemporalFunctor: <E1 extends TimelineEvent, E2 extends TimelineEvent>() =>
      new ConcreteTemporalFunctor<E1, E2>(),
    createTimeScaleFunctor: <E extends TimelineEvent>(scaleFactor: number) =>
      new ConcreteTimeScaleFunctor<E>(scaleFactor),

    // Time-specific monads
    createUncertaintyMonad: <E extends TimelineEvent>() => new ConcreteUncertaintyMonad<E>(),
    createBranchingMonad: <E extends TimelineEvent>() => new ConcreteBranchingMonad<E>(),

    // Treenity integration
    treenityNode: node,
    synchronizeWithTreenity: <E extends TimelineEvent>(timelineObj: TimelineObject<E>) =>
      synchronizeWithTreenity(timelineObj),
    createFromTreenityNode: <E extends TimelineEvent>(node: TreenityNode) =>
      mapTreenityNodeToTimelineObject<E>(node)
  }), [node]);

  return (
    <CatTypesProvider>
      {/* Use CatTypesContext.Consumer to access the base context */}
      <CatTypesContext.Consumer>
        {(parentContext) => {
          // Create context that extends parent (following Context Scoping Tree pattern)
          const timeContext = Object.create(parentContext);

          // Add time API methods to time context
          Object.assign(timeContext, timeExtensions);

          // Add time namespace to parent context
          parentContext.time = timeExtensions;

          return (
            <TimeCatTypesContext.Provider value={timeContext}>
              {children}
            </TimeCatTypesContext.Provider>
          );
        }}
      </CatTypesContext.Consumer>
    </CatTypesProvider>
  );
};
```

### 8.2 useTimeCatTypes Hook

The package provides a hook for accessing the extended context:

```typescript
/**
 * Hook for accessing the time cat types context
 */
function useTimeCatTypes() {
  const context = useContext(TimeCatTypesContext);

  if (!context) {
    throw new Error('useTimeCatTypes must be used within a TimeCatTypesProvider');
  }

  return context;
}
```

### 8.3 withTimeCatTypes HOC

The package also provides a Higher-Order Component for class components:

```typescript
/**
 * HOC for injecting time cat types into a component
 */
function withTimeCatTypes<P>(Component: React.ComponentType<P & TimeCatTypesContextType>) {
  return function WithTimeCatTypesComponent(props: P) {
    return (
      <TimeCatTypesContext.Consumer>
        {(context) => <Component {...props} {...context} />}
      </TimeCatTypesContext.Consumer>
    );
  };
}
```

## 9. Usage with cat_types

### 9.1 Basic Usage

```tsx
import { TimeCatTypesProvider, useTimeCatTypes } from '@spicetime/time_cat_types';

// Component that uses time cat types
const TimelineComponent = () => {
  // Access both base cat_types and time extensions
  const {
    // From base cat_types
    createCategory,
    createCatObject,
    createMorphism,

    // From time extensions
    createTimelineCategory,
    createTimelineObject,
    createBranchingMonad
  } = useTimeCatTypes();

  // Use both base and time operations
  const baseCategory = createCategory();
  const timelineCategory = createTimelineCategory<SimpleEvent>();

  // Create a timeline object
  const timeline = createTimelineObject<SimpleEvent>({
    events: [],
    startTime: 0,
    endTime: 100,
    resolution: 0.1,
    isLinear: true,
    branchingFactor: 0,
    metadata: { name: 'Main Timeline' }
  });

  // Add to timeline category
  timelineCategory.addObject(timeline);

  return <div>Timeline Component</div>;
};

// App with time cat types provider
const App = () => {
  return (
    <TimeCatTypesProvider>
      <TimelineComponent />
    </TimeCatTypesProvider>
  );
};
```

### 9.2 Combining with Other Extensions

```tsx
import { TimeCatTypesProvider } from '@spicetime/time_cat_types';
import { ForestCatTypesProvider } from '@spicetime/forestry_cat_types';

// App with multiple cat types extensions
const App = () => {
  return (
    <TimeCatTypesProvider>
      <ForestCatTypesProvider>
        <CombinedComponent />
      </ForestCatTypesProvider>
    </TimeCatTypesProvider>
  );
};
```

## 10. Conclusion

This categorical type system for time provides a mathematically rigorous framework for modeling temporal structures and their transformations. By leveraging category theory, it enables precise representations of time-related concepts and integrations with other domains in the SpiceTime architecture.

As a Higher-Order Component that extends the cat_types context, time_cat_types seamlessly integrates temporal concepts into the broader categorical framework, allowing for powerful compositions and transformations across different domains.
