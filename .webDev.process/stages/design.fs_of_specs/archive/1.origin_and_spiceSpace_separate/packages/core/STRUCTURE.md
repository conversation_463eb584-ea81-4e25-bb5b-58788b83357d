# SpiceTime Core Package Structure

## Overview

The core package (formerly utils) provides the fundamental building blocks of the SpiceTime architecture. It follows a tripodic process model defined in origin.rfr, which models the process of creating and evolving software through space and time.

## Package Structure

```
@spicetime/core
├── origin.rfr                # Origin reference frame
│   ├── cat_types.obj         # Categorical types object
│   │   ├── src               # Source code
│   │   │   ├── atomic        # Atomic types
│   │   │   ├── composite     # Composite types
│   │   │   └── functional    # Functional types
│   │   ├── test              # Tests
│   │   └── docs              # Documentation
│   ├── process.rfr           # Process reference frame
│   │   ├── src               # Source code
│   │   │   ├── atomic        # Atomic processes
│   │   │   ├── composite     # Composite processes
│   │   │   └── functional    # Functional processes
│   │   ├── test              # Tests
│   │   └── docs              # Documentation
│   │       └── spec_of_package # Package specifications
│   └── spice.space           # SpiceTime version space
│       ├── src               # Source code
│       │   ├── atomic        # Atomic version nodes
│       │   ├── composite     # Composite version nodes
│       │   └── functional    # Functional version nodes
│       ├── test              # Tests
│       └── docs              # Documentation
├── error                     # Error handling (formerly utils_error)
│   ├── src                   # Source code
│   │   ├── atomic            # Atomic errors
│   │   ├── composite         # Composite errors
│   │   └── functional        # Functional errors
│   ├── test                  # Tests
│   └── docs                  # Documentation
├── pragma                    # Pragma process
│   ├── src                   # Source code
│   │   ├── atomic            # Atomic pragmas
│   │   ├── composite         # Composite pragmas
│   │   └── functional        # Functional pragmas
│   ├── test                  # Tests
│   └── docs                  # Documentation
└── webDev                    # Web development process
    ├── src                   # Source code
    │   ├── atomic            # Atomic web development components
    │   ├── composite         # Composite web development components
    │   └── functional        # Functional web development components
    ├── test                  # Tests
    └── docs                  # Documentation
```

## Tripodic Process Model

The core package models a tripodic process defined in origin.rfr:

1. **Origin Reference Frame (origin.rfr)**: The foundation that defines the coordinate system for the entire architecture.

2. **Categorical Types Object (cat_types.obj)**: Defines the types used throughout the system, organized categorically.

3. **Process Reference Frame (process.rfr)**: Defines the processes that operate on the types, transforming them through space and time.

4. **SpiceTime Version Space (spice.space)**: Defines the version space where each version produces its version of node - the value of the process.

This tripodic model allows the system to evolve through space and time, with each version building on the previous ones.

## Module Organization

Each module in the core package is organized into three layers:

1. **Atomic**: The fundamental, indivisible units of the module.
2. **Composite**: Combinations of atomic units to form more complex structures.
3. **Functional**: Higher-order functions and transformations that operate on atomic and composite units.

This organization allows for a clean separation of concerns and a clear path for composition and transformation.

## Integration with Other Packages

The core package integrates with other packages in the following ways:

1. **Linguistics Package**: Provides the linguistic terms and operations that build on the core types and processes.

2. **Blockchain Package**: Uses the core types and processes to model blockchain interactions.

3. **Project Package**: Uses the core types and processes to manage projects.

4. **Runtime Package**: Builds on the core processes to provide a runtime environment.

5. **Services Package**: Uses the core types and processes to define services.

6. **Components Package**: Uses the core types and processes to define UI components.

## Next Steps

1. **Document the tripodic process**: Create detailed documentation for the tripodic process model.

2. **Create API specifications**: Define the API for each module in the core package.

3. **Create schema definitions**: Define the schemas for the data structures used in the core package.

4. **Create module documentation**: Document each module in the core package.

5. **Create examples**: Provide examples of using the core package in different scenarios.
