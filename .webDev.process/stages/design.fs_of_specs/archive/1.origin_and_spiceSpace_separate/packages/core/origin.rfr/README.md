# Origin Reference Frame (origin.rfr)

## Overview

The Origin Reference Frame (origin.rfr) is the foundation of the SpiceTime architecture. It defines the coordinate system for the entire architecture, providing the basis for all types, processes, and transformations.

## Structure

```
origin.rfr
├── cat_types.obj         # Categorical types object
│   ├── src               # Source code
│   │   ├── atomic        # Atomic types
│   │   ├── composite     # Composite types
│   │   └── functional    # Functional types
│   ├── test              # Tests
│   └── docs              # Documentation
├── process.rfr           # Process reference frame
│   ├── src               # Source code
│   │   ├── atomic        # Atomic processes
│   │   ├── composite     # Composite processes
│   │   └── functional    # Functional processes
│   ├── test              # Tests
│   └── docs              # Documentation
│       └── spec_of_package # Package specifications
└── spice.space           # SpiceTime version space
    ├── src               # Source code
    │   ├── atomic        # Atomic version nodes
    │   ├── composite     # Composite version nodes
    │   └── functional    # Functional version nodes
    ├── test              # Tests
    └── docs              # Documentation
```

## Categorical Types Object (cat_types.obj)

The Categorical Types Object (cat_types.obj) defines the types used throughout the system, organized categorically. It provides a foundation for type-safe operations and transformations.

### Atomic Types

Atomic types are the fundamental, indivisible units of the type system. They include:

- **Primitive Types**: Basic types like string, number, boolean, etc.
- **Unit Types**: Types with exactly one value, used for signaling.
- **Void Types**: Types with no values, used for error handling.

### Composite Types

Composite types are combinations of atomic types to form more complex structures. They include:

- **Product Types**: Types that combine multiple values, like records or tuples.
- **Sum Types**: Types that represent alternatives, like unions or enums.
- **Function Types**: Types that represent functions from one type to another.

### Functional Types

Functional types are higher-order types that operate on other types. They include:

- **Functor Types**: Types that can be mapped over.
- **Applicative Types**: Types that can apply functions wrapped in the type.
- **Monad Types**: Types that can be chained together.

## Process Reference Frame (process.rfr)

The Process Reference Frame (process.rfr) defines the processes that operate on the types, transforming them through space and time. It provides a foundation for process-based programming.

### Atomic Processes

Atomic processes are the fundamental, indivisible units of the process system. They include:

- **Identity Process**: A process that returns its input unchanged.
- **Constant Process**: A process that always returns a constant value.
- **Composition Process**: A process that composes two other processes.

### Composite Processes

Composite processes are combinations of atomic processes to form more complex structures. They include:

- **Parallel Processes**: Processes that run in parallel.
- **Sequential Processes**: Processes that run in sequence.
- **Conditional Processes**: Processes that run conditionally.

### Functional Processes

Functional processes are higher-order processes that operate on other processes. They include:

- **Process Transformers**: Processes that transform other processes.
- **Process Combinators**: Processes that combine other processes.
- **Process Factories**: Processes that create other processes.

## SpiceTime Version Space (spice.space)

The SpiceTime Version Space (spice.space) defines the version space where each version produces its version of node - the value of the process. It provides a foundation for versioning and evolution.

### Atomic Version Nodes

Atomic version nodes are the fundamental, indivisible units of the version space. They include:

- **Version Node**: A node in the version space.
- **Version Edge**: An edge connecting two version nodes.
- **Version Path**: A path through the version space.

### Composite Version Nodes

Composite version nodes are combinations of atomic version nodes to form more complex structures. They include:

- **Version Tree**: A tree of version nodes.
- **Version Graph**: A graph of version nodes.
- **Version Forest**: A forest of version trees.

### Functional Version Nodes

Functional version nodes are higher-order nodes that operate on other nodes. They include:

- **Version Transformers**: Nodes that transform other nodes.
- **Version Combinators**: Nodes that combine other nodes.
- **Version Factories**: Nodes that create other nodes.

## Integration with Other Modules

The Origin Reference Frame integrates with other modules in the following ways:

1. **Error Handling**: Uses the error module for error handling and reporting.
2. **Pragma Process**: Provides the foundation for the pragma process.
3. **WebDev Process**: Provides the foundation for the web development process.

## API

The Origin Reference Frame provides the following API:

```typescript
// Categorical Types Object
export const createType = (name: string, config: TypeConfig): Type;
export const composeTypes = (a: Type, b: Type): Type;
export const mapType = <A, B>(type: Type<A>, f: (a: A) => B): Type<B>;

// Process Reference Frame
export const createProcess = (name: string, config: ProcessConfig): Process;
export const composeProcesses = (a: Process, b: Process): Process;
export const runProcess = <A, B>(process: Process<A, B>, input: A): B;

// SpiceTime Version Space
export const createVersionNode = (name: string, config: VersionNodeConfig): VersionNode;
export const connectVersionNodes = (a: VersionNode, b: VersionNode): VersionEdge;
export const traverseVersionSpace = (start: VersionNode, path: VersionPath): VersionNode;
```

## Examples

### Creating and Composing Types

```typescript
import { createType, composeTypes } from '@spicetime/core/origin.rfr/cat_types.obj';

// Create atomic types
const stringType = createType('string', { kind: 'primitive' });
const numberType = createType('number', { kind: 'primitive' });

// Create a composite type
const pairType = composeTypes(stringType, numberType);

// Use the type
const pair: TypeOf<typeof pairType> = ['hello', 42];
```

### Creating and Running Processes

```typescript
import { createProcess, composeProcesses, runProcess } from '@spicetime/core/origin.rfr/process.rfr';

// Create atomic processes
const doubleProcess = createProcess('double', {
  run: (input: number) => input * 2
});

const addOneProcess = createProcess('addOne', {
  run: (input: number) => input + 1
});

// Compose processes
const doubleThenAddOneProcess = composeProcesses(doubleProcess, addOneProcess);

// Run the process
const result = runProcess(doubleThenAddOneProcess, 5); // 11
```

### Creating and Traversing Version Spaces

```typescript
import { createVersionNode, connectVersionNodes, traverseVersionSpace } from '@spicetime/core/origin.rfr/spice.space';

// Create version nodes
const v1 = createVersionNode('v1', { value: 'Initial version' });
const v2 = createVersionNode('v2', { value: 'Second version' });
const v3 = createVersionNode('v3', { value: 'Third version' });

// Connect version nodes
const v1ToV2 = connectVersionNodes(v1, v2);
const v2ToV3 = connectVersionNodes(v2, v3);

// Traverse the version space
const path = [v1ToV2, v2ToV3];
const finalNode = traverseVersionSpace(v1, path); // v3
```

## Next Steps

1. **Document the categorical types**: Create detailed documentation for the categorical types.
2. **Document the process reference frame**: Create detailed documentation for the process reference frame.
3. **Document the SpiceTime version space**: Create detailed documentation for the SpiceTime version space.
4. **Create API specifications**: Define the API for each module in the origin.rfr.
5. **Create schema definitions**: Define the schemas for the data structures used in the origin.rfr.
6. **Create examples**: Provide examples of using the origin.rfr in different scenarios.
