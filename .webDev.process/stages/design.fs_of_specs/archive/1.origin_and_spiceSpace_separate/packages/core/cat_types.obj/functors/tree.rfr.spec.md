# Tree Functor Implementation Specification

## Overview

This specification defines the implementation details for Tree Functors in the cat_types package. A Tree Functor is a root functor that wraps a treenity tree (@treenity/core), providing a categorical view of tree structures.

## Dependencies

```typescript
import { createTree, Tree, TreeNode } from '@treenity/core';
import { Category } from '../cat';
import { CatObject } from '../catObject';
import { Morphism } from '../morphism';
import { BaseMorphism } from '../morphism';
import { RootFunctor } from '../__types/rfr.type';
import { BaseRootFunctor } from './rfr.fr';
```

## Class Definition

```typescript
/**
 * Implementation of a Tree Functor
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class TreeFunctorImpl<
  T extends CatObject,
  TM extends Morphism<T>
> extends BaseRootFunctor<CatObject, Morphism<CatObject>, T, TM> 
  implements TreeFunctor<T, TM> {
  
  /**
   * The underlying treenity tree
   */
  readonly tree: Tree;
  
  /**
   * Map of tree nodes to category objects
   */
  private nodeMap: Map<string, T> = new Map();
  
  /**
   * Creates a new tree functor
   * 
   * @param target Target category
   * @param objectMap Function that maps tree nodes to target objects
   * @param morphismMap Function that maps tree morphisms to target morphisms
   * @param rootValue Value for the root node
   */
  constructor(
    target: Category<T, TM>,
    objectMap: (node: TreeNode) => T,
    morphismMap: (source: TreeNode, target: TreeNode) => TM,
    rootValue: any = 'root'
  ) {
    // Create a simple source category for tree nodes
    const sourceCategory = createTreeNodeCategory();
    
    // Create the tree
    const tree = createTree({ id: 'root', value: rootValue, children: [] });
    
    // Store the tree
    this.tree = tree;
    
    // Create the root functor
    super(
      sourceCategory,
      target,
      (obj) => {
        const node = obj.value as TreeNode;
        const mappedObj = objectMap(node);
        this.nodeMap.set(node.id, mappedObj);
        return mappedObj;
      },
      (morphism) => {
        const sourceNode = morphism.source.value as TreeNode;
        const targetNode = morphism.target.value as TreeNode;
        return morphismMap(sourceNode, targetNode);
      }
    );
    
    // Initialize with the root node
    const rootNode = tree.getNode('root')!;
    const rootObj = objectMap(rootNode);
    this.nodeMap.set('root', rootObj);
    target.addObject(rootObj);
  }
  
  /**
   * Gets a node by its ID
   * 
   * @param id Node ID
   * @returns The node with the given ID, or undefined if not found
   */
  getNode(id: string): T | undefined {
    return this.nodeMap.get(id);
  }
  
  /**
   * Adds a child to a parent node
   * 
   * @param parentId Parent node ID
   * @param value Child node value
   * @returns The new child node
   */
  addChild(parentId: string, value: any): T | undefined {
    // Get the parent node from the tree
    const parentNode = this.tree.getNode(parentId);
    if (!parentNode) return undefined;
    
    // Add a child to the parent node
    const childNode = this.tree.addChild(parentId, value);
    if (!childNode) return undefined;
    
    // Map the child node to a target object
    const childObj = this.mapObject(createCatObject(childNode));
    
    // Add the child object to the target category
    this.target.addObject(childObj);
    
    // Get the parent object
    const parentObj = this.nodeMap.get(parentId);
    if (!parentObj) return undefined;
    
    // Create a morphism from parent to child
    const morphism = new BaseMorphism(
      parentObj,
      childObj,
      (parent) => childObj
    ) as TM;
    
    // Add the morphism to the target category
    this.target.addMorphism(morphism);
    
    return childObj;
  }
  
  /**
   * Removes a node
   * 
   * @param id Node ID
   * @returns True if the node was removed
   */
  removeNode(id: string): boolean {
    // Remove the node from the tree
    const removed = this.tree.removeNode(id);
    if (!removed) return false;
    
    // Remove the node from the node map
    this.nodeMap.delete(id);
    
    return true;
  }
}
```

## Helper Functions

```typescript
/**
 * Creates a category for tree nodes
 * 
 * @returns A category for tree nodes
 */
function createTreeNodeCategory(): Category<CatObject, Morphism<CatObject>> {
  // Implementation details...
}

/**
 * Creates a category object from a tree node
 * 
 * @param node Tree node
 * @returns Category object
 */
function createCatObject(node: TreeNode): CatObject {
  return {
    id: { id: Symbol(node.id), name: node.id },
    value: node
  };
}
```

## Utility Function

```typescript
/**
 * Creates a tree functor
 * 
 * @param target Target category
 * @param objectMap Function that maps tree nodes to target objects
 * @param morphismMap Function that maps tree morphisms to target morphisms
 * @param rootValue Value for the root node
 * @returns A new tree functor
 */
export function createTreeFunctor<
  T extends CatObject,
  TM extends Morphism<T>
>(
  target: Category<T, TM>,
  objectMap: (node: TreeNode) => T,
  morphismMap: (source: TreeNode, target: TreeNode) => TM,
  rootValue: any = 'root'
): TreeFunctor<T, TM> {
  return new TreeFunctorImpl(
    target,
    objectMap,
    morphismMap,
    rootValue
  );
}
```

## Implementation Details

### Constructor

The constructor takes four parameters:
1. `target`: The target category
2. `objectMap`: A function that maps tree nodes to target objects
3. `morphismMap`: A function that maps tree morphisms to target morphisms
4. `rootValue`: The value for the root node (default: 'root')

It creates a source category for tree nodes, creates a treenity tree, and initializes the root functor.

### getNode

The `getNode` method gets a node by its ID:

1. It looks up the node in the node map
2. It returns the corresponding target object, or undefined if not found

### addChild

The `addChild` method adds a child to a parent node:

1. It gets the parent node from the tree
2. It adds a child to the parent node using the treenity tree API
3. It maps the child node to a target object
4. It adds the child object to the target category
5. It creates a morphism from parent to child
6. It adds the morphism to the target category
7. It returns the child object

### removeNode

The `removeNode` method removes a node:

1. It removes the node from the tree using the treenity tree API
2. It removes the node from the node map
3. It returns true if the node was removed, false otherwise

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functors/tree.rfr.ts
```

## Dependencies

- `@treenity/core`: Provides the Tree and TreeNode interfaces
- `__types/tree.type.ts`: Type definitions for tree functors
- `functors/rfr.fr.ts`: Root functor implementation
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
