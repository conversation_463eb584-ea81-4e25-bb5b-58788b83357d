# Functor Specification

## Overview

A functor is a mapping between categories that preserves the categorical structure. It maps objects to objects and morphisms to morphisms, preserving identity morphisms and composition.

## Type Definition

```typescript
interface Functor<
  S extends CatObject,
  SM extends Morphism<S>,
  T extends CatObject,
  TM extends Morphism<T>
> {
  readonly source: Category<S, SM>;
  readonly target: Category<T, TM>;
  mapObject(obj: S): T;
  mapMorphism(morphism: SM): TM;
  validateLaws(): boolean;
}
```

## Implementation

The functor implementation should:

1. Store the source and target categories
2. Provide methods to map objects and morphisms
3. Validate that the functor laws hold:
   - F(id_A) = id_F(A) (Identity morphisms map to identity morphisms)
   - F(g ∘ f) = F(g) ∘ F(f) (Composition is preserved)

## Usage

```typescript
// Create a functor between two categories
const functor = new BaseFunctor(
  sourceCategory,
  targetCategory,
  (obj) => mapObjectFunction(obj),
  (morphism) => mapMorphismFunction(morphism)
);

// Map an object
const targetObject = functor.mapObject(sourceObject);

// Map a morphism
const targetMorphism = functor.mapMorphism(sourceMorphism);

// Validate functor laws
const isValid = functor.validateLaws();
```

## Special Cases

### Endofunctor

An endofunctor is a functor from a category to itself:

```typescript
function createEndofunctor<
  O extends CatObject,
  M extends Morphism<O>
>(
  category: Category<O, M>,
  objectMap: (obj: O) => O,
  morphismMap: (morphism: M) => M
): Functor<O, M, O, M> {
  return new BaseFunctor(
    category,
    category,
    objectMap,
    morphismMap
  );
}
```

### Identity Functor

The identity functor maps each object and morphism to itself:

```typescript
function identityFunctor<
  O extends CatObject,
  M extends Morphism<O>
>(category: Category<O, M>): Functor<O, M, O, M> {
  return createEndofunctor(
    category,
    obj => obj,
    morphism => morphism
  );
}
```

## File Structure

- `__types/functor.type.ts`: Type definitions
- `functor.ts`: Implementation
