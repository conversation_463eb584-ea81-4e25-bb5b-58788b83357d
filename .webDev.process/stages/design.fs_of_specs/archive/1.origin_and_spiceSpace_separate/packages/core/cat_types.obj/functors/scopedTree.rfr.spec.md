# Scoped Tree Functor Implementation Specification

## Overview

This specification defines the implementation details for Scoped Tree Functors in the cat_types package. A Scoped Tree Functor extends a Tree Functor with scoping capabilities, using JavaScript's prototype chain to implement scope inheritance.

## Dependencies

```typescript
import { Tree, TreeNode } from '@treenity/core';
import { Category } from '../cat';
import { CatObject } from '../catObject';
import { Morphism } from '../morphism';
import { TreeFunctor } from '../__types/tree.type';
import { TreeFunctorImpl } from './tree.rfr';
import { Scope, ScopeObject } from '../__types/scopedTree.type';
```

## Class Definition

```typescript
/**
 * Implementation of a Scoped Tree Functor
 * 
 * @typeParam T - Type of objects in the target category
 * @typeParam TM - Type of morphisms in the target category
 */
export class ScopedTreeFunctorImpl<
  T extends CatObject,
  TM extends Morphism<T>
> extends TreeFunctorImpl<T, TM> implements ScopedTreeFunctor<T, TM> {
  /**
   * Global scope (public terms)
   */
  readonly globalScope: ScopeObject = {};
  
  /**
   * Directory scopes
   */
  private directoryScopes: Map<string, ScopeObject> = new Map();
  
  /**
   * Node scopes
   */
  private nodeScopes: Map<string, Scope> = new Map();
  
  /**
   * Creates a new scoped tree functor
   * 
   * @param target Target category
   * @param objectMap Function that maps tree nodes to target objects
   * @param morphismMap Function that maps tree morphisms to target morphisms
   * @param rootValue Value for the root node
   */
  constructor(
    target: Category<T, TM>,
    objectMap: (node: TreeNode) => T,
    morphismMap: (source: TreeNode, target: TreeNode) => TM,
    rootValue: any = 'root'
  ) {
    super(target, objectMap, morphismMap, rootValue);
    
    // Set the scope of the root node to public
    this.nodeScopes.set('root', 'public');
  }
  
  /**
   * Gets a directory scope
   * 
   * @param directory Directory path
   * @returns The scope for the directory
   */
  getDirectoryScope(directory: string): ScopeObject {
    if (!this.directoryScopes.has(directory)) {
      // Create a new scope that inherits from the global scope
      const scope = Object.create(this.globalScope);
      this.directoryScopes.set(directory, scope);
    }
    
    return this.directoryScopes.get(directory)!;
  }
  
  /**
   * Gets a file scope
   * 
   * @param filePath File path
   * @returns The scope for the file
   */
  getFileScope(filePath: string): ScopeObject {
    // Extract the directory from the file path
    const directory = filePath.substring(0, filePath.lastIndexOf('/'));
    
    // Get the directory scope
    const directoryScope = this.getDirectoryScope(directory);
    
    // Extract the file name from the file path
    const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
    
    // Determine the scope based on the file name prefix
    if (fileName.startsWith('_')) {
      // Private scope - create a new scope that inherits from the directory scope
      return Object.create(directoryScope);
    } else if (fileName.startsWith('.')) {
      // Protected scope - use the directory scope directly
      return directoryScope;
    } else if (fileName.startsWith('$')) {
      // Dynamic scope - create a new scope that inherits from the directory scope
      return Object.create(directoryScope);
    } else if (fileName.startsWith('@')) {
      // Injected scope - create a new scope that inherits from the directory scope
      return Object.create(directoryScope);
    } else {
      // Public scope - use the global scope directly
      return this.globalScope;
    }
  }
  
  /**
   * Adds a term to a scope
   * 
   * @param scope Scope to add the term to
   * @param name Term name
   * @param value Term value
   */
  addToScope(scope: ScopeObject, name: string, value: any): void {
    // Define the property as a getter to ensure lazy evaluation
    Object.defineProperty(scope, name, {
      get: () => value,
      enumerable: true,
      configurable: true
    });
  }
  
  /**
   * Gets a term from a scope
   * 
   * @param scope Scope to get the term from
   * @param name Term name
   * @returns The term value
   */
  getFromScope(scope: ScopeObject, name: string): any {
    return scope[name];
  }
  
  /**
   * Adds a child to a parent node with the specified scope
   * 
   * @param parentId Parent node ID
   * @param value Child node value
   * @param scope Child node scope
   * @returns The new child node
   */
  addScopedChild(parentId: string, value: any, scope: Scope): T | undefined {
    // Add a child to the parent node
    const childObj = this.addChild(parentId, value);
    if (!childObj) return undefined;
    
    // Get the child node ID
    const childId = childObj.id.name as string;
    
    // Set the scope of the child node
    this.nodeScopes.set(childId, scope);
    
    return childObj;
  }
  
  /**
   * Gets the scope of a node
   * 
   * @param id Node ID
   * @returns The scope of the node
   */
  getNodeScope(id: string): Scope {
    return this.nodeScopes.get(id) || 'public';
  }
  
  /**
   * Checks if a node is accessible from another node based on scope rules
   * 
   * @param fromId Source node ID
   * @param toId Target node ID
   * @returns True if the target node is accessible from the source node
   */
  isAccessible(fromId: string, toId: string): boolean {
    // Get the scopes of the nodes
    const fromScope = this.getNodeScope(fromId);
    const toScope = this.getNodeScope(toId);
    
    // Public nodes are accessible from anywhere
    if (toScope === 'public') return true;
    
    // Get the parent IDs
    const fromParentId = this.getParentId(fromId);
    const toParentId = this.getParentId(toId);
    
    // Protected nodes are accessible from the same directory and subdirectories
    if (toScope === 'protected') {
      // Check if fromId is in the same directory or a subdirectory of toId
      return fromParentId === toParentId || this.isDescendant(toParentId, fromParentId);
    }
    
    // Private nodes are only accessible from the same directory
    if (toScope === 'private') {
      return fromParentId === toParentId;
    }
    
    // Dynamic and injected nodes have special rules
    // For simplicity, we'll make them accessible from anywhere for now
    return true;
  }
  
  /**
   * Gets the parent ID of a node
   * 
   * @param id Node ID
   * @returns The parent ID, or undefined if the node has no parent
   */
  private getParentId(id: string): string | undefined {
    // Get the node from the tree
    const node = this.tree.getNode(id);
    if (!node) return undefined;
    
    // Get the parent node
    const parentNode = this.tree.getParent(id);
    if (!parentNode) return undefined;
    
    return parentNode.id;
  }
  
  /**
   * Checks if a node is a descendant of another node
   * 
   * @param ancestorId Ancestor node ID
   * @param descendantId Descendant node ID
   * @returns True if descendantId is a descendant of ancestorId
   */
  private isDescendant(ancestorId: string, descendantId: string): boolean {
    // Get the ancestor node
    const ancestorNode = this.tree.getNode(ancestorId);
    if (!ancestorNode) return false;
    
    // Get the descendant node
    const descendantNode = this.tree.getNode(descendantId);
    if (!descendantNode) return false;
    
    // Check if descendantId is a descendant of ancestorId
    let currentId = this.getParentId(descendantId);
    while (currentId) {
      if (currentId === ancestorId) return true;
      currentId = this.getParentId(currentId);
    }
    
    return false;
  }
}
```

## Utility Function

```typescript
/**
 * Creates a scoped tree functor
 * 
 * @param target Target category
 * @param objectMap Function that maps tree nodes to target objects
 * @param morphismMap Function that maps tree morphisms to target morphisms
 * @param rootValue Value for the root node
 * @returns A new scoped tree functor
 */
export function createScopedTreeFunctor<
  T extends CatObject,
  TM extends Morphism<T>
>(
  target: Category<T, TM>,
  objectMap: (node: TreeNode) => T,
  morphismMap: (source: TreeNode, target: TreeNode) => TM,
  rootValue: any = 'root'
): ScopedTreeFunctor<T, TM> {
  return new ScopedTreeFunctorImpl(
    target,
    objectMap,
    morphismMap,
    rootValue
  );
}
```

## Implementation Details

### Constructor

The constructor takes four parameters:
1. `target`: The target category
2. `objectMap`: A function that maps tree nodes to target objects
3. `morphismMap`: A function that maps tree morphisms to target morphisms
4. `rootValue`: The value for the root node (default: 'root')

It calls the super constructor and sets the scope of the root node to public.

### getDirectoryScope

The `getDirectoryScope` method gets a directory scope:

1. It checks if the directory scope exists
2. If not, it creates a new scope that inherits from the global scope
3. It returns the directory scope

### getFileScope

The `getFileScope` method gets a file scope:

1. It extracts the directory from the file path
2. It gets the directory scope
3. It extracts the file name from the file path
4. It determines the scope based on the file name prefix
5. It returns the appropriate scope

### addToScope

The `addToScope` method adds a term to a scope:

1. It defines the property as a getter to ensure lazy evaluation
2. It makes the property enumerable and configurable

### getFromScope

The `getFromScope` method gets a term from a scope:

1. It returns the value of the property with the given name

### addScopedChild

The `addScopedChild` method adds a child to a parent node with the specified scope:

1. It adds a child to the parent node using the tree functor's `addChild` method
2. It sets the scope of the child node
3. It returns the child object

### getNodeScope

The `getNodeScope` method gets the scope of a node:

1. It returns the scope of the node, or 'public' if not found

### isAccessible

The `isAccessible` method checks if a node is accessible from another node based on scope rules:

1. It gets the scopes of the nodes
2. It applies the scope rules:
   - Public nodes are accessible from anywhere
   - Protected nodes are accessible from the same directory and subdirectories
   - Private nodes are only accessible from the same directory
   - Dynamic and injected nodes are accessible from anywhere (for simplicity)

## File Location

This implementation should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/cat_types.obj/functors/scopedTree.rfr.ts
```

## Dependencies

- `@treenity/core`: Provides the Tree and TreeNode interfaces
- `__types/scopedTree.type.ts`: Type definitions for scoped tree functors
- `functors/tree.rfr.ts`: Tree functor implementation
- `cat.ts`: Category implementation
- `catObject.ts`: Category object implementation
- `morphism.ts`: Morphism implementation
