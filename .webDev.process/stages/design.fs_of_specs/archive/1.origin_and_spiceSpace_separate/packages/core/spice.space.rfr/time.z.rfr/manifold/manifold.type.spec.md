# Spacetime Manifold Type Specification

## Overview

This specification defines the type structure for the Spacetime Manifold in the time.rfr package. A Spacetime Manifold represents the overall structure of spacetime, containing nodes, paths, metrics, and gravity.

## Type Definition

```typescript
/**
 * Represents a spacetime manifold
 * 
 * A spacetime manifold is the overall structure that contains
 * all spacetime nodes and paths.
 * 
 * @typeParam D - Type of data associated with spacetime nodes
 */
export interface SpacetimeManifold<D = any> {
  /**
   * The nodes in the manifold
   */
  readonly nodes: Map<symbol, SpacetimeNode<D>>;
  
  /**
   * The paths in the manifold
   */
  readonly paths: Map<symbol, SpacetimePath<D>>;
  
  /**
   * The metric tensor for the manifold
   */
  readonly metric: MetricTensor;
  
  /**
   * The gravity field for the manifold
   */
  readonly gravityField: GravityField<D>;
  
  /**
   * Gets a node by its ID
   * 
   * @param id Node ID
   * @returns The node with the given ID, or undefined if not found
   */
  getNode(id: symbol): SpacetimeNode<D> | undefined;
  
  /**
   * Gets a path by its ID
   * 
   * @param id Path ID
   * @returns The path with the given ID, or undefined if not found
   */
  getPath(id: symbol): SpacetimePath<D> | undefined;
  
  /**
   * Adds a node to the manifold
   * 
   * @param coordinates Spacetime coordinates
   * @param data Data associated with the node
   * @param mass Conceptual mass of the node
   * @returns The new node
   */
  addNode(coordinates: SpacetimeCoordinates, data: D, mass?: number): SpacetimeNode<D>;
  
  /**
   * Adds a path between two nodes
   * 
   * @param sourceId Source node ID
   * @param targetId Target node ID
   * @param type Path type
   * @returns The new path
   */
  addPath(sourceId: symbol, targetId: symbol, type: PathType): SpacetimePath<D>;
  
  /**
   * Removes a node from the manifold
   * 
   * @param id Node ID
   * @returns True if the node was removed
   */
  removeNode(id: symbol): boolean;
  
  /**
   * Removes a path from the manifold
   * 
   * @param id Path ID
   * @returns True if the path was removed
   */
  removePath(id: symbol): boolean;
  
  /**
   * Calculates the geodesic between two nodes
   * 
   * @param sourceId Source node ID
   * @param targetId Target node ID
   * @returns The geodesic path
   */
  calculateGeodesic(sourceId: symbol, targetId: symbol): SpacetimePath<D>;
  
  /**
   * Calculates the spacetime interval between two nodes
   * 
   * @param sourceId Source node ID
   * @param targetId Target node ID
   * @returns The spacetime interval
   */
  calculateInterval(sourceId: symbol, targetId: symbol): number;
  
  /**
   * Updates the metric tensor based on the current distribution of mass
   */
  updateMetric(): void;
  
  /**
   * Updates the gravity field based on the current distribution of mass
   */
  updateGravityField(): void;
  
  /**
   * Gets nodes within a region of spacetime
   * 
   * @param minCoordinates Minimum coordinates
   * @param maxCoordinates Maximum coordinates
   * @returns Nodes within the region
   */
  getNodesInRegion(
    minCoordinates: SpacetimeCoordinates,
    maxCoordinates: SpacetimeCoordinates
  ): SpacetimeNode<D>[];
  
  /**
   * Gets the causal future of a node
   * 
   * @param nodeId Node ID
   * @returns Nodes in the causal future
   */
  getCausalFuture(nodeId: symbol): SpacetimeNode<D>[];
  
  /**
   * Gets the causal past of a node
   * 
   * @param nodeId Node ID
   * @returns Nodes in the causal past
   */
  getCausalPast(nodeId: symbol): SpacetimeNode<D>[];
  
  /**
   * Checks if two nodes are causally connected
   * 
   * @param node1Id First node ID
   * @param node2Id Second node ID
   * @returns True if the nodes are causally connected
   */
  areCausallyConnected(node1Id: symbol, node2Id: symbol): boolean;
  
  /**
   * Creates a region of spacetime
   * 
   * @param nodeIds Node IDs in the region
   * @returns A spacetime region
   */
  createRegion(nodeIds: symbol[]): SpacetimeRegion<D>;
}

/**
 * Represents a region of spacetime
 * 
 * @typeParam D - Type of data associated with nodes
 */
export interface SpacetimeRegion<D = any> {
  /**
   * The manifold this region belongs to
   */
  readonly manifold: SpacetimeManifold<D>;
  
  /**
   * The nodes in the region
   */
  readonly nodes: Set<symbol>;
  
  /**
   * The paths in the region
   */
  readonly paths: Set<symbol>;
  
  /**
   * The boundary of the region
   */
  readonly boundary: Set<symbol>;
  
  /**
   * Gets the volume of the region
   * 
   * @returns The volume
   */
  getVolume(): number;
  
  /**
   * Gets the total mass in the region
   * 
   * @returns The total mass
   */
  getTotalMass(): number;
  
  /**
   * Gets the average curvature in the region
   * 
   * @returns The average curvature
   */
  getAverageCurvature(): number;
  
  /**
   * Checks if a node is in the region
   * 
   * @param nodeId Node ID
   * @returns True if the node is in the region
   */
  containsNode(nodeId: symbol): boolean;
  
  /**
   * Adds a node to the region
   * 
   * @param nodeId Node ID
   * @returns The updated region
   */
  addNode(nodeId: symbol): SpacetimeRegion<D>;
  
  /**
   * Removes a node from the region
   * 
   * @param nodeId Node ID
   * @returns The updated region
   */
  removeNode(nodeId: symbol): SpacetimeRegion<D>;
}
```

## Dependencies

- `SpacetimeNode`: Represents a node in spacetime
- `SpacetimePath`: Represents a path through spacetime
- `MetricTensor`: Represents the metric tensor for measuring distances
- `GravityField`: Represents the gravitational field
- `SpacetimeCoordinates`: Represents coordinates in spacetime
- `PathType`: Defines different types of paths

## Usage

The `SpacetimeManifold` interface is the central structure in time.rfr. It represents the overall spacetime structure and provides methods for:

1. **Managing Nodes and Paths**: Adding, removing, and retrieving nodes and paths
2. **Calculating Geodesics**: Finding the shortest path between nodes
3. **Measuring Intervals**: Calculating the spacetime interval between nodes
4. **Updating Physics**: Updating the metric tensor and gravity field
5. **Analyzing Causality**: Determining causal relationships between nodes
6. **Creating Regions**: Defining regions of spacetime for analysis

The `SpacetimeRegion` interface represents a subset of the manifold, allowing for focused analysis of specific regions of spacetime.

## Implementation Requirements

Implementations of the `SpacetimeManifold` interface must:

1. Maintain collections of nodes and paths
2. Provide a metric tensor for measuring distances
3. Implement a gravity field for modeling gravitational effects
4. Support geodesic calculations
5. Enable causal analysis
6. Allow for the creation of regions

The implementation should reflect our understanding of how spacetime is curved by conceptual mass, how time emerges from recursive interactions, and how gravity shapes the structure of development.

## File Location

This type definition should be placed in:
```
spicetime-architecture/packages/utils/origin.rfr/time.rfr/manifold/manifold.type.ts
```

## Related Files

- `manifold/manifold.ts`: Implementation of the spacetime manifold
- `manifold/region.ts`: Implementation of spacetime regions
- `nodes/node.type.ts`: Definition of spacetime nodes
- `paths/path.type.ts`: Definition of spacetime paths
- `metrics/metric.type.ts`: Definition of metric tensors
- `gravity/gravity.type.ts`: Definition of gravity fields
