# Reactive Architecture Pattern

This document describes the reactive architecture pattern used in the API Standard, which enables composition and side effect management.

## Overview

The reactive architecture pattern is a key aspect of the API Standard, providing a consistent approach to state management, composition, and side effects. This pattern is inspired by functional programming concepts like reducers and is designed to work seamlessly with the pragmatic file structure.

## Core Concepts

### Reducers as API Methods

In the reactive architecture pattern, API methods are implemented as reducers that transform state:

```typescript
// API method as a reducer
function getUser(id: string) {
  // Return a reducer function
  return (state: AppState) => {
    // Find the user in the state
    const user = state.users.find(user => user.id === id);
    
    // Return the user (or null if not found)
    return user || null;
  };
}
```

This approach has several benefits:
- **Pure Functions**: Reducers are pure functions, making them easier to test and reason about
- **Composition**: Reducers can be composed to create more complex operations
- **State Management**: State is managed consistently across the application
- **Side Effect Isolation**: Side effects are isolated from the core logic

### Composition through Function Composition

API methods can be composed through function composition:

```typescript
// Compose API methods
function getUserWithPosts(id: string) {
  // Return a reducer function
  return (state: AppState) => {
    // Get the user
    const user = getUser(id)(state);
    
    // If the user doesn't exist, return null
    if (!user) {
      return null;
    }
    
    // Get the user's posts
    const posts = getPosts({ userId: id })(state);
    
    // Return the user with posts
    return {
      ...user,
      posts
    };
  };
}
```

This composition pattern allows for building complex operations from simpler ones, while maintaining the reducer pattern.

### Side Effect Management

Side effects are managed through a consistent pattern:

```typescript
// Side effect handler
function createUser(user: Omit<User, 'id'>) {
  // Return a reducer function
  return async (state: AppState, context: Context) => {
    // Generate a new ID
    const id = context.idGenerator.generate();
    
    // Create the new user
    const newUser: User = {
      ...user,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Perform the side effect
    await context.db.users.create(newUser);
    
    // Return the new user
    return newUser;
  };
}
```

This pattern isolates side effects from the core logic, making the code more testable and maintainable.

## Implementation in the API Standard

The reactive architecture pattern is implemented in the API Standard as follows:

### API Definition

APIs are defined as collections of reducer functions:

```typescript
// External API (.pragma.api.type)
export default interface {
  // Public API methods (reducers)
  getUser: (id: string) => (state: AppState) => User | null;
  createUser: (user: Omit<User, 'id'>) => (state: AppState, context: Context) => Promise<User>;
  updateUser: (id: string, user: Partial<User>) => (state: AppState, context: Context) => Promise<User | null>;
  deleteUser: (id: string) => (state: AppState, context: Context) => Promise<boolean>;
}
```

### API Implementation

APIs are implemented as reducer functions:

```typescript
// Implementation of getUser
export function getUser(id: string) {
  return (state: AppState) => {
    return state.users.find(user => user.id === id) || null;
  };
}

// Implementation of createUser
export function createUser(user: Omit<User, 'id'>) {
  return async (state: AppState, context: Context) => {
    const id = context.idGenerator.generate();
    
    const newUser: User = {
      ...user,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await context.db.users.create(newUser);
    
    return newUser;
  };
}
```

### API Composition

APIs are composed through function composition:

```typescript
// Composition of API methods
export function getUserWithPosts(id: string) {
  return (state: AppState) => {
    const user = getUser(id)(state);
    
    if (!user) {
      return null;
    }
    
    const posts = getPosts({ userId: id })(state);
    
    return {
      ...user,
      posts
    };
  };
}
```

## Benefits of the Reactive Architecture Pattern

The reactive architecture pattern offers several benefits:

1. **Consistency**: All API methods follow the same pattern
2. **Composability**: API methods can be composed to create more complex operations
3. **Testability**: Pure functions are easier to test
4. **Maintainability**: Side effects are isolated from the core logic
5. **Performance**: State can be optimized through memoization and other techniques

## Integration with Pragmatic File Structure

The reactive architecture pattern integrates seamlessly with the pragmatic file structure:

```
/user
  .pragma.type                    # Type definition
  /query
    find.by_id                    # Find by ID (implemented as a reducer)
    find.by_name                  # Find by name (implemented as a reducer)
  /mutation
    create                        # Create operation (implemented as a reducer)
    update.by_id                  # Update by ID (implemented as a reducer)
```

Each file in the pragmatic structure exports a reducer function, which is automatically composed by the pragma system.

## Conclusion

The reactive architecture pattern is a key aspect of the API Standard, providing a consistent approach to state management, composition, and side effects. By implementing API methods as reducers, we can create more maintainable, testable, and composable APIs.

This pattern integrates seamlessly with the pragmatic file structure, allowing for a more expressive and intuitive API design.
