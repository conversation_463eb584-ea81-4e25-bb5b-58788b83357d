# Type Composition and Pragmatic Syntax

## Overview

This document explores how types define composition rules in the API Standard and how pragmatic syntax can be used to express these rules. It demonstrates the extreme power of pragmatic syntax - expressing the entire AST structure through naming conventions without any code inside files.

## Motivation

The pragmatic syntax approach demonstrates the extreme power of expressing the entire AST structure through naming conventions without any code inside files. While it's an extreme example that might be impractical for everyday use, it showcases the full potential of the system.

In practice, a balanced approach makes more sense:
- Using pragmatic syntax for high-level structure and relationships
- Using TypeScript code inside files for implementation details and complex logic
- Finding the right blend between pragmatic expressions and traditional code

The key insight is that pragma syntax allows splitting expressions between:
1. Pragmatic extensions (file/directory naming)
2. Actual code inside files

By defining atomic pragmas for basic TypeScript leaf types as separate pragmas, we enable this flexibility - developers can choose how much to express through structure versus code, depending on what makes sense for their specific use case.

## Type Composition Rules

Types define their composition rules, and pragmas represent types that are hooked to resolvers (pragmaOperators):

- **Type Composition**: Each type defines its composition rules
- **Pragma Operators**: Pragmas represent types that are hooked to resolvers (pragmaOperators)
- **Duplex Linkage**: Each pragma can constrain how its parent composes it, creating a duplex linkage
- **Cross-linking**: Pragma compounds can be cross-linked when compositional rules are observed

## Atomic Types with Generics

Flexible atomic types with generic parameters allow for compositional flexibility:

- **Generic Types**: Types can be defined with generic parameters
- **Type Constraints**: Generic parameters can be constrained to specific types
- **Type Composition**: Types can be composed using generic parameters

## Pragmatic Syntax for Type Definitions

The pragmatic syntax can be used to define types and their composition rules:

### For a type definition for a specific property (`str.pragma.prop.type`):

```typescript
// str.pragma.prop.type.ts
export default type = string;
```

### For multiple property types (`.pragma.prop.types`):

```typescript
// .pragma.prop.types.ts
export default interface {
  keyType: string;
  valueType: any;
  allowAdditionalProps: boolean;
}
```

## Placeholder Files for Type Definitions

An extreme use of pragmatic syntax is to use placeholder files to define types and their composition rules:

1. `.pragma.prop_key.type_str` - A placeholder file indicating a property named "key" of type "str"
2. `.pragma.prop_(key,value).types_(num,any)` - A placeholder file indicating properties "key" and "value" of types "num" and "any" respectively

These placeholder files wouldn't need content - their names alone convey the type information. The types referenced (str, num, any) would be pragmas already defined in the scope.

## Example: Map Type with Generic Parameters

```typescript
// In a traditional approach
// map.pragma.prop.types.ts
export default interface {
  key: string | number | symbol;
  value: any;
}

// In an extreme pragmatic approach
// .pragma.prop_(key,value).types_(str|num|sym,any)
// (Empty file, type information is in the filename)
```

## Example: Object Type as Specialized Map

```typescript
// In a traditional approach
// object.pragma.prop.types.ts
export default interface {
  keyType: string; // Keys must be strings for objects
  valueType: any;  // Values can be anything
}

// In an extreme pragmatic approach
// .pragma.prop_keyType.type_str
// .pragma.prop_valueType.type_any
// (Empty files, type information is in the filenames)
```

## Example: Array Type as Specialized Map

```typescript
// In a traditional approach
// array.pragma.prop.types.ts
export default interface {
  itemType: any;  // Type of array items
  minLength?: number; // Optional minimum length
  maxLength?: number; // Optional maximum length
}

// In an extreme pragmatic approach
// .pragma.prop_itemType.type_any
// .pragma.prop_minLength.type_num?
// .pragma.prop_maxLength.type_num?
// (Empty files, type information is in the filenames)
```

## Implementation Approach

The implementation approach for type composition and pragmatic syntax will follow these steps:

1. Define atomic pragmas for each JavaScript leaf type
2. Implement the ability to read type information from file names
3. Implement the ability to compose types based on composition rules
4. Provide a balanced approach that allows for both pragmatic syntax and traditional code

As complexity grows, we will quickly advance to a proper blend of pragmatic syntax and traditional code, using each where it makes the most sense.
