# Order of Concepts Example

This example demonstrates how the `order_of_concepts` pragma is composed from the `seq` pragma (aliased as `order`) and the `concept` pragma, showcasing the power of pragma composition in the SpiceTime architecture.

## Pragma Composition

The `order_of_concepts` pragma is a composition of two more basic pragmas:

1. The `seq` pragma (aliased as `order`): Provides sequential structure
2. The `concept` pragma: Defines what each item represents

This composition creates a new pragma that represents an ordered sequence of concepts, without requiring explicit definition of the composed pragma.

## Directory Structure

```
# Repository root
/
  # Main structure with order_of_concepts pragma
  /.webDev.process
    /stages
      /ideation.order_of_concepts     # Using order_of_concepts pragma
        /1.first_concept              # Concept with sequence number
        /2.second_concept
        /15.api_standard_as_face_of_st
          context.md
          summary.md
          /code
          /docs

  # Parallel trees for pragma definitions (orthogonal to webDev.process)
  /_st.pragma                         # Core pragma system
    /...

  /_seq.pragma                        # Separate seq pragma branch
    .pragma.type                      # Type definition for seq
    .pragma.alias                     # Alias definition: order -> seq

  /_concept.pragma                    # Separate concept pragma branch
    .pragma.type                      # Type definition for concept
```

## Type Definitions

### Seq Pragma Type Definition

```typescript
// _seq.pragma/.pragma.type
export default interface {
  // Generic sequence type that can contain any items
  Sequence<T>: {
    [index: number]: T;
    length: number;
  };

  // Methods for working with sequences
  create<T>(items: T[]): Sequence<T>;
  get<T>(seq: Sequence<T>, index: number): T | undefined;
  add<T>(seq: Sequence<T>, item: T): Sequence<T>;
}
```

### Concept Pragma Type Definition

```typescript
// _concept.pragma/.pragma.type
export default interface {
  Concept: {
    id: string;
    name: string;
    context: string;
    summary: string;
    code: Record<string, string>;
    docs: Record<string, string>;
  };

  // Methods for working with concepts
  create(name: string, context?: string): Concept;
  addCode(concept: Concept, name: string, code: string): Concept;
  addDoc(concept: Concept, name: string, content: string): Concept;
}
```

### Seq Pragma Alias Definition

```typescript
// _seq.pragma/.pragma.alias
export default {
  aliases: ['order']
};
```

## How st.pragma Processes This Structure

The st.pragma system processes this structure as follows:

1. It scans both the public tree and the parallel private tree
2. It identifies the pragma files in the private tree
3. It registers the alias from `seq` to `order`
4. It recognizes that `ideation.order_of_concepts` uses a composition of `order` and `concept`
5. It resolves `order` to `seq` using the alias registry
6. The linguistic system automatically handles the composition
7. It generates the appropriate scope for each file

## Generated Implementation

The generated implementation would include:

```typescript
// Generated file: /.webDev.process/stages/production/impl.fs(ts)_shadow_of_fs(pragmatic)/ideation/order_of_concepts/index.ts

// Dynamic imports for lazy loading
async function loadSeqPragma() {
  const module = await import('../../_seq.pragma/index.js');
  return module.default;
}

async function loadConceptPragma() {
  const module = await import('../../_concept.pragma/index.js');
  return module.default;
}

// The order_of_concepts API
export default async function orderOfConcepts(scope) {
  // Load the seq and concept pragmas
  const [seqPragma, conceptPragma] = await Promise.all([
    loadSeqPragma(),
    loadConceptPragma()
  ]);

  // Create a sequence of concepts
  const concepts = scope.children
    .filter(child => /^\d+\./.test(child.name)) // Filter by numeric prefix
    .sort((a, b) => {
      // Sort by numeric prefix
      const aNum = parseInt(a.name.split('.')[0]);
      const bNum = parseInt(b.name.split('.')[0]);
      return aNum - bNum;
    })
    .map(child => {
      // Create a concept from each child
      return conceptPragma.create(
        child.name.split('.').slice(1).join('.'), // Name without prefix
        child.files.find(f => f === 'context.md')?.content // Context from context.md
      );
    });

  // Create a sequence of concepts
  const sequence = seqPragma.create(concepts);

  // Return the enhanced scope
  return {
    ...scope,
    concepts: sequence
  };
}
```

## Benefits of This Approach

This approach to pragma composition offers several benefits:

1. **Reduced Boilerplate**: No need to define composed pragmas explicitly
2. **Flexible Definition Patterns**: Pragmas can be defined as structure develops
3. **Intuitive Naming**: Aliases allow for more intuitive naming (`order` vs `seq`)
4. **Clear Intent**: The file structure clearly expresses relationships
5. **Reduced Mental Load**: Developers don't need to manually compose pragmas

## Version Space Considerations

The version space for this composition involves:

1. The `seq` pragma at coordinate `seq@base`
2. The `concept` pragma at coordinate `concept@base`
3. The alias extension at coordinate `l@addedAliases`
4. The st.pragma extension at coordinate `st.pragma@withAliases`

For more details on version spaces, see [Concept 16: Version Spaces](../16.version_spaces:impl_and_usage/summary.md).

## Dynamic Import Handling

The generated implementation uses dynamic imports for lazy loading:

1. The seq pragma is loaded on demand
2. The concept pragma is loaded on demand
3. Both are loaded in parallel using Promise.all

For more details on dynamic import handling, see [Concept 17: stPragma Dynamic Import Handling](../17.stPragma:dynamic_import_handling/summary.md).

## Conclusion

The `order_of_concepts` pragma demonstrates the power of pragma composition in the SpiceTime architecture. By composing basic pragmas and using aliases, we can create more expressive and intuitive pragmas without explicit definition. This approach reduces boilerplate, increases flexibility, and makes the code more maintainable.
