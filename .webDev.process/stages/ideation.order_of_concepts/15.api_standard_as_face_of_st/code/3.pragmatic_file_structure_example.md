# Pragmatic File Structure Example

This example demonstrates how pragmatic extensions are composed and used in file paths and folder names, eliminating boilerplate by expressing intent through structure.

## Directory Structure

```
# Repository root
/
  # Public tree
  /user
    .pragma.type                    # Type definition
    /query
      find.by_name                  # Find by name
      find.by_role                  # Find by role
      find.by_(name|role)           # Find by either name OR role
      find.by_(name&role)           # Find by both name AND role
      find.where{active:true}       # Find with condition using object syntax
      find.limit[10]                # Find with limit using array syntax
      find.by_name.sort(asc)        # Find by name and sort ascending
    /mutation
      create                        # Create operation
      update.by_id                  # Update by ID
      delete.by_id                  # Delete by ID
  /admin
    .pragma.type                    # Type definition
    /query
      find.all_users                # Find all users
      find.by_department            # Find by department

  # Parallel private tree - rooted at repository root
  /_st.pragma
    /user
      .pragma.type                  # Type definition
      /query
        find.by_(name&role).or.by_dept  # Complex query with AND and OR conditions
        find.where{active:true}.limit[5].sort(created_at)  # Complex query with multiple operations
      /mutation
        create.with_defaults         # Create with defaults
    /admin
      .pragma.type                  # Type definition
      /query
        find.all_active_users       # Find all active users

  # Alternative: Private pragma within user scope
  /user
    /_.pragma
      .pragma.type                  # Type definition within user scope
      /query
        find.by_(name&role)         # Internal query implementation
```

## Pragmatic Extensions in File Paths

The file paths themselves express the intent and relationships:

- `.pragma.type` - Defines a type that updates the t namespace
- `_st.pragma` - Root of the parallel private tree
- `_.pragma` - Private pragma within a node's scope
- `.` - Chains operations (e.g., `find.by_name.sort(asc)`)
- `_` - Indicates parameters (e.g., `by_name`)
- `&` - Indicates AND conditions (e.g., `by_(name&role)`)
- `|` - Indicates OR conditions (e.g., `by_(name|role)`)
- `()` - Indicates function-like operations (e.g., `sort(asc)`)
- `{}` - Indicates object-like conditions (e.g., `where{active:true}`)
- `[]` - Indicates array-like operations (e.g., `limit[10]`)

## How Pragmas Are Composed

1. **Public Tree**:
   - `.pragma.type` - Defines types that update the t namespace
   - Implementation files with rich syntax - Express operations and their parameters
   - The file structure itself defines the API structure

2. **Parallel Private Tree** (`_st.pragma`):
   - `.pragma.type` - Defines types that update the t namespace
   - Implementation files with rich syntax - Express operations and their parameters
   - Once inside the private tree, there's no need to continue using the `_` prefix

3. **Private Pragma within Node Scope** (`_.pragma`):
   - `.pragma.type` - Defines types that update the t namespace
   - Implementation files with rich syntax - Express operations and their parameters
   - Provides a more localized approach to private implementations

## Example: Public and Private Implementation

### .pragma.type
```typescript
// Type definition
// Generated into t namespace in scope
export default interface {
  User: {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user' | 'guest';
    createdAt: Date;
    updatedAt: Date;
  };
}
```

**Note:** All files automatically receive the following features:
- The scope is automatically injected, and template literals are automatically processed with the `l` tag
- Parameters from file extensions are preprocessed and injected directly into the scope:
  - `find.by_name` → `name` variable is available in scope
  - `find.by_(name&role)` → `name` and `role` variables are available in scope
  - `find.where{active:true}` → No variables needed, condition is parsed from extension
- For folders, the `param` namespace propagates like a scope, being extended by child params
- Params can be defined by a `.pragma.param` file in private or public scope (using `_` prefix as needed)
- Different params can exist in private and public APIs:
  - Pragma takes one set of params
  - Public use cases of pragma add their own params as extensions
- All params can be used in extensions
- st.pragma decides which scope a param comes from by name inference
- Name clashes between params result in an error (no duplicate names in param object)
- No imports are needed
- `export default` is automatically added based on content:
  - For a single template literal: `export default l`...`;` (l is added automatically)
  - For multiple template literals: `export default [l`...`, l`...`];` (l is added automatically)
  - For multiple exports: `export default { exportName1, exportName2 };`

### find.by_name
```typescript
`find all users where name contains ${name}`;
```

### find.by_(name|role)
```typescript
`find all users where name contains ${name} or role is ${role}`;
```

### find.by_(name&role)
```typescript
`find all users where name contains ${name} and role is ${role}`;
```

### find.where{active:true}
```typescript
`find all users where active is true`;
```

### find.by_name.sort(asc)
```typescript
`find all users where name contains ${name} sort by name asc`;
```

### Multiple template literal statements in a file
```typescript
// Pattern: automatically becomes export default [l`...`, l`...`];
`find all users where role is "admin"`;
`find all users where role is "manager"`;
```

### Multiple named exports in a file
```typescript
// Pattern: must use export statements
export const findAdmins = `find all users where role is "admin"`;
export const findManagers = `find all users where role is "manager"`;
// Automatically becomes: export default { findAdmins, findManagers };
```

## How Boilerplate Evaporates

1. **No Import Statements**: The pragma system automatically injects the appropriate scope
2. **No API Definitions**: The API is defined by the file structure itself
3. **No Repetitive Patterns**: Common patterns are defined once in parent pragmas
4. **No Configuration Files**: Configuration is expressed through file paths
5. **No Explicit Composition**: Composition happens automatically through the file structure
6. **Clear Separation**: Private and public implementations are clearly separated
7. **Expressive File Paths**: Complex operations are expressed directly in file paths
8. **Rich Syntax**: The `.`, `_`, `&`, `|`, `()`, `{}`, `[]` syntax allows for expressive file paths
9. **Automatic Parameter Injection**: Parameters from file extensions are preprocessed and injected directly into the scope
10. **Automatic Exports**: The system automatically adds `export default` to files:
   - For a single `l` statement: `export default l`...`;`
   - For multiple `l` statements: `export default [l`...`, l`...`];`
   - For multiple exports: `export default { exportName1, exportName2 };`

## How the System Processes This Structure

1. The pragma system scans both the public tree and the parallel private tree (`_st.pragma`)
2. It identifies the pragma files (`.pragma.type` in the public tree, `.pragma.type` in the private tree) and their relationships
3. It parses the pragmatic file extensions to understand the operations and their parameters
   - `.` for chaining operations (e.g., `find.by_name.sort(asc)`)
   - `_` for parameters (e.g., `by_name`)
   - `&` for AND conditions (e.g., `by_(name&role)`)
   - `|` for OR conditions (e.g., `by_(name|role)`)
   - `()` for function-like operations (e.g., `sort(asc)`)
   - `{}` for object-like conditions (e.g., `where{active:true}`)
   - `[]` for array-like operations (e.g., `limit[10]`)
4. It preprocesses parameters from file extensions and injects them directly into the scope:
   - `find.by_name` → `name` variable is available in scope
   - `find.by_(name&role)` → `name` and `role` variables are available in scope
   - `find.where{active:true}` → No variables needed, condition is parsed from extension
   - For folders, the `param` namespace propagates like a scope, being extended by child params
   - Params can be defined by a `.pragma.param` file in private or public scope
   - st.pragma decides which scope a param comes from by name inference

5. It generates the appropriate scope for each file:
   - Public scope from the public tree (accessible to all downstream nodes)
   - Private scope from the private tree (accessible only within the node)
   - Protected members (with `.` prefix, accessible to direct child nodes but not external consumers)
6. It processes the file content, automatically adds the `l` tag to template literals, and adds exports:
   - For a single template literal: `export default l`...`;` (l is added automatically)
   - For multiple template literals: `export default [l`...`, l`...`];` (l is added automatically)
   - For multiple exports: `export default { exportName1, exportName2 };`
7. It generates TypeScript files in the implementation stage:
   - `.scope.ts` and `_scope.ts` files for public and private scopes
   - Index files for each node
   - Implementation files that use the scopes
8. It composes the APIs based on the file structure, with:
   - External API from the public tree
   - Internal API from the private tree

## Example: Generated API

The file structure above would generate an API like:

```typescript
// Public API (accessible to consumers)
const api = {
  user: {
    query: {
      findByName: (name: string) => (state) => /* ... */,
      findByRole: (role: string) => (state) => /* ... */,
      findByNameOrRole: (value: string) => (state) => /* ... */,
      findByNameAndRole: (name: string, role: string) => (state) => /* ... */,
      findWhere: (conditions: { active: boolean }) => (state) => /* ... */,
      findLimit: (limit: number) => (state) => /* ... */,
      findByNameSorted: (name: string, direction: 'asc' | 'desc') => (state) => /* ... */
    },
    mutation: {
      create: (user: CreateUserInput) => (state) => /* ... */,
      updateById: (id: string, user: UpdateUserInput) => (state) => /* ... */,
      deleteById: (id: string) => (state) => /* ... */
    }
  },
  admin: {
    query: {
      findAllUsers: () => (state) => /* ... */,
      findByDepartment: (department: string) => (state) => /* ... */
    }
  }
};

// Private API (accessible only within the node)
const _st = {
  pragma: {
    user: {
      query: {
        findByNameAndRoleOrByDept: (name: string, role: string, dept: string) => (state) => /* ... */,
        findWhereActiveLimitSorted: (limit: number) => (state) => /* ... */
      },
      mutation: {
        createWithDefaults: (user: Partial<CreateUserInput>) => (state) => /* ... */
      }
    },
    admin: {
      query: {
        findAllActiveUsers: () => (state) => /* ... */
      }
    }
  }
};
```

This API is generated automatically from the file structure, with no boilerplate code required. The public API is accessible to consumers, while the private API is only accessible within the node.

The rich syntax in the file paths (`.`, `_`, `&`, `|`, `()`, `{}`, `[]`) is translated into a cohesive API that reflects the operations and their parameters. This approach eliminates boilerplate and makes the code more maintainable.
