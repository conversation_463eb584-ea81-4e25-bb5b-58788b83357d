// Example of API definition using the API Standard with Reactive Architecture

// External API (.pragma.api.type)
export default interface {
  // Public API methods (reducers)
  getUser: (id: string) => (state: AppState) => User;
  createUser: (user: Omit<User, 'id'>) => (state: AppState) => AppState;
  updateUser: (id: string, user: Partial<User>) => (state: AppState) => AppState;
  deleteUser: (id: string) => (state: AppState) => AppState;
}

// Types used in the API
interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  createdAt: Date;
  updatedAt: Date;
}

interface AppState {
  users: User[];
  currentUser: User | null;
  isLoading: boolean;
  error: string | null;
}

// Runtime type definitions
const User = t.object({
  id: t.string().min(1),
  name: t.string().min(1),
  email: t.string().email(),
  role: t.enum(['admin', 'user', 'guest']),
  createdAt: t.date(),
  updatedAt: t.date()
});

const AppState = t.object({
  users: t.array(User),
  currentUser: t.nullable(User),
  isLoading: t.boolean(),
  error: t.nullable(t.string())
});

// API implementation as reducers
export const UserAPI = {
  getUser: (id: string) => (state: AppState) => {
    // Validate input
    t.string().min(1).parse(id);

    // Find user in state
    const user = state.users.find(user => user.id === id);

    // Validate output
    return User.parse(user);
  },

  createUser: (user: Omit<User, 'id'>) => (state: AppState) => {
    // Validate input
    t.object({
      name: t.string().min(1),
      email: t.string().email(),
      role: t.enum(['admin', 'user', 'guest'])
    }).parse(user);

    // Create new user
    const newUser = {
      ...user,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Validate new user
    const validatedUser = User.parse(newUser);

    // Return updated state
    return {
      ...state,
      users: [...state.users, validatedUser]
    };
  },

  updateUser: (id: string, user: Partial<User>) => (state: AppState) => {
    // Validate input
    t.string().min(1).parse(id);
    t.object({
      name: t.string().min(1).optional(),
      email: t.string().email().optional(),
      role: t.enum(['admin', 'user', 'guest']).optional()
    }).parse(user);

    // Update user in state
    const updatedUsers = state.users.map(existingUser => {
      if (existingUser.id === id) {
        const updatedUser = {
          ...existingUser,
          ...user,
          updatedAt: new Date()
        };
        return User.parse(updatedUser);
      }
      return existingUser;
    });

    // Return updated state
    return {
      ...state,
      users: updatedUsers
    };
  },

  deleteUser: (id: string) => (state: AppState) => {
    // Validate input
    t.string().min(1).parse(id);

    // Remove user from state
    const updatedUsers = state.users.filter(user => user.id !== id);

    // Return updated state
    return {
      ...state,
      users: updatedUsers
    };
  }
};

// Helper function to generate ID
function generateId(): string {
  return Math.random().toString(36).substring(2, 15);
}
