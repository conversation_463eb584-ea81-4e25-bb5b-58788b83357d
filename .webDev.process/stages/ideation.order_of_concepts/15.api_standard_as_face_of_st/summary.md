# API Standard as the Face of SpiceTime - Summary

## Overview

The API Standard concept defines how APIs are structured, composed, and exposed in the SpiceTime architecture. It introduces a richer structure than traditional OOP by distinguishing between private, protected, and public terms in both private and public scopes. This concept builds upon the stPragma and Runtime Type System concepts, extending them with patterns for API composition and orchestration.

Unlike traditional static API standards, this is a dynamic standard that evolves with the structure of the codebase. APIs are validatable schemas based on runtime types backed by static types, and they serve as the point of integration between different components, including the bridge to Rust implementations.

## Key Points

1. **Pragmatic File Structure**:
   - File paths and folder names express intent and relationships
   - Pragmatic extensions in file paths indicate operations and parameters
   - No boilerplate code required for API definitions

2. **API Structure**:
   - External API (`.pragma.api.type`): Public API available to dependencies
   - Internal API (`_.pragma.api.type`): Available only within the node
   - APIs are organized as GraphQL-like queries and mutations

3. **Compositional Rules and Extensions**:
   - Types define their composition rules
   - Pragma operators represent types hooked to resolvers
   - Extensions are implemented as branches in the scopes

4. **Linguistic Integration**:
   - Pragmatic file extensions define structure
   - `l` template literal handles specific queries
   - Vocabulary terms define basic operations

5. **API Standards**:
   - Conventions for API design and implementation
   - Package naming and organization
   - Package dependencies and versioning

## Implementation Approach

The API Standard is implemented through extensions to stPragma and the Runtime Type System:

1. **Reactive Architecture Pattern**:
   - APIs are implemented as reducers that transform state
   - Composition happens through function composition
   - Side effects are handled through a consistent pattern

2. **Enhanced Scope Generation**:
   - Generate more sophisticated scope structures
   - Provide helpers for API composition
   - Generate API documentation from scope structures

3. **Runtime Type System Extensions**:
   - Define types for APIs
   - Validate API inputs and outputs
   - Define types for API composition

4. **Linguistics Package Integration**:
   - Define vocabulary for API operations
   - Create grammar for API composition
   - Generate API documentation from linguistic structures

## Pragmatic Composition Example: order_of_concepts

The `ideation.order_of_concepts` structure demonstrates a powerful pattern of pragma composition where:

1. The `order` pragma (aliased from `seq`) provides sequential structure
2. The `concept` pragma defines what each item represents
3. The composition creates a new pragma that represents an ordered sequence of concepts

### Pragma Definition Structure

```
# Repository root
/
  # Main structure with order_of_concepts pragma
  /.webDev.process
    /stages
      /ideation.order_of_concepts     # Using order_of_concepts pragma
        /1.first_concept              # Concept with sequence number
        /2.second_concept
        /15.api_standard_as_face_of_st
          context.md
          summary.md
          /code
          /docs

  # Parallel trees for pragma definitions (orthogonal to webDev.process)
  /_st.pragma                         # Core pragma system
    /...

  /_seq.pragma                        # Separate seq pragma branch
    .pragma.type                      # Type definition for seq
    .pragma.alias                     # Alias definition: order -> seq

  /_concept.pragma                    # Separate concept pragma branch
    .pragma.type                      # Type definition for concept
```

### Type Definitions

```typescript
// _seq.pragma/.pragma.type
export default interface {
  // Generic sequence type that can contain any items
  Sequence<T>: {
    [index: number]: T;
    length: number;
  };

  // Methods for working with sequences
  create<T>(items: T[]): Sequence<T>;
  get<T>(seq: Sequence<T>, index: number): T | undefined;
  add<T>(seq: Sequence<T>, item: T): Sequence<T>;
}

// _concept.pragma/.pragma.type
export default interface {
  Concept: {
    id: string;
    name: string;
    context: string;
    summary: string;
    code: Record<string, string>;
    docs: Record<string, string>;
  };

  // Methods for working with concepts
  create(name: string, context?: string): Concept;
  addCode(concept: Concept, name: string, code: string): Concept;
  addDoc(concept: Concept, name: string, content: string): Concept;
}
```

### Alias Definition

```typescript
// _seq.pragma/.pragma.alias
export default {
  aliases: ['order']
};
```

### How st.pragma Processes This Structure

The st.pragma system processes this structure as follows:

1. It scans both the public tree and the parallel private tree
2. It identifies the pragma files in the private tree
3. It registers the alias from `seq` to `order`
4. It recognizes that `ideation.order_of_concepts` uses a composition of `order` and `concept`
5. It resolves `order` to `seq` using the alias registry
6. The linguistic system automatically handles the composition
7. It generates the appropriate scope for each file

For details on how version spaces are implemented in this context, see [Concept 16: Version Spaces](../16.version_spaces:impl_and_usage/summary.md).

For details on how dynamic imports are handled in the generated code, see [Concept 17: stPragma Dynamic Import Handling](../17.stPragma:dynamic_import_handling/summary.md).

### Benefits of This Approach

This approach to pragma composition offers several benefits:

1. **Reduced Boilerplate**: No need to define composed pragmas explicitly
2. **Flexible Definition Patterns**: Pragmas can be defined as structure develops
3. **Intuitive Naming**: Aliases allow for more intuitive naming (`order` vs `seq`)
4. **Clear Intent**: The file structure clearly expresses relationships
5. **Reduced Mental Load**: Developers don't need to manually compose pragmas

## Related Documents

- [API Structure and Composition](./docs/0.api_structure_and_composition.md)
- [Reactive Architecture Pattern](./docs/1.reactive_architecture_pattern.md)
- [Linguistic Integration](./docs/2.linguistic_integration.md)

## Code References

- [API Definition Example](./code/0.api_definition_example.ts)
- [API Composition Example](./code/1.api_composition_example.ts)
- [Pragmatic File Structure Example](./code/3.pragmatic_file_structure_example.md)
- [Order of Concepts Example](./code/4.order_of_concepts_example.md)
