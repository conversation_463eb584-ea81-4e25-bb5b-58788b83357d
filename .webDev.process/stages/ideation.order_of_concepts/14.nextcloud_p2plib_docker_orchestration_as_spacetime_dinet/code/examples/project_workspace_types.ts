// project_workspace_types.ts
//
// Type definitions for the project workspace example
// See docs/examples/project_workspace.md for conceptual overview

/**
 * Project definition
 */
export interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
  startDate: Date | null;
  endDate: Date | null;
  tasks: Task[];
  files: File[];
  events: Event[];
  team: TeamMember[];
}

/**
 * Task definition
 */
export interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  dueDate: Date | null;
  assignees: TeamMember[];
  dependencies: Task[];
  comments: Comment[];
  attachments: File[];
}

/**
 * File definition
 */
export interface File {
  id: string;
  name: string;
  type: string;
  size: number;
  content: string;
  versions: FileVersion[];
  sharedWith: TeamMember[];
  comments: Comment[];
}

/**
 * File version definition
 */
export interface FileVersion {
  id: string;
  version: number;
  content: string;
  createdAt: Date;
  createdBy: TeamMember;
}

/**
 * Event definition
 */
export interface Event {
  id: string;
  title: string;
  description: string;
  location: string;
  startTime: Date | null;
  endTime: Date | null;
  attendees: TeamMember[];
  resources: Resource[];
}

/**
 * Team member definition
 */
export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
}

/**
 * Comment definition
 */
export interface Comment {
  id: string;
  content: string;
  createdAt: Date;
  createdBy: TeamMember;
  replies: Comment[];
}

/**
 * Resource definition
 */
export interface Resource {
  id: string;
  name: string;
  type: string;
  url: string;
}

/**
 * Project dashboard definition
 */
export interface ProjectDashboard {
  project: Project;
  tasks: Task[];
  files: File[];
  events: Event[];
  team: TeamMember[];
  stats: TaskStatistics;
  timeline: TimelineItem[];
  activityStream: ActivityItem[];
}

/**
 * Task statistics definition
 */
export interface TaskStatistics {
  completed: number;
  inProgress: number;
  total: number;
  completionRate: number;
  overdue: number;
  byAssignee: { assignee: TeamMember, count: number }[];
}

/**
 * Timeline item definition
 */
export interface TimelineItem {
  id?: string;
  type: string;
  title: string;
  description: string;
  time: Date;
  entity: any;
}

/**
 * Activity item definition
 */
export interface ActivityItem {
  id?: string;
  type: string;
  title: string;
  description: string;
  time: Date;
  user?: TeamMember;
  entity: any;
}

/**
 * Nextcloud task definition
 */
export interface NextcloudTask {
  id: string;
  uid: string;
  title: string;
  description: string;
  completed: boolean;
  due: string | null;
  createdAt: string;
  lastModified: string;
  parentId?: string;
  assignees: NextcloudUser[];
}

/**
 * Nextcloud file definition
 */
export interface NextcloudFile {
  id: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  createdAt: string;
  lastModified: string;
  parentId?: string;
  sharedWith: NextcloudShare[];
}

/**
 * Nextcloud event definition
 */
export interface NextcloudEvent {
  id: string;
  uid: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  location: string;
  createdAt: string;
  lastModified: string;
  attendees: NextcloudAttendee[];
}

/**
 * Nextcloud user definition
 */
export interface NextcloudUser {
  uid: string;
  displayName: string;
  email: string;
  role?: string;
}

/**
 * Nextcloud share definition
 */
export interface NextcloudShare {
  uid: string;
  displayName: string;
  email: string;
  permissions: number;
}

/**
 * Nextcloud attendee definition
 */
export interface NextcloudAttendee {
  uid: string;
  displayName: string;
  email: string;
  role?: string;
  status?: string;
}
