// knowledge_base_types.ts
//
// Type definitions for the knowledge base example
// See docs/examples/knowledge_base.md for conceptual overview

/**
 * Topic definition
 */
export interface Topic {
  id: string;
  name: string;
  description: string;
  subtopics: Topic[];
  articles: Article[];
  relatedTopics: Topic[];
  contributors: Contributor[];
}

/**
 * Article definition
 */
export interface Article {
  id: string;
  title: string;
  content: string;
  tags: string[];
  categories: string[];
  references: Reference[];
  versions: ArticleVersion[];
}

/**
 * Resource definition
 */
export interface Resource {
  id: string;
  name: string;
  type: string;
  location: string;
  tags: string[];
  categories: string[];
  relatedEntities: Entity[];
  usageContext: string;
}

/**
 * Contributor definition
 */
export interface Contributor {
  id: string;
  name: string;
  email: string;
  expertise: string[];
  contributions: Contribution[];
  reputation: number;
}

/**
 * Reference definition
 */
export interface Reference {
  id: string;
  title: string;
  url: string;
  authors: string[];
  publishDate?: Date;
  publisher?: string;
}

/**
 * Article version definition
 */
export interface ArticleVersion {
  id: string;
  title: string;
  content: string;
  created: Date;
  createdBy: Contributor;
  versionNumber: number;
}

/**
 * Contribution definition
 */
export interface Contribution {
  id: string;
  entityId: string;
  entityType: string;
  role: string;
  timestamp: Date;
}

/**
 * Entity definition (union type)
 */
export type Entity = Topic | Article | Resource;

/**
 * Search result definition
 */
export interface SearchResult {
  id: string;
  type: string;
  title: string;
  snippet: string;
  relevance: number;
  entity: Entity;
}

/**
 * Nextcloud note definition
 */
export interface NextcloudNote {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  lastModified: string;
  createdBy: string;
  lastModifiedBy: string;
  categories: string[];
}

/**
 * Nextcloud file definition
 */
export interface NextcloudFile {
  id: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  createdAt: string;
  lastModified: string;
  parentId?: string;
  sharedWith: NextcloudShare[];
}

/**
 * Nextcloud user definition
 */
export interface NextcloudUser {
  uid: string;
  displayName: string;
  email: string;
}

/**
 * Nextcloud share definition
 */
export interface NextcloudShare {
  uid: string;
  displayName: string;
  email: string;
  permissions: number;
}
