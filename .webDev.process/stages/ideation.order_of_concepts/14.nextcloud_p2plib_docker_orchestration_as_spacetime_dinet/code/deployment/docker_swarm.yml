version: '3.8'

# docker_swarm.yml
#
# Docker Swarm configuration for DiNet
# See docs/deployment/docker_services.md for conceptual overview

services:
  # Core Services
  p2p-node:
    image: dinet/p2p-node:latest
    build:
      context: .
      dockerfile: ./docker/p2p-node/Dockerfile
    volumes:
      - p2p-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - P2P_BOOTSTRAP_NODES=${P2P_BOOTSTRAP_NODES:-}
      - P2P_LISTEN_ADDRESSES=${P2P_LISTEN_ADDRESSES:-/ip4/0.0.0.0/tcp/4001,/ip4/0.0.0.0/tcp/4002/ws}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    ports:
      - "4001:4001"
      - "4002:4002"
    deploy:
      mode: global
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
      placement:
        constraints:
          - node.role == worker

  semantic-store:
    image: dinet/semantic-store:latest
    build:
      context: .
      dockerfile: ./docker/semantic-store/Dockerfile
    volumes:
      - semantic-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - P2P_NODE_ADDRESS=p2p-node:4001
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    depends_on:
      - p2p-node
    deploy:
      mode: global
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
      placement:
        constraints:
          - node.role == worker

  api-gateway:
    image: dinet/api-gateway:latest
    build:
      context: .
      dockerfile: ./docker/api-gateway/Dockerfile
    volumes:
      - gateway-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - P2P_NODE_ADDRESS=p2p-node:4001
      - SEMANTIC_STORE_ADDRESS=semantic-store:8000
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    ports:
      - "8080:8080"
    depends_on:
      - p2p-node
      - semantic-store
    deploy:
      mode: global
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
      placement:
        constraints:
          - node.role == worker

  # Domain Services
  task-service:
    image: dinet/task-service:latest
    build:
      context: .
      dockerfile: ./docker/task-service/Dockerfile
    volumes:
      - task-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - API_GATEWAY_ADDRESS=api-gateway:8080
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    depends_on:
      - api-gateway
    deploy:
      mode: replicated
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
      placement:
        constraints:
          - node.labels.domain-services == true

  knowledge-service:
    image: dinet/knowledge-service:latest
    build:
      context: .
      dockerfile: ./docker/knowledge-service/Dockerfile
    volumes:
      - knowledge-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - API_GATEWAY_ADDRESS=api-gateway:8080
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    depends_on:
      - api-gateway
    deploy:
      mode: replicated
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
      placement:
        constraints:
          - node.labels.domain-services == true

  workflow-service:
    image: dinet/workflow-service:latest
    build:
      context: .
      dockerfile: ./docker/workflow-service/Dockerfile
    volumes:
      - workflow-data:/data
    environment:
      - NODE_ID={{.Node.ID}}
      - API_GATEWAY_ADDRESS=api-gateway:8080
      - LOG_LEVEL=${LOG_LEVEL:-info}
    networks:
      - dinet-overlay
    depends_on:
      - api-gateway
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
      placement:
        constraints:
          - node.labels.domain-services == true

  # Nextcloud Services
  nextcloud:
    image: nextcloud:latest
    volumes:
      - nextcloud-data:/var/www/html
    environment:
      - POSTGRES_HOST=nextcloud-db
      - POSTGRES_DB=nextcloud
      - POSTGRES_USER=nextcloud
      - POSTGRES_PASSWORD=${NEXTCLOUD_DB_PASSWORD:-nextcloud}
      - NEXTCLOUD_ADMIN_USER=${NEXTCLOUD_ADMIN_USER:-admin}
      - NEXTCLOUD_ADMIN_PASSWORD=${NEXTCLOUD_ADMIN_PASSWORD:-admin}
      - NEXTCLOUD_TRUSTED_DOMAINS=${NEXTCLOUD_TRUSTED_DOMAINS:-localhost}
      - REDIS_HOST=nextcloud-redis
    networks:
      - dinet-overlay
    ports:
      - "8000:80"
    depends_on:
      - nextcloud-db
      - nextcloud-redis
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
      placement:
        constraints:
          - node.labels.nextcloud == true

  nextcloud-db:
    image: postgres:13
    volumes:
      - nextcloud-db-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=nextcloud
      - POSTGRES_USER=nextcloud
      - POSTGRES_PASSWORD=${NEXTCLOUD_DB_PASSWORD:-nextcloud}
    networks:
      - dinet-overlay
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
      placement:
        constraints:
          - node.labels.nextcloud == true

  nextcloud-redis:
    image: redis:alpine
    networks:
      - dinet-overlay
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
      resources:
        limits:
          cpus: '0.2'
          memory: 256M
      placement:
        constraints:
          - node.labels.nextcloud == true

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - dinet-overlay
    ports:
      - "9090:9090"
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.monitoring-node == true

  grafana:
    image: grafana/grafana:latest
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - dinet-overlay
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.monitoring-node == true

networks:
  dinet-overlay:
    driver: overlay
    attachable: true

volumes:
  p2p-data:
  semantic-data:
  gateway-data:
  task-data:
  knowledge-data:
  workflow-data:
  nextcloud-data:
  nextcloud-db-data:
  prometheus-data:
  grafana-data:
