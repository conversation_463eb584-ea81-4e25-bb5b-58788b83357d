# DiNet: A Distributed Infrastructure Network with Semantic Persistence

## Executive Summary

DiNet is a novel distributed infrastructure that creates a unified semantic spacetime across independent nodes while maintaining local autonomy. By integrating p2plib (libp2p), Nextcloud, and Docker, we've designed a system where nodes can maintain their own perspective on shared resources while participating in a coherent distributed network.

## Key Innovations

1. **Local Perspective on Shared Space**: Each node maintains its own view of a unified semantic graph
2. **Semantic Persistence**: Tracking the evolution of data entities while preserving their semantic identity
3. **Database Agnosticism**: Supporting heterogeneous datastores (graph, document, relational, vector) across the network
4. **Market-Based Self-Organization**: Implementing economic incentives for optimal resource allocation
5. **Seamless End-User Experience**: Leveraging Nextcloud's mature application layer for intuitive collaboration

## Core Components

### 1. p2plib Integration
- **Addressing Scheme**: Provides semantic addressing for distributed system
- **Networking Layer**: Enables node-to-node communication
- **Content Discovery**: Locates resources across the network

See [docs/core/p2plib_integration.md](docs/core/p2plib_integration.md) and [code/core/p2p_adapter.rs](code/core/p2p_adapter.rs)

### 2. Nextcloud Connectivity
- **Database Adapters**: Connect Nextcloud to the distributed data layer
- **Application Integration**: Extend Nextcloud's collaboration tools
- **User Interface**: Provide familiar interfaces for end users

See [docs/nextcloud/nextcloud_integration.md](docs/nextcloud/nextcloud_integration.md) and [code/nextcloud/db_adapter.ts](code/nextcloud/db_adapter.ts)

### 3. Docker Swarm Layer
- **Service Orchestration**: Manage containerized services across nodes
- **Resource Allocation**: Optimize service placement based on resources
- **Resilience**: Ensure service availability despite node failures

See [docs/deployment/docker_services.md](docs/deployment/docker_services.md) and [code/deployment/docker_swarm.yml](code/deployment/docker_swarm.yml)

### 4. Treenity Trees
- **Domain Projections**: Project semantic graph into domain-specific trees
- **Reactive Programming**: Automatically update views as data changes
- **Context Management**: Maintain cross-domain relationships

See [docs/treenity/domain_projection.md](docs/treenity/domain_projection.md) and [code/treenity/domain_projection.rs](code/treenity/domain_projection.rs)

## Example Applications

### 1. Project Workspace
- Unified view of project tasks, files, events, and team members
- Cross-node collaboration with local autonomy
- Semantic relationships between project elements

See [docs/examples/project_workspace.md](docs/examples/project_workspace.md) and [code/examples/project_workspace.ts](code/examples/project_workspace.ts)

### 2. Knowledge Base
- Distributed knowledge management across organizational boundaries
- Semantic linking of related concepts
- Version tracking with semantic persistence

See [docs/examples/knowledge_base.md](docs/examples/knowledge_base.md) and [code/examples/knowledge_base.ts](code/examples/knowledge_base.ts)

## Implementation Status

This concept is currently in the ideation phase. The documentation and code examples provided are architectural blueprints for implementation. The next steps include:

1. Developing a proof-of-concept implementation of the p2plib integration
2. Creating a custom Nextcloud app for DiNet integration
3. Implementing the Docker Swarm configuration
4. Building the Treenity Trees projection system

## Related Concepts

- Semantic addressing and persistence
- Distributed systems architecture
- Reactive programming models
- Economic mechanisms for resource allocation
