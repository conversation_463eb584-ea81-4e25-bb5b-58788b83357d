# Domain Projection with Treenity Trees

This document describes how DiNet uses Treenity Trees to project the semantic graph into domain-specific hierarchical views.

## Conceptual Overview

Treenity Trees provide a powerful projection mechanism that transforms the underlying graph structure into domain-specific hierarchical views. This allows complex graph structures to be navigated and comprehended for specific use cases.

Key concepts include:

1. **Graph Projection**: Transforming a graph into a tree by selecting specific relationship types
2. **Domain Focus**: Creating views optimized for specific domains
3. **Context Trees**: Maintaining cross-domain relationships
4. **Reactive Updates**: Automatically reflecting changes in the underlying graph

## Graph Projection

The graph projection process:

1. Selects a specific relationship type as the parent/child relationship
2. Creates a tree structure based on this relationship
3. Preserves other relationships as context links
4. Maintains the semantic identity of nodes

See the implementation in [code/treenity/domain_projection.rs](../code/treenity/domain_projection.rs).

## Domain Focus

Domain focus allows:

1. Creating views optimized for specific domains
2. Prioritizing relationships relevant to the domain
3. Filtering out irrelevant information
4. Providing domain-specific operations

See the implementation in [code/treenity/domain_view.rs](../code/treenity/domain_view.rs).

## Context Trees

Context trees:

1. Maintain cross-domain relationships
2. Provide additional context for nodes
3. Can be hierarchical when relationships have asymmetry
4. Can be imported from other domain trees

This creates a rich ecosystem of interconnected yet cleanly separated domains - effectively a "forest" of semantic trees.

See the implementation in [code/treenity/domain_context.rs](../code/treenity/domain_context.rs).

## Reactive Programming Model

The reactive programming model:

1. Trees automatically reflect the current state of links and nodes
2. Changes propagate through the system
3. Views update in real-time
4. Reducers and resolvers process changes

See the implementation in [code/treenity/reactive.rs](../code/treenity/reactive.rs).

## GNN-Based Domain Discovery

DiNet uses Graph Neural Networks (GNNs) to:

1. Automatically identify domains in the semantic graph
2. Suggest optimal projections for specific use cases
3. Learn from user interactions to improve projections
4. Discover hidden patterns in the data

This is implemented in the Rust core of each node.

See the implementation in [code/treenity/domain_discovery.rs](../code/treenity/domain_discovery.rs).

## Implementation Details

### Tree Node Structure

Each tree node contains:

1. Reference to the semantic node
2. Parent-child relationships
3. Context links
4. Domain-specific metadata

```rust
struct TreeNode {
    // Reference to the semantic node
    semantic_id: String,
    // Parent node (if any)
    parent: Option<String>,
    // Child nodes
    children: Vec<String>,
    // Context links
    context_links: Vec<ContextLink>,
    // Domain-specific metadata
    metadata: HashMap<String, Value>,
}
```

### Tree Construction

Trees are constructed by:

1. Selecting a root node
2. Traversing the graph using the selected relationship type
3. Building the tree structure
4. Adding context links

```rust
fn build_tree(graph: &SemanticGraph, root_id: &str, relationship_type: &str) -> Tree {
    // Implementation details
}
```

### Tree Synchronization

Trees are synchronized by:

1. Monitoring changes in the semantic graph
2. Updating the tree structure
3. Notifying observers of changes
4. Applying reducers and resolvers

```rust
fn sync_tree(tree: &mut Tree, graph: &SemanticGraph, changes: &[GraphChange]) {
    // Implementation details
}
```

## Application Examples

### Project Management

In project management:

1. Projects are root nodes
2. Tasks are child nodes
3. Dependencies are context links
4. Team members are context nodes

See the implementation in [code/examples/project_workspace.ts](../code/examples/project_workspace.ts).

### Knowledge Management

In knowledge management:

1. Topics are root nodes
2. Subtopics are child nodes
3. Related topics are context links
4. Resources are context nodes

See the implementation in [code/examples/knowledge_base.ts](../code/examples/knowledge_base.ts).

## Related Documentation

- [p2plib Integration](../core/p2plib_integration.md)
- [Nextcloud Integration](../nextcloud/nextcloud_integration.md)
- [Project Workspace Example](../examples/project_workspace.md)
