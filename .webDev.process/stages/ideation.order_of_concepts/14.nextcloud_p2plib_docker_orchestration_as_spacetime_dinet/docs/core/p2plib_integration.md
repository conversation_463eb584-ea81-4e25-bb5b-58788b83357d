# p2plib Integration

This document describes how DiNet integrates with p2plib (libp2p) to provide the networking and addressing foundation for the distributed infrastructure.

## Conceptual Overview

p2plib serves as the networking backbone of DiNet, providing:

1. **Peer-to-Peer Communication**: Direct node-to-node communication without central servers
2. **Content Addressing**: Location-independent resource identification
3. **Semantic Addressing**: Extension of content addressing to maintain semantic identity over time
4. **Distributed Discovery**: Finding resources across the network without central coordination

The key innovation in our approach is extending p2plib's addressing scheme to support semantic persistence, allowing entities to maintain their identity as they evolve over time.

## Semantic Addressing

Traditional content addressing (like IPFS) creates identifiers based on content hashes, which change whenever the content changes. Our semantic addressing scheme:

1. Maintains a persistent identifier for semantic entities
2. Links to the current content hash
3. Preserves the history of content changes
4. Enables traversal through time for any entity

See the implementation in [code/core/addressing.rs](../code/core/addressing.rs).

## Network Topology

DiNet uses a mesh network topology where:

- Each node connects to multiple peers
- Connections are established based on network proximity and resource needs
- The network self-organizes based on usage patterns
- Redundant paths ensure resilience

## Service Discovery

Services in DiNet are discovered through:

1. **Distributed Hash Table (DHT)**: For locating content and services
2. **Publish/Subscribe**: For real-time updates and notifications
3. **Semantic Routing**: For finding resources based on their semantic properties

## Implementation Details

The p2p adapter implements:

- Connection management
- Content routing
- Semantic addressing
- Data transfer

See the implementation in [code/core/p2p_adapter.rs](../code/core/p2p_adapter.rs).

## Integration with Other Components

p2plib integrates with:

- **Nextcloud**: Through custom database adapters
- **Docker Swarm**: For service orchestration
- **Treenity Trees**: For domain-specific projections

## Security Considerations

The p2plib integration includes:

- End-to-end encryption for data transfer
- Authentication of peers
- Authorization for resource access
- Privacy-preserving routing

## Performance Optimization

To ensure optimal performance, the p2plib integration:

- Caches frequently accessed content
- Prioritizes network connections based on usage patterns
- Implements efficient routing algorithms
- Uses compression for data transfer

## Related Documentation

- [Semantic Persistence](semantic_persistence.md)
- [Nextcloud Integration](../nextcloud/nextcloud_integration.md)
- [Docker Services](../deployment/docker_services.md)
