# DiNet: Distributed Infrastructure Network

## Project Overview

This project develops DiNet, a novel distributed infrastructure that creates a unified semantic spacetime across independent nodes while maintaining local autonomy. By integrating p2plib (libp2p), Nextcloud, and Docker, we've designed a system where nodes can maintain their own perspective on shared resources while participating in a coherent distributed network.

## Key Components

### 1. Core Architecture

- **p2plib Integration**: Provides addressing scheme and networking for distributed system ([docs/architecture/p2plib_integration.md](docs/architecture/p2plib_integration.md), [code/core/p2p_adapter.rs](code/core/p2p_adapter.rs))
- **Nextcloud Connectivity**: Leverages existing Nextcloud database adapters and APIs ([docs/architecture/nextcloud_integration.md](docs/architecture/nextcloud_integration.md), [code/nextcloud/db_adapter.ts](code/nextcloud/db_adapter.ts))
- **Docker Swarm Layer**: Implements delocalized services with local API access ([docs/architecture/docker_services.md](docs/architecture/docker_services.md), [code/deployment/docker_swarm.yml](code/deployment/docker_swarm.yml))
- **Semantic Persistence**: Maintains entity identity across content changes ([docs/concepts/semantic_persistence.md](docs/concepts/semantic_persistence.md), [code/semantic/addressing.rs](code/semantic/addressing.rs))

### 2. Treenity Trees Implementation

Treenity Trees is our implementation approach for creating domain-specific views from the underlying graph data:

- **Graph Projection System**: Projects semantic graphs to domain-specific trees ([docs/implementation/graph_projection.md](docs/implementation/graph_projection.md), [code/treenity/domain_projection.rs](code/treenity/domain_projection.rs))
- **Reactive Programming Model**: Propagates changes through the system ([docs/implementation/reactive_model.md](docs/implementation/reactive_model.md), [code/treenity/observable.rs](code/treenity/observable.rs))
- **GNN-Based Domain Creation**: Uses neural networks to discover and optimize domains ([docs/implementation/gnn_domains.md](docs/implementation/gnn_domains.md), [code/treenity/domain_discovery.rs](code/treenity/domain_discovery.rs))

### 3. Integration Examples

- **Nextcloud Task Enhancement**: Adding semantic capabilities to Nextcloud tasks ([docs/examples/task_enhancement.md](docs/examples/task_enhancement.md), [code/examples/nextcloud_tasks.ts](code/examples/nextcloud_tasks.ts))
- **Project Workspace**: Creating unified views across Nextcloud components ([docs/examples/project_workspace.md](docs/examples/project_workspace.md), [code/examples/project_workspace.ts](code/examples/project_workspace.ts))
- **Distributed Knowledge Base**: Semantic graph for organizational knowledge ([docs/examples/knowledge_base.md](docs/examples/knowledge_base.md), [code/examples/knowledge_graph.rs](code/examples/knowledge_graph.rs))
- **Federation Between Nodes**: Maintaining consistent views across separate instances ([docs/examples/federation.md](docs/examples/federation.md), [code/examples/node_federation.rs](code/examples/node_federation.rs))

### 4. Production Considerations

- **Deployment Strategies**: Options for single-node to enterprise deployment ([docs/deployment/strategies.md](docs/deployment/strategies.md), [code/deployment/deploy.sh](code/deployment/deploy.sh))
- **Performance Optimization**: Techniques for maintaining responsiveness ([docs/deployment/performance.md](docs/deployment/performance.md), [code/optimization/caching.rs](code/optimization/caching.rs))
- **Security Model**: Ensuring proper authentication and authorization ([docs/deployment/security.md](docs/deployment/security.md), [code/security/auth.rs](code/security/auth.rs))
- **Scalability Testing**: Benchmarks for different network sizes ([docs/deployment/scalability.md](docs/deployment/scalability.md), [code/testing/benchmarks.rs](code/testing/benchmarks.rs))
