// types.ts
//
// Type definitions for the project workspace example
// See docs/examples/project_workspace.md for conceptual overview

import { Node } from '../../core/semantic/types';

/**
 * Definition of project dashboard data
 */
export interface ProjectDashboard {
  project: Node;
  tasks: Node[];
  files: Node[];
  events: Node[];
  team: Node[];
  stats: TaskStatistics;
  timeline: TimelineItem[];
  activityStream: ActivityItem[];
}

/**
 * Task statistics
 */
export interface TaskStatistics {
  completed: number;
  inProgress: number;
  total: number;
  completionRate: number;
  overdue: number;
  byAssignee: { assignee: Node, count: number }[];
}

/**
 * Timeline item
 */
export interface TimelineItem {
  type: string;
  node: Node;
  time: Date;
  title: string;
  description: string;
}

/**
 * Activity item
 */
export interface ActivityItem {
  type: string;
  node: Node;
  time: Date;
  user?: Node;
  description: string;
}

/**
 * Nextcloud task data
 */
export interface NextcloudTask {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  due: string;
  createdAt: string;
  lastModified: string;
  parentId?: string;
  assignees: NextcloudUser[];
}

/**
 * Nextcloud file data
 */
export interface NextcloudFile {
  id: string;
  name: string;
  path: string;
  mimeType: string;
  size: number;
  createdAt: string;
  lastModified: string;
  parentId?: string;
  sharedWith: NextcloudShare[];
}

/**
 * Nextcloud event data
 */
export interface NextcloudEvent {
  id: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  location: string;
  createdAt: string;
  lastModified: string;
  attendees: NextcloudAttendee[];
}

/**
 * Nextcloud user data
 */
export interface NextcloudUser {
  uid: string;
  displayName: string;
  email: string;
  role?: string;
}

/**
 * Nextcloud share data
 */
export interface NextcloudShare {
  uid: string;
  displayName: string;
  email: string;
  permissions: number;
}

/**
 * Nextcloud attendee data
 */
export interface NextcloudAttendee {
  uid: string;
  displayName: string;
  email: string;
  role?: string;
  status?: string;
}
