// db_adapter.ts
//
// Integration with Nextcloud's database layer
// See docs/architecture/nextcloud_integration.md for conceptual overview

import { Pool, QueryResult } from 'pg';
import { Database as SqliteDatabase } from 'sqlite3';
import { createConnection, Connection } from 'mysql2/promise';
import { SemanticGraph, NodeId, Node, Link, LinkType } from '../../core/semantic_graph';
import { Config } from '../../config/config';
import { Logger } from '../../util/logger';

/**
 * Adapter for connecting to Nextcloud's database
 */
export class NextcloudDbAdapter {
  private dbType: string;
  private pgPool?: Pool;
  private sqliteDb?: SqliteDatabase;
  private mysqlConn?: Connection;
  private logger: Logger;
  private config: Config;
  private semanticGraph: SemanticGraph;
  
  /**
   * Creates a new Nextcloud database adapter
   */
  constructor(config: Config, semanticGraph: SemanticGraph, logger: Logger) {
    this.config = config;
    this.semanticGraph = semanticGraph;
    this.logger = logger;
    this.dbType = config.nextcloud.database.type;
    
    // Initialize database connection based on type
    this.initializeDbConnection();
  }
  
  /**
   * Initializes the database connection
   */
  private async initializeDbConnection(): Promise<void> {
    try {
      switch (this.dbType) {
        case 'postgres':
          this.pgPool = new Pool({
            host: this.config.nextcloud.database.host,
            port: this.config.nextcloud.database.port,
            user: this.config.nextcloud.database.user,
            password: this.config.nextcloud.database.password,
            database: this.config.nextcloud.database.name,
            ssl: this.config.nextcloud.database.ssl
          });
          
          // Test the connection
          await this.pgPool.query('SELECT 1');
          this.logger.info('Successfully connected to PostgreSQL database');
          break;
          
        case 'sqlite':
          this.sqliteDb = new SqliteDatabase(this.config.nextcloud.database.path);
          this.logger.info('Successfully connected to SQLite database');
          break;
          
        case 'mysql':
          this.mysqlConn = await createConnection({
            host: this.config.nextcloud.database.host,
            port: this.config.nextcloud.database.port,
            user: this.config.nextcloud.database.user,
            password: this.config.nextcloud.database.password,
            database: this.config.nextcloud.database.name,
            ssl: this.config.nextcloud.database.ssl
          });
          
          // Test the connection
          await this.mysqlConn.query('SELECT 1');
          this.logger.info('Successfully connected to MySQL database');
          break;
          
        default:
          throw new Error(`Unsupported database type: ${this.dbType}`);
      }
    } catch (error) {
      this.logger.error(`Failed to connect to database: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Executes a query on the database
   */
  async query(sql: string, params: any[] = []): Promise<any[]> {
    try {
      switch (this.dbType) {
        case 'postgres':
          const pgResult = await this.pgPool!.query(sql, params);
          return pgResult.rows;
          
        case 'sqlite':
          return new Promise((resolve, reject) => {
            this.sqliteDb!.all(sql, params, (error, rows) => {
              if (error) {
                reject(error);
              } else {
                resolve(rows);
              }
            });
          });
          
        case 'mysql':
          const [mysqlRows] = await this.mysqlConn!.query(sql, params);
          return mysqlRows as any[];
          
        default:
          throw new Error(`Unsupported database type: ${this.dbType}`);
      }
    } catch (error) {
      this.logger.error(`Query failed: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Synchronizes Nextcloud tasks with the semantic graph
   */
  async syncTasks(): Promise<void> {
    try {
      // Get all tasks from Nextcloud
      const tasks = await this.query(`
        SELECT id, uid, title, description, completed, due, created_at, last_modified
        FROM ${this.config.nextcloud.database.tablePrefix}tasks_tasks
      `);
      
      // Process each task
      for (const task of tasks) {
        // Convert to semantic node
        const nodeId = `task:${task.id}`;
        const node: Node = {
          id: nodeId,
          type: 'Task',
          data: {
            title: task.title,
            description: task.description,
            completed: task.completed === 1,
            due: task.due,
            createdAt: task.created_at,
            lastModified: task.last_modified,
            externalIds: {
              nextcloud: task.id
            }
          }
        };
        
        // Add or update node in semantic graph
        this.semanticGraph.addOrUpdateNode(nodeId, node);
        
        // Get task assignments
        const assignments = await this.query(`
          SELECT user_uid
          FROM ${this.config.nextcloud.database.tablePrefix}tasks_assignments
          WHERE task_id = ?
        `, [task.id]);
        
        // Add assignment links
        for (const assignment of assignments) {
          const userId = `user:${assignment.user_uid}`;
          
          // Ensure user node exists
          if (!this.semanticGraph.hasNode(userId)) {
            this.semanticGraph.addNode(userId, {
              id: userId,
              type: 'User',
              data: {
                uid: assignment.user_uid,
                externalIds: {
                  nextcloud: assignment.user_uid
                }
              }
            });
          }
          
          // Add assignment link
          this.semanticGraph.addLink(nodeId, userId, {
            type: 'ASSIGNED_TO',
            data: {}
          });
        }
        
        // Get subtask relationships
        const parentTasks = await this.query(`
          SELECT parent_id
          FROM ${this.config.nextcloud.database.tablePrefix}tasks_subtasks
          WHERE task_id = ?
        `, [task.id]);
        
        // Add parent-child links
        for (const parentTask of parentTasks) {
          const parentId = `task:${parentTask.parent_id}`;
          
          // Add subtask link
          this.semanticGraph.addLink(nodeId, parentId, {
            type: 'SUBTASK_OF',
            data: {}
          });
        }
      }
      
      this.logger.info(`Successfully synchronized ${tasks.length} tasks`);
    } catch (error) {
      this.logger.error(`Failed to sync tasks: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Synchronizes Nextcloud files with the semantic graph
   */
  async syncFiles(): Promise<void> {
    try {
      // Get all files from Nextcloud
      const files = await this.query(`
        SELECT f.fileid, f.name, f.path, f.mimetype, f.mtime, f.size,
               s.etag, s.storage_id
        FROM ${this.config.nextcloud.database.tablePrefix}filecache f
        JOIN ${this.config.nextcloud.database.tablePrefix}storages s ON f.storage = s.numeric_id
        WHERE f.path IS NOT NULL
      `);
      
      // Process each file
      for (const file of files) {
        // Convert to semantic node
        const nodeId = `file:${file.fileid}`;
        const node: Node = {
          id: nodeId,
          type: 'File',
          data: {
            name: file.name,
            path: file.path,
            mimeType: file.mimetype,
            modifiedTime: file.mtime,
            size: file.size,
            etag: file.etag,
            storageId: file.storage_id,
            externalIds: {
              nextcloud: file.fileid
            }
          }
        };
        
        // Add or update node in semantic graph
        this.semanticGraph.addOrUpdateNode(nodeId, node);
        
        // Get file sharing info
        const shares = await this.query(`
          SELECT share_with, share_type, permissions
          FROM ${this.config.nextcloud.database.tablePrefix}share
          WHERE file_source = ?
        `, [file.fileid]);
        
        // Add sharing links
        for (const share of shares) {
          if (share.share_with && share.share_type === 0) { // User share
            const userId = `user:${share.share_with}`;
            
            // Ensure user node exists
            if (!this.semanticGraph.hasNode(userId)) {
              this.semanticGraph.addNode(userId, {
                id: userId,
                type: 'User',
                data: {
                  uid: share.share_with,
                  externalIds: {
                    nextcloud: share.share_with
                  }
                }
              });
            }
            
            // Add sharing link
            this.semanticGraph.addLink(nodeId, userId, {
              type: 'SHARED_WITH',
              data: {
                permissions: share.permissions
              }
            });
          }
        }
        
        // Handle folder structure
        if (file.path.includes('/')) {
          const pathParts = file.path.split('/');
          pathParts.pop(); // Remove file name
          
          if (pathParts.length > 0) {
            // Find parent folder
            const parentPath = pathParts.join('/');
            const parentFolders = await this.query(`
              SELECT fileid
              FROM ${this.config.nextcloud.database.tablePrefix}filecache
              WHERE path = ?
            `, [parentPath]);
            
            if (parentFolders.length > 0) {
              const parentId = `file:${parentFolders[0].fileid}`;
              
              // Add containment link
              this.semanticGraph.addLink(nodeId, parentId, {
                type: 'CONTAINED_IN',
                data: {}
              });
            }
          }
        }
      }
      
      this.logger.info(`Successfully synchronized ${files.length} files`);
    } catch (error) {
      this.logger.error(`Failed to sync files: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Updates a Nextcloud task from the semantic graph
   */
  async updateTask(nodeId: string): Promise<void> {
    try {
      // Get the node from the semantic graph
      const node = this.semanticGraph.getNode(nodeId);
      if (!node || node.type !== 'Task') {
        throw new Error(`Node ${nodeId} is not a task`);
      }
      
      // Get the Nextcloud task ID
      const nextcloudId = node.data.externalIds?.nextcloud;
      if (!nextcloudId) {
        throw new Error(`Task ${nodeId} has no Nextcloud ID`);
      }
      
      // Update the task in Nextcloud
      await this.query(`
        UPDATE ${this.config.nextcloud.database.tablePrefix}tasks_tasks
        SET title = ?, description = ?, completed = ?, due = ?, last_modified = ?
        WHERE id = ?
      `, [
        node.data.title,
        node.data.description,
        node.data.completed ? 1 : 0,
        node.data.due,
        Math.floor(Date.now() / 1000), // Current Unix timestamp
        nextcloudId
      ]);
      
      // Get assigned users
      const assignedLinks = this.semanticGraph.getOutgoingLinks(nodeId, 'ASSIGNED_TO');
      
      // Clear existing assignments
      await this.query(`
        DELETE FROM ${this.config.nextcloud.database.tablePrefix}tasks_assignments
        WHERE task_id = ?
      `, [nextcloudId]);
      
      // Add new assignments
      for (const link of assignedLinks) {
        const userNode = this.semanticGraph.getNode(link.target);
        if (userNode && userNode.type === 'User' && userNode.data.externalIds?.nextcloud) {
          await this.query(`
            INSERT INTO ${this.config.nextcloud.database.tablePrefix}tasks_assignments
            (task_id, user_uid)
            VALUES (?, ?)
          `, [nextcloudId, userNode.data.externalIds.nextcloud]);
        }
      }
      
      this.logger.info(`Successfully updated task ${nodeId} in Nextcloud`);
    } catch (error) {
      this.logger.error(`Failed to update task ${nodeId}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Creates a new Nextcloud task from the semantic graph
   */
  async createTask(nodeId: string): Promise<void> {
    try {
      // Get the node from the semantic graph
      const node = this.semanticGraph.getNode(nodeId);
      if (!node || node.type !== 'Task') {
        throw new Error(`Node ${nodeId} is not a task`);
      }
      
      // Create the task in Nextcloud
      const result = await this.query(`
        INSERT INTO ${this.config.nextcloud.database.tablePrefix}tasks_tasks
        (uid, title, description, completed, due, created_at, last_modified)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        RETURNING id
      `, [
        'default', // Use default UID
        node.data.title,
        node.data.description,
        node.data.completed ? 1 : 0,
        node.data.due,
        Math.floor(Date.now() / 1000), // Current Unix timestamp
        Math.floor(Date.now() / 1000) // Current Unix timestamp
      ]);
      
      const taskId = result[0].id;
      
      // Update the semantic node with the Nextcloud ID
      this.semanticGraph.updateNode(nodeId, {
        ...node,
        data: {
          ...node.data,
          externalIds: {
            ...node.data.externalIds,
            nextcloud: taskId
          }
        }
      });
      
      // Get assigned users
      const assignedLinks = this.semanticGraph.getOutgoingLinks(nodeId, 'ASSIGNED_TO');
      
      // Add assignments
      for (const link of assignedLinks) {
        const userNode = this.semanticGraph.getNode(link.target);
        if (userNode && userNode.type === 'User' && userNode.data.externalIds?.nextcloud) {
          await this.query(`
            INSERT INTO ${this.config.nextcloud.database.tablePrefix}tasks_assignments
            (task_id, user_uid)
            VALUES (?, ?)
          `, [taskId, userNode.data.externalIds.nextcloud]);
        }
      }
      
      // Handle subtask relationships
      const subtaskLinks = this.semanticGraph.getOutgoingLinks(nodeId, 'SUBTASK_OF');
      
      for (const link of subtaskLinks) {
        const parentNode = this.semanticGraph.getNode(link.target);
        if (parentNode && parentNode.type === 'Task' && parentNode.data.externalIds?.nextcloud) {
          await this.query(`
            INSERT INTO ${this.config.nextcloud.database.tablePrefix}tasks_subtasks
            (task_id, parent_id)
            VALUES (?, ?)
          `, [taskId, parentNode.data.externalIds.nextcloud]);
        }
      }
      
      this.logger.info(`Successfully created task ${nodeId} in Nextcloud with ID ${taskId}`);
    } catch (error) {
      this.logger.error(`Failed to create task ${nodeId}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Deletes a Nextcloud task from the semantic graph
   */
  async deleteTask(nodeId: string): Promise<void> {
    try {
      // Get the node from the semantic graph
      const node = this.semanticGraph.getNode(nodeId);
      if (!node || node.type !== 'Task') {
        throw new Error(`Node ${nodeId} is not a task`);
      }
      
      // Get the Nextcloud task ID
      const nextcloudId = node.data.externalIds?.nextcloud;
      if (!nextcloudId) {
        throw new Error(`Task ${nodeId} has no Nextcloud ID`);
      }
      
      // Delete subtask relationships
      await this.query(`
        DELETE FROM ${this.config.nextcloud.database.tablePrefix}tasks_subtasks
        WHERE task_id = ? OR parent_id = ?
      `, [nextcloudId, nextcloudId]);
      
      // Delete assignments
      await this.query(`
        DELETE FROM ${this.config.nextcloud.database.tablePrefix}tasks_assignments
        WHERE task_id = ?
      `, [nextcloudId]);
      
      // Delete the task
      await this.query(`
        DELETE FROM ${this.config.nextcloud.database.tablePrefix}tasks_tasks
        WHERE id = ?
      `, [nextcloudId]);
      
      this.logger.info(`Successfully deleted task ${nodeId} from Nextcloud`);
    } catch (error) {
      this.logger.error(`Failed to delete task ${nodeId}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Closes the database connection
   */
  async close(): Promise<void> {
    try {
      switch (this.dbType) {
        case 'postgres':
          await this.pgPool!.end();
          break;
          
        case 'sqlite':
          await new Promise<void>((resolve, reject) => {
            this.sqliteDb!.close((error) => {
              if (error) {
                reject(error);
              } else {
                resolve();
              }
            });
          });
          break;
          
        case 'mysql':
          await this.mysqlConn!.end();
          break;
      }
      
      this.logger.info('Database connection closed');
    } catch (error) {
      this.logger.error(`Failed to close database connection: ${error.message}`);
      throw error;
    }
  }
}
