# DiNet Project Structure

This document outlines the file structure for the DiNet project, showing how the code is organized into manageable components.

## Directory Structure

```
dinet/
├── code/
│   ├── core/
│   │   ├── p2p_adapter.rs         # p2plib integration
│   │   ├── semantic/
│   │   │   ├── addressing.rs      # Semantic addressing implementation
│   │   │   ├── graph.rs           # Semantic graph model
│   │   │   └── persistence.rs     # Semantic persistence
│   │   ├── reactive/
│   │   │   ├── observable.rs      # Reactive programming model
│   │   │   └── observer.rs        # Observer pattern implementation
│   │   └── api/
│   │       ├── gateway.rs         # Local API gateway
│   │       └── local_api.ts       # TypeScript API interfaces
│   ├── nextcloud/
│   │   ├── db_adapter.ts          # Database adapter
│   │   ├── integration.ts         # Integration with Nextcloud
│   │   ├── storage_backend.php    # External storage backend
│   │   └── app_integration.php    # Nextcloud app integration
│   ├── treenity/
│   │   ├── domain_projection.rs   # Domain projection implementation
│   │   ├── domain_view.rs         # Domain view implementation
│   │   └── domain_discovery.rs    # GNN-based domain discovery
│   ├── deployment/
│   │   ├── docker_swarm.yml       # Docker Swarm configuration
│   │   ├── deploy.sh              # Deployment script
│   │   └── monitoring.yml         # Monitoring configuration
│   └── examples/
│       ├── project_workspace/
│       │   ├── types.ts           # Type definitions
│       │   ├── workspace.ts       # Project workspace implementation
│       │   ├── sync.ts            # Data synchronization
│       │   └── dashboard.ts       # Dashboard generation
│       ├── knowledge_base.ts      # Knowledge base example
│       ├── task_enhancement.ts    # Task enhancement example
│       └── federation.ts          # Federation example
├── docs/
│   ├── architecture/
│   │   ├── p2plib_integration.md  # p2plib integration documentation
│   │   ├── nextcloud_integration.md # Nextcloud integration documentation
│   │   └── docker_services.md     # Docker services architecture
│   ├── concepts/
│   │   └── semantic_persistence.md # Semantic persistence concept
│   ├── implementation/
│   │   ├── graph_projection.md    # Graph projection implementation
│   │   ├── gnn_domains.md         # GNN-based domain discovery
│   │   └── reactive_model.md      # Reactive programming model
│   └── examples/
│       ├── project_workspace.md   # Project workspace example
│       ├── knowledge_base.md      # Knowledge base example
│       ├── task_enhancement.md    # Task enhancement example
│       └── federation.md          # Federation example
└── README.md                      # Project overview
```

## Core Components

The DiNet project is organized into several key components:

1. **Core**: Fundamental infrastructure including p2plib integration, semantic addressing, reactive programming model, and API interfaces.

2. **Nextcloud**: Integration with Nextcloud, including database adapters, storage backends, and application integration.

3. **Treenity**: Implementation of domain projections, views, and GNN-based domain discovery.

4. **Deployment**: Docker configurations, deployment scripts, and monitoring.

5. **Examples**: Complete examples demonstrating various use cases.

## Documentation Structure

Documentation is organized to mirror the code structure:

1. **Architecture**: High-level architectural documentation describing the major components and their interactions.

2. **Concepts**: Explanations of core concepts like semantic persistence.

3. **Implementation**: Detailed documentation of specific implementation aspects.

4. **Examples**: Documentation of example applications and use cases.

## Getting Started

To explore the project:

1. Start with the README.md file for an overview.
2. Review the architecture documentation to understand the major components.
3. Explore the examples to see how the components work together.
4. Dive into the specific implementation details as needed.

Each file is kept small and focused on a specific aspect of the system, making it easier to understand and maintain.
