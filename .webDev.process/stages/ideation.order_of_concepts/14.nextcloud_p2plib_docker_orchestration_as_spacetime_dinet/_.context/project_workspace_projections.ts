// projections.ts
//
// Domain projections for the project workspace
// See docs/examples/project_workspace.md for conceptual overview

import { DomainProjection } from '../../core/semantic/types';

/**
 * Creates the main project domain projection
 */
export function createProjectDomain(projectId: string): DomainProjection {
  // Create a projection that puts the project at the root
  // and organizes tasks hierarchically
  return new DomainProjection({
    name: "Project Hierarchy",
    hierarchicalLinkType: "SUBTASK_OF",
    rootNodeIds: [projectId],
    hierarchyResolutionPriority: [
      { linkType: "PRIORITY", direction: "higher-first" }
    ],
    contextLinkTypes: [
      "ASSIGNED_TO",
      "DEPENDS_ON",
      "REFERENCES",
      "SCHEDULED_AS"
    ]
  });
}

/**
 * Creates a projection organized by assignee
 */
export function createAssigneeProjection(): DomainProjection {
  return new DomainProjection({
    name: "By Assignee",
    hierarchicalLinkType: "ASSIGNED_TO",
    hierarchyResolutionPriority: [
      { linkType: "PRIORITY", direction: "higher-first" }
    ],
    contextLinkTypes: [
      "PART_OF",
      "DEPENDS_ON",
      "REFERENCES",
      "SCHEDULED_AS"
    ]
  });
}

/**
 * Creates a projection organized by timeline
 */
export function createTimelineProjection(): DomainProjection {
  return new DomainProjection({
    name: "Timeline",
    hierarchicalLinkType: "PRECEDES",
    hierarchyResolutionPriority: [
      { linkType: "DUE_DATE", direction: "earlier-first" }
    ],
    contextLinkTypes: [
      "PART_OF",
      "ASSIGNED_TO",
      "REFERENCES",
      "SCHEDULED_AS"
    ]
  });
}

/**
 * Creates a projection organized by file type
 */
export function createFileTypeProjection(): DomainProjection {
  return new DomainProjection({
    name: "By File Type",
    hierarchicalLinkType: "HAS_TYPE",
    contextLinkTypes: [
      "PART_OF",
      "CREATED_BY",
      "REFERENCED_BY"
    ]
  });
}

/**
 * Creates a projection organized by location
 */
export function createLocationProjection(): DomainProjection {
  return new DomainProjection({
    name: "By Location",
    hierarchicalLinkType: "LOCATED_IN",
    contextLinkTypes: [
      "PART_OF",
      "SCHEDULED_AT"
    ]
  });
}
