# Docker Services Architecture

This document describes how DiNet uses Docker for service deployment, creating a layered architecture with delocalized services that can be accessed through a local API.

## Conceptual Overview

DiNet uses Docker Swarm to create a flexible, scalable service layer that spans multiple physical nodes while presenting a unified API to local applications. This approach allows services to be distributed based on available resources while maintaining the simplicity of local API calls.

## Key Components

### Layered Architecture

DiNet's Docker architecture consists of several layers:

```
+-----------------------+
| Application Layer     | <- Nextcloud
+-----------------------+
| Local API Layer       | <- API Gateway
+-----------------------+
| Service Mesh          | <- Docker Swarm Overlay Network
+-----------------------+
| Container Services    | <- Delocalized Containers
+-----------------------+
| Infrastructure        | <- Physical Nodes
+-----------------------+
```

For implementation details, see [code/deployment/docker_architecture.yml](../code/deployment/docker_architecture.yml).

### Service Containers

Each DiNet service runs in its own container:

```yaml
# Reference to code/deployment/service_definition.yml
# This example shows a service container definition
```

The service containers are orchestrated by Docker Swarm. See full implementation in [code/deployment/service_definition.yml](../code/deployment/service_definition.yml).

### Local API Gateway

The local API gateway provides a unified interface to delocalized services:

```typescript
// Reference to code/api/gateway.ts
// This example shows the API gateway implementation
```

This gateway routes requests to the appropriate services, regardless of where they are running. See full implementation in [code/api/gateway.ts](../code/api/gateway.ts).

## Orchestration

### Service Placement

Docker Swarm handles service placement based on:

```yaml
# Reference to code/deployment/placement_constraints.yml
# This example shows service placement constraints
```

These constraints ensure that services are placed on the most appropriate nodes. See full implementation in [code/deployment/placement_constraints.yml](../code/deployment/placement_constraints.yml).

### Service Discovery

Services discover each other through Docker's built-in DNS:

```typescript
// Reference to code/services/discovery.ts
// This example shows service discovery
```

This allows services to communicate without hardcoded addresses. See full implementation in [code/services/discovery.ts](../code/services/discovery.ts).

## Network Topology

DiNet creates an overlay network for communication between services:

```yaml
# Reference to code/deployment/network_config.yml
# This example shows network configuration
```

This overlay network spans all nodes in the swarm. See full implementation in [code/deployment/network_config.yml](../code/deployment/network_config.yml).

## Service Types

DiNet deploys several types of services:

### Core Services

These services provide the essential functionality of DiNet:

- **p2p-node**: Implements the p2plib networking layer
- **semantic-engine**: Maintains the semantic graph
- **api-gateway**: Routes API requests to appropriate services

For implementation details, see [code/deployment/core_services.yml](../code/deployment/core_services.yml).

### Database Services

These services handle data persistence:

- **graph-db**: Stores the semantic graph
- **cache-db**: Provides caching for frequently accessed data
- **sync-db**: Manages synchronization state

For implementation details, see [code/deployment/database_services.yml](../code/deployment/database_services.yml).

### Domain-Specific Services

These services provide domain-specific functionality:

- **task-manager**: Enhances task management
- **knowledge-base**: Implements the knowledge graph
- **workflow-engine**: Provides workflow automation

For implementation details, see [code/deployment/domain_services.yml](../code/deployment/domain_services.yml).

## Local API Implementation

The local API provides a unified interface to delocalized services:

```typescript
// Reference to code/api/local_api.ts
// This example shows the local API implementation
```

This API allows applications to interact with services as if they were local. See full implementation in [code/api/local_api.ts](../code/api/local_api.ts).

## Deployment Process

DiNet deployment involves several steps:

```bash
# Reference to code/deployment/deploy.sh
# This example shows the deployment process
```

This script automates the deployment of DiNet services. See full implementation in [code/deployment/deploy.sh](../code/deployment/deploy.sh).

## Monitoring and Management

DiNet includes tools for monitoring and managing services:

```yaml
# Reference to code/deployment/monitoring.yml
# This example shows monitoring configuration
```

These tools provide visibility into service health and performance. See full implementation in [code/deployment/monitoring.yml](../code/deployment/monitoring.yml).

## Related Components

Docker services work closely with:

- **p2plib Integration**: For distributed networking ([p2plib_integration.md](p2plib_integration.md))
- **Nextcloud Integration**: For application connectivity ([nextcloud_integration.md](nextcloud_integration.md))
- **Treenity Trees**: For domain-specific views ([../implementation/graph_projection.md](../implementation/graph_projection.md))

## Example Configurations

### Single-Node Deployment

```yaml
# Reference to code/examples/single_node.yml
# This example shows a single-node deployment
```

Full example configuration can be found in [code/examples/single_node.yml](../code/examples/single_node.yml).

### Multi-Node Deployment

```yaml
# Reference to code/examples/multi_node.yml
# This example shows a multi-node deployment
```

Complete example is available in [code/examples/multi_node.yml](../code/examples/multi_node.yml).
