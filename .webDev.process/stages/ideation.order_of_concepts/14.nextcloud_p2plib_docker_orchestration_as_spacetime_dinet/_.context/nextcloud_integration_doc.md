# Nextcloud Integration

This document describes how DiNet integrates with Nextcloud to leverage its mature application layer while extending it with distributed infrastructure capabilities.

## Conceptual Overview

Nextcloud provides a robust application layer with file management, task tracking, calendaring, and other collaboration features. DiNet extends these capabilities by integrating p2plib's addressing scheme and distributed data layer, allowing Nextcloud instances to operate as nodes in a larger distributed network.

The integration is designed to be minimally invasive, using existing Nextcloud database adapters and extension points rather than requiring significant modifications to Nextcloud itself.

## Key Components

### Database Adapter

DiNet connects to Nextcloud's database through standard adapters:

```typescript
// Reference to code/nextcloud/db_adapter.ts
// This example shows database connection with Nextcloud
```

The database adapter translates between Nextcloud's data model and DiNet's semantic graph. See full implementation in [code/nextcloud/db_adapter.ts](../code/nextcloud/db_adapter.ts).

### External Storage Backend

DiNet implements a custom storage backend for Nextcloud:

```php
// Reference to code/nextcloud/storage_backend.php
// This example shows the external storage implementation
```

This backend allows Nextcloud to access content through p2plib's addressing scheme. See full implementation in [code/nextcloud/storage_backend.php](../code/nextcloud/storage_backend.php).

### Application Integration

DiNet extends Nextcloud's applications through its plugin architecture:

```php
// Reference to code/nextcloud/app_integration.php
// This example shows integration with Nextcloud apps
```

The application integration adds DiNet functionality to Nextcloud's existing apps. See full implementation in [code/nextcloud/app_integration.php](../code/nextcloud/app_integration.php).

## Integration Points

### Tasks Integration

DiNet enhances Nextcloud's task management:

```typescript
// Reference to code/nextcloud/tasks_integration.ts
// This example shows task enhancement
```

Tasks are enriched with semantic relationships and synchronized across the network. See full implementation in [code/nextcloud/tasks_integration.ts](../code/nextcloud/tasks_integration.ts).

### Files Integration

DiNet extends Nextcloud's file management:

```typescript
// Reference to code/nextcloud/files_integration.ts
// This example shows file system integration
```

Files are accessed through content-addressed storage, with semantic metadata. See full implementation in [code/nextcloud/files_integration.ts](../code/nextcloud/files_integration.ts).

### Calendar Integration

DiNet enhances Nextcloud's calendar:

```typescript
// Reference to code/nextcloud/calendar_integration.ts
// This example shows calendar enhancement
```

Calendar events are enriched with semantic relationships and synchronized across the network. See full implementation in [code/nextcloud/calendar_integration.ts](../code/nextcloud/calendar_integration.ts).

## Data Flow

### Local to Distributed

When data is created or modified in Nextcloud:

1. **Database Hook**: Intercepts database operations
2. **Semantic Transformation**: Converts to semantic representation
3. **p2plib Publication**: Publishes changes to the network

For implementation details, see [code/nextcloud/outbound_sync.ts](../code/nextcloud/outbound_sync.ts).

### Distributed to Local

When data is received from the distributed network:

1. **p2plib Subscription**: Receives notifications of changes
2. **Semantic Resolution**: Resolves semantic references
3. **Database Update**: Updates Nextcloud's database

For implementation details, see [code/nextcloud/inbound_sync.ts](../code/nextcloud/inbound_sync.ts).

## User Interface Extensions

DiNet adds UI components to Nextcloud:

```typescript
// Reference to code/nextcloud/ui_extensions.ts
// This example shows UI integration
```

These components provide user interfaces for DiNet-specific features. See full implementation in [code/nextcloud/ui_extensions.ts](../code/nextcloud/ui_extensions.ts).

## Authentication and Authorization

DiNet integrates with Nextcloud's authentication:

```php
// Reference to code/nextcloud/auth_integration.php
// This example shows authentication integration
```

This ensures that DiNet respects Nextcloud's user permissions. See full implementation in [code/nextcloud/auth_integration.php](../code/nextcloud/auth_integration.php).

## Deployment

DiNet's Nextcloud integration is deployed as a Nextcloud app:

```xml
<!-- Reference to code/nextcloud/appinfo/info.xml -->
<!-- This example shows the app configuration -->
```

For full deployment configuration, see [code/nextcloud/appinfo/info.xml](../code/nextcloud/appinfo/info.xml).

## Related Components

Nextcloud integration works closely with:

- **p2plib Integration**: For distributed data access ([p2plib_integration.md](p2plib_integration.md))
- **Docker Services**: For service deployment ([docker_services.md](docker_services.md))
- **Treenity Trees**: For creating domain-specific views ([../implementation/graph_projection.md](../implementation/graph_projection.md))

## Example Applications

### Enhanced Task Management

```typescript
// Reference to code/examples/enhanced_tasks.ts
// This example demonstrates enhanced task management
```

Full example code can be found in [code/examples/enhanced_tasks.ts](../code/examples/enhanced_tasks.ts).

### Distributed Knowledge Base

```typescript
// Reference to code/examples/knowledge_base.ts
// This example demonstrates a distributed knowledge base
```

Complete example is available in [code/examples/knowledge_base.ts](../code/examples/knowledge_base.ts).
