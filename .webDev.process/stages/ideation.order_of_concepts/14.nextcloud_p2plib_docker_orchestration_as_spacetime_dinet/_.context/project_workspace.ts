// workspace.ts
//
// Main implementation of the project workspace
// See docs/examples/project_workspace.md for conceptual overview

import { 
  SemanticGraph, 
  DomainProjection,
  DomainView,
  Node
} from '../../core/semantic/types';
import { NextcloudIntegration } from '../../nextcloud/integration';
import { LocalApiGateway } from '../../api/gateway';
import { Logger } from '../../util/logger';
import { Observable, Observer } from '../../core/reactive';
import { ProjectDashboard } from './types';
import { createProjectDomain, createAssigneeProjection, createTimelineProjection } from './projections';
import { syncTasks, syncFiles, syncCalendar, syncTeam } from './sync';
import { createRelationships } from './relationships';
import { generateDashboard } from './dashboard';

/**
 * Unified project workspace that integrates multiple Nextcloud components
 */
export class ProjectWorkspace {
  private projectId: string;
  private semanticGraph: SemanticGraph;
  private nextcloudIntegration: NextcloudIntegration;
  private apiGateway: LocalApiGateway;
  private logger: Logger;
  private projectDomain: DomainView;
  private dashboardObservable: Observable<ProjectDashboard>;
  private observers: Observer[] = [];
  
  /**
   * Creates a new project workspace
   */
  constructor(
    projectId: string,
    semanticGraph: SemanticGraph,
    nextcloudIntegration: NextcloudIntegration,
    apiGateway: LocalApiGateway,
    logger: Logger
  ) {
    this.projectId = projectId;
    this.semanticGraph = semanticGraph;
    this.nextcloudIntegration = nextcloudIntegration;
    this.apiGateway = apiGateway;
    this.logger = logger;
    
    // Initialize the domain view
    this.initializeDomain();
    
    // Set up the dashboard observable
    this.dashboardObservable = new Observable<ProjectDashboard>(
      generateDashboard(this.projectId, this.semanticGraph)
    );
  }
  
  /**
   * Initializes the project domain view
   */
  private initializeDomain(): void {
    // Create the domain projection
    const projection = createProjectDomain(this.projectId);
    
    // Create the domain view
    this.projectDomain = new DomainView(
      `project-${this.projectId}`,
      `Project Workspace`,
      projection,
      this.semanticGraph
    );
    
    // Add context projections
    this.projectDomain.addContextProjection(
      createAssigneeProjection()
    );
    this.projectDomain.addContextProjection(
      createTimelineProjection()
    );
    
    // Set up change listeners
    this.setupChangeListeners();
  }
  
  /**
   * Sets up listeners for changes to project components
   */
  private setupChangeListeners(): void {
    // Listen for changes to the project domain
    this.projectDomain.onChanged(() => {
      // Update the dashboard
      const dashboard = generateDashboard(this.projectId, this.semanticGraph);
      this.dashboardObservable.setValue(dashboard);
    });
    
    // Listen for changes to Nextcloud components
    this.nextcloudIntegration.onTasksChanged(() => {
      syncTasks(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
    });
    
    this.nextcloudIntegration.onFilesChanged(() => {
      syncFiles(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
    });
    
    this.nextcloudIntegration.onEventsChanged(() => {
      syncCalendar(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
    });
  }
  
  /**
   * Initializes the project workspace
   */
  public async initialize(): Promise<void> {
    try {
      // Sync data from Nextcloud
      await this.syncNextcloudData();
      
      // Create cross-component relationships
      await createRelationships(this.projectId, this.semanticGraph, this.logger);
      
      this.logger.info(`Project workspace initialized for project ${this.projectId}`);
    } catch (error) {
      this.logger.error(`Failed to initialize project workspace: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Synchronizes data from Nextcloud
   */
  private async syncNextcloudData(): Promise<void> {
    try {
      // Sync tasks
      await syncTasks(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      // Sync files
      await syncFiles(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      // Sync calendar
      await syncCalendar(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      // Sync team members
      await syncTeam(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      this.logger.info(`Synchronized Nextcloud data for project ${this.projectId}`);
    } catch (error) {
      this.logger.error(`Failed to sync Nextcloud data: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Gets the dashboard observable
   */
  public getDashboard(): Observable<ProjectDashboard> {
    return this.dashboardObservable;
  }
  
  /**
   * Creates a new task in the project
   */
  public async createTask(taskData: any): Promise<Node> {
    try {
      // Create the task in Nextcloud
      const task = await this.nextcloudIntegration.createTask({
        ...taskData,
        projectId: this.projectId
      });
      
      // Sync the task to the semantic graph
      await syncTasks(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      // Return the created task node
      const taskNode = this.semanticGraph.getNode(`task:${task.id}`);
      return taskNode;
    } catch (error) {
      this.logger.error(`Failed to create task: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Updates a task in the project
   */
  public async updateTask(taskId: string, taskData: any): Promise<Node> {
    try {
      // Get the Nextcloud ID
      const taskNode = this.semanticGraph.getNode(taskId);
      if (!taskNode) {
        throw new Error(`Task ${taskId} not found`);
      }
      
      const nextcloudId = taskNode.data.externalIds?.nextcloud;
      if (!nextcloudId) {
        throw new Error(`Task ${taskId} has no Nextcloud ID`);
      }
      
      // Update the task in Nextcloud
      await this.nextcloudIntegration.updateTask(nextcloudId, taskData);
      
      // Sync the task to the semantic graph
      await syncTasks(this.projectId, this.semanticGraph, this.nextcloudIntegration, this.logger);
      
      // Return the updated task node
      return this.semanticGraph.getNode(taskId);
    } catch (error) {
      this.logger.error(`Failed to update task ${taskId}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Collaborates with another node on this project
   */
  public async collaborateWithNode(nodeId: string): Promise<void> {
    try {
      // Get the node's API gateway
      const nodeGateway = await this.apiGateway.getRemoteGateway(nodeId);
      
      // Share the project with the remote node
      await nodeGateway.shareProject(this.projectId, {
        sender: await this.apiGateway.getNodeId(),
        permissions: 'read-write'
      });
      
      this.logger.info(`Shared project ${this.projectId} with node ${nodeId}`);
    } catch (error) {
      this.logger.error(`Failed to collaborate with node ${nodeId}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Cleans up resources when the workspace is no longer needed
   */
  public async cleanup(): Promise<void> {
    try {
      // Detach all observers
      for (const observer of this.observers) {
        observer.detach();
      }
      
      this.logger.info(`Cleaned up project workspace for project ${this.projectId}`);
    } catch (error) {
      this.logger.error(`Failed to clean up project workspace: ${error.message}`);
      throw error;
    }
  }
}
