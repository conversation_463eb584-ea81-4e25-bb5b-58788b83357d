# Time and What It Does

## Context

This concept explores the fundamental structure of time in our repository architecture. It defines how temporal sequences are organized, how archiving works, and how different nodes in the repository structure relate to time.

Key aspects:
- Repository spacetime as an organizing principle
- Temporal scopes and their relationship to filesystem structure
- Archiving mechanism through tics
- How nodes in the repository structure relate to time
- The relationship between sequential and structural nodes

This concept serves as the foundation for our temporal architecture, which is independent of git's branching structure but can be mapped to it in certain ways.
