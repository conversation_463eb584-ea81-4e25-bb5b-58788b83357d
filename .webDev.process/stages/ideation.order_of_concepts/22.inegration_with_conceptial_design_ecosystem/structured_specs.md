# Structured Specification Formats

This document explores the structured specification formats that can be integrated with the SpiceTime architecture, focusing on how they can be adapted to work with our pragmatic syntax and categorical approach.

## Why Structured Formats?

Structured formats like YAML and JSON offer several advantages:

1. **Clear Structure**: Well-defined schemas reduce ambiguity
2. **Tooling Support**: Extensive ecosystem of parsers, validators, and editors
3. **Interoperability**: Easy integration with existing systems
4. **Validation**: Automated validation against schemas
5. **Transformation**: Simple to transform between different representations

## Key Specification Formats

### 1. OpenAPI/Swagger

OpenAPI is a specification format for RESTful APIs that describes:

- Endpoints and operations
- Request and response formats
- Authentication methods
- Data models

**Example:**
```yaml
openapi: 3.0.0
info:
  title: User API
  version: 1.0.0
paths:
  /users:
    get:
      summary: Find users
      parameters:
        - name: status
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        status:
          type: string
```

**Mapping to Pragmatic Syntax:**
- API endpoints → File structure
- Operations → Pragmas
- Parameters → Function parameters
- Responses → Return types

### 2. JSON Schema

JSON Schema defines the structure of JSON data, specifying:

- Property types and formats
- Required vs. optional properties
- Validation rules
- Nested structures

**Example:**
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "User",
  "type": "object",
  "properties": {
    "id": {
      "type": "integer"
    },
    "name": {
      "type": "string"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive", "pending"]
    },
    "createdAt": {
      "type": "string",
      "format": "date-time"
    }
  },
  "required": ["id", "name", "status"]
}
```

**Mapping to Pragmatic Syntax:**
- Schemas → Type definitions
- Properties → Object properties
- Validation rules → Runtime validators
- Required fields → Non-nullable types

### 3. Gherkin-JSON

While Gherkin is typically text-based, it can be represented in JSON format for structured processing:

**Example:**
```json
{
  "feature": "User Management",
  "scenarios": [
    {
      "name": "Find active users",
      "given": [
        "there are users with different statuses"
      ],
      "when": [
        "I search for users with status \"active\""
      ],
      "then": [
        "I should see only active users in the results"
      ]
    }
  ]
}
```

**Mapping to Pragmatic Syntax:**
- Features → Modules
- Scenarios → Test files
- Given/When/Then → Test setup/execution/assertions
- Parameters → Test inputs

### 4. YAML Test Specifications

A simplified test specification format in YAML:

**Example:**
```yaml
test:
  name: "Find active users"
  given:
    - users:
        - id: 1
          name: "Alice"
          status: "active"
        - id: 2
          name: "Bob"
          status: "inactive"
  when:
    action: "findUsers"
    parameters:
      status: "active"
  then:
    - assert: "length"
      value: 1
    - assert: "property"
      path: "[0].name"
      value: "Alice"
```

**Mapping to Pragmatic Syntax:**
- Test specifications → Test files
- Given → Setup code
- When → Action code
- Then → Assertion code

## Categorical Functors for Translation

To integrate these formats with our categorical approach, we can define functors:

### 1. Specification to Category Functor

Maps specification elements to categorical objects:

```typescript
class SpecToCategoryFunctor implements Functor<Specification, Category> {
  map(spec: Specification): Category {
    // Convert specification to categorical objects
    const objects = this.mapObjects(spec);
    const morphisms = this.mapMorphisms(spec);
    
    return new Category(objects, morphisms);
  }
  
  private mapObjects(spec: Specification): Object[] {
    // Map specification elements to objects
  }
  
  private mapMorphisms(spec: Specification): Morphism[] {
    // Map specification relationships to morphisms
  }
}
```

### 2. Category to Implementation Functor

Maps categorical objects to implementation code:

```typescript
class CategoryToImplFunctor implements Functor<Category, Implementation> {
  map(category: Category): Implementation {
    // Convert categorical objects to implementation
    const files = this.mapFiles(category);
    const code = this.mapCode(category);
    
    return new Implementation(files, code);
  }
  
  private mapFiles(category: Category): File[] {
    // Map categorical objects to files
  }
  
  private mapCode(category: Category): Code[] {
    // Map categorical morphisms to code
  }
}
```

### 3. Category to Test Functor

Maps categorical objects to test code:

```typescript
class CategoryToTestFunctor implements Functor<Category, Test> {
  map(category: Category): Test {
    // Convert categorical objects to tests
    const testFiles = this.mapTestFiles(category);
    const testCode = this.mapTestCode(category);
    
    return new Test(testFiles, testCode);
  }
  
  private mapTestFiles(category: Category): TestFile[] {
    // Map categorical objects to test files
  }
  
  private mapTestCode(category: Category): TestCode[] {
    // Map categorical morphisms to test code
  }
}
```

## Adaptability Layer

The adaptability layer would provide:

### 1. Format Parsers

```typescript
interface FormatParser<T> {
  parse(input: string): T;
  format(data: T): string;
}

class YAMLParser<T> implements FormatParser<T> {
  parse(input: string): T {
    // Parse YAML to object
  }
  
  format(data: T): string {
    // Format object to YAML
  }
}

class JSONParser<T> implements FormatParser<T> {
  parse(input: string): T {
    // Parse JSON to object
  }
  
  format(data: T): string {
    // Format object to JSON
  }
}
```

### 2. Schema Validators

```typescript
interface SchemaValidator<T> {
  validate(data: T, schema: Schema): ValidationResult;
}

class JSONSchemaValidator<T> implements SchemaValidator<T> {
  validate(data: T, schema: Schema): ValidationResult {
    // Validate data against JSON Schema
  }
}

class OpenAPIValidator<T> implements SchemaValidator<T> {
  validate(data: T, schema: Schema): ValidationResult {
    // Validate data against OpenAPI schema
  }
}
```

### 3. Code Generators

```typescript
interface CodeGenerator<T> {
  generate(spec: T): GeneratedCode;
}

class PragmaticCodeGenerator<T> implements CodeGenerator<T> {
  generate(spec: T): GeneratedCode {
    // Generate pragmatic syntax from specification
  }
}

class TestCodeGenerator<T> implements CodeGenerator<T> {
  generate(spec: T): GeneratedCode {
    // Generate test code from specification
  }
}
```

## Integration with Linguistics System

The structured specifications can be integrated with our linguistics system:

```typescript
// Define a functor from specifications to linguistic expressions
class SpecToLinguisticFunctor implements Functor<Specification, Expression> {
  map(spec: Specification): Expression {
    // Convert specification to linguistic expression
    
    // For API specifications
    if (spec.type === 'api') {
      return this.mapAPISpec(spec);
    }
    
    // For data model specifications
    if (spec.type === 'model') {
      return this.mapModelSpec(spec);
    }
    
    // For test specifications
    if (spec.type === 'test') {
      return this.mapTestSpec(spec);
    }
    
    throw new Error(`Unknown specification type: ${spec.type}`);
  }
  
  private mapAPISpec(spec: APISpecification): Expression {
    // Map API specification to linguistic expression
  }
  
  private mapModelSpec(spec: ModelSpecification): Expression {
    // Map data model specification to linguistic expression
  }
  
  private mapTestSpec(spec: TestSpecification): Expression {
    // Map test specification to linguistic expression
  }
}

// Use the functor with the l tag
function specL(spec: Specification): any {
  // Convert specification to linguistic expression
  const expression = specToLinguisticFunctor.map(spec);
  
  // Evaluate the expression
  return l`${expression}`;
}
```

## Example Workflow

1. **Define Specification**:
   ```yaml
   # api.yaml
   paths:
     /users:
       get:
         summary: Find users
         parameters:
           - name: status
             in: query
             schema:
               type: string
   ```

2. **Parse Specification**:
   ```typescript
   const parser = new YAMLParser<APISpecification>();
   const spec = parser.parse(fs.readFileSync('api.yaml', 'utf8'));
   ```

3. **Validate Specification**:
   ```typescript
   const validator = new OpenAPIValidator();
   const result = validator.validate(spec, openAPISchema);
   
   if (!result.valid) {
     console.error('Invalid specification:', result.errors);
     return;
   }
   ```

4. **Generate Pragmatic Code**:
   ```typescript
   const generator = new PragmaticCodeGenerator();
   const code = generator.generate(spec);
   
   // Write files
   for (const file of code.files) {
     fs.writeFileSync(file.path, file.content);
   }
   ```

5. **Generate Tests**:
   ```typescript
   const testGenerator = new TestCodeGenerator();
   const tests = testGenerator.generate(spec);
   
   // Write test files
   for (const file of tests.files) {
     fs.writeFileSync(file.path, file.content);
   }
   ```

## Conclusion

By adapting established structured specification formats like YAML and JSON, we can create a powerful integration between our pragmatic syntax and existing conceptual design ecosystems. This approach provides clear structure, leverages existing tools, and enables bidirectional translation through categorical functors.
