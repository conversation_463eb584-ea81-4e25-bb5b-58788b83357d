# Integration Examples

This document provides concrete examples of how the SpiceTime architecture can integrate with existing conceptual design ecosystems through structured specification formats.

## Example 1: API Specification to Implementation

### OpenAPI Specification

```yaml
openapi: 3.0.0
info:
  title: User API
  version: 1.0.0
paths:
  /users:
    get:
      summary: Find users
      parameters:
        - name: status
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        status:
          type: string
          enum: [active, inactive, pending]
      required: [id, name, status]
```

### Generated Directory Structure

```
users/
├── _types.ts                 # Generated types
├── query.by_status.ts        # API implementation
└── query.by_status.test.ts   # Generated tests
```

### Generated Type Definitions

```typescript
// users/_types.ts
export interface User {
  id: number;
  name: string;
  status: 'active' | 'inactive' | 'pending';
}

export const validateUser = (data: any): data is User => {
  if (typeof data !== 'object' || data === null) return false;
  if (typeof data.id !== 'number') return false;
  if (typeof data.name !== 'string') return false;
  if (!['active', 'inactive', 'pending'].includes(data.status)) return false;
  return true;
};

export const validateUsers = (data: any): data is User[] => {
  if (!Array.isArray(data)) return false;
  return data.every(validateUser);
};
```

### Generated API Implementation

```typescript
// users/query.by_status.ts
import { User, validateUsers } from './_types';

export default async (context) => {
  const { status } = context.params;
  
  try {
    // Implementation using the available API
    const users = await context.db.users.find({ 
      status: status || undefined 
    });
    
    // Validate response
    if (!validateUsers(users)) {
      throw new Error('Invalid response format');
    }
    
    return users;
  } catch (error) {
    context.logger.error('Error finding users', error);
    throw error;
  }
};
```

### Generated Tests

```typescript
// users/query.by_status.test.ts
import { test, expect } from '@spicetime/test';
import handler from './query.by_status';
import { mockContext } from '../test/helpers';

test('Find users by status', async () => {
  // Test with status parameter
  const activeContext = mockContext({
    params: { status: 'active' }
  });
  
  const activeResult = await handler(activeContext);
  
  expect(Array.isArray(activeResult)).toBe(true);
  activeResult.forEach(user => {
    expect(user.status).toBe('active');
  });
  
  // Test without status parameter
  const allContext = mockContext({
    params: {}
  });
  
  const allResult = await handler(allContext);
  
  expect(Array.isArray(allResult)).toBe(true);
});

test('Error handling', async () => {
  const errorContext = mockContext({
    params: { status: 'active' },
    db: {
      users: {
        find: () => Promise.reject(new Error('Database error'))
      }
    }
  });
  
  await expect(handler(errorContext)).rejects.toThrow('Database error');
  expect(errorContext.logger.error).toHaveBeenCalled();
});
```

## Example 2: Data Model Specification to Types

### JSON Schema Specification

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Product",
  "type": "object",
  "properties": {
    "id": {
      "type": "string",
      "format": "uuid"
    },
    "name": {
      "type": "string",
      "minLength": 1,
      "maxLength": 100
    },
    "price": {
      "type": "number",
      "minimum": 0
    },
    "category": {
      "type": "string",
      "enum": ["electronics", "clothing", "food", "books"]
    },
    "tags": {
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "metadata": {
      "type": "object",
      "additionalProperties": true
    }
  },
  "required": ["id", "name", "price", "category"]
}
```

### Generated Type Definition

```typescript
// types/product.type.ts
export interface Product {
  id: string;
  name: string;
  price: number;
  category: 'electronics' | 'clothing' | 'food' | 'books';
  tags?: string[];
  metadata?: Record<string, any>;
}

export const validateProduct = (data: any): data is Product => {
  if (typeof data !== 'object' || data === null) return false;
  
  // Validate required fields
  if (typeof data.id !== 'string' || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(data.id)) {
    return false;
  }
  
  if (typeof data.name !== 'string' || data.name.length < 1 || data.name.length > 100) {
    return false;
  }
  
  if (typeof data.price !== 'number' || data.price < 0) {
    return false;
  }
  
  if (!['electronics', 'clothing', 'food', 'books'].includes(data.category)) {
    return false;
  }
  
  // Validate optional fields
  if (data.tags !== undefined) {
    if (!Array.isArray(data.tags) || !data.tags.every(tag => typeof tag === 'string')) {
      return false;
    }
  }
  
  if (data.metadata !== undefined) {
    if (typeof data.metadata !== 'object' || data.metadata === null) {
      return false;
    }
  }
  
  return true;
};

export const createProduct = (data: Omit<Product, 'id'>): Product => {
  return {
    id: crypto.randomUUID(),
    ...data
  };
};
```

### Generated Factory Function

```typescript
// factories/product.factory.ts
import { Product } from '../types/product.type';

export const createProductFactory = () => {
  return {
    create: (data: Partial<Omit<Product, 'id'>> = {}): Product => {
      return {
        id: crypto.randomUUID(),
        name: data.name || 'Default Product',
        price: data.price !== undefined ? data.price : 9.99,
        category: data.category || 'electronics',
        tags: data.tags || [],
        metadata: data.metadata || {}
      };
    },
    
    createMany: (count: number, template: Partial<Omit<Product, 'id'>> = {}): Product[] => {
      return Array.from({ length: count }, () => this.create(template));
    }
  };
};
```

## Example 3: Test Specification to Test Implementation

### YAML Test Specification

```yaml
test:
  name: "Create product"
  given:
    - products: []
  when:
    action: "createProduct"
    parameters:
      name: "Test Product"
      price: 19.99
      category: "electronics"
      tags:
        - "new"
        - "featured"
  then:
    - assert: "success"
    - assert: "property"
      path: "name"
      value: "Test Product"
    - assert: "property"
      path: "price"
      value: 19.99
    - assert: "property"
      path: "category"
      value: "electronics"
    - assert: "property"
      path: "tags"
      contains:
        - "new"
        - "featured"
```

### Generated Test Implementation

```typescript
// products/create_product.test.ts
import { test, expect } from '@spicetime/test';
import { createProduct } from './create_product';
import { Product } from '../types/product.type';
import { setupDatabase } from '../test/helpers';

test('Create product', async () => {
  // Given
  const { db, cleanup } = await setupDatabase({
    products: []
  });
  
  try {
    // When
    const result = await createProduct({
      name: "Test Product",
      price: 19.99,
      category: "electronics",
      tags: ["new", "featured"]
    });
    
    // Then
    expect(result).toBeTruthy();
    expect(result.name).toBe("Test Product");
    expect(result.price).toBe(19.99);
    expect(result.category).toBe("electronics");
    expect(result.tags).toContain("new");
    expect(result.tags).toContain("featured");
    
    // Verify database state
    const savedProduct = await db.products.findById(result.id);
    expect(savedProduct).toEqual(result);
  } finally {
    await cleanup();
  }
});
```

## Example 4: Gherkin to Test Implementation

### Gherkin Specification

```gherkin
Feature: Product Management
  Scenario: Filter products by category
    Given there are products in different categories
      | name          | price | category    |
      | Laptop        | 999   | electronics |
      | T-shirt       | 19.99 | clothing    |
      | Apple         | 1.99  | food        |
      | Programming   | 39.99 | books       |
    When I filter products by category "electronics"
    Then I should see 1 product
    And the product name should be "Laptop"
```

### Generated Test Implementation

```typescript
// products/filter_by_category.test.ts
import { test, expect } from '@spicetime/test';
import { filterProducts } from './filter_products';
import { Product } from '../types/product.type';
import { createProductFactory } from '../factories/product.factory';

test('Filter products by category', async () => {
  // Given
  const productFactory = createProductFactory();
  const products: Product[] = [
    productFactory.create({ name: 'Laptop', price: 999, category: 'electronics' }),
    productFactory.create({ name: 'T-shirt', price: 19.99, category: 'clothing' }),
    productFactory.create({ name: 'Apple', price: 1.99, category: 'food' }),
    productFactory.create({ name: 'Programming', price: 39.99, category: 'books' })
  ];
  
  // Mock database with products
  const mockDb = {
    products: {
      find: jest.fn().mockImplementation((query) => {
        return Promise.resolve(
          products.filter(product => {
            if (query.category && product.category !== query.category) {
              return false;
            }
            return true;
          })
        );
      })
    }
  };
  
  // When
  const result = await filterProducts({ category: 'electronics' }, { db: mockDb });
  
  // Then
  expect(result.length).toBe(1);
  expect(result[0].name).toBe('Laptop');
});
```

## Example 5: Combining Multiple Specifications

### API Specification (OpenAPI)

```yaml
paths:
  /products:
    get:
      summary: Filter products
      parameters:
        - name: category
          in: query
          schema:
            type: string
      responses:
        '200':
          description: List of products
```

### Data Model Specification (JSON Schema)

```json
{
  "title": "Product",
  "type": "object",
  "properties": {
    "id": { "type": "string" },
    "name": { "type": "string" },
    "category": { "type": "string" }
  }
}
```

### Test Specification (Gherkin)

```gherkin
Scenario: Filter products by category
  When I filter products by category "electronics"
  Then I should see only electronics products
```

### Generated Implementation

```typescript
// products/query.by_category.ts
import { Product, validateProducts } from '../types/product.type';

export default async (context) => {
  const { category } = context.params;
  
  try {
    // Implementation using the available API
    const products = await context.db.products.find({ 
      category: category || undefined 
    });
    
    // Validate response
    if (!validateProducts(products)) {
      throw new Error('Invalid response format');
    }
    
    return products;
  } catch (error) {
    context.logger.error('Error filtering products', error);
    throw error;
  }
};
```

### Generated Test

```typescript
// products/query.by_category.test.ts
import { test, expect } from '@spicetime/test';
import handler from './query.by_category';
import { mockContext } from '../test/helpers';
import { createProductFactory } from '../factories/product.factory';

test('Filter products by category', async () => {
  // Create test products
  const productFactory = createProductFactory();
  const products = [
    productFactory.create({ category: 'electronics' }),
    productFactory.create({ category: 'clothing' }),
    productFactory.create({ category: 'electronics' })
  ];
  
  // Create mock context
  const context = mockContext({
    params: { category: 'electronics' },
    db: {
      products: {
        find: jest.fn().mockImplementation((query) => {
          return Promise.resolve(
            products.filter(product => product.category === query.category)
          );
        })
      }
    }
  });
  
  // Execute handler
  const result = await handler(context);
  
  // Verify results
  expect(Array.isArray(result)).toBe(true);
  expect(result.length).toBe(2);
  result.forEach(product => {
    expect(product.category).toBe('electronics');
  });
});
```

## Example 6: Using the Categorical Approach

### Functor Application

```typescript
// Apply the OpenAPI to Pragmatic functor
const apiSpec = parseOpenAPI(openApiYaml);
const pragmaticFiles = openAPIToPragmaticFunctor.mapObject(apiSpec);

// Apply the JSON Schema to Type functor
const schemaSpec = parseJSONSchema(jsonSchemaJson);
const typeFiles = jsonSchemaToPragmaticFunctor.mapObject(schemaSpec);

// Apply the Gherkin to Test functor
const gherkinSpec = parseGherkin(gherkinText);
const testFiles = gherkinToPragmaticFunctor.mapObject(gherkinSpec);

// Write all generated files
for (const file of [...pragmaticFiles, ...typeFiles, ...testFiles]) {
  writeFile(file.path, file.content);
}
```

### Natural Transformation Application

```typescript
// Create a natural transformation from API to Test
const apiToTestTransformation = new APIToTestTransformation();

// Apply the transformation to generate tests from API specs
const apiSpec = parseOpenAPI(openApiYaml);
const pragmaticFiles = openAPIToPragmaticFunctor.mapObject(apiSpec);

for (const file of pragmaticFiles) {
  // Apply the natural transformation to each file
  const testFile = apiToTestTransformation.component(file.path)(file);
  
  // Write the test file
  writeFile(testFile.path, testFile.content);
}
```

### Composition of Functors

```typescript
// Compose functors to create a direct mapping from OpenAPI to Tests
const openAPIToTestFunctor = composeFunctors(
  openAPIToPragmaticFunctor,
  pragmaticToTestFunctor
);

// Apply the composed functor
const apiSpec = parseOpenAPI(openApiYaml);
const testFiles = openAPIToTestFunctor.mapObject(apiSpec);

// Write test files
for (const file of testFiles) {
  writeFile(file.path, file.content);
}
```

## Example 7: Integration with Linguistics System

### Using the `specL` Tag

```typescript
import { specL } from '@spicetime/linguistics';

// Parse OpenAPI specification
const apiSpec = parseOpenAPI(openApiYaml);

// Use the specL tag to generate linguistic expressions
const apiExpression = specL(apiSpec);

// Use the expression in code
const result = apiExpression.findUsers({ status: 'active' });
```

### Linguistic Patterns for Specifications

```typescript
// Define linguistic patterns for OpenAPI
definePattern(TermType.VERB, 'endpoint', (path, method) => {
  return `API endpoint ${method.toUpperCase()} ${path}`;
});

definePattern(TermType.NOUN, 'parameter', (name, location) => {
  return `${location} parameter ${name}`;
});

definePattern(TermType.ADJECTIVE, 'required', (target) => {
  return `required ${target}`;
});

// Use the patterns
const description = l`endpoint /users GET with parameter status in query`;
```

## Conclusion

These examples demonstrate how the SpiceTime architecture can integrate with existing conceptual design ecosystems through structured specification formats. By leveraging our categorical approach, we can create powerful translations between specifications and implementations while maintaining the mathematical rigor of our system.

The examples show:

1. **API Specifications**: Generating API implementations and tests from OpenAPI
2. **Data Models**: Creating type definitions and validators from JSON Schema
3. **Test Specifications**: Implementing tests from YAML and Gherkin specifications
4. **Combined Approaches**: Using multiple specification formats together
5. **Categorical Approach**: Applying functors and natural transformations
6. **Linguistics Integration**: Using specifications with our linguistics system

This integration enables developers to work with familiar specification formats while leveraging the power of our pragmatic syntax and categorical foundation.
