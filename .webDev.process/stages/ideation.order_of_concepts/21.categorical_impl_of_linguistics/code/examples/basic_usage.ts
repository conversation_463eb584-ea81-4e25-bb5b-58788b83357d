/**
 * Categorical implementation of linguistics - Basic Usage Examples
 * 
 * This file demonstrates basic usage of the linguistics system.
 */

import {
  l, defineTerm, TermType, usePrivateContext, usePublicContext,
  getCurrentContext, createContext
} from '../index';

// Initialize with some basic vocabulary
function initializeExamples() {
  console.log('Initializing examples...');
  
  // Define some basic terms in the public context
  usePublicContext();
  
  // Define verbs
  defineTerm(TermType.VERB, 'find', (target: any) => {
    console.log(`[Public] Finding ${target}`);
    return `Found ${target}`;
  });
  
  defineTerm(TermType.VERB, 'create', (target: any) => {
    console.log(`[Public] Creating ${target}`);
    return `Created ${target}`;
  });
  
  defineTerm(TermType.VERB, 'update', (target: any) => {
    console.log(`[Public] Updating ${target}`);
    return `Updated ${target}`;
  });
  
  defineTerm(TermType.VERB, 'delete', (target: any) => {
    console.log(`[Public] Deleting ${target}`);
    return `Deleted ${target}`;
  });
  
  // Define nouns
  defineTerm(TermType.NOUN, 'users', 'users');
  defineTerm(TermType.NOUN, 'products', 'products');
  defineTerm(TermType.NOUN, 'orders', 'orders');
  
  // Define adjectives
  defineTerm(TermType.ADJECTIVE, 'active', (target: any) => {
    return `${target} that are active`;
  });
  
  defineTerm(TermType.ADJECTIVE, 'new', (target: any) => {
    return `${target} that are new`;
  });
  
  // Define the same terms in the private context with different implementations
  usePrivateContext();
  
  // Define verbs
  defineTerm(TermType.VERB, 'find', (target: any) => {
    console.log(`[Private] Finding ${target} with advanced query`);
    return `Found ${target} with advanced query`;
  });
  
  defineTerm(TermType.VERB, 'create', (target: any) => {
    console.log(`[Private] Creating ${target} with validation`);
    return `Created ${target} with validation`;
  });
  
  defineTerm(TermType.VERB, 'update', (target: any) => {
    console.log(`[Private] Updating ${target} with optimistic locking`);
    return `Updated ${target} with optimistic locking`;
  });
  
  defineTerm(TermType.VERB, 'delete', (target: any) => {
    console.log(`[Private] Deleting ${target} with soft delete`);
    return `Deleted ${target} with soft delete`;
  });
  
  // Define the same nouns
  defineTerm(TermType.NOUN, 'users', 'users');
  defineTerm(TermType.NOUN, 'products', 'products');
  defineTerm(TermType.NOUN, 'orders', 'orders');
  
  // Define the same adjectives
  defineTerm(TermType.ADJECTIVE, 'active', (target: any) => {
    return `${target} that are active`;
  });
  
  defineTerm(TermType.ADJECTIVE, 'new', (target: any) => {
    return `${target} that are new`;
  });
  
  // Switch back to public context for examples
  usePublicContext();
}

// Run basic examples
function runBasicExamples() {
  console.log('\nRunning basic examples...');
  
  // Basic usage
  console.log('\n1. Basic usage:');
  const result1 = l`find users`;
  console.log('Result:', result1);
  
  // With adjectives
  console.log('\n2. With adjectives:');
  const result2 = l`find active users`;
  console.log('Result:', result2);
  
  // With multiple adjectives
  console.log('\n3. With multiple adjectives:');
  const result3 = l`find active new users`;
  console.log('Result:', result3);
  
  // Different verbs
  console.log('\n4. Different verbs:');
  console.log('Create:', l`create users`);
  console.log('Update:', l`update users`);
  console.log('Delete:', l`delete users`);
  
  // Different nouns
  console.log('\n5. Different nouns:');
  console.log('Users:', l`find users`);
  console.log('Products:', l`find products`);
  console.log('Orders:', l`find orders`);
}

// Run context switching examples
function runContextExamples() {
  console.log('\nRunning context examples...');
  
  // Public context
  console.log('\n1. Public context:');
  usePublicContext();
  console.log('Find users:', l`find users`);
  
  // Private context
  console.log('\n2. Private context:');
  usePrivateContext();
  console.log('Find users:', l`find users`);
  
  // Custom context
  console.log('\n3. Custom context:');
  const customContext = createContext('custom', 'custom', 'default');
  
  // Define terms in the custom context
  defineTerm(TermType.VERB, 'find', (target: any) => {
    console.log(`[Custom] Finding ${target} with custom implementation`);
    return `Found ${target} with custom implementation`;
  }, customContext);
  
  defineTerm(TermType.NOUN, 'users', 'users', customContext);
  
  // Set the current context to the custom context
  setCurrentContext(customContext);
  
  console.log('Find users:', l`find users`);
  
  // Switch back to public context
  usePublicContext();
}

// Run the examples
function runExamples() {
  initializeExamples();
  runBasicExamples();
  runContextExamples();
}

// Helper function to set the current context
function setCurrentContext(context: any) {
  // This is just for the example - the actual implementation is in template_literal.ts
  (global as any).currentContext = context;
}

// Export the examples
export {
  initializeExamples,
  runBasicExamples,
  runContextExamples,
  runExamples
};

// Run the examples if this file is executed directly
if (require.main === module) {
  runExamples();
}
