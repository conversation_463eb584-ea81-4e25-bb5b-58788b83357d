/**
 * Categorical implementation of linguistics - Advanced Usage Examples
 * 
 * This file demonstrates advanced usage of the linguistics system.
 */

import {
  l, defineTerm, TermType, usePrivateContext, usePublicContext,
  getCurrentContext, createContext,
  termCategory, expressionCategory, meaningCategory, contextCategory,
  syntaxFunctor, semanticFunctor, pragmaticFunctor,
  ContextTransformation, LanguageTransformation, SemanticTransformation
} from '../index';

// Initialize with advanced vocabulary
function initializeAdvancedExamples() {
  console.log('Initializing advanced examples...');
  
  // Define advanced terms in the public context
  usePublicContext();
  
  // Define complex verbs
  defineTerm(TermType.VERB, 'query', (target: any) => {
    console.log(`[Public] Querying ${target} with complex filters`);
    return `Queried ${target} with complex filters`;
  });
  
  defineTerm(TermType.VERB, 'analyze', (target: any) => {
    console.log(`[Public] Analyzing ${target} with statistical methods`);
    return `Analyzed ${target} with statistical methods`;
  });
  
  defineTerm(TermType.VERB, 'transform', (target: any) => {
    console.log(`[Public] Transforming ${target} with data mapping`);
    return `Transformed ${target} with data mapping`;
  });
  
  // Define complex nouns
  defineTerm(TermType.NOUN, 'transactions', 'transactions');
  defineTerm(TermType.NOUN, 'metrics', 'metrics');
  defineTerm(TermType.NOUN, 'configurations', 'configurations');
  
  // Define complex adjectives
  defineTerm(TermType.ADJECTIVE, 'optimized', (target: any) => {
    return `${target} that are optimized for performance`;
  });
  
  defineTerm(TermType.ADJECTIVE, 'validated', (target: any) => {
    return `${target} that are validated against schema`;
  });
  
  // Define prepositions
  defineTerm(TermType.PREPOSITION, 'with', (target: any, modifier: any) => {
    return `${target} with ${modifier}`;
  });
  
  defineTerm(TermType.PREPOSITION, 'by', (target: any, modifier: any) => {
    return `${target} by ${modifier}`;
  });
  
  // Define conjunctions
  defineTerm(TermType.CONJUNCTION, 'and', (left: any, right: any) => {
    return `${left} and ${right}`;
  });
  
  defineTerm(TermType.CONJUNCTION, 'or', (left: any, right: any) => {
    return `${left} or ${right}`;
  });
  
  // Switch back to public context for examples
  usePublicContext();
}

// Run examples with categories
function runCategoryExamples() {
  console.log('\nRunning category examples...');
  
  // Term category
  console.log('\n1. Term category:');
  
  // Create terms
  const findTerm = termCategory.createTerm(TermType.VERB, 'find');
  const usersTerm = termCategory.createTerm(TermType.NOUN, 'users');
  const activeTerm = termCategory.createTerm(TermType.ADJECTIVE, 'active');
  
  console.log('Find term:', findTerm);
  console.log('Users term:', usersTerm);
  console.log('Active term:', activeTerm);
  
  // Create morphisms
  const activeMorphism = termCategory.createAdjectiveMorphism(activeTerm);
  const modifiedTerm = activeMorphism(usersTerm);
  
  console.log('Modified term:', modifiedTerm);
  
  // Expression category
  console.log('\n2. Expression category:');
  
  // Create expressions
  const nounPhrase = expressionCategory.createExpression(
    ExpressionType.NOUN_PHRASE,
    [activeTerm, usersTerm]
  );
  
  const verbPhrase = expressionCategory.createExpression(
    ExpressionType.VERB_PHRASE,
    [findTerm, nounPhrase]
  );
  
  console.log('Noun phrase:', nounPhrase);
  console.log('Verb phrase:', verbPhrase);
  
  // Meaning category
  console.log('\n3. Meaning category:');
  
  // Create meanings
  const actionMeaning = meaningCategory.createMeaning(
    MeaningType.ACTION,
    'Finding active users',
    { source: 'example' }
  );
  
  console.log('Action meaning:', actionMeaning);
  
  // Context category
  console.log('\n4. Context category:');
  
  // Create contexts
  const customContext = contextCategory.createContext(
    'custom',
    'custom',
    'example'
  );
  
  // Add terms to context
  contextCategory.addTermToContext(customContext, findTerm);
  contextCategory.addTermToContext(customContext, usersTerm);
  
  // Set values in context
  contextCategory.setValueInContext(customContext, 'find', (target: any) => {
    return `Custom find implementation for ${target}`;
  });
  
  console.log('Custom context:', customContext);
  
  // Get terms and values from context
  const findTermFromContext = contextCategory.getTermFromContext(customContext, 'find');
  const findValueFromContext = contextCategory.getValueFromContext(customContext, 'find');
  
  console.log('Find term from context:', findTermFromContext);
  console.log('Find value from context:', findValueFromContext);
}

// Run examples with functors
function runFunctorExamples() {
  console.log('\nRunning functor examples...');
  
  // Syntax functor
  console.log('\n1. Syntax functor:');
  
  try {
    // Parse text into an expression
    const expression = syntaxFunctor.map('find active users');
    console.log('Parsed expression:', expression);
  } catch (error) {
    console.error('Error parsing text:', error);
  }
  
  // Semantic functor
  console.log('\n2. Semantic functor:');
  
  try {
    // Parse text into an expression
    const expression = syntaxFunctor.map('find active users');
    
    // Evaluate the expression
    const meaning = semanticFunctor.map(expression, getCurrentContext());
    console.log('Evaluated meaning:', meaning);
  } catch (error) {
    console.error('Error evaluating expression:', error);
  }
  
  // Pragmatic functor
  console.log('\n3. Pragmatic functor:');
  
  // Create contexts
  const sourceContext = contextCategory.createContext(
    'source',
    'source',
    'example'
  );
  
  // Add terms to context
  contextCategory.addTermToContext(sourceContext, termCategory.createTerm(TermType.VERB, 'find'));
  contextCategory.addTermToContext(sourceContext, termCategory.createTerm(TermType.NOUN, 'users'));
  
  // Set values in context
  contextCategory.setValueInContext(sourceContext, 'find', (target: any) => {
    return `Source find implementation for ${target}`;
  });
  
  // Map to target context
  const targetContext = pragmaticFunctor.map(sourceContext, 'target');
  console.log('Target context:', targetContext);
}

// Run examples with natural transformations
function runTransformationExamples() {
  console.log('\nRunning transformation examples...');
  
  // Context transformation
  console.log('\n1. Context transformation:');
  
  // Create contexts
  const privateContext = contextCategory.createContext(
    'private',
    'private',
    'example'
  );
  
  // Add terms to context
  contextCategory.addTermToContext(privateContext, termCategory.createTerm(TermType.VERB, 'find'));
  contextCategory.addTermToContext(privateContext, termCategory.createTerm(TermType.NOUN, 'users'));
  
  // Set values in context
  contextCategory.setValueInContext(privateContext, 'find', (target: any) => {
    return `Private find implementation for ${target}`;
  });
  
  // Create context transformation
  const contextTransformation = new ContextTransformation(
    'private',
    'public',
    new Map([
      ['find', 'query'],
      ['users', 'customers']
    ])
  );
  
  // Apply transformation
  const transformedContext = contextTransformation.apply(privateContext, null);
  console.log('Transformed context:', transformedContext);
  
  // Language transformation
  console.log('\n2. Language transformation:');
  
  try {
    // Parse text into an expression
    const expression = syntaxFunctor.map('find active users');
    
    // Create language transformation
    const languageTransformation = new LanguageTransformation(
      'english',
      'spanish',
      new Map([
        ['find', 'encontrar'],
        ['active', 'activos'],
        ['users', 'usuarios']
      ])
    );
    
    // Apply transformation
    const transformedExpression = languageTransformation.apply(expression, null);
    console.log('Transformed expression:', transformedExpression);
  } catch (error) {
    console.error('Error transforming expression:', error);
  }
  
  // Semantic transformation
  console.log('\n3. Semantic transformation:');
  
  try {
    // Parse text into an expression
    const expression = syntaxFunctor.map('find active users');
    
    // Evaluate the expression
    const meaning = semanticFunctor.map(expression, getCurrentContext());
    
    // Create semantic transformation
    const semanticTransformation = new SemanticTransformation(
      'default',
      'analytics',
      new Map([
        [MeaningType.ACTION, (value: any) => {
          return {
            operation: 'analytics',
            original: value,
            timestamp: Date.now()
          };
        }]
      ])
    );
    
    // Apply transformation
    const transformedMeaning = semanticTransformation.apply(meaning, null);
    console.log('Transformed meaning:', transformedMeaning);
  } catch (error) {
    console.error('Error transforming meaning:', error);
  }
}

// Run the examples
function runAdvancedExamples() {
  initializeAdvancedExamples();
  runCategoryExamples();
  runFunctorExamples();
  runTransformationExamples();
}

// Export the examples
export {
  initializeAdvancedExamples,
  runCategoryExamples,
  runFunctorExamples,
  runTransformationExamples,
  runAdvancedExamples
};

// Run the examples if this file is executed directly
if (require.main === module) {
  runAdvancedExamples();
}
