/**
 * Categorical implementation of linguistics - Integration Examples
 * 
 * This file demonstrates how to integrate the linguistics system with other parts of the SpiceTime architecture.
 */

import {
  l, defineTerm, TermType, usePrivateContext, usePublicContext,
  getCurrentContext, createContext,
  termCategory, expressionCategory, meaningCategory, contextCategory,
  syntaxFunctor, semanticFunctor, pragmaticFunctor
} from '../index';

// Mock treenityTree for demonstration
class MockTreenityTree {
  private state: any;
  private listeners: Set<(state: any) => void>;
  
  constructor(initialState: any) {
    this.state = initialState;
    this.listeners = new Set();
  }
  
  getState() {
    return this.state;
  }
  
  setState(newState: any) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }
  
  subscribe(listener: (state: any) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }
  
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.state));
  }
}

// Mock stPragma for demonstration
const mockStPragma = {
  getCurrentScope() {
    return 'public';
  },
  
  processFile(file: any) {
    return file;
  },
  
  applyPragma(pragma: any, node: any) {
    return { ...node, pragma };
  },
  
  registerPragma(pragma: any) {
    console.log(`Registered pragma: ${pragma.name}`);
  }
};

// Integration with treenityTree
function demonstrateTreenityIntegration() {
  console.log('\nDemonstrating treenityTree integration...');
  
  // Create a reactive linguistic context
  const linguisticContext = new MockTreenityTree({
    vocabulary: {
      verbs: {
        find: (target: any) => `Finding ${target} reactively`,
        create: (target: any) => `Creating ${target} reactively`
      },
      nouns: {
        users: 'users',
        products: 'products'
      }
    },
    domain: 'default'
  });
  
  // Create a wrapper for the l tag that uses the reactive context
  function reactiveL(strings: TemplateStringsArray, ...values: any[]): any {
    // Combine strings and values
    const text = strings.reduce((result, str, i) => {
      return result + str + (i < values.length ? String(values[i]) : '');
    }, '');
    
    try {
      // Parse the text into an expression
      const expression = syntaxFunctor.map(text);
      
      // Create a context from the reactive state
      const state = linguisticContext.getState();
      const context = createContext('reactive', 'reactive', state.domain);
      
      // Add vocabulary from the reactive state
      Object.entries(state.vocabulary.verbs).forEach(([name, impl]) => {
        contextCategory.addTermToContext(context, termCategory.createTerm(TermType.VERB, name));
        contextCategory.setValueInContext(context, name, impl);
      });
      
      Object.entries(state.vocabulary.nouns).forEach(([name, value]) => {
        contextCategory.addTermToContext(context, termCategory.createTerm(TermType.NOUN, name));
        contextCategory.setValueInContext(context, name, value);
      });
      
      // Evaluate the expression in the context
      const meaning = semanticFunctor.map(expression, context);
      
      // Return the value of the meaning
      return meaning.value;
    } catch (error) {
      console.error(`Error processing reactive linguistic expression: ${text}`);
      console.error(error);
      
      // Return the original text if parsing fails
      return text;
    }
  }
  
  // Use the reactive l tag
  console.log('Initial state:');
  console.log('find users:', reactiveL`find users`);
  console.log('create products:', reactiveL`create products`);
  
  // Update the reactive context
  console.log('\nUpdating reactive context...');
  linguisticContext.setState({
    vocabulary: {
      verbs: {
        find: (target: any) => `Finding ${target} with updated implementation`,
        create: (target: any) => `Creating ${target} with updated implementation`
      }
    }
  });
  
  // Use the reactive l tag again
  console.log('\nAfter update:');
  console.log('find users:', reactiveL`find users`);
  console.log('create products:', reactiveL`create products`);
  
  // Subscribe to changes
  console.log('\nSubscribing to changes...');
  const unsubscribe = linguisticContext.subscribe(state => {
    console.log('Linguistic context changed:', state);
  });
  
  // Update the domain
  console.log('\nUpdating domain...');
  linguisticContext.setState({
    domain: 'analytics'
  });
  
  // Unsubscribe
  unsubscribe();
}

// Integration with stPragma
function demonstrateStPragmaIntegration() {
  console.log('\nDemonstrating stPragma integration...');
  
  // Create enhanced stPragma
  const enhancedStPragma = {
    ...mockStPragma,
    
    // Add the l tag
    l,
    
    // Add a method to define terms in the current scope
    defineTerm: (type: TermType, name: string, implementation: any) => {
      const currentScope = mockStPragma.getCurrentScope();
      const context = currentScope === 'private' ? 'private' : 'public';
      console.log(`Defining term in ${context} scope: ${name}`);
      
      // Use the appropriate context
      if (context === 'private') {
        usePrivateContext();
      } else {
        usePublicContext();
      }
      
      // Define the term
      defineTerm(type, name, implementation);
    },
    
    // Add a method to process a file with linguistics
    processLinguisticFile: (file: any) => {
      console.log(`Processing linguistic file: ${file.path}`);
      
      // Set the current context based on the file path
      const isPrivate = file.path.includes('_');
      if (isPrivate) {
        usePrivateContext();
      } else {
        usePublicContext();
      }
      
      // Process the file with stPragma
      return mockStPragma.processFile(file);
    }
  };
  
  // Use the enhanced stPragma
  console.log('\nUsing enhanced stPragma:');
  
  // Define terms
  enhancedStPragma.defineTerm(TermType.VERB, 'query', (target: any) => {
    return `Querying ${target} with stPragma`;
  });
  
  enhancedStPragma.defineTerm(TermType.NOUN, 'database', 'database');
  
  // Use the l tag
  console.log('query database:', enhancedStPragma.l`query database`);
  
  // Process a file
  const result = enhancedStPragma.processLinguisticFile({
    path: 'user/query.l.ts',
    content: 'export default l`query database`;'
  });
  
  console.log('Processed file:', result);
  
  // Define a linguistics pragma
  const linguisticsPragma = {
    name: 'linguistics',
    apply: (node: any) => {
      console.log('Applying linguistics pragma to node');
      
      // Inject the linguistics system into the node
      return {
        ...node,
        l,
        defineTerm,
        TermType
      };
    }
  };
  
  // Register the pragma
  enhancedStPragma.registerPragma(linguisticsPragma);
  
  // Apply the pragma
  const node = { id: 'test-node' };
  const enhancedNode = enhancedStPragma.applyPragma(linguisticsPragma, node);
  
  console.log('Enhanced node:', enhancedNode);
}

// Integration with forestry_cat_types
function demonstrateForestryIntegration() {
  console.log('\nDemonstrating forestry_cat_types integration...');
  
  // Mock forestry for demonstration
  const mockForestry = {
    createCategoricalTree(name: string, options: any) {
      console.log(`Creating categorical tree: ${name}`);
      return {
        name,
        options,
        getCategory(categoryName: string) {
          return options.categories[categoryName];
        },
        addMorphism(categoryName: string, morphismName: string, morphism: any) {
          console.log(`Adding morphism ${morphismName} to category ${categoryName}`);
        }
      };
    }
  };
  
  // Create a linguistic tree in the forestry system
  const linguisticTree = mockForestry.createCategoricalTree('linguistics', {
    categories: {
      term: termCategory,
      expression: expressionCategory,
      meaning: meaningCategory,
      context: contextCategory
    },
    functors: {
      syntax: syntaxFunctor,
      semantic: semanticFunctor,
      pragmatic: pragmaticFunctor
    }
  });
  
  // Use the tree
  console.log('\nUsing the linguistic tree:');
  
  // Get categories
  const termCat = linguisticTree.getCategory('term');
  const expressionCat = linguisticTree.getCategory('expression');
  
  // Create terms
  const findTerm = termCat.createTerm(TermType.VERB, 'process');
  const dataTerm = termCat.createTerm(TermType.NOUN, 'data');
  
  console.log('Find term:', findTerm);
  console.log('Data term:', dataTerm);
  
  // Add morphisms
  linguisticTree.addMorphism('term', 'transform', (term: any) => {
    return {
      ...term,
      metadata: {
        ...term.metadata,
        transformed: true
      }
    };
  });
}

// Run all integration examples
function runIntegrationExamples() {
  console.log('Running integration examples...');
  demonstrateTreenityIntegration();
  demonstrateStPragmaIntegration();
  demonstrateForestryIntegration();
}

// Export the examples
export {
  demonstrateTreenityIntegration,
  demonstrateStPragmaIntegration,
  demonstrateForestryIntegration,
  runIntegrationExamples
};

// Run the examples if this file is executed directly
if (require.main === module) {
  runIntegrationExamples();
}
