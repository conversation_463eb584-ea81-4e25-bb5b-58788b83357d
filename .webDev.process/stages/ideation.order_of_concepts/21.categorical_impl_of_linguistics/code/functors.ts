/**
 * Categorical implementation of linguistics - Functors
 * 
 * This file defines the functors that map between linguistic categories.
 */

import * as P from 'parsimmon';
import {
  Term, TermType, Expression, ExpressionType, Meaning, MeaningType, Context,
  TermCategory, ExpressionCategory, MeaningCategory, ContextCategory
} from './categories';

/**
 * Generic Functor interface
 */
export interface Functor<A, B> {
  /**
   * Map an object from category A to category B
   */
  map(a: A, ...args: any[]): B;
  
  /**
   * Map a morphism from category A to category B
   */
  mapMorphism<X, Y>(f: (x: X) => Y): (x: any) => any;
}

/**
 * Syntax Functor - Maps from strings to expressions
 */
export class SyntaxFunctor implements Functor<string, Expression> {
  private termCategory: TermCategory;
  private expressionCategory: ExpressionCategory;
  private parser: P.Parser<Expression>;
  
  constructor(termCategory: TermCategory, expressionCategory: ExpressionCategory) {
    this.termCategory = termCategory;
    this.expressionCategory = expressionCategory;
    this.parser = this.createParser();
  }
  
  /**
   * Map a string to an expression
   */
  map(text: string): Expression {
    const result = this.parser.parse(text);
    
    if (result.status) {
      return result.value;
    }
    
    throw new Error(`Failed to parse: ${result.expected.join(', ')}`);
  }
  
  /**
   * Map a morphism from strings to expressions
   */
  mapMorphism<X, Y>(f: (x: X) => Y): (x: any) => any {
    // Implementation depends on the specific morphism
    return (x: any) => f(x);
  }
  
  /**
   * Create a parser using Parsimmon
   */
  private createParser(): P.Parser<Expression> {
    // Define basic parsers for different parts of speech
    const word = P.regexp(/[A-Za-z_][A-Za-z0-9_]*/);
    
    const noun = word.map(value => 
      this.termCategory.createTerm(TermType.NOUN, value)
    );
    
    const verb = word.map(value => 
      this.termCategory.createTerm(TermType.VERB, value)
    );
    
    const adjective = word.map(value => 
      this.termCategory.createTerm(TermType.ADJECTIVE, value)
    );
    
    const adverb = word.map(value => 
      this.termCategory.createTerm(TermType.ADVERB, value)
    );
    
    const preposition = P.alt(
      P.string('with'),
      P.string('of'),
      P.string('by'),
      P.string('to'),
      P.string('from'),
      P.string('in'),
      P.string('on'),
      P.string('at')
    ).map(value => 
      this.termCategory.createTerm(TermType.PREPOSITION, value)
    );
    
    const conjunction = P.alt(
      P.string('and'),
      P.string('or'),
      P.string('but'),
      P.string('because'),
      P.string('if'),
      P.string('when')
    ).map(value => 
      this.termCategory.createTerm(TermType.CONJUNCTION, value)
    );
    
    const determiner = P.alt(
      P.string('the'),
      P.string('a'),
      P.string('an'),
      P.string('this'),
      P.string('that'),
      P.string('these'),
      P.string('those'),
      P.string('all'),
      P.string('some'),
      P.string('any'),
      P.string('each'),
      P.string('every')
    ).map(value => 
      this.termCategory.createTerm(TermType.DETERMINER, value)
    );
    
    // Define phrase parsers
    const nounPhrase = P.seq(
      determiner.skip(P.whitespace).atMost(1),
      adjective.skip(P.whitespace).many(),
      noun
    ).map(([det, adjs, n]) => {
      const components: Term[] = [];
      
      if (det.length > 0) {
        components.push(det[0]);
      }
      
      components.push(...adjs);
      components.push(n);
      
      return this.expressionCategory.createExpression(
        ExpressionType.NOUN_PHRASE,
        components
      );
    });
    
    const verbPhrase = P.seq(
      adverb.skip(P.whitespace).many(),
      verb,
      P.whitespace.then(nounPhrase).atMost(1)
    ).map(([advs, v, np]) => {
      const components: (Term | Expression)[] = [];
      
      components.push(...advs);
      components.push(v);
      
      if (np.length > 0) {
        components.push(np[0]);
      }
      
      return this.expressionCategory.createExpression(
        ExpressionType.VERB_PHRASE,
        components
      );
    });
    
    const prepositionalPhrase = P.seq(
      preposition,
      P.whitespace.then(nounPhrase)
    ).map(([prep, np]) => {
      return this.expressionCategory.createExpression(
        ExpressionType.PREPOSITIONAL_PHRASE,
        [prep, np]
      );
    });
    
    const clause = P.seq(
      nounPhrase.skip(P.whitespace),
      verbPhrase,
      P.whitespace.then(prepositionalPhrase).many()
    ).map(([subject, predicate, modifiers]) => {
      const components: Expression[] = [subject, predicate, ...modifiers];
      
      return this.expressionCategory.createExpression(
        ExpressionType.CLAUSE,
        components
      );
    });
    
    const sentence = P.alt(
      clause,
      P.seq(
        clause,
        P.whitespace.then(conjunction).skip(P.whitespace),
        clause
      ).map(([left, conj, right]) => {
        return this.expressionCategory.createExpression(
          ExpressionType.SENTENCE,
          [left, conj, right]
        );
      })
    );
    
    // Return the sentence parser as the main parser
    return sentence;
  }
}

/**
 * Semantic Functor - Maps from expressions to meanings
 */
export class SemanticFunctor implements Functor<Expression, Meaning> {
  private expressionCategory: ExpressionCategory;
  private meaningCategory: MeaningCategory;
  
  constructor(expressionCategory: ExpressionCategory, meaningCategory: MeaningCategory) {
    this.expressionCategory = expressionCategory;
    this.meaningCategory = meaningCategory;
  }
  
  /**
   * Map an expression to a meaning
   */
  map(expression: Expression, context: Context): Meaning {
    switch (expression.type) {
      case ExpressionType.NOUN_PHRASE:
        return this.evaluateNounPhrase(expression, context);
      
      case ExpressionType.VERB_PHRASE:
        return this.evaluateVerbPhrase(expression, context);
      
      case ExpressionType.PREPOSITIONAL_PHRASE:
        return this.evaluatePrepositionalPhrase(expression, context);
      
      case ExpressionType.CLAUSE:
        return this.evaluateClause(expression, context);
      
      case ExpressionType.SENTENCE:
        return this.evaluateSentence(expression, context);
      
      default:
        throw new Error(`Unknown expression type: ${expression.type}`);
    }
  }
  
  /**
   * Map a morphism from expressions to meanings
   */
  mapMorphism<X, Y>(f: (x: X) => Y): (x: any) => any {
    // Implementation depends on the specific morphism
    return (x: any) => f(x);
  }
  
  /**
   * Evaluate a noun phrase
   */
  private evaluateNounPhrase(expression: Expression, context: Context): Meaning {
    // Extract the noun (last component)
    const noun = expression.components[expression.components.length - 1] as Term;
    
    // Look up the noun in the context
    const value = this.lookupTerm(noun, context);
    
    // Create a meaning
    return this.meaningCategory.createMeaning(
      MeaningType.VALUE,
      value,
      {
        expression
      }
    );
  }
  
  /**
   * Evaluate a verb phrase
   */
  private evaluateVerbPhrase(expression: Expression, context: Context): Meaning {
    // Find the verb (first non-adverb component)
    const verbIndex = expression.components.findIndex(
      comp => (comp as Term).type === TermType.VERB
    );
    
    if (verbIndex === -1) {
      throw new Error('No verb found in verb phrase');
    }
    
    const verb = expression.components[verbIndex] as Term;
    
    // Look up the verb in the context
    const action = this.lookupTerm(verb, context);
    
    // If there's a noun phrase, evaluate it
    let object: any = undefined;
    
    for (let i = verbIndex + 1; i < expression.components.length; i++) {
      const comp = expression.components[i];
      if ((comp as Expression).type === ExpressionType.NOUN_PHRASE) {
        const objectMeaning = this.map(comp as Expression, context);
        object = objectMeaning.value;
        break;
      }
    }
    
    // If the action is a function, call it with the object
    if (typeof action === 'function') {
      const result = object !== undefined ? action(object) : action();
      
      return this.meaningCategory.createMeaning(
        MeaningType.ACTION,
        result,
        {
          expression,
          action: verb.value,
          object
        }
      );
    }
    
    // Otherwise, return the action itself
    return this.meaningCategory.createMeaning(
      MeaningType.ACTION,
      action,
      {
        expression,
        action: verb.value,
        object
      }
    );
  }
  
  /**
   * Evaluate a prepositional phrase
   */
  private evaluatePrepositionalPhrase(expression: Expression, context: Context): Meaning {
    // Extract the preposition and noun phrase
    const preposition = expression.components[0] as Term;
    const nounPhrase = expression.components[1] as Expression;
    
    // Evaluate the noun phrase
    const nounMeaning = this.map(nounPhrase, context);
    
    // Look up the preposition in the context
    const relation = this.lookupTerm(preposition, context);
    
    // Create a meaning
    return this.meaningCategory.createMeaning(
      MeaningType.CONDITION,
      {
        type: 'relation',
        relation: preposition.value,
        target: nounMeaning.value
      },
      {
        expression
      }
    );
  }
  
  /**
   * Evaluate a clause
   */
  private evaluateClause(expression: Expression, context: Context): Meaning {
    // Extract the subject, predicate, and modifiers
    const subject = expression.components[0] as Expression;
    const predicate = expression.components[1] as Expression;
    const modifiers = expression.components.slice(2) as Expression[];
    
    // Evaluate the subject
    const subjectMeaning = this.map(subject, context);
    
    // Evaluate the predicate
    const predicateMeaning = this.map(predicate, context);
    
    // Evaluate the modifiers
    const modifierMeanings = modifiers.map(mod => this.map(mod, context));
    
    // Create a meaning
    return this.meaningCategory.createMeaning(
      MeaningType.ACTION,
      {
        subject: subjectMeaning.value,
        predicate: predicateMeaning.value,
        modifiers: modifierMeanings.map(m => m.value)
      },
      {
        expression
      }
    );
  }
  
  /**
   * Evaluate a sentence
   */
  private evaluateSentence(expression: Expression, context: Context): Meaning {
    // Check if it's a compound sentence
    if (expression.components.length === 3 && 
        (expression.components[1] as Term).type === TermType.CONJUNCTION) {
      // Extract the left clause, conjunction, and right clause
      const leftClause = expression.components[0] as Expression;
      const conjunction = expression.components[1] as Term;
      const rightClause = expression.components[2] as Expression;
      
      // Evaluate the left and right clauses
      const leftMeaning = this.map(leftClause, context);
      const rightMeaning = this.map(rightClause, context);
      
      // Look up the conjunction in the context
      const operator = this.lookupTerm(conjunction, context);
      
      // If the operator is a function, call it with the left and right meanings
      if (typeof operator === 'function') {
        const result = operator(leftMeaning.value, rightMeaning.value);
        
        return this.meaningCategory.createMeaning(
          MeaningType.ACTION,
          result,
          {
            expression,
            operator: conjunction.value,
            left: leftMeaning.value,
            right: rightMeaning.value
          }
        );
      }
      
      // Otherwise, return a compound meaning
      return this.meaningCategory.createMeaning(
        MeaningType.ACTION,
        {
          type: 'compound',
          operator: conjunction.value,
          left: leftMeaning.value,
          right: rightMeaning.value
        },
        {
          expression
        }
      );
    }
    
    // If it's a simple sentence (just a clause), evaluate it directly
    if (expression.components.length === 1) {
      return this.map(expression.components[0] as Expression, context);
    }
    
    // Otherwise, treat it as a clause
    return this.evaluateClause(expression, context);
  }
  
  /**
   * Look up a term in the context
   */
  private lookupTerm(term: Term, context: Context): any {
    // Check if the term is in the context's vocabulary
    if (context.vocabulary.has(term.value)) {
      // Get the implementation from the context
      const implementation = context.values.get(term.value);
      
      if (implementation !== undefined) {
        return implementation;
      }
    }
    
    // Check parent contexts
    if (context.parent) {
      return this.lookupTerm(term, context.parent);
    }
    
    // If not found, return the term itself
    return term.value;
  }
}

/**
 * Pragmatic Functor - Maps between different contexts
 */
export class PragmaticFunctor implements Functor<Context, Context> {
  private contextCategory: ContextCategory;
  
  constructor(contextCategory: ContextCategory) {
    this.contextCategory = contextCategory;
  }
  
  /**
   * Map a context to another context
   */
  map(context: Context, targetScope: string): Context {
    // Create a new context with the target scope
    const targetContext = this.contextCategory.createContext(
      context.name,
      targetScope,
      context.domain,
      context.parent
    );
    
    // Copy vocabulary and values from the source context
    context.vocabulary.forEach((term, key) => {
      targetContext.vocabulary.set(key, term);
    });
    
    context.values.forEach((value, key) => {
      targetContext.values.set(key, value);
    });
    
    return targetContext;
  }
  
  /**
   * Map a morphism between contexts
   */
  mapMorphism<X, Y>(f: (x: X) => Y): (x: any) => any {
    // Implementation depends on the specific morphism
    return (x: any) => f(x);
  }
}
