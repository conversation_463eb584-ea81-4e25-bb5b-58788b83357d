/**
 * Categorical implementation of linguistics - Main Entry Point
 * 
 * This file exports the public API of the linguistics system.
 */

// Export categories
export {
  Term, TermType, Expression, ExpressionType, Meaning, MeaningType, Context,
  TermCategory, ExpressionCategory, MeaningCategory, ContextCategory
} from './categories';

// Export functors
export {
  Functor, SyntaxFunctor, SemanticFunctor, PragmaticFunctor
} from './functors';

// Export natural transformations
export {
  NaturalTransformation, ContextTransformation, LanguageTransformation, SemanticTransformation
} from './natural_transformations';

// Export template literal and utilities
export {
  l, setCurrentContext, getCurrentContext, usePrivateContext, usePublicContext,
  createContext, defineTerm, defineCommonVocabulary, initialize,
  termCategory, expressionCategory, meaningCategory, contextCategory,
  syntaxFunctor, semanticFunctor, pragmaticFunctor
} from './template_literal';

// Initialize the linguistics system
import { initialize } from './template_literal';
initialize();
