/**
 * Categorical implementation of linguistics - Template Literal
 * 
 * This file implements the l template literal tag using the categorical framework.
 */

import {
  Term, TermType, Expression, ExpressionType, Meaning, MeaningType, Context,
  TermCategory, ExpressionCategory, MeaningCategory, ContextCategory
} from './categories';
import {
  SyntaxFunctor, SemanticFunctor, PragmaticFunctor
} from './functors';

/**
 * Global instances of categories
 */
const termCategory = new TermCategory();
const expressionCategory = new ExpressionCategory();
const meaningCategory = new MeaningCategory();
const contextCategory = new ContextCategory();

/**
 * Global instances of functors
 */
const syntaxFunctor = new SyntaxFunctor(termCategory, expressionCategory);
const semanticFunctor = new SemanticFunctor(expressionCategory, meaningCategory);
const pragmaticFunctor = new PragmaticFunctor(contextCategory);

/**
 * Global contexts
 */
const privateContext = contextCategory.createContext('root', 'private', 'default');
const publicContext = contextCategory.createContext('root', 'public', 'default');

/**
 * Current context (default to public)
 */
let currentContext = publicContext;

/**
 * Set the current context
 */
export function setCurrentContext(context: Context): void {
  currentContext = context;
}

/**
 * Get the current context
 */
export function getCurrentContext(): Context {
  return currentContext;
}

/**
 * Switch to private context
 */
export function usePrivateContext(): void {
  currentContext = privateContext;
}

/**
 * Switch to public context
 */
export function usePublicContext(): void {
  currentContext = publicContext;
}

/**
 * Create a context
 */
export function createContext(name: string, scope: string, domain: string, parent?: Context): Context {
  return contextCategory.createContext(name, scope, domain, parent);
}

/**
 * Add a term to the vocabulary
 */
export function defineTerm(type: TermType, name: string, implementation: any, context: Context = currentContext): void {
  // Create a term
  const term = termCategory.createTerm(type, name);
  
  // Add it to the context's vocabulary
  contextCategory.addTermToContext(context, term);
  
  // Add the implementation to the context's values
  contextCategory.setValueInContext(context, name, implementation);
}

/**
 * Define common vocabulary
 */
export function defineCommonVocabulary(): void {
  // Define common verbs
  defineTerm(TermType.VERB, 'find', (target: any) => {
    // Implementation of find
    return `Finding ${target}`;
  });
  
  defineTerm(TermType.VERB, 'create', (target: any) => {
    // Implementation of create
    return `Creating ${target}`;
  });
  
  defineTerm(TermType.VERB, 'update', (target: any) => {
    // Implementation of update
    return `Updating ${target}`;
  });
  
  defineTerm(TermType.VERB, 'delete', (target: any) => {
    // Implementation of delete
    return `Deleting ${target}`;
  });
  
  // Define common nouns
  defineTerm(TermType.NOUN, 'users', 'users');
  defineTerm(TermType.NOUN, 'products', 'products');
  defineTerm(TermType.NOUN, 'orders', 'orders');
  
  // Define common adjectives
  defineTerm(TermType.ADJECTIVE, 'active', (target: any) => {
    // Implementation of active
    return `${target} that are active`;
  });
  
  defineTerm(TermType.ADJECTIVE, 'new', (target: any) => {
    // Implementation of new
    return `${target} that are new`;
  });
  
  // Define common prepositions
  defineTerm(TermType.PREPOSITION, 'with', (left: any, right: any) => {
    // Implementation of with
    return `${left} with ${right}`;
  });
  
  defineTerm(TermType.PREPOSITION, 'by', (left: any, right: any) => {
    // Implementation of by
    return `${left} by ${right}`;
  });
  
  // Define common conjunctions
  defineTerm(TermType.CONJUNCTION, 'and', (left: any, right: any) => {
    // Implementation of and
    return `${left} and ${right}`;
  });
  
  defineTerm(TermType.CONJUNCTION, 'or', (left: any, right: any) => {
    // Implementation of or
    return `${left} or ${right}`;
  });
}

/**
 * Initialize the linguistics system
 */
export function initialize(): void {
  // Define common vocabulary
  defineCommonVocabulary();
}

/**
 * The l template literal tag
 */
export function l(strings: TemplateStringsArray, ...values: any[]): any {
  // Combine strings and values
  const text = combineText(strings, values);
  
  try {
    // Parse the text into an expression
    const expression = syntaxFunctor.map(text);
    
    // Evaluate the expression in the current context
    const meaning = semanticFunctor.map(expression, currentContext);
    
    // Return the value of the meaning
    return meaning.value;
  } catch (error) {
    console.error(`Error processing linguistic expression: ${text}`);
    console.error(error);
    
    // Return the original text if parsing fails
    return text;
  }
}

/**
 * Combine template strings and values
 */
function combineText(strings: TemplateStringsArray, values: any[]): string {
  return strings.reduce((result, str, i) => {
    return result + str + (i < values.length ? String(values[i]) : '');
  }, '');
}

/**
 * Export categories and functors for advanced usage
 */
export {
  termCategory,
  expressionCategory,
  meaningCategory,
  contextCategory,
  syntaxFunctor,
  semanticFunctor,
  pragmaticFunctor
};
