/**
 * Categorical implementation of linguistics - Categories
 * 
 * This file defines the core categories for the linguistics system.
 */

/**
 * Generic Category interface
 */
export interface Category<Obj, Mor> {
  /**
   * Objects in the category
   */
  objects: Set<Obj>;
  
  /**
   * Morphisms in the category
   */
  morphisms: Map<string, Mor>;
  
  /**
   * Composition of morphisms
   */
  compose<A, B, C>(f: (b: B) => C, g: (a: A) => B): (a: A) => C;
  
  /**
   * Identity morphism
   */
  identity<A>(a: A): (a: A) => A;
  
  /**
   * Add an object to the category
   */
  addObject(obj: Obj): void;
  
  /**
   * Add a morphism to the category
   */
  addMorphism(name: string, morphism: Mor): void;
  
  /**
   * Get a morphism by name
   */
  getMorphism(name: string): Mor | undefined;
}

/**
 * Base implementation of a Category
 */
export class BaseCategory<Obj, Mor> implements Category<Obj, Mor> {
  objects: Set<Obj>;
  morphisms: Map<string, Mor>;
  
  constructor() {
    this.objects = new Set<Obj>();
    this.morphisms = new Map<string, Mor>();
  }
  
  compose<A, B, C>(f: (b: B) => C, g: (a: A) => B): (a: A) => C {
    return (a: A) => f(g(a));
  }
  
  identity<A>(a: A): (a: A) => A {
    return (x: A) => x;
  }
  
  addObject(obj: Obj): void {
    this.objects.add(obj);
  }
  
  addMorphism(name: string, morphism: Mor): void {
    this.morphisms.set(name, morphism);
  }
  
  getMorphism(name: string): Mor | undefined {
    return this.morphisms.get(name);
  }
}

/**
 * Term interface representing linguistic terms
 */
export interface Term {
  id: string;
  type: TermType;
  value: string;
  metadata?: Record<string, any>;
}

/**
 * Term types
 */
export enum TermType {
  NOUN = 'noun',
  VERB = 'verb',
  ADJECTIVE = 'adjective',
  ADVERB = 'adverb',
  PREPOSITION = 'preposition',
  CONJUNCTION = 'conjunction',
  DETERMINER = 'determiner',
  PRONOUN = 'pronoun',
  INTERJECTION = 'interjection'
}

/**
 * Expression interface representing linguistic expressions
 */
export interface Expression {
  id: string;
  type: ExpressionType;
  components: (Term | Expression)[];
  metadata?: Record<string, any>;
}

/**
 * Expression types
 */
export enum ExpressionType {
  NOUN_PHRASE = 'noun_phrase',
  VERB_PHRASE = 'verb_phrase',
  PREPOSITIONAL_PHRASE = 'prepositional_phrase',
  CLAUSE = 'clause',
  SENTENCE = 'sentence'
}

/**
 * Meaning interface representing semantic interpretations
 */
export interface Meaning {
  id: string;
  type: MeaningType;
  value: any;
  metadata?: Record<string, any>;
}

/**
 * Meaning types
 */
export enum MeaningType {
  VALUE = 'value',
  ACTION = 'action',
  QUERY = 'query',
  CONDITION = 'condition',
  TRANSFORMATION = 'transformation'
}

/**
 * Context interface representing evaluation contexts
 */
export interface Context {
  id: string;
  name: string;
  scope: string;
  domain: string;
  vocabulary: Map<string, Term>;
  values: Map<string, any>;
  parent?: Context;
}

/**
 * Term Category
 */
export class TermCategory extends BaseCategory<Term, (term: Term) => Term> {
  constructor() {
    super();
  }
  
  // Term-specific methods
  createTerm(type: TermType, value: string, metadata?: Record<string, any>): Term {
    const term: Term = {
      id: `term-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      value,
      metadata
    };
    
    this.addObject(term);
    return term;
  }
  
  // Morphisms for different parts of speech
  createAdjectiveMorphism(adjective: Term): (noun: Term) => Term {
    if (adjective.type !== TermType.ADJECTIVE) {
      throw new Error(`Term ${adjective.value} is not an adjective`);
    }
    
    const morphism = (noun: Term): Term => {
      if (noun.type !== TermType.NOUN) {
        throw new Error(`Term ${noun.value} is not a noun`);
      }
      
      return this.createTerm(
        TermType.NOUN,
        `${adjective.value}_${noun.value}`,
        {
          base: noun,
          modifier: adjective
        }
      );
    };
    
    this.addMorphism(`adjective_${adjective.value}`, morphism);
    return morphism;
  }
  
  createAdverbMorphism(adverb: Term): (verb: Term) => Term {
    if (adverb.type !== TermType.ADVERB) {
      throw new Error(`Term ${adverb.value} is not an adverb`);
    }
    
    const morphism = (verb: Term): Term => {
      if (verb.type !== TermType.VERB) {
        throw new Error(`Term ${verb.value} is not a verb`);
      }
      
      return this.createTerm(
        TermType.VERB,
        `${adverb.value}_${verb.value}`,
        {
          base: verb,
          modifier: adverb
        }
      );
    };
    
    this.addMorphism(`adverb_${adverb.value}`, morphism);
    return morphism;
  }
}

/**
 * Expression Category
 */
export class ExpressionCategory extends BaseCategory<Expression, (expr: Expression) => Expression> {
  constructor() {
    super();
  }
  
  // Expression-specific methods
  createExpression(type: ExpressionType, components: (Term | Expression)[], metadata?: Record<string, any>): Expression {
    const expression: Expression = {
      id: `expr-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      components,
      metadata
    };
    
    this.addObject(expression);
    return expression;
  }
  
  // Morphisms for different expression operations
  createConjunctionMorphism(conjunction: Term): (left: Expression, right: Expression) => Expression {
    if (conjunction.type !== TermType.CONJUNCTION) {
      throw new Error(`Term ${conjunction.value} is not a conjunction`);
    }
    
    const morphism = (left: Expression, right: Expression): Expression => {
      return this.createExpression(
        ExpressionType.SENTENCE,
        [left, conjunction, right],
        {
          type: 'compound',
          operator: conjunction.value
        }
      );
    };
    
    this.addMorphism(`conjunction_${conjunction.value}`, morphism);
    return morphism;
  }
}

/**
 * Meaning Category
 */
export class MeaningCategory extends BaseCategory<Meaning, (meaning: Meaning) => Meaning> {
  constructor() {
    super();
  }
  
  // Meaning-specific methods
  createMeaning(type: MeaningType, value: any, metadata?: Record<string, any>): Meaning {
    const meaning: Meaning = {
      id: `meaning-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      value,
      metadata
    };
    
    this.addObject(meaning);
    return meaning;
  }
}

/**
 * Context Category
 */
export class ContextCategory extends BaseCategory<Context, (context: Context) => Context> {
  constructor() {
    super();
  }
  
  // Context-specific methods
  createContext(name: string, scope: string, domain: string, parent?: Context): Context {
    const context: Context = {
      id: `context-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name,
      scope,
      domain,
      vocabulary: new Map<string, Term>(),
      values: new Map<string, any>(),
      parent
    };
    
    this.addObject(context);
    return context;
  }
  
  // Add a term to a context's vocabulary
  addTermToContext(context: Context, term: Term): void {
    context.vocabulary.set(term.value, term);
  }
  
  // Get a term from a context's vocabulary
  getTermFromContext(context: Context, name: string): Term | undefined {
    // Check in the current context
    if (context.vocabulary.has(name)) {
      return context.vocabulary.get(name);
    }
    
    // Check in parent contexts
    if (context.parent) {
      return this.getTermFromContext(context.parent, name);
    }
    
    return undefined;
  }
  
  // Set a value in a context
  setValueInContext(context: Context, name: string, value: any): void {
    context.values.set(name, value);
  }
  
  // Get a value from a context
  getValueFromContext(context: Context, name: string): any {
    // Check in the current context
    if (context.values.has(name)) {
      return context.values.get(name);
    }
    
    // Check in parent contexts
    if (context.parent) {
      return this.getValueFromContext(context.parent, name);
    }
    
    return undefined;
  }
}
