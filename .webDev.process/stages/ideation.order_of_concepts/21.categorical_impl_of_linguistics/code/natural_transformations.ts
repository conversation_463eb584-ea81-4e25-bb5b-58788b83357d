/**
 * Categorical implementation of linguistics - Natural Transformations
 * 
 * This file defines the natural transformations between functors.
 */

import {
  Term, TermType, Expression, ExpressionType, Meaning, MeaningType, Context,
  TermCategory, ExpressionCategory, MeaningCategory, ContextCategory
} from './categories';
import {
  Functor, SyntaxFunctor, SemanticFunctor, PragmaticFunctor
} from './functors';

/**
 * Generic Natural Transformation interface
 */
export interface NaturalTransformation<F extends Functor<any, any>, G extends Functor<any, any>> {
  /**
   * Component of the natural transformation at object A
   */
  component<A>(a: A): (fa: ReturnType<F['map']>) => ReturnType<G['map']>;
  
  /**
   * Apply the natural transformation
   */
  apply<A>(fa: ReturnType<F['map']>, a: A): ReturnType<G['map']>;
}

/**
 * Context Transformation - Transforms between different context functors
 */
export class ContextTransformation implements NaturalTransformation<PragmaticFunctor, PragmaticFunctor> {
  private sourceContextType: string;
  private targetContextType: string;
  private contextMappings: Map<string, string>;
  
  constructor(sourceContextType: string, targetContextType: string, contextMappings: Map<string, string>) {
    this.sourceContextType = sourceContextType;
    this.targetContextType = targetContextType;
    this.contextMappings = contextMappings;
  }
  
  /**
   * Component of the natural transformation at object A
   */
  component<A>(a: A): (fa: Context) => Context {
    return (fa: Context) => {
      // Create a new context of the target type
      const targetContext: Context = {
        id: `context-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        name: fa.name,
        scope: this.targetContextType,
        domain: fa.domain,
        vocabulary: new Map<string, Term>(),
        values: new Map<string, any>(),
        parent: fa.parent
      };
      
      // Map vocabulary terms
      fa.vocabulary.forEach((term, key) => {
        // Check if there's a mapping for this term
        const targetKey = this.contextMappings.get(key) || key;
        targetContext.vocabulary.set(targetKey, term);
      });
      
      // Map values
      fa.values.forEach((value, key) => {
        // Check if there's a mapping for this value
        const targetKey = this.contextMappings.get(key) || key;
        targetContext.values.set(targetKey, value);
      });
      
      return targetContext;
    };
  }
  
  /**
   * Apply the natural transformation
   */
  apply<A>(fa: Context, a: A): Context {
    return this.component(a)(fa);
  }
}

/**
 * Language Transformation - Transforms between different language functors
 */
export class LanguageTransformation implements NaturalTransformation<SyntaxFunctor, SyntaxFunctor> {
  private sourceLanguage: string;
  private targetLanguage: string;
  private termMappings: Map<string, string>;
  
  constructor(sourceLanguage: string, targetLanguage: string, termMappings: Map<string, string>) {
    this.sourceLanguage = sourceLanguage;
    this.targetLanguage = targetLanguage;
    this.termMappings = termMappings;
  }
  
  /**
   * Component of the natural transformation at object A
   */
  component<A>(a: A): (fa: Expression) => Expression {
    return (fa: Expression) => {
      // Create a new expression with translated terms
      return this.translateExpression(fa);
    };
  }
  
  /**
   * Apply the natural transformation
   */
  apply<A>(fa: Expression, a: A): Expression {
    return this.component(a)(fa);
  }
  
  /**
   * Translate an expression from source language to target language
   */
  private translateExpression(expression: Expression): Expression {
    // Translate each component
    const translatedComponents = expression.components.map(component => {
      if ((component as Expression).type) {
        // If it's an expression, recursively translate it
        return this.translateExpression(component as Expression);
      } else {
        // If it's a term, translate it
        return this.translateTerm(component as Term);
      }
    });
    
    // Create a new expression with translated components
    return {
      id: `expr-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type: expression.type,
      components: translatedComponents,
      metadata: expression.metadata
    };
  }
  
  /**
   * Translate a term from source language to target language
   */
  private translateTerm(term: Term): Term {
    // Check if there's a mapping for this term
    const translatedValue = this.termMappings.get(term.value) || term.value;
    
    // Create a new term with translated value
    return {
      id: `term-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type: term.type,
      value: translatedValue,
      metadata: {
        ...term.metadata,
        originalValue: term.value,
        originalLanguage: this.sourceLanguage,
        targetLanguage: this.targetLanguage
      }
    };
  }
}

/**
 * Semantic Transformation - Transforms between different semantic functors
 */
export class SemanticTransformation implements NaturalTransformation<SemanticFunctor, SemanticFunctor> {
  private sourceDomain: string;
  private targetDomain: string;
  private meaningMappings: Map<string, (value: any) => any>;
  
  constructor(sourceDomain: string, targetDomain: string, meaningMappings: Map<string, (value: any) => any>) {
    this.sourceDomain = sourceDomain;
    this.targetDomain = targetDomain;
    this.meaningMappings = meaningMappings;
  }
  
  /**
   * Component of the natural transformation at object A
   */
  component<A>(a: A): (fa: Meaning) => Meaning {
    return (fa: Meaning) => {
      // Create a new meaning with transformed value
      return this.transformMeaning(fa);
    };
  }
  
  /**
   * Apply the natural transformation
   */
  apply<A>(fa: Meaning, a: A): Meaning {
    return this.component(a)(fa);
  }
  
  /**
   * Transform a meaning from source domain to target domain
   */
  private transformMeaning(meaning: Meaning): Meaning {
    // Check if there's a mapping for this meaning type
    const transformFn = this.meaningMappings.get(meaning.type);
    
    // Transform the value if there's a mapping
    const transformedValue = transformFn ? transformFn(meaning.value) : meaning.value;
    
    // Create a new meaning with transformed value
    return {
      id: `meaning-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type: meaning.type,
      value: transformedValue,
      metadata: {
        ...meaning.metadata,
        originalValue: meaning.value,
        originalDomain: this.sourceDomain,
        targetDomain: this.targetDomain
      }
    };
  }
}
