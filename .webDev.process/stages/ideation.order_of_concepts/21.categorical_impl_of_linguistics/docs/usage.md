# Categorical Linguistics - Usage Guide

This document provides examples of how to use the categorical implementation of linguistics in the SpiceTime architecture.

## Basic Usage

### Importing the Library

```typescript
import { l, defineTerm, TermType, usePrivateContext, usePublicContext } from '../path/to/linguistics';
```

### Using the Template Literal

```typescript
// Basic usage
const result = l`find users where status is active`;
console.log(result); // "Finding users that are active"

// With interpolation
const name = 'John';
const searchResult = l`find users where name contains ${name}`;
console.log(searchResult); // "Finding users where name contains John"
```

### Defining Custom Terms

```typescript
// Define a custom verb
defineTerm(TermType.VERB, 'search', (target: any) => {
  return `Searching for ${target}`;
});

// Define a custom noun
defineTerm(TermType.NOUN, 'customers', 'customers');

// Use the custom terms
const result = l`search customers`;
console.log(result); // "Searching for customers"
```

### Switching Between Contexts

```typescript
// Use the public context
usePublicContext();
const publicResult = l`find users`;
console.log(publicResult); // "Finding users"

// Use the private context
usePrivateContext();
const privateResult = l`find users`;
console.log(privateResult); // "Finding users" (but using the private implementation)

// Switch back to public
usePublicContext();
```

## Advanced Usage

### Working with Categories

```typescript
import { 
  termCategory, expressionCategory, meaningCategory, contextCategory,
  TermType, ExpressionType, MeaningType
} from '../path/to/linguistics';

// Create a custom term
const customTerm = termCategory.createTerm(TermType.VERB, 'analyze');

// Create a custom expression
const customExpression = expressionCategory.createExpression(
  ExpressionType.VERB_PHRASE,
  [customTerm]
);

// Create a custom meaning
const customMeaning = meaningCategory.createMeaning(
  MeaningType.ACTION,
  'Analyzing data',
  { custom: 'metadata' }
);

// Create a custom context
const customContext = contextCategory.createContext(
  'custom',
  'private',
  'analytics'
);
```

### Working with Functors

```typescript
import { 
  syntaxFunctor, semanticFunctor, pragmaticFunctor,
  getCurrentContext
} from '../path/to/linguistics';

// Parse text into an expression
const expression = syntaxFunctor.map('find active users');

// Evaluate an expression to get a meaning
const meaning = semanticFunctor.map(expression, getCurrentContext());

// Map between contexts
const newContext = pragmaticFunctor.map(getCurrentContext(), 'custom');
```

### Working with Natural Transformations

```typescript
import { 
  ContextTransformation, LanguageTransformation, SemanticTransformation
} from '../path/to/linguistics';

// Create a context transformation
const contextTransformation = new ContextTransformation(
  'private',
  'public',
  new Map([
    ['internalUsers', 'users'],
    ['internalProducts', 'products']
  ])
);

// Create a language transformation
const languageTransformation = new LanguageTransformation(
  'english',
  'spanish',
  new Map([
    ['find', 'encontrar'],
    ['users', 'usuarios'],
    ['active', 'activos']
  ])
);

// Apply transformations
const transformedContext = contextTransformation.apply(getCurrentContext(), null);
const transformedExpression = languageTransformation.apply(expression, null);
```

## Integration with SpiceTime Architecture

### Integration with forestry_cat_types

```typescript
import { forestry } from '@spicetime/forestry';
import { termCategory, expressionCategory, meaningCategory } from '../path/to/linguistics';

// Create a linguistic tree in the forestry system
const linguisticTree = forestry.createCategoricalTree('linguistics', {
  categories: {
    term: termCategory,
    expression: expressionCategory,
    meaning: meaningCategory
  }
});

// Use the tree
const term = linguisticTree.getCategory('term').createTerm(TermType.VERB, 'process');
```

### Integration with stPragma

```typescript
import { stPragma } from '@spicetime/pragma';
import { l, syntaxFunctor } from '../path/to/linguistics';

// Parse a pragma extension
const pragmaExpression = syntaxFunctor.map('component with state');

// Use the l tag with stPragma
const MyComponent = stPragma.apply(() => {
  return l`component "MyComponent" with state and props`;
});
```

### Integration with treenityTree

```typescript
import { treenityTree } from '@spicetime/treenity';
import { l, defineTerm, TermType } from '../path/to/linguistics';

// Create a reactive linguistic context
const linguisticContext = new treenityTree({
  vocabulary: {
    verbs: {},
    nouns: {},
    adjectives: {}
  },
  
  // Add methods
  methods: {
    defineTerm(type, name, implementation) {
      if (!this.vocabulary[type]) {
        this.vocabulary[type] = {};
      }
      this.vocabulary[type][name] = implementation;
    }
  }
});

// Define terms in the reactive context
linguisticContext.defineTerm('verbs', 'find', (target) => `Finding ${target}`);

// Use the reactive context
const result = l`find users`;
```

## Example Use Cases

### API Queries

```typescript
// Define query terms
defineTerm(TermType.VERB, 'query', (target) => {
  return async () => {
    const response = await fetch(`/api/${target}`);
    return response.json();
  };
});

// Use in an async context
async function fetchData() {
  const users = await l`query users`;
  console.log(users);
}
```

### UI Components

```tsx
import React from 'react';
import { l } from '../path/to/linguistics';

// Create a linguistic component
const UserList = () => {
  const [users, setUsers] = React.useState([]);
  
  React.useEffect(() => {
    // Fetch users using linguistics
    const fetchUsers = async () => {
      const result = await l`find active users`;
      setUsers(result);
    };
    
    fetchUsers();
  }, []);
  
  return (
    <div>
      <h1>{l`active users list`}</h1>
      <ul>
        {users.map(user => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    </div>
  );
};
```

### Domain-Specific Languages

```typescript
// Define domain-specific vocabulary
defineTerm(TermType.VERB, 'calculate', (target) => {
  // Implementation for financial calculations
  return `Calculating ${target}`;
});

defineTerm(TermType.NOUN, 'interest', 'interest');
defineTerm(TermType.NOUN, 'principal', 'principal');

// Use the domain-specific language
const result = l`calculate interest on principal`;
```

## Best Practices

1. **Organize vocabulary by domain**: Keep related terms together in domain-specific modules
2. **Use consistent naming**: Follow a consistent naming convention for terms
3. **Separate grammar from vocabulary**: Keep grammar rules separate from vocabulary definitions
4. **Use appropriate contexts**: Use private context for internal operations and public context for external APIs
5. **Leverage category theory**: Use the categorical structure to create powerful compositions
6. **Document your vocabulary**: Provide clear documentation for custom terms
7. **Test linguistic expressions**: Write tests for your linguistic expressions to ensure they work as expected

## Conclusion

The categorical implementation of linguistics provides a powerful and flexible way to express computational intent in natural language. By leveraging category theory, we create a mathematically rigorous foundation that can be integrated with the SpiceTime architecture and extended to handle complex linguistic phenomena.
