# Git and Time

## Context

This concept explores the relationship between our temporal structure and Git's version control system. While our repository spacetime is independent of Git's branching structure, there are important interactions and mappings between the two that need to be understood and managed.

Time forms a temporal scope of a tree, creating an archive namespace in each node - a scope in each branch, through tic layers. Each proto layer has a tic getter that returns the tic number, but reversed from the tic index - starting at 0 for present and 1 for the nearest past. The term "past" exists in the present context, not in the archive itself.

In the archive scope of each node, all previous protos, time slices, and tics are frozen. When a node in the structure needs to be pruned, it's not deleted but shadowed by an innumerable namesake in the present, making it not directly visible through scope, as scope lists only numerable terms.

Key aspects:
- How Git branches relate to our temporal structure
- Constraints on branching to maintain temporal coherence
- How feature branches stay in the "present" while the repository evolves
- Recording Git history in our graph structure
- Using Git effectively without compromising our temporal model
- Temporal scopes and their relationship to Git branches
- Tic numbering and visibility in the archive namespace

This concept builds on the foundation established in "0.time_and_what_it_does" and provides practical guidance for working with G<PERSON> in the context of our temporal architecture.
