# Temporal Scopes

This document explains the concept of temporal scopes in our repository structure and how they relate to Git.

## Overview

Temporal scopes are a fundamental aspect of our repository architecture. They provide a way to organize and access different time slices of the repository structure, independent of Git's branching mechanism.

## Temporal Scope Structure

1. **Tree-Based Scopes**:
   - Time forms a temporal scope of a tree
   - Each node in the repository has its own archive namespace
   - This creates a scope in each branch through tic layers

2. **Tic Numbering**:
   - Each proto layer has a tic getter that returns the tic number
   - Tic numbers are reversed from the tic index:
     - 0 for present
     - 1 for nearest past
     - 2 for the past before that, and so on
   - The term "past" exists in the present context, not in the archive itself

3. **Frozen Archives**:
   - In the archive scope of each node, all previous protos, time slices, and tics are frozen
   - This ensures historical integrity and prevents retroactive changes

4. **Node Pruning**:
   - When a node in the structure needs to be pruned, it's not deleted
   - Instead, it's shadowed by an innumerable namesake in the present
   - This makes it not directly visible through scope, as scope lists only numerable terms
   - The original node remains in the archive, preserving history

## Relationship to Git

1. **Independence from Git**:
   - Temporal scopes exist independently of Git branches
   - They provide a more fine-grained and structured approach to versioning

2. **Mapping to Git**:
   - Git branches can be mapped to temporal scopes, but this is not required
   - Feature branches always exist in the present temporal scope
   - As the repository evolves, feature branches update to stay in the common present

3. **Advantages over Git Branches**:
   - More precise control over versioning at the node level
   - Better support for parallel development in different parts of the repository
   - Clearer separation between present and historical states

## Implementation

The implementation of temporal scopes involves:

1. **Archive Namespaces**:
   - Each node has a `_.archive` folder or equivalent structure
   - Archives are organized by tic number

2. **Tic Getters**:
   - Proto layers provide methods to access tic numbers
   - These are used for addressing and navigation

3. **Shadowing Mechanism**:
   - When nodes are pruned, they are shadowed rather than deleted
   - This preserves history while maintaining a clean present view

## References

- [Git Branch Management](./0.git_branch_management.md)
- [Temporal Coherence in Git](./2.temporal_coherence_in_git.md)

## Code References

- [Temporal Scope Implementation](../code/2.temporal_scope.ts)
