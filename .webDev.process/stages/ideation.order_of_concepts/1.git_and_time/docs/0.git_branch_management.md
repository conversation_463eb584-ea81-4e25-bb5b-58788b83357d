# Git Branch Management

This document outlines the approach to managing Git branches in the context of our temporal repository structure.

## Overview

While our repository spacetime is independent of Git's branching structure, we need to establish certain conventions to maintain temporal coherence when using Git.

## Branch Organization

Branches are organized in time layers:
- Past layers are frozen
- Present branches are all synchronized
- Feature branches always exist in the present

## Feature Branch Workflow

1. Feature branches are created from the current present state
2. As other nodes tic, feature branches update to stay in the common present
3. Developers issue their own tics in their areas of responsibility
4. When complete, feature branches are merged back into the main branch

## Conflict Resolution

Conflicts are resolved at the conceptual level through a proposal mechanism, minimizing red tape and confusion.

## References

- [Feature Branch Workflow](./1.feature_branch_workflow.md)
- [Temporal Coherence in Git](./2.temporal_coherence_in_git.md)

## Code References

- [Git Integration](../code/0.git_integration.ts)
- [Branch Synchronization](../code/1.branch_synchronization.ts)
