# Git and Time - Summary

## Core Concepts

1. **Temporal Scopes in Repository Structure**:
   - Time forms a temporal scope of a tree
   - Each node has an archive namespace - a scope in each branch through tic layers
   - Each proto layer has a tic getter that returns the tic number (0 for present, 1 for nearest past)
   - In archive scope, all previous protos, time slices, and tics are frozen
   - Pruned nodes are shadowed by innumerable namesakes in present, not directly visible through scope

2. **Git as Implementation Detail**:
   - Git is just an implementation detail for our repository spacetime
   - The graph in persistent storage layer is the source of truth
   - Git history is recorded in our graph structure

3. **Branch Organization**:
   - Branches should be organized in time layers
   - Past layers are frozen
   - Present branches are all synchronized

4. **Feature Branches**:
   - Feature branches always exist in the present
   - As other nodes tic, feature branches update to stay in the common present
   - Developers issue their own tics in their areas of responsibility

5. **Archiving and Git**:
   - Archiving doesn't necessarily map to Git branches
   - We transfer ideation concepts to archive folders rather than using Git branches for archiving
   - This allows for easier referencing in scripts without having to specify Git branches

6. **Addressing Through Temporal Structure**:
   - All addressing is from the perspective of the present time layer
   - References to archived elements use tic indices or absolute paths
   - This is simpler than referencing Git branches and their time slices
   - <PERSON><PERSON> lists only numerable terms, making shadowed nodes invisible

## Practical Implications

- Developers can work in feature branches while maintaining temporal coherence
- Conflicts are resolved at the conceptual level through a proposal mechanism
- The repository structure remains coherent even as Git branches come and go
- Archived content is easily accessible without Git branch manipulation

## Related Documents

- [Git Branch Management](./docs/0.git_branch_management.md)
- [Feature Branch Workflow](./docs/1.feature_branch_workflow.md)
- [Temporal Coherence in Git](./docs/2.temporal_coherence_in_git.md)
- [Temporal Scopes](./docs/3.temporal_scopes.md)

## Code References

- [Git Integration](./code/0.git_integration.ts)
- [Branch Synchronization](./code/1.branch_synchronization.ts)
- [Temporal Scope](./code/2.temporal_scope.ts)
