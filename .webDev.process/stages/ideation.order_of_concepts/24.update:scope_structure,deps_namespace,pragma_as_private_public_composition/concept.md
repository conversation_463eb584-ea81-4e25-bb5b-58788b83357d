# Update: Scope Structure, Deps Namespace, Pragma as Private-Public Composition

## Overview

This concept corrects and updates the previous definition of pragma and API standard from concept 15. It introduces a refined understanding of scope structure where private and public scopes are completely separate, representing two entangled functions being constructed in one document. The filesystem is equivalent to a script, with each folder as a function with its own scope, but here we have two functions defined - each with its own scope.

## Key Insights

1. **Two-Function Composition**: Each node builds a two-function composition - private pragma and public pragma
2. **Entangled Functions**: Private and public functions are entangled, with the return of private pragma feeding into public pragma
3. **Scope Separation**: Private and public scopes are completely separate but reactive
4. **Extension Pattern**: Public pragma extends private pragma
5. **Dependency Namespace**: Dependencies are managed through a deps namespace rather than import statements

## Core Architecture

### Two-Function Composition at Each Node

```
┌─────────────────────────────────────────────┐
│ Node (Folder)                               │
├─────────────────────────────────────────────┤
│                                             │
│ ┌─────────────────┐    ┌─────────────────┐  │
│ │ Private Pragma  │───►│ Public Pragma   │  │
│ │ (from filename) │    │ (local def)     │  │
│ └─────────────────┘    └─────────────────┘  │
│         │                       │          │
│         ▼                       ▼          │
│ Private Scope              Public API       │
│                                             │
└─────────────────────────────────────────────┘
```

### Function Entanglement

1. **Private Pragma**: Defined by extensions in node filename, processed by private linguistic system
2. **Public Pragma**: Defined locally inside the node, extends the private pragma
3. **Composition**: Return of private pragma feeds into public pragma
4. **API**: Return of public pragma becomes the public API of the node

## Pragma Definition Rules

### Private Pragma

- **Definition**: Specified by extensions in node filename (not filepath, just local name)
- **Resolution**: Extensions processed by private linguistic system to resolve to specific private pragma
- **Accessibility**: Must be accessible through private scope of parent node
- **Fallback**: If nothing visible, it's a noop
- **Scope**: Defined in private scope above current node

### Public Pragma

- **Definition**: Defined locally inside the node (if it's a folder)
- **Extension**: Extends the private pragma
- **Accessibility**: Accessible through linguistic system: `l\`pragma of ${myNode}\`` or `l\`pragma of myNode\``
- **Tracking**: Kept track of inside WebDev process closure or upper scope entity
- **Indication**: Indicated by omitting `_` prefix (just `pragma`)

### Entry Points

- **`_pragma`**: Entry point into private pragma composition, interpreted by WebDev process
- **`pragma`**: Starting point for public pragma composition

## Scope Structure

### Private and Public Scopes

```
┌─────────────────────────────────────────────┐
│ WebDev Process Closure                      │
├─────────────────────────────────────────────┤
│                                             │
│ ┌─────────────────┐    ┌─────────────────┐  │
│ │ Private Scope   │    │ Public Scope    │  │
│ │ (TreenityTree)  │    │ (TreenityTree)  │  │
│ └─────────────────┘    └─────────────────┘  │
│                                             │
│ ┌─────────────────┐    ┌─────────────────┐  │
│ │ Private Pragmas │    │ Public Pragmas  │  │
│ │ (TreenityTree)  │    │ (TreenityTree)  │  │
│ └─────────────────┘    └─────────────────┘  │
│                                             │
└─────────────────────────────────────────────┘
```

### Reactive Architecture

- **TreenityTrees**: Private and public pragmas form reactive trees
- **AutoExec**: WebDev runs as autoexec function, reacting to structure modifications
- **State Changes**: Automatic reaction to any modifications of the structure

## Pragma Extension Patterns

### Local Pragma Definitions

```
.obj.pragma          # Local definition of obj pragma (does not travel downscope)
pragma.obj           # Composes obj as defined by obj.pragma upscope or sibling
pragma.funny.obj     # Funny pragma (noop if doesn't exist, but descriptive)
.pragma.funny.obj    # Extends node private pragma, not accessible downscope
```

### Sequential Composition

```
1.pragma.first.obj   # Sequential pragma with index prefix
2.pragma.second.obj  # Ensures composition order
3.pragma.third.obj   # Predictable extension sequence
```

### Protected Pragma

```
_.pragma             # Protected pragma - no node can extend past local node
                     # Creates isolated version spaces
```

## Dependencies and Package Realms

### Package Realm Concept

- **Realm**: Scope/domain for package categorization (org, local, npm, custom)
- **Unique Names**: Within realm, packages have unique names
- **Global Addressing**: Cross-realm addressing through global scheme (IPFS with temporal dimension)
- **Registry**: Each realm has naming registry maintaining uniqueness

### Dependency Resolution

```
myDep.dep_somePackageName           # Injects package into deps namespace under myDep
myDep.dep_somePackageName.path_myPath # Injects into scope directly under myPath
myDep.path_myPath                   # Error - l cannot resolve
```

### Dependency Access

- **No Import Statements**: Dependencies accessed through deps namespace in scope
- **Context Preservation**: Remote packages preserve their local context
- **API Interaction**: Interaction through API and props only
- **Scope Injection**: `scope('myDep')` pattern for dependency access

## Filesystem as Script Abstraction

### Module Mapping

```
┌─────────────────────────────────────────────┐
│ Entire FS = Single Module                   │
├─────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ Folder =    │ │ Folder =    │ │ Folder =│ │
│ │ Function    │ │ Function    │ │ Function│ │
│ │ Definition  │ │ Definition  │ │ Def     │ │
│ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────┘
```

### Monorepo Structure

- **Nested Packages**: Monorepo with nested packages mapped to single module abstraction
- **Scope Construction**: Proper scope construction through package mapping
- **Local vs Remote**: Package categorization (arbitrary but necessary)

## Implementation Details

### Pragma File Structure

Each node can have:

```
node/
├── _pragma              # Private pragma entry point
├── pragma               # Public pragma entry point  
├── pragma.type          # Type definitions (static and dynamic)
├── .obj.pragma          # Local obj pragma definition
└── 1.pragma.first.obj   # Sequential pragma composition
```

### Type Definition

```typescript
// pragma.type
export default interface {
  // Static type definitions
  StaticType: {
    property: string;
    value: any;
  };
  
  // Dynamic type definitions  
  DynamicType: (input: any) => any;
}
```

### Pragma Composition

```typescript
// pragma
export default function publicPragma(privatePragmaResult) {
  // Extend private pragma result
  return {
    ...privatePragmaResult,
    // Additional public functionality
    publicMethod: () => { /* implementation */ }
  };
}
```

## Examples

### Basic Node Structure

```
users/
├── _pragma                    # Private pragma entry point
├── pragma                     # Public pragma extends private
├── pragma.type               # Type definitions
├── query/
│   ├── find.by_status        # Node with private pragma from filename
│   └── pragma                # Public pragma for query operations
└── mutation/
    ├── create                # Node with private pragma from filename  
    └── pragma                # Public pragma for mutation operations
```

### Pragma Resolution

```
# Filename: find.by_status
# Private l processes: find.by_status → FilterOperation pragma
# Public pragma extends FilterOperation with query-specific functionality
# Result: Public API for filtering users by status
```

### Dependency Usage

```typescript
// In scope
deps.myPackage.someFunction()    # Access dependency through deps namespace
deps.localPackage.utilities()    # Local realm package
deps.npmPackage.library()        # NPM realm package
```

## Next Steps

1. **Define Pragma File Format**: Establish syntax for pragma composition
2. **Implement Linguistic Resolution**: Build private/public l systems
3. **Create Dependency System**: Implement deps namespace and realm management
4. **Build Reactive Trees**: Implement TreenityTree for pragma scopes
5. **Establish Type System**: Define static and dynamic type composition

## Conclusion

This updated understanding provides a clearer foundation for pragma and API standard implementation. The two-function composition pattern with entangled private and public pragmas creates a powerful abstraction that maintains separation of concerns while enabling flexible composition and extension patterns. The dependency namespace approach eliminates import statements while providing clean access to external packages across different realms.
