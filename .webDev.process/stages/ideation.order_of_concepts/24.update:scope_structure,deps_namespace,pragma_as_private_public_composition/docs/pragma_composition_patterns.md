# Pragma Composition Patterns

## Overview

This document details the various patterns for composing pragmas in the updated scope structure. It covers private pragma resolution, public pragma extension, and the interaction between the two function compositions at each node.

## Private Pragma Resolution

### Filename Extension Processing

Private pragmas are defined by extensions in the node filename and processed by the private linguistic system:

```
# Filename patterns and their pragma resolutions
find.by_status          → FilterOperation pragma
create.with_defaults    → CreateOperation pragma  
update.by_id           → UpdateOperation pragma
sort.by_name.asc       → SortOperation pragma (chained)
filter.where{active}   → FilterOperation pragma (with condition)
```

### Resolution Process

1. **Extension Parsing**: Private l system parses filename extensions
2. **Pragma Lookup**: Searches for matching pragma in private scope of parent node
3. **Fallback**: If no match found, defaults to noop
4. **Context Binding**: Binds resolved pragma to current node context

### Private Scope Accessibility

```
parent/
├── _pragma/
│   ├── filter.pragma          # FilterOperation definition
│   ├── sort.pragma           # SortOperation definition  
│   └── create.pragma         # CreateOperation definition
└── child/
    ├── find.by_status        # Resolves to parent's filter.pragma
    └── create.with_defaults  # Resolves to parent's create.pragma
```

## Public Pragma Extension

### Local Definition Patterns

Public pragmas are defined locally within nodes and extend private pragmas:

```typescript
// pragma (basic extension)
export default function publicPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    // Additional public functionality
    validate: (input) => { /* validation logic */ },
    transform: (data) => { /* transformation logic */ }
  };
}

// pragma.obj (specific object extension)
export default function objPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    // Object-specific extensions
    properties: extractProperties(privatePragmaResult),
    methods: extractMethods(privatePragmaResult)
  };
}
```

### Extension Hierarchy

```
.obj.pragma              # Local obj pragma (node-scoped)
pragma.obj               # Composed obj pragma (inherits from upscope)
pragma.funny.obj         # Descriptive but noop if funny doesn't exist
.pragma.funny.obj        # Local extension, not accessible downscope
```

### Sequential Composition

```
1.pragma.first.obj       # First in sequence
2.pragma.second.obj      # Second in sequence  
3.pragma.third.obj       # Third in sequence

# Composed in order: first → second → third
```

## Two-Function Entanglement

### Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Node Filename   │───►│ Private Pragma  │───►│ Public Pragma   │
│ Extensions      │    │ Resolution      │    │ Extension       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Private Result  │    │ Public API      │
                       └─────────────────┘    └─────────────────┘
```

### Example Flow

```typescript
// 1. Filename: find.by_status
// 2. Private l resolves to FilterOperation pragma
// 3. Private pragma executes:
function filterOperation(context) {
  return {
    type: 'filter',
    entity: context.entity,
    criteria: context.criteria,
    execute: (data) => data.filter(context.criteria)
  };
}

// 4. Public pragma extends:
function publicPragma(privateResult) {
  return {
    ...privateResult,
    // Public API additions
    validate: (criteria) => validateCriteria(criteria),
    count: (data) => privateResult.execute(data).length,
    paginate: (data, page, size) => {
      const filtered = privateResult.execute(data);
      return filtered.slice(page * size, (page + 1) * size);
    }
  };
}

// 5. Final public API available to consumers
```

## Pragma Definition Syntax

### Basic Pragma Structure

```typescript
// _pragma/filter.pragma
export default function filterPragma(context) {
  return {
    type: 'FilterOperation',
    entity: context.entity,
    
    // Core functionality
    execute: (data, criteria) => {
      return data.filter(item => 
        Object.entries(criteria).every(([key, value]) => 
          item[key] === value
        )
      );
    },
    
    // Metadata
    metadata: {
      operation: 'filter',
      parameters: ['criteria'],
      returns: 'filtered array'
    }
  };
}
```

### Pragma Type Definition

```typescript
// pragma.type
export default interface {
  // Static types
  FilterCriteria: Record<string, any>;
  FilterResult: any[];
  
  // Dynamic types
  FilterOperation: {
    execute: (data: any[], criteria: FilterCriteria) => FilterResult;
    metadata: {
      operation: string;
      parameters: string[];
      returns: string;
    };
  };
  
  // Composition rules
  compose: (pragmas: any[]) => any;
  extend: (base: any, extension: any) => any;
}
```

### Child Pragma Specification

```typescript
// obj.pragma - specifies child pragmas
export default {
  // Child pragma definitions
  children: {
    prop: {
      pragma: 'property',
      type: 'any',
      required: false
    },
    method: {
      pragma: 'function', 
      type: 'function',
      required: false
    }
  },
  
  // Composition rules
  compose: (children) => {
    const obj = {};
    
    // Add properties
    children.filter(c => c.type === 'property')
      .forEach(prop => {
        obj[prop.name] = prop.value;
      });
    
    // Add methods
    children.filter(c => c.type === 'function')
      .forEach(method => {
        obj[method.name] = method.implementation;
      });
    
    return obj;
  }
};
```

## Protected Pragma Pattern

### Isolation with `_.pragma`

```typescript
// _.pragma (protected - cannot be extended past local node)
export default function protectedPragma(context) {
  return {
    // Private implementation
    _internal: {
      secret: 'hidden from children',
      implementation: () => { /* private logic */ }
    },
    
    // Public interface only
    public: {
      method: () => { /* public method */ }
    }
  };
}
```

### Version Space Isolation

```
project/
├── _.pragma              # Protected base pragma
├── module-a/
│   ├── pragma           # Cannot extend parent's _.pragma
│   └── implementation   # Isolated version space
└── module-b/
    ├── pragma           # Cannot extend parent's _.pragma  
    └── implementation   # Separate isolated version space
```

## Linguistic Access Patterns

### Public Pragma Access

```typescript
// Access through linguistic system
const userPragma = l`pragma of users`;
const queryPragma = l`pragma of users.query`;
const filterPragma = l`pragma of users.query.find`;

// Composition through l terms
const composedPragma = l`pragma of ${myNode}`;
```

### Dynamic Pragma Resolution

```typescript
// Runtime pragma resolution
function resolvePragma(nodePath) {
  const node = l`pragma of ${nodePath}`;
  
  if (!node) {
    return noop; // Fallback to noop
  }
  
  return node;
}

// Usage
const dynamicPragma = resolvePragma('users.query.find.by_status');
```

## Error Handling and Fallbacks

### Resolution Failures

```typescript
// When pragma cannot be resolved
function handlePragmaResolution(filename, privateScope) {
  try {
    const pragma = resolvePrivatePragma(filename, privateScope);
    return pragma;
  } catch (error) {
    console.warn(`Pragma resolution failed for ${filename}: ${error.message}`);
    return noop; // Graceful fallback
  }
}

// Noop pragma
const noop = {
  execute: (data) => data, // Pass-through
  metadata: {
    operation: 'noop',
    parameters: [],
    returns: 'unchanged input'
  }
};
```

### Composition Conflicts

```typescript
// Handle conflicting pragma extensions
function resolveCompositionConflicts(pragmas) {
  const conflicts = findConflicts(pragmas);
  
  conflicts.forEach(conflict => {
    console.warn(`Pragma composition conflict: ${conflict.description}`);
    // Apply resolution strategy (last wins, merge, error, etc.)
  });
  
  return mergeWithStrategy(pragmas, 'lastWins');
}
```

## Best Practices

### Pragma Naming

1. **Descriptive Names**: Use clear, descriptive names for pragma files
2. **Consistent Patterns**: Follow consistent naming patterns across the project
3. **Avoid Conflicts**: Ensure pragma names don't conflict within scope
4. **Sequential Ordering**: Use numeric prefixes for ordered composition

### Composition Guidelines

1. **Single Responsibility**: Each pragma should have a single, clear responsibility
2. **Minimal Interface**: Keep pragma interfaces minimal and focused
3. **Composability**: Design pragmas to be easily composable
4. **Error Handling**: Include proper error handling and fallbacks

### Performance Considerations

1. **Lazy Loading**: Load pragmas only when needed
2. **Caching**: Cache resolved pragmas to avoid repeated resolution
3. **Minimal Dependencies**: Keep pragma dependencies minimal
4. **Efficient Composition**: Optimize pragma composition for performance

## Conclusion

The pragma composition patterns provide a powerful and flexible system for building modular, reusable functionality. The two-function entanglement pattern ensures clean separation between private implementation and public interface while maintaining the ability to compose and extend functionality at multiple levels.
