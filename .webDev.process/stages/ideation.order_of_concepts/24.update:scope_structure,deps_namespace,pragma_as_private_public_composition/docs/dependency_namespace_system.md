# Dependency Namespace System

## Overview

This document details the dependency namespace system that replaces traditional import statements in the SpiceTime architecture. Dependencies are managed through a `deps` namespace that provides clean access to external packages across different realms while preserving context isolation.

## Package Realm Concept

### Realm Definition

A **realm** is a scope/domain for package categorization that maintains unique naming within its boundaries:

- **Local Realm**: Packages within the current monorepo
- **NPM Realm**: Packages from the NPM registry  
- **Org Realm**: Organization-specific packages
- **Custom Realms**: User-defined package sources (GitHub, private registries, etc.)

### Global Addressing Scheme

Cross-realm addressing uses a global scheme extending NPM's naming system:

```
# Within realm (short form)
myPackage

# Cross-realm (full form)  
realm://npm/lodash@4.17.21
realm://local/utils@1.0.0
realm://github/user/repo@main
realm://ipfs/QmHash@timestamp
```

### Realm Registry

Each realm maintains a naming registry ensuring uniqueness:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Local Registry  │    │ NPM Registry    │    │ Org Registry    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ utils@1.0.0     │    │ lodash@4.17.21  │    │ design@2.1.0    │
│ helpers@2.0.0   │    │ react@18.2.0    │    │ auth@1.5.0      │
│ components@1.1  │    │ express@4.18.2  │    │ api@3.0.0       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Dependency Access Patterns

### Basic Namespace Access

Dependencies are accessed through the `deps` namespace in scope:

```typescript
// Traditional import (NOT used)
// import { someFunction } from 'myPackage';

// SpiceTime dependency access
deps.myPackage.someFunction();
deps.localUtils.helpers.formatDate();
deps.lodash.map(array, fn);
```

### Dependency Injection Patterns

```typescript
// Inject package into deps namespace under custom name
myDep.dep_somePackageName           # → deps.myDep
myDep.dep_lodash                    # → deps.myDep (lodash functionality)

// Inject with path mapping
myDep.dep_somePackageName.path_myPath  # → scope.myPath (direct scope injection)
myDep.dep_lodash.path_utilities        # → scope.utilities (lodash as utilities)

// Error case - path without dep
myDep.path_myPath                   # Error: l cannot resolve
```

### Scope Injection Examples

```typescript
// Example node with dependency injection
users/
├── myUtils.dep_lodash              # Injects lodash as deps.myUtils
├── helpers.dep_localUtils.path_h   # Injects localUtils as scope.h  
└── query/
    └── find.by_status              # Can access deps.myUtils.map()
                                    # Can access scope.h.formatDate()
```

## Context Preservation

### Remote Package Isolation

Remote packages preserve their local context and interact only through API and props:

```typescript
// Remote package maintains its own context
class RemotePackage {
  private localContext = {
    config: { /* package-specific config */ },
    state: { /* package-specific state */ },
    cache: { /* package-specific cache */ }
  };
  
  // Public API only
  public api = {
    method1: (input) => this.processWithLocalContext(input),
    method2: (data) => this.transformWithLocalContext(data)
  };
  
  private processWithLocalContext(input) {
    // Uses package's own context, not consuming node's context
    return this.localContext.processor.process(input);
  }
}

// Consuming node accesses only the public API
deps.remotePackage.api.method1(data);
```

### API and Props Interaction

```typescript
// Package interaction through well-defined interfaces
interface PackageAPI {
  // Methods available to consumers
  process: (input: any, options?: any) => any;
  configure: (config: any) => void;
  getStatus: () => string;
}

interface PackageProps {
  // Configuration passed to package
  config: any;
  environment: string;
  callbacks?: {
    onSuccess?: (result: any) => void;
    onError?: (error: Error) => void;
  };
}

// Usage in consuming node
const result = deps.myPackage.process(data, {
  config: myConfig,
  environment: 'production',
  callbacks: {
    onSuccess: (result) => handleSuccess(result),
    onError: (error) => handleError(error)
  }
});
```

## Dependency Resolution

### Resolution Algorithm

```typescript
function resolveDependency(depPath: string) {
  // Parse dependency path
  const [realm, packageName, version] = parseDependencyPath(depPath);
  
  // Check local cache
  const cached = dependencyCache.get(`${realm}:${packageName}@${version}`);
  if (cached) return cached;
  
  // Resolve from appropriate realm
  const resolver = getRealmResolver(realm);
  const package = resolver.resolve(packageName, version);
  
  // Cache and return
  dependencyCache.set(`${realm}:${packageName}@${version}`, package);
  return package;
}

// Realm-specific resolvers
const realmResolvers = {
  local: new LocalRealmResolver(),
  npm: new NPMRealmResolver(), 
  org: new OrgRealmResolver(),
  github: new GitHubRealmResolver(),
  ipfs: new IPFSRealmResolver()
};
```

### PNPM-Style Resolution

Adapting PNPM's resolution strategy for our scope system:

```typescript
// Dependency resolution similar to PNPM
class DependencyResolver {
  resolveForNode(nodePath: string, depName: string) {
    // Start from current node and walk up the tree
    let currentPath = nodePath;
    
    while (currentPath) {
      const nodeManifest = this.getNodeManifest(currentPath);
      
      // Check direct dependencies
      if (nodeManifest.dependencies[depName]) {
        return this.resolveDependency(nodeManifest.dependencies[depName]);
      }
      
      // Check parent node
      currentPath = this.getParentPath(currentPath);
    }
    
    // Check global dependencies
    return this.resolveGlobalDependency(depName);
  }
  
  private getNodeManifest(nodePath: string) {
    // Read pragma.deps or package.json equivalent
    return readManifest(`${nodePath}/pragma.deps`);
  }
}
```

## Dependency Declaration

### Pragma Deps File

```typescript
// pragma.deps (equivalent to package.json dependencies)
export default {
  // Local realm dependencies
  local: {
    'utils': '^1.0.0',
    'components': '^2.1.0',
    'helpers': '~1.5.0'
  },
  
  // NPM realm dependencies  
  npm: {
    'lodash': '^4.17.21',
    'react': '^18.2.0',
    'express': '^4.18.2'
  },
  
  // Organization realm dependencies
  org: {
    'design-system': '^2.0.0',
    'auth-lib': '^1.3.0'
  },
  
  // Custom realm dependencies
  github: {
    'user/special-lib': 'main',
    'org/internal-tool': 'v2.1.0'
  }
};
```

### Alternative: Deps Section in Pragma

```typescript
// pragma (with deps section)
export default {
  // Main pragma functionality
  implementation: (context) => {
    // Use dependencies through deps namespace
    const formatted = deps.lodash.map(context.data, deps.utils.formatter);
    return formatted;
  },
  
  // Dependencies declaration
  deps: {
    local: ['utils@^1.0.0'],
    npm: ['lodash@^4.17.21'],
    org: ['design-system@^2.0.0']
  }
};
```

## Advanced Dependency Patterns

### Conditional Dependencies

```typescript
// Conditional dependency loading
export default {
  implementation: (context) => {
    // Load different dependencies based on environment
    if (context.environment === 'development') {
      const devTools = deps.devUtils.debugger;
      devTools.log('Development mode active');
    }
    
    // Load optional dependencies
    if (deps.optionalFeature) {
      return deps.optionalFeature.enhance(context.data);
    }
    
    return context.data;
  },
  
  deps: {
    npm: ['lodash@^4.17.21'],
    optional: {
      npm: ['debug-tools@^1.0.0'],
      condition: 'development'
    }
  }
};
```

### Dependency Composition

```typescript
// Compose multiple dependencies
export default {
  implementation: (context) => {
    // Create composed functionality from multiple deps
    const processor = composeProcessors([
      deps.validator.validate,
      deps.transformer.transform,
      deps.formatter.format
    ]);
    
    return processor(context.data);
  },
  
  deps: {
    local: ['validator@^1.0.0', 'transformer@^2.0.0'],
    npm: ['formatter@^1.5.0']
  }
};

function composeProcessors(processors) {
  return (data) => {
    return processors.reduce((result, processor) => {
      return processor(result);
    }, data);
  };
}
```

### Dependency Injection with Configuration

```typescript
// Configure dependencies at injection time
myProcessor.dep_dataValidator.config{strict:true}
myFormatter.dep_dateLib.config{locale:"en-US",timezone:"UTC"}

// Results in configured dependency access
deps.myProcessor.validate(data, { strict: true });
deps.myFormatter.format(date, { locale: "en-US", timezone: "UTC" });
```

## Error Handling

### Missing Dependencies

```typescript
function handleMissingDependency(depName: string, nodePath: string) {
  const error = new DependencyError(
    `Dependency '${depName}' not found for node '${nodePath}'`
  );
  
  // Attempt graceful fallback
  const fallback = findFallbackDependency(depName);
  if (fallback) {
    console.warn(`Using fallback dependency '${fallback}' for '${depName}'`);
    return resolveDependency(fallback);
  }
  
  // No fallback available
  throw error;
}
```

### Version Conflicts

```typescript
function resolveVersionConflicts(conflicts: VersionConflict[]) {
  conflicts.forEach(conflict => {
    const resolution = selectResolutionStrategy(conflict);
    
    switch (resolution.strategy) {
      case 'latest':
        conflict.resolve(conflict.versions.sort().pop());
        break;
      case 'semver':
        conflict.resolve(findBestSemverMatch(conflict.versions, conflict.constraint));
        break;
      case 'explicit':
        conflict.resolve(resolution.explicitVersion);
        break;
    }
  });
}
```

## Performance Optimizations

### Lazy Loading

```typescript
// Lazy dependency loading
const lazyDeps = {
  get heavyLibrary() {
    if (!this._heavyLibrary) {
      this._heavyLibrary = resolveDependency('heavy-library@^2.0.0');
    }
    return this._heavyLibrary;
  }
};

// Usage
if (needsHeavyProcessing) {
  result = lazyDeps.heavyLibrary.process(data);
}
```

### Dependency Caching

```typescript
class DependencyCache {
  private cache = new Map<string, any>();
  private ttl = new Map<string, number>();
  
  get(key: string) {
    if (this.isExpired(key)) {
      this.cache.delete(key);
      this.ttl.delete(key);
      return null;
    }
    return this.cache.get(key);
  }
  
  set(key: string, value: any, ttlMs = 3600000) { // 1 hour default
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }
  
  private isExpired(key: string): boolean {
    const expiry = this.ttl.get(key);
    return expiry ? Date.now() > expiry : false;
  }
}
```

## Integration with Package.json

### Compatibility Layer

```typescript
// Read existing package.json for NPM dependencies
function loadNPMDependencies(packageJsonPath: string) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  return {
    dependencies: packageJson.dependencies || {},
    devDependencies: packageJson.devDependencies || {},
    peerDependencies: packageJson.peerDependencies || {}
  };
}

// Convert to pragma.deps format
function convertToDepsPragma(packageJson: any) {
  return {
    npm: {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    },
    peer: packageJson.peerDependencies || {}
  };
}
```

### Migration Strategy

```typescript
// Gradual migration from import statements to deps namespace
function migrateImportsToDepNamespace(sourceCode: string) {
  // Replace import statements with deps namespace access
  return sourceCode
    .replace(/import\s+{\s*([^}]+)\s*}\s+from\s+['"]([^'"]+)['"]/g, 
      (match, imports, packageName) => {
        const cleanPackageName = packageName.replace(/[^a-zA-Z0-9]/g, '');
        return `// Migrated: ${match}\n// Use: deps.${cleanPackageName}.${imports}`;
      })
    .replace(/import\s+(\w+)\s+from\s+['"]([^'"]+)['"]/g,
      (match, defaultImport, packageName) => {
        const cleanPackageName = packageName.replace(/[^a-zA-Z0-9]/g, '');
        return `// Migrated: ${match}\n// Use: deps.${cleanPackageName}`;
      });
}
```

## Conclusion

The dependency namespace system provides a clean, context-preserving approach to package management that eliminates import statements while maintaining clear separation between local and external code. The realm-based organization and PNPM-style resolution ensure efficient and predictable dependency management across the entire SpiceTime architecture.
