/**
 * Pragma File Format Examples
 * 
 * This file demonstrates various pragma file formats and composition patterns
 * based on the updated scope structure and private-public composition model.
 */

// ============================================================================
// BASIC PRAGMA STRUCTURE
// ============================================================================

// _pragma/filter.pragma - Private pragma definition
export default function filterPragma(context) {
  return {
    type: 'FilterOperation',
    entity: context.entity,
    
    // Core filtering functionality
    execute: (data, criteria) => {
      return data.filter(item => 
        Object.entries(criteria).every(([key, value]) => 
          item[key] === value
        )
      );
    },
    
    // Metadata for introspection
    metadata: {
      operation: 'filter',
      parameters: ['data', 'criteria'],
      returns: 'filtered array',
      reactive: true
    },
    
    // Validation rules
    validate: (criteria) => {
      if (typeof criteria !== 'object') {
        throw new Error('Filter criteria must be an object');
      }
      return true;
    }
  };
}

// ============================================================================
// PUBLIC PRAGMA EXTENSION
// ============================================================================

// pragma - Public pragma that extends private pragma
export default function publicPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Additional public functionality
    count: (data, criteria) => {
      const filtered = privatePragmaResult.execute(data, criteria);
      return filtered.length;
    },
    
    paginate: (data, criteria, page = 0, size = 10) => {
      const filtered = privatePragmaResult.execute(data, criteria);
      const start = page * size;
      const end = start + size;
      return {
        data: filtered.slice(start, end),
        total: filtered.length,
        page,
        size,
        hasNext: end < filtered.length,
        hasPrev: page > 0
      };
    },
    
    // Enhanced validation with public-specific rules
    validate: (criteria) => {
      // Call private validation first
      privatePragmaResult.validate(criteria);
      
      // Add public-specific validation
      const allowedKeys = ['status', 'role', 'department', 'active'];
      const invalidKeys = Object.keys(criteria).filter(key => 
        !allowedKeys.includes(key)
      );
      
      if (invalidKeys.length > 0) {
        throw new Error(`Invalid filter keys: ${invalidKeys.join(', ')}`);
      }
      
      return true;
    },
    
    // Public API metadata
    publicMetadata: {
      version: '1.0.0',
      author: 'SpiceTime Architecture',
      description: 'Enhanced filtering with pagination and validation',
      examples: [
        {
          description: 'Filter active users',
          code: 'filter(users, { active: true })'
        },
        {
          description: 'Paginated filter results',
          code: 'paginate(users, { role: "admin" }, 0, 5)'
        }
      ]
    }
  };
}

// ============================================================================
// PRAGMA TYPE DEFINITIONS
// ============================================================================

// pragma.type - Type definitions for the pragma
export default interface {
  // Static type definitions
  FilterCriteria: Record<string, any>;
  FilterResult: any[];
  PaginationResult: {
    data: any[];
    total: number;
    page: number;
    size: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  
  // Dynamic type definitions (runtime types)
  FilterOperation: {
    execute: (data: any[], criteria: FilterCriteria) => FilterResult;
    count: (data: any[], criteria: FilterCriteria) => number;
    paginate: (data: any[], criteria: FilterCriteria, page?: number, size?: number) => PaginationResult;
    validate: (criteria: FilterCriteria) => boolean;
    metadata: {
      operation: string;
      parameters: string[];
      returns: string;
      reactive: boolean;
    };
    publicMetadata: {
      version: string;
      author: string;
      description: string;
      examples: Array<{
        description: string;
        code: string;
      }>;
    };
  };
  
  // Composition rules
  compose: (pragmas: any[]) => any;
  extend: (base: any, extension: any) => any;
}

// ============================================================================
// OBJECT PRAGMA WITH CHILD SPECIFICATIONS
// ============================================================================

// obj.pragma - Object pragma that specifies child pragmas
export default {
  // Child pragma specifications
  children: {
    prop: {
      pragma: 'property',
      type: 'any',
      required: false,
      description: 'Object property'
    },
    method: {
      pragma: 'function',
      type: 'function', 
      required: false,
      description: 'Object method'
    },
    getter: {
      pragma: 'getter',
      type: 'function',
      required: false,
      description: 'Property getter'
    },
    setter: {
      pragma: 'setter', 
      type: 'function',
      required: false,
      description: 'Property setter'
    }
  },
  
  // Composition rules for building objects
  compose: (children) => {
    const obj = {};
    const descriptors = {};
    
    // Add properties
    children.filter(c => c.pragma === 'property')
      .forEach(prop => {
        obj[prop.name] = prop.value;
      });
    
    // Add methods
    children.filter(c => c.pragma === 'function')
      .forEach(method => {
        obj[method.name] = method.implementation;
      });
    
    // Add getters and setters
    children.filter(c => c.pragma === 'getter')
      .forEach(getter => {
        descriptors[getter.name] = {
          ...descriptors[getter.name],
          get: getter.implementation,
          enumerable: true,
          configurable: true
        };
      });
    
    children.filter(c => c.pragma === 'setter')
      .forEach(setter => {
        descriptors[setter.name] = {
          ...descriptors[setter.name],
          set: setter.implementation,
          enumerable: true,
          configurable: true
        };
      });
    
    // Apply property descriptors
    Object.defineProperties(obj, descriptors);
    
    return obj;
  },
  
  // Validation rules for object composition
  validate: (children) => {
    // Check for naming conflicts
    const names = children.map(c => c.name);
    const duplicates = names.filter((name, index) => names.indexOf(name) !== index);
    
    if (duplicates.length > 0) {
      throw new Error(`Duplicate property names: ${duplicates.join(', ')}`);
    }
    
    // Validate getter/setter pairs
    const getters = children.filter(c => c.pragma === 'getter').map(c => c.name);
    const setters = children.filter(c => c.pragma === 'setter').map(c => c.name);
    
    getters.forEach(getter => {
      if (children.find(c => c.name === getter && c.pragma === 'property')) {
        throw new Error(`Property '${getter}' conflicts with getter of same name`);
      }
    });
    
    return true;
  },
  
  // Metadata
  metadata: {
    type: 'ObjectComposition',
    description: 'Composes objects from child pragmas',
    supportedChildren: ['property', 'function', 'getter', 'setter']
  }
};

// ============================================================================
// SEQUENTIAL PRAGMA COMPOSITION
// ============================================================================

// 1.pragma.first.validation - First in sequence
export default function firstValidation(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // First validation step
    validateInput: (input) => {
      if (!input) {
        throw new Error('Input is required');
      }
      
      if (typeof input !== 'object') {
        throw new Error('Input must be an object');
      }
      
      return input;
    },
    
    sequence: 1,
    step: 'input-validation'
  };
}

// 2.pragma.second.transformation - Second in sequence  
export default function secondTransformation(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Second transformation step
    transform: (validatedInput) => {
      // Normalize property names
      const normalized = {};
      Object.entries(validatedInput).forEach(([key, value]) => {
        const normalizedKey = key.toLowerCase().replace(/[^a-z0-9]/g, '_');
        normalized[normalizedKey] = value;
      });
      
      return normalized;
    },
    
    sequence: 2,
    step: 'transformation'
  };
}

// 3.pragma.third.output - Third in sequence
export default function thirdOutput(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Final output step
    formatOutput: (transformedData) => {
      return {
        data: transformedData,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        processed: true
      };
    },
    
    sequence: 3,
    step: 'output-formatting'
  };
}

// ============================================================================
// PROTECTED PRAGMA (ISOLATED VERSION SPACE)
// ============================================================================

// _.pragma - Protected pragma that cannot be extended past local node
export default function protectedPragma(context) {
  // Private implementation details
  const _internal = {
    secret: 'hidden-from-children',
    privateState: new Map(),
    
    internalMethod: (data) => {
      // Private processing logic
      return data.map(item => ({
        ...item,
        _processed: true,
        _timestamp: Date.now()
      }));
    }
  };
  
  return {
    // Public interface only - internal details are hidden
    process: (data) => {
      // Validate access
      if (!context.authorized) {
        throw new Error('Unauthorized access to protected pragma');
      }
      
      // Use internal method but don't expose it
      return _internal.internalMethod(data);
    },
    
    configure: (config) => {
      // Allow configuration but protect internal state
      if (config.secret) {
        _internal.secret = config.secret;
      }
      
      return {
        configured: true,
        timestamp: Date.now()
      };
    },
    
    // Metadata indicates this is protected
    metadata: {
      type: 'ProtectedPragma',
      isolated: true,
      extensible: false,
      description: 'Protected pragma with isolated version space'
    }
  };
}

// ============================================================================
// DEPENDENCY PRAGMA WITH DEPS NAMESPACE
// ============================================================================

// pragma.with.deps - Pragma that uses dependencies
export default {
  // Dependency declarations
  deps: {
    local: {
      'utils': '^1.0.0',
      'validators': '^2.1.0'
    },
    npm: {
      'lodash': '^4.17.21',
      'moment': '^2.29.4'
    },
    org: {
      'design-system': '^2.0.0'
    }
  },
  
  // Implementation using dependencies through deps namespace
  implementation: (privatePragmaResult) => {
    return {
      ...privatePragmaResult,
      
      // Use local dependencies
      validate: (data) => {
        return deps.validators.validateObject(data, {
          required: ['id', 'name'],
          types: { id: 'string', name: 'string' }
        });
      },
      
      // Use NPM dependencies
      process: (data) => {
        const processed = deps.lodash.map(data, item => ({
          ...item,
          processedAt: deps.moment().toISOString(),
          formatted: deps.utils.formatters.standardize(item)
        }));
        
        return processed;
      },
      
      // Use organization dependencies
      render: (data) => {
        return deps.designSystem.components.DataTable({
          data,
          theme: deps.designSystem.themes.default,
          pagination: true
        });
      }
    };
  },
  
  // Dependency injection configuration
  injection: {
    // Custom names for dependencies
    myUtils: 'dep_utils',
    lodash: 'dep_lodash.path_utilities',
    
    // Conditional dependencies
    conditionalDeps: {
      development: ['dep_debugTools'],
      production: ['dep_analytics']
    }
  }
};

// ============================================================================
// LINGUISTIC ACCESS PRAGMA
// ============================================================================

// pragma.linguistic - Pragma accessible through linguistic system
export default function linguisticPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Register with linguistic system
    registerWithL: () => {
      // Make this pragma accessible via l`pragma of myNode`
      l.register('pragma', 'myNode', this);
    },
    
    // Linguistic composition
    composeWith: (otherNodeName) => {
      const otherPragma = l`pragma of ${otherNodeName}`;
      
      if (!otherPragma) {
        console.warn(`Pragma for '${otherNodeName}' not found`);
        return this;
      }
      
      // Compose with other pragma
      return {
        ...this,
        ...otherPragma,
        composed: true,
        composedWith: otherNodeName
      };
    },
    
    // Dynamic pragma resolution
    resolveDynamic: (pragmaPath) => {
      try {
        return l`pragma of ${pragmaPath}`;
      } catch (error) {
        console.warn(`Failed to resolve pragma '${pragmaPath}': ${error.message}`);
        return null;
      }
    }
  };
}

// ============================================================================
// ERROR HANDLING AND FALLBACK PRAGMA
// ============================================================================

// pragma.fallback - Pragma with error handling and fallbacks
export default function fallbackPragma(privatePragmaResult) {
  return {
    ...privatePragmaResult,
    
    // Safe execution with fallbacks
    safeExecute: (operation, data, fallbackValue = null) => {
      try {
        if (typeof operation === 'function') {
          return operation(data);
        } else if (typeof operation === 'string' && this[operation]) {
          return this[operation](data);
        } else {
          throw new Error(`Unknown operation: ${operation}`);
        }
      } catch (error) {
        console.error(`Operation failed: ${error.message}`);
        
        // Try fallback operation
        if (this.fallbackOperation) {
          try {
            return this.fallbackOperation(data);
          } catch (fallbackError) {
            console.error(`Fallback operation failed: ${fallbackError.message}`);
          }
        }
        
        // Return fallback value
        return fallbackValue;
      }
    },
    
    // Graceful degradation
    gracefulDegrade: (requiredFeatures, data) => {
      const availableFeatures = requiredFeatures.filter(feature => 
        typeof this[feature] === 'function'
      );
      
      if (availableFeatures.length === 0) {
        console.warn('No required features available, using minimal functionality');
        return this.minimal(data);
      }
      
      // Use available features
      return availableFeatures.reduce((result, feature) => {
        try {
          return this[feature](result);
        } catch (error) {
          console.warn(`Feature '${feature}' failed: ${error.message}`);
          return result;
        }
      }, data);
    },
    
    // Minimal fallback functionality
    minimal: (data) => {
      // Absolute minimum functionality - just pass through
      return Array.isArray(data) ? [...data] : { ...data };
    }
  };
}

// ============================================================================
// EXPORT ALL EXAMPLES
// ============================================================================

export {
  filterPragma,
  publicPragma,
  firstValidation,
  secondTransformation, 
  thirdOutput,
  protectedPragma,
  linguisticPragma,
  fallbackPragma
};
