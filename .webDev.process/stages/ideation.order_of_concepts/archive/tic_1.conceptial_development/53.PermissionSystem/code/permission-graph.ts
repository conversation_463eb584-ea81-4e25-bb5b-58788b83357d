/**
 * Permission Graph Structure
 * 
 * This file defines the core structure of the permission graph,
 * including nodes, edges, and operations on the graph.
 */

// Types of entities in the permission system
type EntityType = 'user' | 'organization' | 'resource' | 'group' | 'role';

// Types of permission links
type LinkType = 'membership' | 'access' | 'ownership' | 'delegation' | 'representation';

// Access levels for resources
type AccessLevel = 'read' | 'write' | 'admin';

// Base entity interface
interface Entity {
  id: string;
  type: EntityType;
  name: string;
  metadata: Record<string, any>;
}

// Base permission link interface
interface PermissionLink {
  id: string;
  sourceId: string;
  targetId: string;
  linkType: LinkType;
  createdAt: number;
  expiresAt: number | null;
  isRevoked: boolean;
  revokedAt: number | null;
  constraints: LinkConstraint[];
}

// Specific link types
interface MembershipLink extends PermissionLink {
  linkType: 'membership';
  role: 'admin' | 'member' | 'viewer';
}

interface AccessLink extends PermissionLink {
  linkType: 'access';
  accessLevel: AccessLevel;
}

interface DelegationLink extends PermissionLink {
  linkType: 'delegation';
  delegatedScopes: string[];
  maxUses: number | null;
  usageCount: number;
}

// Link constraints
type LinkConstraint = 
  | TimeConstraint
  | LocationConstraint
  | DeviceConstraint;

interface TimeConstraint {
  type: 'time';
  allowedDays?: number[]; // 0-6, where 0 is Sunday
  allowedHours?: [number, number][]; // Ranges of hours in 24h format
  timeZone?: string;
}

interface LocationConstraint {
  type: 'location';
  allowedRegions?: string[]; // ISO country/region codes
  allowedCoordinates?: {
    lat: number;
    lng: number;
    radiusKm: number;
  }[];
}

interface DeviceConstraint {
  type: 'device';
  allowedDeviceIds?: string[];
  allowedDeviceTypes?: string[];
}

/**
 * Permission Graph class
 * 
 * Represents the entire permission structure as a directed graph.
 */
class PermissionGraph {
  private nodes: Map<string, Entity> = new Map();
  private outgoingEdges: Map<string, Map<string, PermissionLink>> = new Map();
  private incomingEdges: Map<string, Map<string, PermissionLink>> = new Map();
  
  /**
   * Add a node to the graph
   */
  addNode(entity: Entity): void {
    this.nodes.set(entity.id, entity);
    
    // Initialize edge maps if they don't exist
    if (!this.outgoingEdges.has(entity.id)) {
      this.outgoingEdges.set(entity.id, new Map());
    }
    
    if (!this.incomingEdges.has(entity.id)) {
      this.incomingEdges.set(entity.id, new Map());
    }
  }
  
  /**
   * Add a permission link to the graph
   */
  addLink(link: PermissionLink): void {
    // Ensure nodes exist
    if (!this.nodes.has(link.sourceId) || !this.nodes.has(link.targetId)) {
      throw new Error(`Cannot add link: nodes do not exist`);
    }
    
    // Add to outgoing edges
    if (!this.outgoingEdges.has(link.sourceId)) {
      this.outgoingEdges.set(link.sourceId, new Map());
    }
    this.outgoingEdges.get(link.sourceId)!.set(link.id, link);
    
    // Add to incoming edges
    if (!this.incomingEdges.has(link.targetId)) {
      this.incomingEdges.set(link.targetId, new Map());
    }
    this.incomingEdges.get(link.targetId)!.set(link.id, link);
  }
  
  /**
   * Remove a permission link from the graph
   */
  removeLink(linkId: string): void {
    // Find the link
    for (const [sourceId, links] of this.outgoingEdges.entries()) {
      if (links.has(linkId)) {
        const link = links.get(linkId)!;
        
        // Remove from outgoing edges
        links.delete(linkId);
        
        // Remove from incoming edges
        this.incomingEdges.get(link.targetId)?.delete(linkId);
        
        return;
      }
    }
    
    throw new Error(`Link ${linkId} not found`);
  }
  
  /**
   * Update a permission link
   */
  updateLink(linkId: string, updates: Partial<PermissionLink>): void {
    // Find the link
    for (const [sourceId, links] of this.outgoingEdges.entries()) {
      if (links.has(linkId)) {
        const link = links.get(linkId)!;
        
        // Update the link
        const updatedLink = { ...link, ...updates };
        
        // Replace in outgoing edges
        links.set(linkId, updatedLink);
        
        // Replace in incoming edges
        this.incomingEdges.get(link.targetId)?.set(linkId, updatedLink);
        
        return;
      }
    }
    
    throw new Error(`Link ${linkId} not found`);
  }
  
  /**
   * Find all paths between two nodes
   */
  findAllPaths(
    sourceId: string,
    targetId: string,
    maxDepth: number = 5
  ): PermissionLink[][] {
    const paths: PermissionLink[][] = [];
    
    const visited = new Set<string>();
    const currentPath: PermissionLink[] = [];
    
    const dfs = (currentId: string, depth: number) => {
      if (depth > maxDepth) return;
      if (currentId === targetId) {
        paths.push([...currentPath]);
        return;
      }
      
      visited.add(currentId);
      
      const outgoing = this.outgoingEdges.get(currentId);
      if (!outgoing) return;
      
      for (const link of outgoing.values()) {
        if (!link.isRevoked && (!link.expiresAt || link.expiresAt > Date.now())) {
          if (!visited.has(link.targetId)) {
            currentPath.push(link);
            dfs(link.targetId, depth + 1);
            currentPath.pop();
          }
        }
      }
      
      visited.delete(currentId);
    };
    
    dfs(sourceId, 0);
    
    return paths;
  }
  
  /**
   * Check if a permission exists between two entities
   */
  hasPermission(
    sourceId: string,
    targetId: string,
    linkType: LinkType,
    properties: Partial<PermissionLink> = {}
  ): boolean {
    // Check direct links
    const outgoing = this.outgoingEdges.get(sourceId);
    if (outgoing) {
      for (const link of outgoing.values()) {
        if (link.targetId === targetId && 
            link.linkType === linkType && 
            !link.isRevoked &&
            (!link.expiresAt || link.expiresAt > Date.now()) &&
            this.linkMatchesProperties(link, properties)) {
          return true;
        }
      }
    }
    
    // Check for transitive permissions
    if (linkType === 'access') {
      // Check membership + access path
      const membershipLinks = this.getOutgoingLinksByType(sourceId, 'membership');
      
      for (const membershipLink of membershipLinks) {
        if (membershipLink.isRevoked || 
            (membershipLink.expiresAt && membershipLink.expiresAt < Date.now())) {
          continue;
        }
        
        const groupId = membershipLink.targetId;
        const accessLinks = this.getOutgoingLinksByType(groupId, 'access');
        
        for (const accessLink of accessLinks) {
          if (accessLink.targetId === targetId && 
              !accessLink.isRevoked &&
              (!accessLink.expiresAt || accessLink.expiresAt > Date.now()) &&
              this.linkMatchesProperties(accessLink, properties)) {
            return true;
          }
        }
      }
    }
    
    return false;
  }
  
  /**
   * Get all outgoing links of a specific type
   */
  private getOutgoingLinksByType(
    sourceId: string,
    linkType: LinkType
  ): PermissionLink[] {
    const outgoing = this.outgoingEdges.get(sourceId);
    if (!outgoing) return [];
    
    return Array.from(outgoing.values())
      .filter(link => link.linkType === linkType);
  }
  
  /**
   * Check if a link matches the specified properties
   */
  private linkMatchesProperties(
    link: PermissionLink,
    properties: Partial<PermissionLink>
  ): boolean {
    for (const [key, value] of Object.entries(properties)) {
      // Special handling for access level
      if (key === 'accessLevel' && link.linkType === 'access') {
        const accessLink = link as AccessLink;
        const requiredLevel = value as AccessLevel;
        
        if (requiredLevel === 'read') {
          // Any access level satisfies 'read'
          if (['read', 'write', 'admin'].includes(accessLink.accessLevel)) {
            continue;
          }
        } else if (requiredLevel === 'write') {
          // 'write' or 'admin' satisfies 'write'
          if (['write', 'admin'].includes(accessLink.accessLevel)) {
            continue;
          }
        } else if (requiredLevel === 'admin') {
          // Only 'admin' satisfies 'admin'
          if (accessLink.accessLevel === 'admin') {
            continue;
          }
        }
        
        return false;
      }
      
      // For other properties, exact match is required
      if (link[key] !== value) {
        return false;
      }
    }
    
    // Check constraints
    if (!this.linkSatisfiesConstraints(link)) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Check if a link satisfies its constraints
   */
  private linkSatisfiesConstraints(link: PermissionLink): boolean {
    const now = new Date();
    
    for (const constraint of link.constraints) {
      switch (constraint.type) {
        case 'time':
          // Check time constraints
          if (constraint.allowedDays && 
              !constraint.allowedDays.includes(now.getDay())) {
            return false;
          }
          
          if (constraint.allowedHours) {
            const currentHour = now.getHours();
            const isInAllowedHours = constraint.allowedHours.some(
              ([start, end]) => currentHour >= start && currentHour < end
            );
            
            if (!isInAllowedHours) {
              return false;
            }
          }
          break;
          
        case 'location':
          // Location constraints would require additional context
          // This is a placeholder for the implementation
          break;
          
        case 'device':
          // Check device constraints
          if (constraint.allowedDeviceIds && 
              !constraint.allowedDeviceIds.includes(getCurrentDeviceId())) {
            return false;
          }
          break;
      }
    }
    
    return true;
  }
}

// Helper function to get the current device ID
function getCurrentDeviceId(): string {
  // In a real implementation, this would get the device ID from the environment
  return 'current-device-id';
}

export {
  PermissionGraph,
  Entity,
  PermissionLink,
  MembershipLink,
  AccessLink,
  DelegationLink,
  LinkConstraint,
  EntityType,
  LinkType,
  AccessLevel
};
