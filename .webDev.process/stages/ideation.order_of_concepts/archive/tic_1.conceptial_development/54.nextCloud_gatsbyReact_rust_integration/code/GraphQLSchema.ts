/**
 * GraphQLSchema.ts
 * 
 * GraphQL schema definition for the NextCloud-Gatsby-Rust integration.
 * This schema provides a clean abstraction layer between the domain model
 * and the implementation details of each integration axis.
 */

import { gql } from 'apollo-server';

/**
 * GraphQL schema
 */
export const typeDefs = gql`
  """
  Time coordinate in the SpiceTime system
  """
  type TimeCoordinate {
    """
    Timestamp in ISO format
    """
    timestamp: String!
    
    """
    Duration in milliseconds
    """
    duration: Int
    
    """
    Time zone
    """
    timeZone: String
    
    """
    Time scale (e.g., 'linear', 'logarithmic')
    """
    scale: String
  }
  
  """
  Space coordinate in the SpiceTime system
  """
  type SpaceCoordinate {
    """
    Location name
    """
    location: String
    
    """
    Geographic coordinates
    """
    coordinates: GeoCoordinates
    
    """
    Space scale (e.g., 'local', 'global')
    """
    scale: String
  }
  
  """
  Geographic coordinates
  """
  type GeoCoordinates {
    """
    Latitude
    """
    latitude: Float!
    
    """
    Longitude
    """
    longitude: Float!
    
    """
    Altitude in meters
    """
    altitude: Float
  }
  
  """
  Category coordinate in the SpiceTime system
  """
  type CategoryCoordinate {
    """
    Category ID
    """
    categoryId: ID!
    
    """
    Category name
    """
    categoryName: String!
    
    """
    Category weight (0.0 to 1.0)
    """
    weight: Float!
    
    """
    Parent category
    """
    parent: CategoryCoordinate
  }
  
  """
  Integration axis values
  """
  type AxisValues {
    """
    NextCloud integration level (0-3)
    """
    nextcloud: Int!
    
    """
    Gatsby integration level (0-3)
    """
    gatsby: Int!
    
    """
    Rust integration level (0-3)
    """
    rust: Int!
    
    """
    TSX integration level (0-3)
    """
    tsx: Int!
  }
  
  """
  Extended stNode
  """
  type ExtendedStNode {
    """
    Node ID
    """
    id: ID!
    
    """
    Node type
    """
    type: String!
    
    """
    Time coordinate
    """
    timeCoordinate: TimeCoordinate!
    
    """
    Space coordinate
    """
    spaceCoordinate: SpaceCoordinate
    
    """
    Category coordinates
    """
    categoryCoordinates: [CategoryCoordinate!]!
    
    """
    Integration axis values
    """
    axisValues: AxisValues!
    
    """
    Version
    """
    version: String!
    
    """
    Metadata
    """
    metadata: JSON
    
    """
    NextCloud extension (if nextcloud > 0)
    """
    nextcloudExtension: NextCloudExtension
    
    """
    Gatsby extension (if gatsby > 0)
    """
    gatsbyExtension: GatsbyExtension
    
    """
    Rust extension (if rust > 0)
    """
    rustExtension: RustExtension
  }
  
  """
  NextCloud extension
  """
  type NextCloudExtension {
    """
    Integration level (1-3)
    """
    level: Int!
    
    """
    User ID
    """
    userId: String
    
    """
    File ID
    """
    fileId: String
    
    """
    Sharing information
    """
    sharing: NextCloudSharing
    
    """
    App data
    """
    appData: JSON
  }
  
  """
  NextCloud sharing information
  """
  type NextCloudSharing {
    """
    Share ID
    """
    shareId: String
    
    """
    Users/groups the item is shared with
    """
    shareWith: [String!]
    
    """
    Permissions
    """
    permissions: Int
  }
  
  """
  Gatsby extension
  """
  type GatsbyExtension {
    """
    Integration level (1-3)
    """
    level: Int!
    
    """
    Whether this node was generated at build time
    """
    isStatic: Boolean!
    
    """
    Path to the node in the Gatsby data layer
    """
    dataPath: String
    
    """
    GraphQL fragment for querying this node
    """
    fragment: String
    
    """
    Static data loaded at build time
    """
    staticData: JSON
    
    """
    Dynamic data loaded at runtime
    """
    dynamicData: JSON
  }
  
  """
  Rust extension
  """
  type RustExtension {
    """
    Integration level (1-3)
    """
    level: Int!
    
    """
    Whether Rust components are initialized
    """
    initialized: Boolean!
    
    """
    Performance metrics
    """
    performance: RustPerformance
  }
  
  """
  Rust performance metrics
  """
  type RustPerformance {
    """
    Initialization time in milliseconds
    """
    initTime: Int
    
    """
    Render time in milliseconds
    """
    renderTime: Int
    
    """
    Memory usage in bytes
    """
    memoryUsage: Int
  }
  
  """
  NextCloud file
  """
  type NextCloudFile {
    """
    File ID
    """
    id: ID!
    
    """
    File path
    """
    path: String!
    
    """
    File name
    """
    name: String!
    
    """
    MIME type
    """
    mimeType: String!
    
    """
    File size in bytes
    """
    size: Int!
    
    """
    Last modified timestamp
    """
    lastModified: String!
    
    """
    File owner
    """
    owner: NextCloudUser!
    
    """
    File shares
    """
    shares: [NextCloudShare!]!
    
    """
    File content (if requested)
    """
    content: String
  }
  
  """
  NextCloud user
  """
  type NextCloudUser {
    """
    User ID
    """
    id: ID!
    
    """
    Display name
    """
    displayName: String!
    
    """
    Email address
    """
    email: String!
  }
  
  """
  NextCloud share
  """
  type NextCloudShare {
    """
    Share ID
    """
    id: ID!
    
    """
    User/group the item is shared with
    """
    shareWith: String!
    
    """
    Permissions
    """
    permissions: Int!
    
    """
    Creation timestamp
    """
    createdAt: String!
  }
  
  """
  Gatsby page
  """
  type GatsbyPage {
    """
    Page path
    """
    path: String!
    
    """
    Page title
    """
    title: String!
    
    """
    Page description
    """
    description: String
    
    """
    Whether the page is a client-only route
    """
    isClientOnly: Boolean!
    
    """
    Page data
    """
    data: JSON
  }
  
  """
  Rust component
  """
  type RustComponent {
    """
    Component name
    """
    name: String!
    
    """
    Component module
    """
    module: String!
    
    """
    Performance metrics
    """
    performance: RustPerformance
  }
  
  """
  Trajectory point
  """
  type TrajectoryPoint {
    """
    Point ID
    """
    id: ID!
    
    """
    Integration axis values
    """
    axisValues: AxisValues!
    
    """
    Version
    """
    version: String!
    
    """
    Description
    """
    description: String
  }
  
  """
  Trajectory
  """
  type Trajectory {
    """
    Trajectory ID
    """
    id: ID!
    
    """
    Trajectory name
    """
    name: String!
    
    """
    Starting point
    """
    start: TrajectoryPoint!
    
    """
    Ending point
    """
    end: TrajectoryPoint!
    
    """
    Intermediate points
    """
    points: [TrajectoryPoint!]!
  }
  
  """
  JSON scalar type
  """
  scalar JSON
  
  """
  Query root
  """
  type Query {
    """
    Get an extended stNode by ID
    """
    stNode(id: ID!): ExtendedStNode
    
    """
    Get multiple stNodes
    """
    stNodes(ids: [ID!]): [ExtendedStNode!]!
    
    """
    Get stNodes by axis values
    """
    stNodesByAxisValues(axisValues: AxisValuesInput!): [ExtendedStNode!]!
    
    """
    Get a NextCloud file by path
    """
    nextCloudFile(path: String!): NextCloudFile
    
    """
    Get NextCloud files in a directory
    """
    nextCloudFiles(directory: String!): [NextCloudFile!]!
    
    """
    Get a Gatsby page by path
    """
    gatsbyPage(path: String!): GatsbyPage
    
    """
    Get all Gatsby pages
    """
    gatsbyPages: [GatsbyPage!]!
    
    """
    Get a Rust component by name
    """
    rustComponent(name: String!): RustComponent
    
    """
    Get all Rust components
    """
    rustComponents: [RustComponent!]!
    
    """
    Get a trajectory by ID
    """
    trajectory(id: ID!): Trajectory
    
    """
    Get all trajectories
    """
    trajectories: [Trajectory!]!
  }
  
  """
  Mutation root
  """
  type Mutation {
    """
    Create an extended stNode
    """
    createStNode(input: CreateStNodeInput!): ExtendedStNode!
    
    """
    Update an extended stNode
    """
    updateStNode(id: ID!, input: UpdateStNodeInput!): ExtendedStNode!
    
    """
    Delete an extended stNode
    """
    deleteStNode(id: ID!): Boolean!
    
    """
    Move along an integration axis
    """
    moveAlongAxis(nodeId: ID!, axis: String!, newValue: Int!): ExtendedStNode!
    
    """
    Move along multiple integration axes
    """
    moveAlongAxes(nodeId: ID!, axisValues: AxisValuesInput!): ExtendedStNode!
    
    """
    Save a NextCloud file
    """
    saveNextCloudFile(path: String!, content: String!): NextCloudFile!
    
    """
    Share a NextCloud file
    """
    shareNextCloudFile(path: String!, shareWith: String!, permissions: Int): NextCloudShare!
    
    """
    Create a Gatsby page
    """
    createGatsbyPage(input: CreateGatsbyPageInput!): GatsbyPage!
    
    """
    Process data with Rust
    """
    processWithRust(data: JSON!, module: String!, function: String!): JSON!
    
    """
    Create a trajectory
    """
    createTrajectory(input: CreateTrajectoryInput!): Trajectory!
    
    """
    Execute a trajectory step
    """
    executeTrajectoryStep(nodeId: ID!, trajectoryId: ID!, stepIndex: Int!): ExtendedStNode!
  }
  
  """
  Subscription root
  """
  type Subscription {
    """
    Subscribe to stNode changes
    """
    stNodeChanged(id: ID!): ExtendedStNode!
    
    """
    Subscribe to NextCloud file changes
    """
    nextCloudFileChanged(path: String!): NextCloudFile!
    
    """
    Subscribe to Gatsby page changes
    """
    gatsbyPageChanged(path: String!): GatsbyPage!
  }
  
  """
  Input for axis values
  """
  input AxisValuesInput {
    """
    NextCloud integration level (0-3)
    """
    nextcloud: Int!
    
    """
    Gatsby integration level (0-3)
    """
    gatsby: Int!
    
    """
    Rust integration level (0-3)
    """
    rust: Int!
    
    """
    TSX integration level (0-3)
    """
    tsx: Int!
  }
  
  """
  Input for creating an extended stNode
  """
  input CreateStNodeInput {
    """
    Node type
    """
    type: String!
    
    """
    Time coordinate
    """
    timeCoordinate: TimeCoordinateInput!
    
    """
    Space coordinate
    """
    spaceCoordinate: SpaceCoordinateInput
    
    """
    Category coordinates
    """
    categoryCoordinates: [CategoryCoordinateInput!]!
    
    """
    Integration axis values
    """
    axisValues: AxisValuesInput!
    
    """
    Version
    """
    version: String
    
    """
    Metadata
    """
    metadata: JSON
  }
  
  """
  Input for updating an extended stNode
  """
  input UpdateStNodeInput {
    """
    Node type
    """
    type: String
    
    """
    Time coordinate
    """
    timeCoordinate: TimeCoordinateInput
    
    """
    Space coordinate
    """
    spaceCoordinate: SpaceCoordinateInput
    
    """
    Category coordinates
    """
    categoryCoordinates: [CategoryCoordinateInput!]
    
    """
    Integration axis values
    """
    axisValues: AxisValuesInput
    
    """
    Version
    """
    version: String
    
    """
    Metadata
    """
    metadata: JSON
  }
  
  """
  Input for time coordinate
  """
  input TimeCoordinateInput {
    """
    Timestamp in ISO format
    """
    timestamp: String!
    
    """
    Duration in milliseconds
    """
    duration: Int
    
    """
    Time zone
    """
    timeZone: String
    
    """
    Time scale (e.g., 'linear', 'logarithmic')
    """
    scale: String
  }
  
  """
  Input for space coordinate
  """
  input SpaceCoordinateInput {
    """
    Location name
    """
    location: String
    
    """
    Geographic coordinates
    """
    coordinates: GeoCoordinatesInput
    
    """
    Space scale (e.g., 'local', 'global')
    """
    scale: String
  }
  
  """
  Input for geographic coordinates
  """
  input GeoCoordinatesInput {
    """
    Latitude
    """
    latitude: Float!
    
    """
    Longitude
    """
    longitude: Float!
    
    """
    Altitude in meters
    """
    altitude: Float
  }
  
  """
  Input for category coordinate
  """
  input CategoryCoordinateInput {
    """
    Category ID
    """
    categoryId: ID!
    
    """
    Category name
    """
    categoryName: String!
    
    """
    Category weight (0.0 to 1.0)
    """
    weight: Float!
    
    """
    Parent category ID
    """
    parentId: ID
  }
  
  """
  Input for creating a Gatsby page
  """
  input CreateGatsbyPageInput {
    """
    Page path
    """
    path: String!
    
    """
    Page title
    """
    title: String!
    
    """
    Page description
    """
    description: String
    
    """
    Whether the page is a client-only route
    """
    isClientOnly: Boolean
    
    """
    Page data
    """
    data: JSON
  }
  
  """
  Input for creating a trajectory
  """
  input CreateTrajectoryInput {
    """
    Trajectory name
    """
    name: String!
    
    """
    Starting point
    """
    start: TrajectoryPointInput!
    
    """
    Ending point
    """
    end: TrajectoryPointInput!
    
    """
    Intermediate points
    """
    points: [TrajectoryPointInput!]
  }
  
  """
  Input for trajectory point
  """
  input TrajectoryPointInput {
    """
    Integration axis values
    """
    axisValues: AxisValuesInput!
    
    """
    Version
    """
    version: String!
    
    """
    Description
    """
    description: String
  }
`;

/**
 * Export the schema
 */
export default typeDefs;
