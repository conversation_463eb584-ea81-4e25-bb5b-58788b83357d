## i wanna create a gobal script that helps with organizain and performing meaningful and contextual git commits
- it util will generate a list of staged files
- it will send that list to augment thru its mcp server
- with instructions to analyze all changes, in time sequence, by file and patch timetags 
- then augment should break the changes along granular functionalities, and alliegned with current fs structure
- it will be farely granular, but each commit should have a certain volume and meaning
- it will generate the commit messages for each commit
- produce a list of commits with content and message
- and, upon approval will by the dev, will perform the commits, and provide an option to push upstream
- it will simply wait for the dev to provide list of commits from augment

i dont know if its even worth it to provide integration with augment, wo an mcp server of mynown
