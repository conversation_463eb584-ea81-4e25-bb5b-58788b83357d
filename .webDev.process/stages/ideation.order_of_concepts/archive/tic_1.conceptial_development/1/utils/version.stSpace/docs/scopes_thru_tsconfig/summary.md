# SpiceTime Path Management Specification

## 1. Path Naming Conventions

### 1.1 Public Paths
- **Syntax**: No prefix (`package-name/*`)
- **Accessibility**: Available throughout the entire downscope hierarchy
- **Purpose**: Expose APIs, utilities, and components for broad use
- **Example**: `import { Button } from "ui-components/buttons"`

### 1.2 Protected Paths
- **Syntax**: Dot prefix (`.package-name/*`)
- **Accessibility**: Available only to local nodes within the same package
- **Purpose**: Share implementation details between closely related modules
- **Example**: `import { validateInput } from ".core/validation"`

### 1.3 Private Paths
- **Syntax**: Underscore prefix (`_package-name/*`)
- **Accessibility**: Available only to index files
- **Purpose**: Rename and localize external dependencies
- **Example**: `import * as RemoteAPI from "_api/endpoints"`

## 2. Configuration Cascade

### 2.1 TSConfig Structure
- Each package level maintains its own tsconfig.json
- Each extends its parent configuration
- Path mappings are defined at the appropriate level

```json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "paths": {
      "public-api/*": ["./public-api/*"],
      ".core/*": ["./core/*"],
      "_internal/*": ["./internal/*"]
    }
  }
}
```

### 2.2 Package Dependencies
- Local dependencies managed via tsconfig paths
- External dependencies declared in package.json
- Most dependencies hoisted to root package.json

## 3. Scope Management

### 3.1 Index File Role
- Acts as the entry point for package scope definition
- Imports and renames external dependencies using private paths
- Exports a clean, consistent API

### 3.2 Local Node Access
- Direct access to files within the same directory
- Access to protected paths within the same package
- Access to public paths from any parent package

### 3.3 Selective Exposure
- Not all files need to be exposed in paths
- "Scratch" files can exist outside the defined scope
- Future: declarative scope.json to control exposure

## 4. Future Pragma System

### 4.1 Declarative Scope Definition
```json
{
  "public": {
    "components": "./components",
    "hooks": "./hooks"
  },
  "protected": {
    "utils": "./utils",
    "constants": "./constants"
  },
  "private": {
    "api": "../api/client",
    "types": "../types"
  }
}
```

### 4.2 Generated Artifacts
- TSConfig path mappings
- Package.json dependencies
- Index file imports and exports