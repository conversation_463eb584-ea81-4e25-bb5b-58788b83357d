// Core configuration interface
interface WebDevProcessConfig {
  project: ProjectConfig;
  stages: Record<string, StageConfig>;
  git?: GitConfig;
  pipelines?: Record<string, PipelineConfig>;
  integrations?: {
    turbo?: TurboIntegration;
    github?: GitHubIntegration;
    gitlab?: GitLabIntegration;
    // Other tool integrations
  };
}

// Core process interface
interface WebDevProcess {
  // Stage management
  getCurrentStage(): string;
  startTransition(from: string, to: string): Promise<StageTransition>;

  // Artifact management
  getStageArtifacts(stageName: string): Promise<string[]>;
  registerArtifact(path: string, metadata?: Record<string, any>): Promise<void>;

  // Tool integration
  generateToolConfigs(): Promise<Record<string, any>>;

  // Git integration
  syncWithGit(stageName: string): Promise<void>;

  // Pipeline execution
  executePipeline(pipelineName: string): Promise<void>;
}