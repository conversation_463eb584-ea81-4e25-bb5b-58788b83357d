# WebDevProcess Configuration Schema

## 1. Overview

This document defines the schema for configuring the WebDevProcess system, focusing on integrating existing tools and providing a unified configuration interface.

## 2. Schema Definition

```typescript
/**
 * Root configuration for WebDevProcess
 */
interface WebDevProcessConfig {
  /**
   * Project metadata
   */
  project: {
    /** Project name */
    name: string;
    /** Project description */
    description?: string;
    /** Project version */
    version: string;
    /** Repository URL */
    repository?: string;
    /** Project owner or team */
    owner?: string;
  };

  /**
   * Workspace configuration (monorepo settings)
   */
  workspace: {
    /** Package manager (npm, yarn, pnpm) */
    packageManager: "npm" | "yarn" | "pnpm";
    /** Monorepo tool configuration */
    monorepo?: {
      /** Tool used for monorepo management */
      tool: "turbo" | "nx" | "lerna" | "rush";
      /** Path to tool's config file */
      configPath: string;
      /** Custom settings to merge with tool config */
      settings?: Record<string, any>;
    };
    /** Workspace packages */
    packages: {
      /** Package glob patterns */
      patterns: string[];
      /** Excluded package patterns */
      exclude?: string[];
    };
  };

  /**
   * Stage configurations
   */
  stages: {
    /** Ideation stage configuration */
    ideation?: {
      /** Directory for ideation artifacts */
      directory: string;
      /** Tools used in ideation stage */
      tools: Array<{
        /** Tool name */
        name: string;
        /** Tool configuration */
        config?: Record<string, any>;
      }>;
      /** Structure configuration */
      structure?: {
        /** Concepts directory */
        conceptsDir: string;
        /** Principles directory */
        principlesDir?: string;
        /** Goals directory */
        goalsDir?: string;
        /** Decisions directory */
        decisionsDir?: string;
      };
    };

    /** Design stage configuration */
    design?: {
      /** Directory for design artifacts */
      directory: string;
      /** Tools used in design stage */
      tools: Array<{
        /** Tool name */
        name: string;
        /** Tool configuration */
        config?: Record<string, any>;
      }>;
      /** Structure configuration */
      structure?: {
        /** Specifications directory */
        specsDir: string;
        /** Diagrams directory */
        diagramsDir?: string;
        /** Prototypes directory */
        prototypesDir?: string;
      };
    };

    /** Development stage configuration */
    development?: {
      /** Directory for development artifacts */
      directory: string;
      /** Tools used in development stage */
      tools: Array<{
        /** Tool name */
        name: string;
        /** Tool configuration */
        config?: Record<string, any>;
      }>;
      /** Framework configuration */
      framework?: {
        /** Framework name */
        name: string;
        /** Framework version */
        version?: string;
        /** Framework configuration */
        config?: Record<string, any>;
      };
      /** Testing configuration */
      testing?: {
        /** Test framework */
        framework: string;
        /** Test directory */
        directory: string;
        /** Test patterns */
        patterns: string[];
      };
    };

    /** Build stage configuration */
    build?: {
      /** Build tool configuration */
      tools: Array<{
        /** Tool name */
        name: string;
        /** Tool configuration */
        config?: Record<string, any>;
      }>;
      /** Output directory */
      outputDir: string;
      /** Build artifacts */
      artifacts?: Array<{
        /** Artifact name */
        name: string;
        /** Artifact path */
        path: string;
        /** Artifact type */
        type: string;
      }>;
    };

    /** Deployment stage configuration */
    deployment?: {
      /** Deployment environments */
      environments: Array<{
        /** Environment name */
        name: string;
        /** Environment URL */
        url?: string;
        /** Deployment provider */
        provider?: string;
        /** Provider-specific configuration */
        config?: Record<string, any>;
      }>;
      /** CI/CD configuration */
      cicd?: {
        /** CI/CD provider */
        provider: string;
        /** Provider configuration */
        config?: Record<string, any>;
      };
    };
  };

  /**
   * Tool configurations
   */
  tools: {
    /** Tool definitions by name */
    [toolName: string]: {
      /** Tool version */
      version?: string;
      /** Tool command or binary */
      command?: string;
      /** Tool configuration file path */
      configPath?: string;
      /** Tool-specific settings */
      settings?: Record<string, any>;
      /** Stages where this tool is used */
      stages?: string[];
    };
  };

  /**
   * Pipeline configurations
   */
  pipelines?: {
    /** Pipeline definitions by name */
    [pipelineName: string]: {
      /** Pipeline stages in order */
      stages: string[];
      /** Stage-specific configurations */
      stageConfigs?: Record<string, any>;
      /** Pipeline triggers */
      triggers?: Array<{
        /** Trigger type */
        type: "push" | "pull_request" | "schedule" | "manual";
        /** Trigger configuration */
        config?: Record<string, any>;
      }>;
    };
  };

  /**
   * Integration configurations
   */
  integrations?: {
    /** GitHub configuration */
    github?: {
      /** Repository owner */
      owner: string;
      /** Repository name */
      repo: string;
      /** Branch configuration */
      branches?: {
        /** Main branch name */
        main: string;
        /** Development branch name */
        development?: string;
        /** Branch naming pattern */
        pattern?: string;
      };
      /** Pull request configuration */
      pullRequests?: {
        /** PR template path */
        templatePath?: string;
        /** Required reviewers count */
        requiredReviewers?: number;
      };
    };
    
    /** Jira configuration */
    jira?: {
      /** Jira instance URL */
      url: string;
      /** Jira project key */
      projectKey: string;
      /** Issue type mapping */
      issueTypes?: Record<string, string>;
    };
    
    /** Slack configuration */
    slack?: {
      /** Webhook URL */
      webhookUrl?: string;
      /** Channel for notifications */
      channel?: string;
      /** Notification events */
      events?: string[];
    };
  };
}
```

## 3. Configuration File Format

The WebDevProcess configuration can be defined in multiple formats:

### 3.1 JSON Format (webdev.config.json)

```json
{
  "project": {
    "name": "my-web-app",
    "version": "1.0.0"
  },
  "workspace": {
    "packageManager": "pnpm",
    "monorepo": {
      "tool": "turbo",
      "configPath": "turbo.json"
    },
    "packages": {
      "patterns": ["packages/*"]
    }
  },
  "stages": {
    "ideation": {
      "directory": "meta/ideation",
      "tools": [
        { "name": "conceptMapper" }
      ]
    },
    "design": {
      "directory": "meta/design",
      "tools": [
        { "name": "figma" }
      ]
    },
    "development": {
      "directory": "packages",
      "framework": {
        "name": "react",
        "version": "18.2.0"
      }
    }
  }
}
```

### 3.2 JavaScript Format (webdev.config.js)

```javascript
module.exports = {
  project: {
    name: "my-web-app",
    version: "1.0.0"
  },
  workspace: {
    packageManager: "pnpm",
    monorepo: {
      tool: "turbo",
      configPath: "turbo.json"
    },
    packages: {
      patterns: ["packages/*"]
    }
  },
  // Additional configuration...
};
```

### 3.3 TypeScript Format (webdev.config.ts)

```typescript
import { WebDevProcessConfig } from '@sta/config';

const config: WebDevProcessConfig = {
  project: {
    name: "my-web-app",
    version: "1.0.0"
  },
  workspace: {
    packageManager: "pnpm",
    monorepo: {
      tool: "turbo",
      configPath: "turbo.json"
    },
    packages: {
      patterns: ["packages/*"]
    }
  },
  // Additional configuration...
};

export default config;
```

## 4. Integration with Existing Tools

The WebDevProcess configuration system is designed to wrap and integrate with existing tools:

### 4.1 Turbo Integration

```typescript
// Example of how WebDevProcess config generates turbo.json
function generateTurboConfig(config: WebDevProcessConfig): TurboConfig {
  return {
    $schema: "https://turbo.build/schema.json",
    pipeline: {
      build: {
        dependsOn: ["^build"],
        outputs: [config.stages.build?.outputDir || "dist/**"]
      },
      test: {
        dependsOn: ["build"],
        inputs: [config.stages.development?.testing?.patterns || "src/**/*.test.{js,ts}"]
      },
      // Other pipeline configurations
    }
  };
}
```

### 4.2 ESLint Integration

```typescript
// Example of how WebDevProcess config generates .eslintrc.js
function generateESLintConfig(config: WebDevProcessConfig): ESLintConfig {
  const toolConfig = config.tools.eslint;
  
  return {
    extends: toolConfig?.settings?.extends || ["eslint:recommended"],
    rules: toolConfig?.settings?.rules || {},
    // Other ESLint configurations
  };
}
```

### 4.3 Jest Integration

```typescript
// Example of how WebDevProcess config generates jest.config.js
function generateJestConfig(config: WebDevProcessConfig): JestConfig {
  const testConfig = config.stages.development?.testing;
  
  return {
    testMatch: testConfig?.patterns || ["**/*.test.{js,ts}"],
    // Other Jest configurations
  };
}
```

## 5. API Usage

```typescript
import { loadConfig, generateToolConfigs } from '@sta/config';

// Load configuration
const config = loadConfig();

// Generate configurations for all tools
const toolConfigs = generateToolConfigs(config);

// Access specific tool configuration
const eslintConfig = toolConfigs.eslint;
const jestConfig = toolConfigs.jest;
const turboConfig = toolConfigs.turbo;

// Write configurations to files
writeConfigFiles(toolConfigs);
```