# Version Space Node

A library for managing version nodes in a distributed version space where nodes differ in structure but remain equivalent through transforms.

## Core Concepts

### Version Space

Version space is a conceptual framework where all versions of all repositories exist in relation to each other. Key properties:

- **Different Structure, Equivalent Meaning**: Nodes preserve their unique path through version space, giving them different structures, but they remain equivalent through conformal transforms.
- **Universal Relatability**: Any node can be projected onto any reference frame.
- **Causal Resilience**: Even if the original repository is lost, nodes can relate through any other node in the space.
- **Lineage Preservation**: Each node maintains its historical path through version space.

### Reference Frames

All reference frames are related and can be projected onto any other reference frame:

- No reference frame is inherently special
- All have lineage to a primordial proto-node
- If that repo is lost, they all relate through some other node
- This is how causal disconnections don't matter

## Usage

```typescript
import { 
  VersionNodeFactory, 
  VersionTransform, 
  ReferenceFrame, 
  CausalConnector 
} from '@spicetime/version-space-node';

// Create nodes with different structures
const nodeA = VersionNodeFactory.createNode(
  { major: 1, minor: 0, patch: 0 },
  [],
  [],
  { repo: "repo-a" }
);

const nodeB = VersionNodeFactory.createNode(
  { major: 2, minor: 3, patch: 1 },
  [],
  [],
  { repo: "repo-b" }
);

// Transform between nodes while preserving equivalence
const transformedNode = VersionTransform.project(nodeA, nodeB);

// Create reference frames
const frameA = new ReferenceFrame({ major: 1, minor: 0, patch: 0 });
const frameB = frameA.createRelatedFrame({ major: 1, minor: 1, patch: 0 });

// Project one frame onto another
const projection = frameA.projectOnto(frameB);

// Connect causally disconnected nodes
const bridgeNode = CausalConnector.connectDisconnectedNodes(nodeA, nodeB);
```

## Key Components

- **VersionNode**: Preserves its path through version space
- **VersionTransform**: Enables transforms between different node structures
- **ReferenceFrame**: Provides a coordinate system for version space
- **CausalConnector**: Handles relationships between causally connected and disconnected nodes

## Advanced Concepts

### Causal Disconnection

Even when nodes evolve independently without knowledge of each other, they can still be related:

```typescript
// Even if original repo is lost
const alternateOrigin = VersionNodeFactory.createNode(
  { major: 5, minor: 0, patch: 0 },
  [],
  [],
  { description: "Alternate origin" }
);

// Nodes can still relate through any other node
const relationship = CausalConnector.relateThroughAlternateOrigin(
  nodeA, 
  nodeB, 
  alternateOrigin
);
```

### Reference Frame Transformation

Any node can serve as a reference origin:

```typescript
const frame = new ReferenceFrame({ major: 3, minor: 2, patch: 1 });

// Any node can become a reference origin
// This is how causal disconnections don't matter
frame.becomeReferenceOrigin();
```

## License

MIT