# WebDevProcess Configuration Specification

## 1. Overview

The WebDevProcess configuration module defines how the WebDevProcess orchestration system is configured, including tool configurations for each stage of the development process.

## 2. Configuration Structure

```typescript
interface WebDevProcessConfig {
  // Global process configuration
  process: {
    name: string;
    version: string;
    description?: string;
    repository?: string;
  };
  
  // Stage-specific configurations
  stages: {
    ideation: IdeationStageConfig;
    design: DesignStageConfig;
    development: DevelopmentStageConfig;
    build: BuildStageConfig;
    deployment: DeploymentStageConfig;
  };
  
  // Tool configurations
  tools: {
    [toolName: string]: ToolConfig;
  };
  
  // Transition configurations
  transitions: {
    [transitionName: string]: TransitionConfig;
  };
  
  // Artifact configurations
  artifacts: {
    [artifactType: string]: ArtifactConfig;
  };
}
```

## 3. Stage Configurations

Each stage has its own configuration structure:

### 3.1 Ideation Stage

```typescript
interface IdeationStageConfig {
  // Tools used in ideation stage
  tools: string[]; // References to tool configurations
  
  // Artifact types produced in ideation stage
  artifacts: string[]; // References to artifact configurations
  
  // Structure configuration for ideation stage
  structure: {
    conceptsDir: string;
    principlesDir: string;
    goalsDir: string;
    versionsDir: string;
  };
  
  // Progression tracking
  progression: {
    timeTagging: boolean;
    decisionRecording: boolean;
    specLinking: boolean;
  };
}
```

### 3.2 Design Stage

```typescript
interface DesignStageConfig {
  // Tools used in design stage
  tools: string[]; // References to tool configurations
  
  // Artifact types produced in design stage
  artifacts: string[]; // References to artifact configurations
  
  // Structure configuration for design stage
  structure: {
    specsDir: string;
    diagramsDir: string;
    prototypesDir: string;
  };
  
  // Documentation generation
  documentation: {
    format: string;
    outputDir: string;
    templates: string[];
  };
}
```

### 3.3 Development Stage

```typescript
interface DevelopmentStageConfig {
  // Similar structure to other stages...
}
```

## 4. Tool Configurations

Tool configurations define how specific tools are used within the WebDevProcess:

```typescript
interface ToolConfig {
  name: string;
  version: string;
  command?: string;
  configFile?: string;
  stages: string[]; // Stages where this tool is used
  dependencies?: string[]; // Other tools this depends on
  options?: Record<string, any>; // Tool-specific options
}
```

## 5. Transition Configurations

Transition configurations define how artifacts move between stages:

```typescript
interface TransitionConfig {
  name: string;
  fromStage: string;
  toStage: string;
  requiredArtifacts: string[];
  validations: ValidationConfig[];
  automationScript?: string;
}
```

## 6. Artifact Configurations

Artifact configurations define the structure and validation of artifacts:

```typescript
interface ArtifactConfig {
  name: string;
  description?: string;
  schema?: string; // JSON Schema or similar
  validations?: ValidationConfig[];
  storage?: {
    type: string;
    location: string;
  };
}
```

## 7. Pragma-Based Configuration

WebDevProcess configurations can be derived from folder pragmas:

```typescript
// Example pragma in a folder
/**
 * @webDevProcess.stage ideation
 * @webDevProcess.tools ["conceptMapper", "decisionRecorder"]
 * @webDevProcess.artifacts ["concept", "principle", "goal"]
 */
```

This allows for minimal explicit configuration files while maintaining consistent structure across the repository.