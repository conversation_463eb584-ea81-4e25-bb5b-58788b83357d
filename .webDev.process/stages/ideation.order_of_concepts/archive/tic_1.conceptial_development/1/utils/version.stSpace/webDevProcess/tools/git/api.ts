interface GitWorkflowStrategy {
// Different strategies for managing stages in Git
type: 'stage-per-branch' | 'trunk-based' | 'gitflow';

// Create a branch for a specific stage
createStageBranch(stage: string): Promise<string>;

// Merge artifacts from one stage to another
mergeStageArtifacts(from: string, to: string, artifacts: string[]): Promise<void>;

// Tag releases with stage information
tagStageCompletion(stage: string, version: string): Promise<void>;
}

// Example configuration:
const gitConfig = {
branchStrategy: 'stage-per-branch',
stageBranchPrefix: 'stage/',
mainBranch: 'main',
artifactPatterns: {
design: ['docs/specs/**/*', 'designs/**/*'],
development: ['src/**/*', 'tests/**/*'],
build: ['dist/**/*']
}
};