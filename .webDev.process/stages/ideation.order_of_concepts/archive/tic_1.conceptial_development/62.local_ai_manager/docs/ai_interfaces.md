# AI Interfaces and Services

The Local AI Manager orchestrates multiple AI services through their respective APIs:

## Supported AI Services

1. **OpenAI Services**: Integrates with GPT models through the OpenAI API for general code generation, explanation, and transformation tasks.

2. **Anthropic Claude**: Leverages Claude models for reasoning about complex code structures and generating detailed explanations.

3. **Augment Code**: Utilizes Augment's context engine for codebase-aware assistance and refactoring.

4. **GitHub Copilot**: Integrates with Copilot for inline code suggestions and completions.

5. **Hugging Face Models**: Incorporates specialized models for specific tasks like code summarization, bug detection, or language translation.

6. **Local Models**: Runs smaller, specialized models locally for tasks that don't require the full power of cloud-based services, reducing latency and costs.

7. **Custom Fine-tuned Models**: Supports integration with custom models fine-tuned on specific codebases or domains.

## Service Selection Strategy

The manager selects the appropriate service for each task based on:

- Task requirements and complexity
- Service capabilities and specializations
- Past performance on similar tasks
- Cost and latency considerations
- Developer preferences

See the [service orchestrator implementation](../code/service_orchestrator.ts) for details on how these services are integrated and selected.
