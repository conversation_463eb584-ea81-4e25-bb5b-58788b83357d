/**
 * WebAssembly Bindings for Rust Components
 * 
 * This file provides the WebAssembly bindings for the Rust components
 * of the Local AI Manager, allowing them to be used from JavaScript/TypeScript.
 */

use wasm_bindgen::prelude::*;
use serde::{Serialize, Deserialize};

// Re-export the VectorStore for use in JavaScript
pub use crate::vector_store::VectorStore;

/// Configuration for the WASM module
#[wasm_bindgen]
pub struct WasmConfig {
    memory_limit: usize,
    enable_threading: bool,
    debug_mode: bool,
}

#[wasm_bindgen]
impl WasmConfig {
    #[wasm_bindgen(constructor)]
    pub fn new() -> Self {
        WasmConfig {
            memory_limit: 1024 * 1024 * 100, // 100MB default
            enable_threading: true,
            debug_mode: false,
        }
    }
    
    #[wasm_bindgen]
    pub fn set_memory_limit(&mut self, limit_mb: usize) {
        self.memory_limit = limit_mb * 1024 * 1024;
    }
    
    #[wasm_bindgen]
    pub fn set_enable_threading(&mut self, enable: bool) {
        self.enable_threading = enable;
    }
    
    #[wasm_bindgen]
    pub fn set_debug_mode(&mut self, debug: bool) {
        self.debug_mode = debug;
    }
}

/// Initialize the WASM module with the given configuration
#[wasm_bindgen]
pub fn initialize(config: WasmConfig) -> Result<(), JsValue> {
    // Set up panic hook for better error messages
    console_error_panic_hook::set_once();
    
    // Configure memory limits
    if config.debug_mode {
        log("Initializing WASM module in debug mode");
        log(&format!("Memory limit: {}MB", config.memory_limit / (1024 * 1024)));
        log(&format!("Threading enabled: {}", config.enable_threading));
    }
    
    Ok(())
}

/// Log a message to the console (only in debug mode)
#[wasm_bindgen]
pub fn log(message: &str) {
    web_sys::console::log_1(&JsValue::from_str(message));
}

/// Represents a task to be executed in a separate thread
#[derive(Serialize, Deserialize)]
pub struct AsyncTask {
    id: String,
    task_type: String,
    parameters: serde_json::Value,
}

/// Execute a task asynchronously
#[wasm_bindgen]
pub fn execute_async_task(task_json: &str) -> String {
    let task: AsyncTask = match serde_json::from_str(task_json) {
        Ok(t) => t,
        Err(e) => {
            return format!("{{\"error\": \"Failed to parse task: {}\"}}", e);
        }
    };
    
    match task.task_type.as_str() {
        "vector_search" => {
            // This would spawn a thread to perform the search
            format!("{{\"status\": \"started\", \"task_id\": \"{}\"}}", task.id)
        }
        "model_inference" => {
            // This would run a local model inference
            format!("{{\"status\": \"started\", \"task_id\": \"{}\"}}", task.id)
        }
        _ => {
            format!("{{\"error\": \"Unknown task type: {}\"}}", task.task_type)
        }
    }
}

/// Check the status of an asynchronous task
#[wasm_bindgen]
pub fn check_task_status(task_id: &str) -> String {
    // This would check the actual status of the task
    format!("{{\"status\": \"running\", \"task_id\": \"{}\", \"progress\": 0.5}}", task_id)
}

/// Memory statistics for the WASM module
#[wasm_bindgen]
pub struct MemoryStats {
    total_js_heap_size: usize,
    used_js_heap_size: usize,
    wasm_memory_size: usize,
}

#[wasm_bindgen]
impl MemoryStats {
    #[wasm_bindgen(getter)]
    pub fn total_js_heap_size(&self) -> usize {
        self.total_js_heap_size
    }
    
    #[wasm_bindgen(getter)]
    pub fn used_js_heap_size(&self) -> usize {
        self.used_js_heap_size
    }
    
    #[wasm_bindgen(getter)]
    pub fn wasm_memory_size(&self) -> usize {
        self.wasm_memory_size
    }
}

/// Get memory statistics for the WASM module
#[wasm_bindgen]
pub fn get_memory_stats() -> MemoryStats {
    // In a real implementation, these would be actual values
    MemoryStats {
        total_js_heap_size: 100 * 1024 * 1024,
        used_js_heap_size: 50 * 1024 * 1024,
        wasm_memory_size: 30 * 1024 * 1024,
    }
}
