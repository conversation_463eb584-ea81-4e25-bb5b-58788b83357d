/**
 * GraphQL Resolvers for Local AI Manager with NextCloud Integration
 *
 * These resolvers implement the GraphQL schema by connecting to NextCloud
 * services and AI services to provide the functionality of the Local AI Manager.
 *
 * NOTE: These resolvers are designed to work with the GraphQL schema generated
 * from the Linguistic Scoped Tree (lTree) structure. The lTree serves as the
 * primary abstraction for APIs between SaaS modules, with these resolvers being
 * used for compatibility with modules that don't natively support our architecture.
 */

import { createClient } from 'webdav';
import { ServiceOrchestrator } from '../code/service_orchestrator';
import { LearningSystem } from '../code/learning_system';
import { LossFunction } from '../code/loss_function';
import { KnowledgeGraph } from '../code/knowledge_base';

/**
 * NextCloud client configuration
 */
interface NextCloudConfig {
  baseUrl: string;
  username: string;
  password: string;
  token: string;
}

/**
 * NextCloud Search Client
 */
class NextCloudSearch {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  /**
   * Search NextCloud for files and content
   */
  async search(options: {
    term: string;
    providers?: string[];
    limit?: number;
    filters?: Record<string, any>;
  }): Promise<any[]> {
    try {
      const { term, providers = ['files'], limit = 10, filters = {} } = options;

      // Build query parameters
      const params = new URLSearchParams();
      params.append('term', term);
      providers.forEach(provider => params.append('provider', provider));
      params.append('limit', limit.toString());

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter_${key}`, v));
        } else {
          params.append(`filter_${key}`, value.toString());
        }
      });

      // Make request to NextCloud search API
      const response = await fetch(
        `${this.baseUrl}/ocs/v2.php/search/providers/search?${params.toString()}`,
        {
          headers: {
            'OCS-APIRequest': 'true',
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Search request failed: ${response.statusText}`);
      }

      const data = await response.json();

      // Extract and normalize search results
      return this.normalizeSearchResults(data);
    } catch (error) {
      console.error('Error searching NextCloud:', error);
      throw new Error('Failed to search NextCloud');
    }
  }

  /**
   * Normalize search results from NextCloud format
   */
  private normalizeSearchResults(data: any): any[] {
    try {
      // Extract entries from NextCloud response format
      const entries = data?.ocs?.data?.entries || [];

      // Map to a consistent format
      return entries.map(entry => ({
        path: entry.path || entry.link,
        title: entry.title,
        excerpt: entry.excerpt || entry.subline,
        mimetype: entry.mimetype,
        lastModified: entry.modified || entry.date,
        score: entry.score || 0
      }));
    } catch (error) {
      console.error('Error normalizing search results:', error);
      return [];
    }
  }
}

/**
 * NextCloud Notifications Client
 */
class NextCloudNotifications {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  /**
   * Create a notification for a user
   */
  async createNotification(options: {
    user: string;
    app: string;
    subject: string;
    message: string;
    object_type: string;
    object_id: string;
    link?: string;
  }): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/ocs/v2.php/apps/notifications/api/v2/admin_notifications`,
        {
          method: 'POST',
          headers: {
            'OCS-APIRequest': 'true',
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify(options)
        }
      );

      if (!response.ok) {
        throw new Error(`Create notification request failed: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        id: data?.ocs?.data?.id || `notification-${Date.now()}`,
        timestamp: new Date().toISOString(),
        link: options.link
      };
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }
}

/**
 * NextCloud Sharing Client
 */
class NextCloudSharing {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  /**
   * Create a share for a file or folder
   */
  async createShare(options: {
    path: string;
    shareType: number;
    shareWith?: string;
    permissions?: number;
    description?: string;
  }): Promise<any> {
    try {
      // Build form data
      const formData = new FormData();
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      // Make request to NextCloud sharing API
      const response = await fetch(
        `${this.baseUrl}/ocs/v2.php/apps/files_sharing/api/v1/shares`,
        {
          method: 'POST',
          headers: {
            'OCS-APIRequest': 'true',
            'Authorization': `Bearer ${this.token}`
          },
          body: formData
        }
      );

      if (!response.ok) {
        throw new Error(`Create share request failed: ${response.statusText}`);
      }

      const data = await response.json();

      // Extract and normalize share info
      return {
        id: data?.ocs?.data?.[0]?.id || `share-${Date.now()}`,
        url: data?.ocs?.data?.[0]?.url || '',
        token: data?.ocs?.data?.[0]?.token || '',
        owner: data?.ocs?.data?.[0]?.uid_owner || ''
      };
    } catch (error) {
      console.error('Error creating share:', error);
      throw new Error('Failed to create share');
    }
  }
}

/**
 * Main resolver class for the Local AI Manager
 */
export class NextCloudAIManagerResolvers {
  private webdavClient: any;
  private searchClient: NextCloudSearch;
  private notificationsClient: NextCloudNotifications;
  private sharingClient: NextCloudSharing;

  // AI Manager components
  private serviceOrchestrator: ServiceOrchestrator;
  private learningSystem: LearningSystem;
  private lossFunction: LossFunction;
  private knowledgeGraph: KnowledgeGraph;

  constructor(config: NextCloudConfig) {
    // Initialize NextCloud clients
    this.webdavClient = createClient(
      config.baseUrl + '/remote.php/webdav',
      { username: config.username, password: config.password }
    );

    this.searchClient = new NextCloudSearch(config.baseUrl, config.token);
    this.notificationsClient = new NextCloudNotifications(config.baseUrl, config.token);
    this.sharingClient = new NextCloudSharing(config.baseUrl, config.token);

    // Initialize AI Manager components
    this.serviceOrchestrator = new ServiceOrchestrator();
    this.learningSystem = new LearningSystem();
    this.lossFunction = new LossFunction();
    this.knowledgeGraph = new KnowledgeGraph();
  }

  /**
   * Get resolvers for GraphQL server
   */
  getResolvers() {
    return {
      Query: {
        aiServices: this.getAIServices.bind(this),
        searchCodebase: this.searchCodebase.bind(this),
        getFile: this.getFile.bind(this),
        listFiles: this.listFiles.bind(this),
        getFileVersions: this.getFileVersions.bind(this),
        getLearningHistory: this.getLearningHistory.bind(this)
      },
      Mutation: {
        processRequest: this.processRequest.bind(this),
        writeFile: this.writeFile.bind(this),
        shareCodeSnippet: this.shareCodeSnippet.bind(this),
        createNotification: this.createNotification.bind(this),
        restoreFileVersion: this.restoreFileVersion.bind(this),
        provideFeedback: this.provideFeedback.bind(this)
      }
    };
  }

  /**
   * Query Resolvers
   */

  async getAIServices() {
    return this.serviceOrchestrator.getServices();
  }

  async searchCodebase(_, { query, limit }) {
    try {
      const results = await this.searchClient.search({
        term: query,
        providers: ['files'],
        limit,
        filters: {
          mimetype: ['text/plain', 'application/javascript', 'text/x-python', 'text/typescript']
        }
      });

      return results;
    } catch (error) {
      console.error('Error searching codebase:', error);
      throw new Error('Failed to search codebase');
    }
  }

  async getFile(_, { path }) {
    try {
      const content = await this.webdavClient.getFileContents(path, { format: 'text' });
      const stat = await this.webdavClient.stat(path);

      return {
        path,
        name: path.split('/').pop(),
        content,
        mimeType: stat.mime,
        size: stat.size,
        lastModified: stat.lastmod,
        owner: stat.owner || 'unknown'
      };
    } catch (error) {
      console.error('Error getting file:', error);
      return null;
    }
  }

  async listFiles(_, { directory }) {
    try {
      const directoryItems = await this.webdavClient.getDirectoryContents(directory);

      return directoryItems.map(item => ({
        path: item.filename,
        name: item.basename,
        mimeType: item.mime,
        size: item.size,
        lastModified: item.lastmod,
        owner: item.owner || 'unknown'
      }));
    } catch (error) {
      console.error('Error listing files:', error);
      throw new Error('Failed to list files');
    }
  }

  async getFileVersions(_, { path }) {
    try {
      const versions = await this.webdavClient.getFileVersions(path);

      return versions.map(version => ({
        id: version.id,
        path,
        size: version.size,
        created: version.created,
        author: version.author
      }));
    } catch (error) {
      console.error('Error getting file versions:', error);
      throw new Error('Failed to get file versions');
    }
  }

  async getLearningHistory(_, { limit }) {
    return this.learningSystem.getHistory(limit);
  }

  /**
   * Mutation Resolvers
   */

  async processRequest(_, { request, userId, context }) {
    try {
      // Enhance context with NextCloud search results if needed
      let enhancedContext = context || {};

      if (!enhancedContext.relevantFiles) {
        const searchResults = await this.searchCodebase(null, {
          query: request,
          limit: 5
        });

        enhancedContext.relevantFiles = searchResults.map(result => result.path);

        // Load content for relevant files
        const fileContents = {};
        for (const path of enhancedContext.relevantFiles) {
          const file = await this.getFile(null, { path });
          if (file && file.content) {
            fileContents[path] = file.content;
          }
        }

        enhancedContext.fileContents = fileContents;
      }

      // Use loss function to select the best service
      const serviceId = await this.lossFunction.selectService(request, enhancedContext);

      // Process the request with the selected service
      const startTime = Date.now();
      const response = await this.serviceOrchestrator.processRequest(
        serviceId,
        request,
        enhancedContext
      );
      const processingTime = Date.now() - startTime;

      // Create response object
      const aiResponse = {
        id: `response-${Date.now()}`,
        content: response.content,
        serviceUsed: serviceId,
        timestamp: new Date().toISOString(),
        processingTime,
        tokens: response.tokens,
        confidence: response.confidence
      };

      // Record the interaction for learning
      await this.learningSystem.recordInteraction({
        request,
        response: aiResponse,
        context: enhancedContext,
        serviceId,
        userId
      });

      return aiResponse;
    } catch (error) {
      console.error('Error processing request:', error);
      throw new Error('Failed to process request');
    }
  }

  async writeFile(_, { path, content }) {
    try {
      await this.webdavClient.putFileContents(path, content);

      // Get updated file info
      return this.getFile(null, { path });
    } catch (error) {
      console.error('Error writing file:', error);
      throw new Error('Failed to write file');
    }
  }

  async shareCodeSnippet(_, { content, recipient, description }) {
    try {
      // Save the snippet to a temporary file
      const path = `/ai_snippets/${Date.now()}.js`;
      await this.webdavClient.putFileContents(path, content);

      // Share the file
      const shareInfo = await this.sharingClient.createShare({
        path,
        shareType: 0, // User share
        shareWith: recipient,
        permissions: 1, // Read permission
        description
      });

      return {
        id: shareInfo.id,
        content,
        path,
        shareUrl: shareInfo.url,
        recipient,
        creator: shareInfo.owner,
        timestamp: new Date().toISOString(),
        description
      };
    } catch (error) {
      console.error('Error sharing code snippet:', error);
      throw new Error('Failed to share code snippet');
    }
  }

  async createNotification(_, { userId, message, relatedFile }) {
    try {
      const notification = await this.notificationsClient.createNotification({
        user: userId,
        app: 'local_ai_manager',
        subject: 'AI Assistant',
        message,
        object_type: 'file',
        object_id: relatedFile,
        link: relatedFile ? `/apps/files/?dir=${encodeURIComponent(relatedFile)}` : undefined
      });

      return {
        id: notification.id,
        userId,
        message,
        timestamp: notification.timestamp,
        read: false,
        relatedFile,
        link: notification.link
      };
    } catch (error) {
      console.error('Error creating notification:', error);
      throw new Error('Failed to create notification');
    }
  }

  async restoreFileVersion(_, { path, versionId }) {
    try {
      await this.webdavClient.restoreFileVersion(path, versionId);

      // Get updated file info
      return this.getFile(null, { path });
    } catch (error) {
      console.error('Error restoring file version:', error);
      throw new Error('Failed to restore file version');
    }
  }

  async provideFeedback(_, { responseId, rating, comments }) {
    try {
      const feedback = await this.learningSystem.recordFeedback(
        responseId,
        rating,
        comments
      );

      return {
        id: feedback.id,
        responseId,
        rating,
        comments,
        timestamp: feedback.timestamp,
        userId: feedback.userId
      };
    } catch (error) {
      console.error('Error providing feedback:', error);
      throw new Error('Failed to provide feedback');
    }
  }
}
