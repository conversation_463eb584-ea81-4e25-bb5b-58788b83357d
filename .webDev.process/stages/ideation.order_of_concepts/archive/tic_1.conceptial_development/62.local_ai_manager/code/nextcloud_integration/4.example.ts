/**
 * Example usage of the NextCloud integration for Local AI Manager
 * 
 * This file demonstrates how to set up and use the NextCloud integration
 * for the Local AI Manager.
 */

import { createNextCloudIntegrationServer, createNextCloudIntegrationClient, initializeLocalAIManagerWithNextCloud } from './index';

/**
 * Example server setup
 */
async function setupServer() {
  // Configuration
  const config = {
    nextCloud: {
      baseUrl: 'https://nextcloud.example.com',
      username: 'admin',
      password: 'password',
      token: 'api-token'
    },
    server: {
      port: 4000,
      path: '/graphql'
    },
    aiManager: {
      services: [
        {
          id: 'openai',
          name: 'OpenAI',
          provider: 'OpenAI',
          apiKey: process.env.OPENAI_API_KEY || '',
          baseUrl: 'https://api.openai.com/v1',
          modelName: 'gpt-4'
        },
        {
          id: 'claude',
          name: '<PERSON>',
          provider: 'Anthropic',
          apiKey: process.env.ANTHROPIC_API_KEY || '',
          baseUrl: 'https://api.anthropic.com',
          modelName: 'claude-3-opus-20240229'
        },
        {
          id: 'augment',
          name: 'Augment',
          provider: 'Augment',
          apiKey: process.env.AUGMENT_API_KEY || '',
          baseUrl: 'https://api.augment.dev',
          modelName: 'default'
        }
      ]
    }
  };

  // Create and start the server
  const server = await createNextCloudIntegrationServer(config);
  const httpServer = await server.start();
  
  console.log(`NextCloud AI Manager GraphQL API running at http://localhost:${config.server.port}/graphql`);
  
  return {
    server,
    httpServer,
    stop: async () => {
      httpServer.close();
    }
  };
}

/**
 * Example client usage
 */
async function clientExample() {
  // Create client
  const client = createNextCloudIntegrationClient(
    'http://localhost:4000/graphql',
    'user-token'
  );
  
  // Process a simple request
  const response = await client.processRequest(
    'Create a function that validates email addresses',
    {
      currentFile: '/path/to/current/file.js',
      relevantFiles: ['/path/to/related/file.js'],
      projectContext: 'This is a Node.js project using Express',
      userPreferences: {
        preferredService: 'claude',
        codeStyle: 'functional',
        verbosity: 2,
        language: 'typescript'
      }
    }
  );
  
  console.log('Response:');
  console.log(response.content);
  console.log(`Service used: ${response.serviceUsed}`);
  console.log(`Processing time: ${response.processingTime}ms`);
}

/**
 * Example of initializing the Local AI Manager with NextCloud integration
 */
async function initializeExample() {
  // Configuration
  const config = {
    nextCloud: {
      baseUrl: 'https://nextcloud.example.com',
      username: 'admin',
      password: 'password',
      token: 'api-token'
    },
    server: {
      port: 4000,
      path: '/graphql'
    },
    aiManager: {
      services: [
        {
          id: 'openai',
          name: 'OpenAI',
          provider: 'OpenAI',
          apiKey: process.env.OPENAI_API_KEY || '',
          baseUrl: 'https://api.openai.com/v1',
          modelName: 'gpt-4'
        },
        {
          id: 'claude',
          name: 'Claude',
          provider: 'Anthropic',
          apiKey: process.env.ANTHROPIC_API_KEY || '',
          baseUrl: 'https://api.anthropic.com',
          modelName: 'claude-3-opus-20240229'
        }
      ]
    }
  };

  // Initialize the Local AI Manager with NextCloud integration
  const { aiManager, server, stop } = await initializeLocalAIManagerWithNextCloud(config);
  
  console.log('Local AI Manager initialized with NextCloud integration');
  
  // Use the AI Manager
  // ...
  
  // Stop the server when done
  await stop();
}

// Run the examples if this file is executed directly
if (require.main === module) {
  (async () => {
    try {
      // Example 1: Set up a server
      const { stop } = await setupServer();
      
      // Example 2: Use a client
      await clientExample();
      
      // Clean up
      await stop();
      
      // Example 3: Initialize the Local AI Manager with NextCloud integration
      await initializeExample();
    } catch (error) {
      console.error('Error running examples:', error);
    }
  })();
}
