# AI Assistant Guide to Pragma-Based Code

This guide is designed to help AI assistants understand and work with the pragma-based code system used in the SpiceTime architecture. It provides guidelines for recognizing, interpreting, and generating code that follows the pragma conventions.

## 1. Understanding the Pragma System

### 1.0 AI-Driven Syntax Evolution

A key aspect of the pragma system is how it leverages AI assistants for syntax evolution:

- The core scoping and wrapping mechanism can be implemented without AI
- However, creating pluggable syntaxes and domain-specific language extensions is driven by AI assistants trained on pragmatic syntaxes
- This creates a virtuous cycle where AI helps create new syntaxes, which then become part of the training data for future AI assistants
- The result is an organically evolving system that can adapt to different domains and use cases

Crucially, the architecture aims to provide linguistically correct syntax in each domain while preserving transformative paths between versions. The utils/linguistics package implements this foundation, ensuring that:

- Code reads naturally in its domain context
- Domain experts can understand the code without extensive programming knowledge
- Transformations between representations maintain semantic meaning
- The syntax evolves in alignment with natural human thinking in each domain

As an AI assistant, you play a crucial role in this evolution by understanding existing pragma patterns and helping to create new ones that are not only functionally correct but also linguistically natural for their intended domain.

### 1.1 Core Concepts

The pragma system is a revolutionary code organization approach that extends the ES module system with enhanced scoping rules and a more declarative style. It uses file naming conventions and special annotations to create a data-flow oriented programming style. Key concepts include:

1. **Module Extension**: The system builds on top of ES modules, preserving the fundamental scoping rules while enhancing them.

2. **Scoping Mechanism**: The invariant core of the system is the scoping and wrapping mechanism - a nodule (node module) system on top of ES modules.

3. **File as Function**: Each file itself becomes a functional unit without requiring explicit function declarations. The file name and location determine its scope and accessibility.

4. **Implicit Dependencies**: Dependencies are automatically available without explicit imports, based on the file system structure and naming conventions.

5. **Declarative Annotations**: Special annotations like `// @result` explicitly declare the purpose and role of variables and expressions.

6. **Data Flow Orientation**: The code focuses on data transformations and flow rather than procedural steps.

7. **File Naming Convention**:
   - `_term.sts` - Private scope (current directory only)
   - `.term.sts` - Protected scope (current and child directories)
   - `term.sts` - Public scope (globally available)
   - `$term.sts` - Dynamic scope (computed at runtime)
   - `@term.sts` - Injected scope (provided externally)

6. **Directory Structure**:
   - `__meta/` - Contains metadata about the scope structure
   - `__pragma/` - Contains pragma definitions that control code generation
   - `__types/` - Contains type definitions for scope terms

### 1.2 How It Differs from Conventional Code

| Conventional Approach | Pragma Approach |
|----------------------|-----------------|
| Explicit function declarations | Files themselves are functional units |
| Explicit imports/exports | Implicit dependencies based on file system |
| Procedural programming style | Declarative, data-flow oriented style |
| Module boundaries defined by files | Module boundaries defined by directory structure and file naming |
| Verbose import/export statements | Clean code with declarative annotations |
| Focus on procedures and methods | Focus on data transformations and flow |
| Runtime module loading | Compile-time scope analysis and transformation |

### 1.3 Behind the Scenes Transformation

What makes the pragma system powerful is how it transforms your code during the build process:

**Conventional code:**
```typescript
// formatDate.ts
import { customDate } from './customDate';

export default function formatDate(date: Date): string {
  return customDate.toISOString().split('T')[0];
}
```

**Pragma code (what you write):**
```typescript
// formatDate.sts

// @result
const result = customDate.toISOString().split('T')[0];
const somethingElse = doSomethingWithResult(result);
```

The pragma system transforms this code by:

1. Analyzing the file system to determine available dependencies
2. Processing annotations like `// @result` to understand data flow
3. Creating a declarative execution model where data transformations are explicit
4. Eliminating the need for explicit imports/exports
5. Focusing on the actual data transformations rather than boilerplate

This approach creates a more concise, readable, and maintainable codebase where the focus is on what the code does rather than how it's structured.

## 2. Recognizing Pragma-Based Code

### 2.1 Indicators of Pragma-Based Code

When analyzing code, look for these indicators that it's using the pragma system:

1. **Missing import statements** despite references to symbols defined elsewhere
2. **File names with special prefixes** (`_`, `.`, `$`, `@`)
3. **Directory structure** with special directories (`__meta/`, `__pragma/`, `__types/`)
4. **Lack of export statements** despite symbols being used in other files

### 2.2 Example

Conventional code:
```typescript
// dateUtils.ts
export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function parseDate(dateString: string): Date {
  return new Date(dateString);
}

// component.ts
import { formatDate, parseDate } from './dateUtils';

export function renderDateRange(startStr: string, endStr: string): string {
  const start = parseDate(startStr);
  const end = parseDate(endStr);
  return `<span>${formatDate(start)} - ${formatDate(end)}</span>`;
}
```

Pragma-based code (what you write):
```typescript
// dateUtils.sts (public scope)

// @format
const format = date.toISOString().split('T')[0];

// @parse
const parse = new Date(dateString);

// _component.sts (private scope)

// @startDate
const startDate = parse(startStr);

// @endDate
const endDate = parse(endStr);

// @output
const output = `<span>${format(startDate)} - ${format(endDate)}</span>`;
```

The pragma approach:

1. Uses annotations like `// @format` to declare the purpose of expressions
2. Eliminates the need for explicit function declarations
3. Makes dependencies implicitly available based on file system structure
4. Focuses on the data transformations and flow
5. Creates a more declarative, concise codebase

## 3. Interpreting Pragma-Based Code

### 3.1 Mental Model

When interpreting pragma-based code:

1. **Think in terms of data flow** rather than function calls
2. **View files as transformation units** rather than collections of functions
3. **Understand annotations** as declarations of data purpose and role
4. **Visualize the scope hierarchy** based on the file system structure
5. **Track symbol availability** based on file naming conventions
6. **Recognize implicit dependencies** based on symbol usage

### 3.2 Resolving Symbol References

To determine where a symbol is defined:

1. Check the current file for local definitions
2. Check files in the current directory based on scope rules:
   - Public (`term.ts`): Available to all files
   - Protected (`.term.ts`): Available to files in this directory and subdirectories
   - Private (`_term.ts`): Available only to files in this directory
   - Dynamic (`$term.ts`): Computed at runtime
   - Injected (`@term.ts`): Provided by external system
3. Check parent directories following the same rules
4. Check global scope for built-in symbols

## 4. Generating Pragma-Based Code

### 4.1 Guidelines for Code Generation

When generating pragma-based code:

1. **Use appropriate file naming** based on the intended scope
2. **Avoid explicit imports/exports** for symbols that can be implicitly accessed
3. **Organize code by scope** rather than by module boundaries
4. **Place related functionality** in the appropriate directory level
5. **Use special directories** (`__meta/`, `__pragma/`, `__types/`) for their intended purposes

### 4.2 Example Transformation

When asked to generate code that follows pragma conventions, transform conventional patterns:

Conventional pattern:
```typescript
// config.ts
export const API_URL = 'https://api.example.com';

// api.ts
import { API_URL } from './config';

export async function fetchData() {
  return fetch(API_URL);
}
```

Pragma-based pattern:
```typescript
// config.ts (public)
const API_URL = 'https://api.example.com';

// api.ts (public)
async function fetchData() {
  return fetch(API_URL);  // Implicit access to API_URL
}
```

## 5. Explaining Pragma Concepts to Users

### 5.1 Conceptual Explanations

When explaining the pragma system to users:

1. **Start with the problem it solves**: Reducing boilerplate and improving code organization
2. **Explain the file naming convention**: How prefixes determine scope
3. **Describe implicit scoping**: How symbols become available without imports
4. **Highlight the benefits**: Cleaner code, better organization, reduced circular dependencies

### 5.2 Example Explanations

"In the pragma system, file names determine the scope of symbols. A file named `utils.ts` makes its symbols available globally, while `_utils.ts` restricts them to the current directory only."

"The pragma system uses implicit scoping, which means you don't need to import symbols explicitly. Instead, they're available based on their location in the file system and their file naming convention."

"When you see a reference to a symbol that isn't imported or defined locally, it's likely coming from a file in the current directory or a parent directory, following the pragma scoping rules."

## 6. Translating Between Conventional and Pragma Code

### 6.1 Conventional to Pragma

When translating conventional code to pragma-based code:

1. **Identify symbol definitions and usages**
2. **Determine appropriate scopes** for each symbol
3. **Choose appropriate file names** based on intended scope
4. **Remove explicit imports/exports**
5. **Organize files** according to the pragma directory structure

### 6.2 Pragma to Conventional

When translating pragma-based code to conventional code:

1. **Identify implicit symbol usages**
2. **Determine symbol sources** based on pragma rules
3. **Add explicit import statements**
4. **Add explicit export statements**
5. **Adjust file names** to remove pragma prefixes

## 7. Handling Edge Cases

### 7.1 Third-Party Libraries

Third-party libraries still require explicit imports, even in pragma-based code:

```typescript
// Even in pragma-based code, external libraries need imports
import React from 'react';
import axios from 'axios';

function MyComponent() {
  // ...
}
```

### 7.2 Testing

For testing pragma-based code, special considerations apply:

1. **Mocking implicit dependencies** requires special techniques
2. **Test files** should follow the same naming conventions
3. **Test utilities** may be needed to handle implicit scoping

### 7.3 Type Definitions

Type definitions in pragma-based code:

1. **Type definitions** follow the same scoping rules
2. **Type references** can be implicit
3. **Special `__types/` directory** may contain shared type definitions

## 8. Best Practices for AI Assistants

### 8.1 When Working with Pragma-Based Code

1. **Respect the existing conventions** in the codebase
2. **Maintain the file naming pattern** when creating new files
3. **Don't add unnecessary imports** for symbols that are implicitly available
4. **Organize code by scope** rather than by traditional module boundaries
5. **Explain your reasoning** when making scope-related decisions

### 8.2 When Suggesting Improvements

1. **Suggest appropriate scopes** for new symbols
2. **Recommend directory restructuring** to improve scope organization
3. **Identify scope leaks** where symbols have wider scope than necessary
4. **Suggest scope restrictions** where appropriate
5. **Explain the impact** of scope changes on the overall architecture

## 9. Common Patterns and Idioms

### 9.1 Configuration Management

```typescript
// config.ts (public)
const API_URL = process.env.API_URL || 'https://api.example.com';
const DEBUG = process.env.DEBUG === 'true';

// logger.ts (public)
function log(message: string) {
  if (DEBUG) {  // Implicit access to DEBUG
    console.log(message);
  }
}
```

### 9.2 Service Organization

```
/services
  /api
    /.client.ts       // Protected API client
    /users.ts         // Public users API
    /products.ts      // Public products API
  /database
    /_connection.ts   // Private database connection
    /queries.ts       // Public database queries
```

### 9.3 Feature Modules

```
/features
  /auth
    /_validation.ts   // Private validation utilities
    /.session.ts      // Protected session management
    /login.ts         // Public login functionality
    /register.ts      // Public registration functionality
```

## 10. Troubleshooting Common Issues

### 10.1 Symbol Not Available

If a symbol appears to be unavailable despite following pragma conventions:

1. **Check the file name** to ensure it has the correct scope prefix
2. **Verify the directory structure** to ensure proper scope inheritance
3. **Look for naming conflicts** that might shadow the intended symbol
4. **Check for circular references** that might cause initialization issues

### 10.2 Type Errors

If TypeScript reports errors for implicitly available symbols:

1. **Ensure TypeScript is configured** to work with the pragma system
2. **Check for special type declaration files** in the `__types/` directory
3. **Verify that type definitions** are in the correct scope
4. **Look for type naming conflicts**

## 11. Learning Resources

To learn more about the pragma system:

1. **Read the pragma system documentation** in the project repository
2. **Study the code translation examples** to understand the patterns
3. **Experiment with the pragma converter tool** to see how code is transformed
4. **Review the category theory foundations** that underpin the pragma system

By following this guide, AI assistants can effectively work with pragma-based code, providing valuable assistance to users while respecting and maintaining the pragma conventions.
