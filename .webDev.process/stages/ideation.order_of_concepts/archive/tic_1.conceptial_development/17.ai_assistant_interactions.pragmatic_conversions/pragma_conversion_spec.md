# Pragma Conversion System Specification

## 1. Overview

The Pragma Conversion System is designed to bridge the gap between conventional coding patterns and the pragma-based approach used in the SpiceTime architecture. It enables:

1. Translation of conventional code to pragma-based code
2. Translation of pragma-based code to conventional code for AI assistants
3. Documentation generation for AI assistants to understand the pragma system

Importantly, while the core scoping and wrapping mechanism can be implemented without AI, the creation of pluggable syntaxes and domain-specific language extensions is greatly enhanced by AI assistants trained on pragmatic syntaxes. This creates a symbiotic relationship where:

- The pragma system provides a structured foundation for code organization
- AI assistants help create and evolve domain-specific syntaxes within this framework
- These syntaxes then become part of the training data for future AI assistants
- The cycle continues, allowing the system to evolve organically

A fundamental goal of the architecture is to provide linguistically correct syntax in each domain while preserving the transformative paths between versions. Linguistics is an absolutely crucial part of each syntax, as implemented in the utils/linguistics package. This linguistic foundation ensures that:

- Code reads naturally in the context of its domain
- Transformations between different representations maintain semantic meaning
- Domain experts can understand and contribute to the code without extensive programming knowledge
- The system evolves in alignment with how humans naturally think about and describe problems in each domain

### 1.1 Linguistic Foundation

The utils/linguistics package provides the foundation for ensuring linguistically correct syntax in each domain. This package:

1. **Analyzes Domain Language**: Understands the natural language patterns used by experts in each domain
2. **Maps Domain Concepts**: Creates mappings between domain concepts and code structures
3. **Ensures Semantic Preservation**: Maintains meaning during transformations between representations
4. **Evolves With Usage**: Learns from how domain experts interact with the code

This linguistic foundation is what allows the pragma system to create code that reads naturally to domain experts while still being technically precise and transformable.

### 1.2 Core Transformation Mechanism

The essence of the pragma system is its extension of the ES module system with a nodule (node module) system on top:

1. **Module Wrapping**: Each file is still wrapped in a module scope, but with enhanced scoping rules
2. **Scoping Mechanism**: The invariant core of the system is the scoping and wrapping mechanism
3. **Declarative Annotations**: Special comments like `// @result` explicitly declare the purpose of expressions
4. **Files as Transformation Units**: Each file represents a set of data transformations rather than a collection of functions
5. **Implicit Dependencies**: Dependencies are automatically available based on file system structure
6. **Data Flow Orientation**: The code focuses on what data is being transformed rather than how

This approach preserves the fundamental module scoping rules while adding a more declarative, data-flow oriented programming style. It's not a replacement for ES modules but an extension that enhances them with more powerful scoping and annotation capabilities.

## 2. Package Structure

### 2.1 Directory Structure

```
/packages/utils/pragma_converter/
├── __meta/                  # Metadata about the package
├── __pragma/                # Pragma definitions for the converter itself
├── __types/                 # Type definitions
├── core/                    # Core conversion logic
│   ├── parser.ts            # Code parser (public)
│   ├── _analyzer.ts         # Private analyzer for parsed code
│   ├── .transformer.ts      # Protected transformer for code conversion
│   ├── $contextProvider.ts  # Dynamic context provider
│   └── @pluginSystem.ts     # Injected plugin system
├── conventional_to_pragma/  # Conversion from conventional to pragma
│   ├── imports.ts           # Import statement conversion
│   ├── exports.ts           # Export statement conversion
│   ├── _moduleResolver.ts   # Private module resolution logic
│   └── .scopeMapper.ts      # Protected scope mapping utilities
├── pragma_to_conventional/  # Conversion from pragma to conventional
│   ├── scopeExtractor.ts    # Extract implicit scopes
│   ├── importGenerator.ts   # Generate import statements
│   ├── _pathResolver.ts     # Private path resolution
│   └── .contextTracker.ts   # Protected context tracking
├── documentation/           # Documentation generation
│   ├── templateEngine.ts    # Template rendering
│   ├── exampleGenerator.ts  # Example code generation
│   └── _formatters.ts       # Private formatting utilities
└── cli/                     # Command-line interface
    ├── commands.ts          # CLI commands
    ├── _validators.ts       # Private input validation
    └── .formatters.ts       # Protected output formatting
```

### 2.2 File Naming Convention (Following Pragma System)

- `_term.sts` - Private scope (current directory only)
- `.term.sts` - Protected scope (current and child directories)
- `term.sts` - Public scope (globally available)
- `$term.sts` - Dynamic scope (computed at runtime)
- `@term.sts` - Injected scope (provided externally)

## 3. Translation Utility Architecture

### 3.1 Core Components

#### 3.1.1 Parser

Responsible for parsing source code into an Abstract Syntax Tree (AST).

```typescript
// parser.ts
export interface ParserOptions {
  sourceType: 'module' | 'script';
  plugins?: string[];
  // Other parser options
}

export function parseCode(source: string, options?: ParserOptions): AST {
  // Implementation using a parser like Babel or TypeScript Compiler API
}
```

#### 3.1.2 Analyzer

Analyzes the AST to identify patterns for conversion.

```typescript
// _analyzer.ts (private)
function analyzeImports(ast: AST): ImportAnalysis {
  // Identify import statements and their patterns
}

function analyzeExports(ast: AST): ExportAnalysis {
  // Identify export statements and their patterns
}

function analyzeScopes(ast: AST): ScopeAnalysis {
  // Identify variable scopes and usage patterns
}
```

#### 3.1.3 Transformer

Transforms the AST based on the analysis, applying the declarative annotation pattern.

```typescript
// .transformer.ts (protected)
function transformImportsToPragma(ast: AST, analysis: ImportAnalysis): AST {
  // 1. Identify all imports and their usage
  // 2. Remove import statements
  // 3. Identify expressions and their purpose
  // 4. Add appropriate annotations (// @result, etc.)
  // 5. Transform function declarations to expression assignments
  // 6. Ensure proper data flow between expressions
}

function transformExportsToPragma(ast: AST, analysis: ExportAnalysis): AST {
  // 1. Identify all exports
  // 2. Remove export statements
  // 3. Add appropriate annotations to exported expressions
  // 4. Transform exported functions to annotated expressions
}

function transformPragmaToImports(ast: AST, analysis: ScopeAnalysis): AST {
  // 1. Identify all annotated expressions
  // 2. Determine dependencies based on symbol usage
  // 3. Generate appropriate import statements
  // 4. Transform annotated expressions to function declarations
  // 5. Add export statements for appropriate symbols
}
```

#### 3.1.4 Context Provider

Provides runtime context for dynamic transformations.

```typescript
// $contextProvider.ts (dynamic)
export function getContext(): ConversionContext {
  // Dynamically determine the conversion context
  return {
    projectRoot: detectProjectRoot(),
    pragmaConfig: loadPragmaConfig(),
    fileSystem: createFileSystemAdapter()
  };
}
```

### 3.2 Conversion Workflows

#### 3.2.1 Conventional to Pragma

1. Parse conventional code
2. Analyze imports, exports, and module structure
3. Map to pragma file naming convention (.sts extension)
4. Generate pragma-based file structure
5. For each file:
   - Identify function declarations and their purpose
   - Transform functions to annotated expressions (// @name)
   - Remove explicit imports/exports
   - Ensure proper data flow between expressions
   - Simplify code to focus on transformations
6. Organize files based on scope and accessibility requirements

#### 3.2.2 Pragma to Conventional

1. Parse pragma-based code
2. Analyze annotated expressions and their dependencies
3. For each file:
   - Identify all annotated expressions
   - Determine implicit dependencies based on symbol usage
   - Transform expressions to function declarations
   - Generate import statements for dependencies
   - Generate export statements for public expressions
4. Adjust file structure to match conventional module patterns

## 4. Examples of Code Translation

### 4.1 Conventional to Pragma

#### 4.1.1 Original Conventional Code

```typescript
// utils/logger.ts
export function log(message: string) {
  console.log(`[LOG]: ${message}`);
}

export function error(message: string) {
  console.error(`[ERROR]: ${message}`);
}

// services/userService.ts
import { log, error } from '../utils/logger';
import { db } from '../database';

export async function getUser(id: string) {
  try {
    log(`Fetching user with id: ${id}`);
    const user = await db.query('SELECT * FROM users WHERE id = ?', [id]);
    return user;
  } catch (err) {
    error(`Failed to fetch user: ${err.message}`);
    throw err;
  }
}
```

#### 4.1.2 Converted Pragma-Based Code

```
/utils/
  logger.sts  // Public scope (globally available)

/services/
  _userService.sts  // Private scope (only in services directory)
```

```typescript
// utils/logger.sts

// @log
console.log(`[LOG]: ${message}`);

// @error
console.error(`[ERROR]: ${message}`);

// services/_userService.sts

// @logFetch
log(`Fetching user with id: ${id}`);  // Implicit access to log

// @query
const user = await db.query('SELECT * FROM users WHERE id = ?', [id]);  // Implicit access to db

// @handleError
try {
  // Use logFetch and query
} catch (err) {
  error(`Failed to fetch user: ${err.message}`);  // Implicit access to error
  throw err;
}
```

### 4.2 Pragma to Conventional

#### 4.2.1 Original Pragma-Based Code

```typescript
// utils/config.sts (public)

// @apiUrl
const url = 'https://api.example.com';

// @timeout
const time = 5000;

// services/_authService.sts (private)

// @requestOptions
const options = {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password }),
  timeout  // Implicit access to timeout
};

// @fetchResponse
const response = await fetch(`${apiUrl}/login`, options);  // Implicit access to apiUrl

// @parseResponse
const result = await response.json();

// @handleError
try {
  // Use fetchResponse and parseResponse
} catch (err) {
  throw new Error(`Authentication failed: ${err.message}`);
}
```

#### 4.2.2 Converted Conventional Code

```typescript
// utils/config.ts
export const apiUrl = 'https://api.example.com';
export const timeout = 5000;

// services/authService.ts
import { apiUrl, timeout } from '../utils/config';

export async function login(username: string, password: string) {
  try {
    const options = {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password }),
      timeout
    };

    const response = await fetch(`${apiUrl}/login`, options);
    return await response.json();
  } catch (err) {
    throw new Error(`Authentication failed: ${err.message}`);
  }
}
```

## 5. Implementation Strategy

### 5.1 Phase 1: Core Parser and Analyzer

1. Implement code parser using TypeScript Compiler API
2. Build analyzers for imports, exports, and scope usage
3. Create basic transformation logic
4. Develop unit tests for core components

### 5.2 Phase 2: Conversion Logic

1. Implement conventional to pragma conversion
2. Implement pragma to conventional conversion
3. Create file system utilities for restructuring
4. Develop integration tests for conversion workflows

### 5.3 Phase 3: Documentation Generator

1. Create template engine for documentation
2. Implement example code generator
3. Build documentation schema
4. Develop user guides for AI assistants

### 5.4 Phase 4: CLI and Integration

1. Build command-line interface
2. Create IDE extensions (VS Code, etc.)
3. Implement CI/CD integration
4. Develop user documentation

## 6. AI Assistant Integration

### 6.1 Documentation for AI Assistants

Generate documentation specifically designed for AI assistants to understand:

1. The pragma system concepts
2. File naming conventions
3. Implicit scoping rules
4. Translation patterns

### 6.2 Interaction Patterns

Define standard interaction patterns for AI assistants:

1. How to recognize pragma-based code
2. How to suggest pragma-based solutions
3. How to translate between conventional and pragma code
4. How to explain pragma concepts to users

### 6.3 Local Agent Design

Specification for a local agent that mediates between users and AI assistants:

1. Pre-processes user code before sending to AI assistants
2. Post-processes AI assistant responses
3. Provides context about the pragma system
4. Maintains consistency in code style

## 7. Future Extensions

### 7.1 Advanced Transformations

1. Support for complex module systems (AMD, UMD, etc.)
2. Integration with bundlers (webpack, Rollup, etc.)
3. Support for non-JavaScript/TypeScript languages

### 7.2 Learning Capabilities

1. Pattern recognition for common code structures
2. Automatic suggestion of pragma conventions
3. Adaptation to user coding style

### 7.3 Ecosystem Integration

1. Integration with package managers (npm, yarn, etc.)
2. Support for monorepo structures
3. Integration with build systems
