# Pragma Composition Pipeline Implementation

This document outlines how the pragma composition pipeline is implemented, focusing on the technical details of how file extensions are processed and transformations are applied.

## Core Architecture

The pragma composition pipeline is implemented as a series of transformer functions, each corresponding to a specific pragma (file extension). These transformers are composed together based on the file's extension sequence.

### Key Components

1. **Extension Parser**: Parses file names to identify the extension pipeline
2. **Transformer Registry**: Maps extensions to transformer functions
3. **Composition Engine**: Composes transformers based on the extension pipeline
4. **Code Generator**: Generates the final code based on the composed transformations

## Extension Parser

The extension parser analyzes file names to identify the sequence of extensions:

```typescript
function parseExtensions(filename: string): string[] {
  const parts = filename.split('.');
  // Remove the base name
  parts.shift();
  // Return extensions in right-to-left order (for composition)
  return parts.reverse();
}

// Example
// parseExtensions('user.rfr.sts.ts') => ['ts', 'sts', 'rfr']
```

## Transformer Registry

The transformer registry maps extensions to transformer functions:

```typescript
type Transformer = (code: string, metadata: any) => { code: string, metadata: any };

const transformers: Record<string, Transformer> = {
  'ts': tsTransformer,
  'sts': stsTransformer,
  'rfr': rfrTransformer,
  'stx': stxTransformer,
  // Add more transformers as needed
};
```

## Composition Engine

The composition engine composes transformers based on the extension pipeline:

```typescript
function composeTransformers(extensions: string[]): Transformer {
  return extensions.reduce((composed, ext) => {
    const transformer = transformers[ext];
    if (!transformer) {
      throw new Error(`Unknown extension: ${ext}`);
    }
    
    return (code, metadata) => {
      const result = transformer(code, metadata);
      return composed(result.code, result.metadata);
    };
  }, (code, metadata) => ({ code, metadata }));
}
```

## Code Generator

The code generator applies the composed transformer to the source code:

```typescript
function generateCode(filename: string, sourceCode: string): string {
  const extensions = parseExtensions(filename);
  const transformer = composeTransformers(extensions);
  const result = transformer(sourceCode, {});
  return result.code;
}
```

## Transformer Implementation Examples

### TypeScript Transformer (`ts`)

The TypeScript transformer is the base transformer that simply passes through the code:

```typescript
function tsTransformer(code: string, metadata: any): { code: string, metadata: any } {
  // TypeScript is the base language, so no transformation is needed
  return { code, metadata };
}
```

### SpiceTime Script Transformer (`sts`)

The SpiceTime Script transformer converts code to a more declarative style:

```typescript
function stsTransformer(code: string, metadata: any): { code: string, metadata: any } {
  // Parse the code to identify functions, variables, etc.
  const ast = parseCode(code);
  
  // Transform functions to annotated expressions
  const transformedAst = transformFunctionsToAnnotatedExpressions(ast);
  
  // Generate code from the transformed AST
  const transformedCode = generateCode(transformedAst);
  
  return { 
    code: transformedCode, 
    metadata: {
      ...metadata,
      sts: {
        annotations: extractAnnotations(ast)
      }
    }
  };
}
```

### Root Functor Transformer (`rfr`)

The Root Functor transformer adds functional programming patterns:

```typescript
function rfrTransformer(code: string, metadata: any): { code: string, metadata: any } {
  // Parse the code
  const ast = parseCode(code);
  
  // Transform to functional style
  const transformedAst = transformToFunctionalStyle(ast);
  
  // Generate code from the transformed AST
  const transformedCode = generateCode(transformedAst);
  
  return { 
    code: transformedCode, 
    metadata: {
      ...metadata,
      rfr: {
        functionalPatterns: extractFunctionalPatterns(ast)
      }
    }
  };
}
```

## Integration with Build System

The pragma composition pipeline is integrated with the build system through a custom loader or plugin:

### Webpack Loader Example

```typescript
// pragma-loader.js
module.exports = function(source) {
  const filename = this.resourcePath;
  const transformedCode = generateCode(filename, source);
  return transformedCode;
};
```

### TypeScript Transformer Plugin Example

```typescript
// pragma-transformer.ts
import * as ts from 'typescript';

export default function(program: ts.Program): ts.TransformerFactory<ts.SourceFile> {
  return (context: ts.TransformationContext) => {
    return (sourceFile: ts.SourceFile) => {
      const filename = sourceFile.fileName;
      const sourceCode = sourceFile.getFullText();
      const transformedCode = generateCode(filename, sourceCode);
      
      // Parse the transformed code back to a TypeScript AST
      const transformedSourceFile = ts.createSourceFile(
        filename,
        transformedCode,
        sourceFile.languageVersion,
        true
      );
      
      return transformedSourceFile;
    };
  };
}
```

## Handling Special Cases

### Mixed Content Files

Some files may contain a mix of regular code and pragma annotations. These are handled by identifying pragma regions and applying transformations only to those regions:

```typescript
function handleMixedContent(code: string): string {
  const regions = identifyPragmaRegions(code);
  
  return regions.map(region => {
    if (region.type === 'pragma') {
      return applyPragmaTransformation(region.code, region.pragma);
    } else {
      return region.code;
    }
  }).join('');
}
```

### IDE Integration

For IDE integration, we provide language services that understand the pragma composition pipeline:

```typescript
// pragma-language-service.ts
class PragmaLanguageService implements LanguageService {
  getCompletionsAtPosition(fileName: string, position: number): CompletionInfo {
    const extensions = parseExtensions(fileName);
    const completionProviders = extensions.map(ext => getCompletionProvider(ext));
    
    // Compose completion providers
    return composeCompletionProviders(completionProviders)(fileName, position);
  }
  
  // Implement other language service methods...
}
```

## Performance Considerations

To optimize performance, the pragma composition pipeline:

1. **Caches transformers**: Composed transformers are cached based on the extension pipeline
2. **Incremental compilation**: Only re-transform files that have changed
3. **Parallel processing**: Transform multiple files in parallel
4. **Lazy loading**: Load transformers only when needed

## Conclusion

The pragma composition pipeline provides a powerful and flexible way to apply different pragmatic transformations to code. By implementing it as a series of composable transformers, we create a system that is both extensible and maintainable, allowing for easy addition of new pragmas and transformation patterns.
