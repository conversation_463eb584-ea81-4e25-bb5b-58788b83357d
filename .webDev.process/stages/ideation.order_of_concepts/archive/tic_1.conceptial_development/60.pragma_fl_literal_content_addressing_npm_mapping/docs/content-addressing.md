# Content Addressing

Content Addressing in the SpiceTime Architecture defines how packages are identified and addressed using cryptographic hashes. This document details how content addressing works, how it complements semantic naming, and how it enables immutable package identification.

## Conceptual Overview

Content addressing uses cryptographic hashes to identify packages based on their content. This creates a unique, immutable identifier for each package that is independent of its name or location. In the SpiceTime Architecture, packages have both a semantic name (npm-compatible) and a content hash (IPFS) as their true address.

The content addressing system is designed to be:
1. **Immutable**: Content hashes are based on the content itself, ensuring immutability
2. **Unique**: Each unique content has a unique hash, preventing collisions
3. **Verifiable**: Content can be verified against its hash, ensuring integrity
4. **Deduplicable**: Identical content has identical hashes, enabling deduplication
5. **Location-Independent**: Content can be retrieved by its hash, regardless of location

## Dual Addressing

Packages in the SpiceTime Architecture have dual addressing:

```typescript
// Package with dual addressing
const package = {
  // Semantic name (npm-compatible)
  semanticName: 'ui-components',
  
  // Content hash (IPFS)
  contentHash: 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A',
  
  // Pragmatic extensions
  pragmaExtensions: { /* ... */ },
  
  // Namepath extensions
  namepathExtensions: { /* ... */ },
  
  // Source code
  sourceCode: new Map([
    ['src/index.ts', '...'],
    ['src/Button.tsx', '...']
  ])
};
```

## Content Hash Calculation

Content hashes are calculated based on the package content:

```typescript
/**
 * Calculate content hash for a package
 * 
 * @param pkg - Package to calculate hash for
 * @returns Content hash
 */
export function calculateContentHash(pkg: Package): string {
  // Create a copy of the package without the content hash
  const { contentHash, ...packageWithoutHash } = pkg;
  
  // Serialize the package
  const serialized = JSON.stringify(packageWithoutHash);
  
  // Calculate hash
  return crypto.createHash('sha256').update(serialized).digest('hex');
}
```

## Content Verification

Content can be verified against its hash:

```typescript
/**
 * Verify package content against its hash
 * 
 * @param pkg - Package to verify
 * @returns True if the content matches the hash
 */
export function verifyContent(pkg: Package): boolean {
  // Calculate hash
  const calculatedHash = calculateContentHash(pkg);
  
  // Compare with stored hash
  return calculatedHash === pkg.contentHash;
}
```

## IPFS Integration

The SpiceTime Architecture integrates with IPFS for content addressing:

```typescript
/**
 * Add a package to IPFS
 * 
 * @param pkg - Package to add
 * @returns IPFS content identifier
 */
export async function addToIPFS(pkg: Package): Promise<string> {
  // Serialize the package
  const serialized = JSON.stringify(pkg);
  
  // Add to IPFS
  const result = await ipfs.add(Buffer.from(serialized));
  
  // Return content identifier
  return result.cid.toString();
}

/**
 * Retrieve a package from IPFS
 * 
 * @param cid - Content identifier
 * @returns Retrieved package
 */
export async function retrieveFromIPFS(cid: string): Promise<Package> {
  // Retrieve from IPFS
  const result = await ipfs.cat(cid);
  
  // Parse the package
  return JSON.parse(result.toString());
}
```

## Semantic Name Resolution

Semantic names can be resolved to content hashes:

```typescript
/**
 * Resolve a semantic name to a content hash
 * 
 * @param semanticName - Semantic name to resolve
 * @returns Content hash
 */
export async function resolveSemanticName(semanticName: string): Promise<string> {
  // Query the registry
  const contentHash = await registry.resolve(semanticName);
  
  // Return content hash
  return contentHash;
}

/**
 * Register a semantic name for a content hash
 * 
 * @param semanticName - Semantic name to register
 * @param contentHash - Content hash to register
 * @returns True if registration was successful
 */
export async function registerSemanticName(semanticName: string, contentHash: string): Promise<boolean> {
  // Register with the registry
  return registry.register(semanticName, contentHash);
}
```

## Version Extraction

Versions can be extracted from content hashes:

```typescript
/**
 * Extract version from content hash
 * 
 * @param contentHash - Content hash to extract version from
 * @returns Extracted version
 */
export function extractVersionFromContentHash(contentHash: string): string {
  // In a real implementation, this would extract a version from the content hash
  // For now, we'll just return a placeholder version
  return '1.0.0';
}
```

## Deduplication

Content addressing enables deduplication:

```typescript
/**
 * Deduplicate packages
 * 
 * @param packages - Packages to deduplicate
 * @returns Deduplicated packages
 */
export function deduplicatePackages(packages: Package[]): Package[] {
  // Create a map of content hashes to packages
  const packageMap = new Map<string, Package>();
  
  // Add packages to the map, overwriting duplicates
  for (const pkg of packages) {
    packageMap.set(pkg.contentHash, pkg);
  }
  
  // Return deduplicated packages
  return Array.from(packageMap.values());
}
```

## Implementation

The content addressing implementation consists of several parts:

```typescript
/**
 * Package interface
 */
export interface Package {
  semanticName: string;
  contentHash: string;
  pragmaExtensions: Record<string, any>;
  namepathExtensions: Record<string, string>;
  sourceCode: Map<string, string>;
}

/**
 * Registry interface
 */
export interface Registry {
  resolve(semanticName: string): Promise<string>;
  register(semanticName: string, contentHash: string): Promise<boolean>;
}

/**
 * IPFS client
 */
const ipfs = {
  /**
   * Add content to IPFS
   * 
   * @param content - Content to add
   * @returns Result with content identifier
   */
  async add(content: Buffer): Promise<{ cid: { toString: () => string } }> {
    // In a real implementation, this would add content to IPFS
    // For now, we'll just return a mock result
    return {
      cid: {
        toString: () => 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A'
      }
    };
  },
  
  /**
   * Retrieve content from IPFS
   * 
   * @param cid - Content identifier
   * @returns Retrieved content
   */
  async cat(cid: string): Promise<{ toString: () => string }> {
    // In a real implementation, this would retrieve content from IPFS
    // For now, we'll just return a mock result
    return {
      toString: () => JSON.stringify({
        semanticName: 'ui-components',
        contentHash: cid,
        pragmaExtensions: {},
        namepathExtensions: {},
        sourceCode: {}
      })
    };
  }
};

/**
 * Registry client
 */
const registry: Registry = {
  /**
   * Resolve a semantic name to a content hash
   * 
   * @param semanticName - Semantic name to resolve
   * @returns Content hash
   */
  async resolve(semanticName: string): Promise<string> {
    // In a real implementation, this would query the registry
    // For now, we'll just return a mock result
    return 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A';
  },
  
  /**
   * Register a semantic name for a content hash
   * 
   * @param semanticName - Semantic name to register
   * @param contentHash - Content hash to register
   * @returns True if registration was successful
   */
  async register(semanticName: string, contentHash: string): Promise<boolean> {
    // In a real implementation, this would register with the registry
    // For now, we'll just return a mock result
    return true;
  }
};
```

## Benefits of Content Addressing

Content addressing provides several benefits:

1. **Immutable Identification**: Content hashes provide immutable identification of packages, ensuring integrity.

2. **Content Verification**: Content can be verified against its hash, ensuring that it has not been tampered with.

3. **Deduplication**: Identical content has identical hashes, enabling deduplication and reducing storage requirements.

4. **Location Independence**: Content can be retrieved by its hash, regardless of location, enabling distributed storage.

5. **Version Integrity**: Content hashes can be used to verify the integrity of versions, ensuring that they have not been tampered with.

6. **Deterministic Builds**: Content addressing enables deterministic builds, ensuring that the same inputs produce the same outputs.

7. **Distributed Storage**: Content addressing enables distributed storage, reducing reliance on centralized repositories.

## Challenges and Considerations

There are some challenges and considerations with content addressing:

1. **Hash Algorithm Selection**: Choosing the right hash algorithm requires careful consideration of factors like collision resistance and performance.

2. **Hash Stability**: Ensuring that hashes are stable across different environments and implementations requires careful design.

3. **Semantic Name Resolution**: Resolving semantic names to content hashes requires a registry, which introduces centralization.

4. **Version Extraction**: Extracting versions from content hashes requires a consistent approach to versioning.

5. **Storage Requirements**: Storing content-addressed packages requires more storage than traditional packages, as each version is stored separately.

## Conclusion

Content Addressing provides a robust system for identifying and addressing packages using cryptographic hashes. By combining content addressing with semantic naming, the SpiceTime Architecture creates a dual addressing system that leverages the benefits of both approaches.

The immutable, verifiable nature of content addressing ensures the integrity of packages, while the semantic naming provides human-readable identifiers. This dual addressing system enables a powerful, flexible approach to package management that is well-suited to the distributed, decentralized nature of the SpiceTime Architecture.
