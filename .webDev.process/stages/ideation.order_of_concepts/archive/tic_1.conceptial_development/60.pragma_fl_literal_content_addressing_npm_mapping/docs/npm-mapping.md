# npm Mapping

npm Mapping in the SpiceTime Architecture defines how SpiceTime packages map to and from npm packages. This document details how packages are transformed between pragmatic and npm formats, how pragmatic information is preserved in npm packages, and how transformations are verified against the blockchain registry.

## Conceptual Overview

SpiceTime packages use a pragmatic format that includes additional metadata and behavior not supported by the standard npm package format. To enable integration with the npm ecosystem, SpiceTime provides bidirectional transformations between pragmatic and npm formats. These transformations preserve all pragmatic information while ensuring compatibility with npm.

The npm mapping system is designed to be:
1. **Bidirectional**: Supports transformation in both directions
2. **Preservative**: Preserves all pragmatic information
3. **Compatible**: Ensures compatibility with npm
4. **Verifiable**: Transformations can be verified against the blockchain registry
5. **Deterministic**: Produces consistent results

## Pragmatic to npm Transformation

Pragmatic packages are transformed to npm packages:

```typescript
/**
 * Transform a pragmatic package to an npm package
 * 
 * @param pkg - Pragmatic package to transform
 * @returns Transformed npm package
 */
export function pragmaToNpm(pkg: Package): any {
  // Transform name
  const npmName = `@spicetime/${pkg.semanticName}`;
  
  // Compile code
  const compiledCode = new Map<string, string>();
  for (const [path, source] of pkg.sourceCode.entries()) {
    const compiled = compileCode(source, path);
    const compiledPath = path.replace(/\.tsx?$/, '.js').replace(/^src\//, 'dist/');
    compiledCode.set(compiledPath, compiled);
  }
  
  // Create source map
  const sourceMap: Record<string, string> = {};
  for (const [path, source] of pkg.sourceCode.entries()) {
    sourceMap[path] = source;
  }
  
  // Transform package.json
  return {
    name: npmName,
    version: extractVersionFromContentHash(pkg.contentHash),
    main: 'dist/index.js',
    dependencies: extractDependencies(pkg),
    spicetime: {
      contentHash: pkg.contentHash,
      pragmaExtensions: pkg.pragmaExtensions,
      namepathExtensions: pkg.namepathExtensions,
      sourceMap
    },
    compiledCode
  };
}
```

## npm to Pragmatic Transformation

npm packages are transformed to pragmatic packages:

```typescript
/**
 * Transform an npm package to a pragmatic package
 * 
 * @param pkg - npm package to transform
 * @returns Transformed pragmatic package
 */
export function npmToPragma(pkg: any): Package {
  // Transform name
  const semanticName = pkg.name.replace(/^@spicetime\//, '');
  
  // Extract source code
  const sourceCode = new Map<string, string>();
  if (pkg.spicetime?.sourceMap) {
    for (const [path, source] of Object.entries(pkg.spicetime.sourceMap)) {
      sourceCode.set(path, source);
    }
  } else {
    // Attempt to decompile
    for (const [path, compiled] of pkg.compiledCode.entries()) {
      const sourcePath = path.replace(/\.js$/, '.tsx').replace(/^dist\//, 'src/');
      const source = decompileCode(compiled, path);
      sourceCode.set(sourcePath, source);
    }
  }
  
  // Transform package.json
  return {
    semanticName,
    contentHash: pkg.spicetime?.contentHash || generateContentHash(pkg),
    pragmaExtensions: pkg.spicetime?.pragmaExtensions || {},
    namepathExtensions: pkg.spicetime?.namepathExtensions || {},
    sourceCode
  };
}
```

## Preservation of Pragmatic Information

Pragmatic information is preserved in npm packages:

```typescript
// Pragmatic package
const pragmaticPackage = {
  semanticName: 'ui-components',
  contentHash: 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A',
  pragmaExtensions: {
    // Pragma extensions
  },
  namepathExtensions: {
    // Namepath extensions
  },
  sourceCode: new Map([
    ['src/index.ts', '...'],
    ['src/Button.tsx', '...']
  ])
};

// Transformed npm package
const npmPackage = pragmaToNpm(pragmaticPackage);
/*
{
  name: '@spicetime/ui-components',
  version: '1.0.0',
  main: 'dist/index.js',
  dependencies: {},
  spicetime: {
    contentHash: 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A',
    pragmaExtensions: {
      // Pragma extensions
    },
    namepathExtensions: {
      // Namepath extensions
    },
    sourceMap: {
      'src/index.ts': '...',
      'src/Button.tsx': '...'
    }
  },
  compiledCode: {
    'dist/index.js': '...',
    'dist/Button.js': '...'
  }
}
*/

// Transform back to pragmatic
const roundTrip = npmToPragma(npmPackage);
// Equivalent to the original pragmaticPackage
```

## Verification Against Blockchain Registry

Transformations can be verified against the blockchain registry:

```typescript
/**
 * Verify a transformation against the blockchain registry
 * 
 * @param sourcePackage - Source package
 * @param targetPackage - Target package
 * @param transformation - Transformation function
 * @returns True if the transformation is verified
 */
export async function verifyTransformation(
  sourcePackage: Package,
  targetPackage: Package,
  transformation: (pkg: Package) => Package
): Promise<boolean> {
  // Calculate transformation hash
  const transformationHash = calculateTransformationHash(transformation);
  
  // Get transformation ID
  const sourcePackageHash = sourcePackage.contentHash;
  const targetPackageHash = targetPackage.contentHash;
  const transformationId = ethers.utils.keccak256(
    ethers.utils.defaultAbiCoder.encode(
      ['bytes32', 'bytes32', 'bytes32'],
      [sourcePackageHash, targetPackageHash, transformationHash]
    )
  );
  
  // Verify transformation
  return blockchainRegistry.verifyPackageTransformation(transformationId, transformationHash);
}
```

## Publishing to npm

Pragmatic packages can be published to npm:

```typescript
/**
 * Publish a pragmatic package to npm
 * 
 * @param pkg - Pragmatic package to publish
 * @returns npm package name and version
 */
export async function publishToNpm(pkg: Package): Promise<{ name: string; version: string }> {
  // Transform to npm
  const npmPackage = pragmaToNpm(pkg);
  
  // Write package.json
  fs.writeFileSync('package.json', JSON.stringify(npmPackage, null, 2));
  
  // Write compiled code
  for (const [path, code] of npmPackage.compiledCode.entries()) {
    fs.mkdirSync(path.split('/').slice(0, -1).join('/'), { recursive: true });
    fs.writeFileSync(path, code);
  }
  
  // Publish to npm
  await execAsync('npm publish');
  
  return {
    name: npmPackage.name,
    version: npmPackage.version
  };
}
```

## Installing from npm

npm packages can be installed and transformed to pragmatic packages:

```typescript
/**
 * Install a package from npm and transform to pragmatic
 * 
 * @param name - Package name
 * @param version - Package version
 * @returns Pragmatic package
 */
export async function installFromNpm(name: string, version: string): Promise<Package> {
  // Install from npm
  await execAsync(`npm install ${name}@${version}`);
  
  // Load package
  const npmPackage = require(`${name}/package.json`);
  
  // Transform to pragmatic
  return npmToPragma(npmPackage);
}
```

## Implementation

The npm mapping implementation consists of several parts:

```typescript
/**
 * Helper function to compile code
 * 
 * @param source - Source code to compile
 * @param path - Path of the source file
 * @returns Compiled code
 */
function compileCode(source: string, path: string): string {
  // In a real implementation, this would use TypeScript or Babel to compile the code
  // For now, we'll just return the source code
  return `// Compiled from ${path}\n${source}`;
}

/**
 * Helper function to decompile code
 * 
 * @param compiled - Compiled code to decompile
 * @param path - Path of the compiled file
 * @returns Decompiled code
 */
function decompileCode(compiled: string, path: string): string {
  // In a real implementation, this would use a decompiler to extract the source code
  // For now, we'll just return the compiled code
  return `// Decompiled from ${path}\n${compiled}`;
}

/**
 * Helper function to extract version from content hash
 * 
 * @param contentHash - Content hash to extract version from
 * @returns Extracted version
 */
function extractVersionFromContentHash(contentHash: string): string {
  // In a real implementation, this would extract a version from the content hash
  // For now, we'll just return a placeholder version
  return '1.0.0';
}

/**
 * Helper function to extract dependencies
 * 
 * @param pkg - Package to extract dependencies from
 * @returns Extracted dependencies
 */
function extractDependencies(pkg: Package): Record<string, string> {
  // In a real implementation, this would extract dependencies from the package
  // For now, we'll just return an empty object
  return {};
}

/**
 * Helper function to generate content hash
 * 
 * @param pkg - Package to generate content hash for
 * @returns Generated content hash
 */
function generateContentHash(pkg: any): string {
  // In a real implementation, this would generate a content hash for the package
  // For now, we'll just return a placeholder hash
  return 'QmZ9myDsXfXzUu4XCX6ZAPQGxYcvdRLYAGqkf5qwuA9K4A';
}

/**
 * Helper function to calculate transformation hash
 * 
 * @param transformation - Transformation function
 * @returns Transformation hash
 */
function calculateTransformationHash(transformation: Function): string {
  // In a real implementation, this would calculate a hash of the transformation function
  // For now, we'll just return a placeholder hash
  return '0x1234567890abcdef';
}

/**
 * Helper function to execute a command
 * 
 * @param command - Command to execute
 * @returns Promise that resolves when the command completes
 */
function execAsync(command: string): Promise<void> {
  return new Promise((resolve, reject) => {
    exec(command, (error) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}
```

## Benefits of npm Mapping

npm mapping provides several benefits:

1. **Integration with npm Ecosystem**: Enables integration with the npm ecosystem, leveraging its package management capabilities.

2. **Preservation of Pragmatic Information**: Preserves all pragmatic information in npm packages, ensuring no loss of metadata or behavior.

3. **Bidirectional Transformation**: Supports transformation in both directions, enabling seamless flow between pragmatic and npm formats.

4. **Verification Against Blockchain**: Transformations can be verified against the blockchain registry, ensuring integrity and authenticity.

5. **Deterministic Transformations**: Produces consistent results, enabling reliable transformation between formats.

6. **Compatibility with Existing Tools**: Ensures compatibility with existing npm tools and workflows.

7. **Seamless Developer Experience**: Provides a seamless experience for developers, who can use familiar npm commands.

## Challenges and Considerations

There are some challenges and considerations with npm mapping:

1. **Compilation Complexity**: Compiling TypeScript to JavaScript can be complex, especially with advanced features.

2. **Decompilation Limitations**: Decompiling JavaScript to TypeScript has limitations, as some information is lost during compilation.

3. **Version Mapping**: Mapping between content hashes and semantic versions requires careful consideration.

4. **Dependency Management**: Managing dependencies between pragmatic and npm packages requires careful handling.

5. **Publishing Workflow**: The publishing workflow needs to be integrated with existing development workflows.

## Conclusion

npm Mapping provides a robust system for transforming between pragmatic and npm formats, enabling integration with the npm ecosystem while preserving all pragmatic information. The bidirectional transformations ensure a seamless flow between formats, while the verification against the blockchain registry ensures integrity and authenticity.

This approach enables SpiceTime to leverage the npm ecosystem while maintaining its unique pragmatic approach, providing the best of both worlds for developers.
