# Categorical Version Space

Categorical Version Space in the SpiceTime Architecture defines how versions are structured as a category, with functors defining transformations between versions. This document details how the version space is structured, how transformations are defined, and how this creates a directed graph of versions.

## Conceptual Overview

The version space of SpiceTime is structured as a category, where each version is an object and transformations between versions are morphisms (functors). This creates a directed graph where not all points are reachable from every other point, and some paths are unidirectional. This categorical approach provides a rigorous mathematical foundation for version management.

The categorical version space is designed to be:
1. **Categorical**: Versions are objects in a category, with transformations as morphisms
2. **Directed**: Transformations form a directed graph of versions
3. **Composable**: Transformations can be composed to form paths between versions
4. **Invertible**: Some transformations are invertible, enabling bidirectional conversion
5. **Verifiable**: Transformations can be verified against the blockchain registry

## Version Representation

Versions are represented as objects in a category:

```typescript
/**
 * Version representation
 */
export interface Version {
  major: number;
  minor: number;
  patch: number;
  variant?: string;
}

/**
 * Create a version
 * 
 * @param major - Major version number
 * @param minor - Minor version number
 * @param patch - Patch version number
 * @param variant - Variant identifier
 * @returns Version
 */
export function createVersion(
  major: number,
  minor: number,
  patch: number,
  variant?: string
): Version {
  return { major, minor, patch, variant };
}

/**
 * Convert version to string
 * 
 * @param version - Version to convert
 * @returns Version string
 */
export function versionToString(version: Version): string {
  return `${version.major}.${version.minor}.${version.patch}${version.variant ? `-${version.variant}` : ''}`;
}

/**
 * Check if versions are equal
 * 
 * @param a - First version
 * @param b - Second version
 * @returns True if versions are equal
 */
export function versionsEqual(a: Version, b: Version): boolean {
  return a.major === b.major &&
         a.minor === b.minor &&
         a.patch === b.patch &&
         a.variant === b.variant;
}
```

## Transformation Representation

Transformations are represented as morphisms (functors) in a category:

```typescript
/**
 * Transformation representation
 */
export interface Transformation<T> {
  source: Version;
  target: Version;
  transform: (input: T) => T;
  isInvertible: boolean;
  inverse?: Transformation<T>;
}

/**
 * Create an upgrade functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The upgrade functor
 */
export function createUpgradeFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major !== source.major) {
    throw new Error(`Upgrade functor cannot change major version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  if (target.minor < source.minor || (target.minor === source.minor && target.patch < source.patch)) {
    throw new Error(`Upgrade functor must go forward in version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}

/**
 * Create a major version functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The major version functor
 */
export function createMajorVersionFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major <= source.major) {
    throw new Error(`Major version functor must increase major version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}

/**
 * Create a variant functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The variant functor
 */
export function createVariantFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major !== source.major || target.minor !== source.minor || target.patch !== source.patch) {
    throw new Error(`Variant functor must preserve version numbers: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  if (target.variant === source.variant) {
    throw new Error(`Variant functor must change variant: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}
```

## Functor Composition

Functors can be composed to form paths between versions:

```typescript
/**
 * Compose transformations
 * 
 * @param first - First transformation
 * @param second - Second transformation
 * @returns Composed transformation
 */
export function composeTransformations<T>(
  first: Transformation<T>,
  second: Transformation<T>
): Transformation<T> {
  // Validate versions
  if (!versionsEqual(first.target, second.source)) {
    throw new Error(`Cannot compose transformations: ${versionToString(first.target)} != ${versionToString(second.source)}`);
  }
  
  // Create the composed functor
  const functor: Transformation<T> = {
    source: first.source,
    target: second.target,
    transform: (input: T) => second.transform(first.transform(input)),
    isInvertible: first.isInvertible && second.isInvertible
  };
  
  // Create the inverse functor if both are invertible
  if (first.isInvertible && second.isInvertible && first.inverse && second.inverse) {
    functor.inverse = {
      source: second.target,
      target: first.source,
      transform: (input: T) => first.inverse!.transform(second.inverse!.transform(input)),
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}
```

## Path Finding

Paths between versions can be found:

```typescript
/**
 * Find a path between versions
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transformations - Available transformations
 * @returns Path as a sequence of transformations, or null if no path exists
 */
export function findPath<T>(
  source: Version,
  target: Version,
  transformations: Transformation<T>[]
): Transformation<T>[] | null {
  // If source and target are the same, return empty path
  if (versionsEqual(source, target)) {
    return [];
  }
  
  // Build a graph of versions
  const graph = new Map<string, Map<string, Transformation<T>>>();
  
  for (const transformation of transformations) {
    const sourceStr = versionToString(transformation.source);
    const targetStr = versionToString(transformation.target);
    
    if (!graph.has(sourceStr)) {
      graph.set(sourceStr, new Map());
    }
    
    graph.get(sourceStr)!.set(targetStr, transformation);
  }
  
  // Perform breadth-first search
  const queue: Array<{ version: Version; path: Transformation<T>[] }> = [
    { version: source, path: [] }
  ];
  const visited = new Set<string>();
  
  while (queue.length > 0) {
    const { version, path } = queue.shift()!;
    const versionStr = versionToString(version);
    
    if (visited.has(versionStr)) {
      continue;
    }
    
    visited.add(versionStr);
    
    if (versionsEqual(version, target)) {
      return path;
    }
    
    const neighbors = graph.get(versionStr);
    
    if (neighbors) {
      for (const [neighborStr, transformation] of neighbors.entries()) {
        if (!visited.has(neighborStr)) {
          queue.push({
            version: transformation.target,
            path: [...path, transformation]
          });
        }
      }
    }
  }
  
  // No path found
  return null;
}
```

## Version Graph

The version space forms a directed graph:

```typescript
/**
 * Build a version graph
 * 
 * @param transformations - Available transformations
 * @returns Version graph
 */
export function buildVersionGraph<T>(
  transformations: Transformation<T>[]
): Map<string, Set<string>> {
  // Build a graph of versions
  const graph = new Map<string, Set<string>>();
  
  for (const transformation of transformations) {
    const sourceStr = versionToString(transformation.source);
    const targetStr = versionToString(transformation.target);
    
    if (!graph.has(sourceStr)) {
      graph.set(sourceStr, new Set());
    }
    
    graph.get(sourceStr)!.add(targetStr);
  }
  
  return graph;
}

/**
 * Find all reachable versions
 * 
 * @param source - Source version
 * @param graph - Version graph
 * @returns Set of reachable versions
 */
export function findReachableVersions(
  source: Version,
  graph: Map<string, Set<string>>
): Set<string> {
  const sourceStr = versionToString(source);
  const reachable = new Set<string>();
  const queue: string[] = [sourceStr];
  
  while (queue.length > 0) {
    const current = queue.shift()!;
    
    if (reachable.has(current)) {
      continue;
    }
    
    reachable.add(current);
    
    const neighbors = graph.get(current);
    
    if (neighbors) {
      for (const neighbor of neighbors) {
        if (!reachable.has(neighbor)) {
          queue.push(neighbor);
        }
      }
    }
  }
  
  return reachable;
}
```

## Functor Factories

Functor factories can be created to simplify functor creation:

```typescript
/**
 * Create a version upgrade functor factory
 * 
 * @param majorVersion - Major version
 * @returns A factory function that creates upgrade functors for the specified major version
 */
export function createUpgradeFunctorFactory<T>(majorVersion: number): (
  sourceMinor: number,
  sourcePatch: number,
  targetMinor: number,
  targetPatch: number,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
) => Transformation<T> {
  return (
    sourceMinor: number,
    sourcePatch: number,
    targetMinor: number,
    targetPatch: number,
    transform: (input: T) => T,
    inverseTransform?: (input: T) => T
  ) => {
    const source = createVersion(majorVersion, sourceMinor, sourcePatch);
    const target = createVersion(majorVersion, targetMinor, targetPatch);
    
    return createUpgradeFunctor(source, target, transform, inverseTransform);
  };
}

/**
 * Create a major version functor factory
 * 
 * @returns A factory function that creates major version functors
 */
export function createMajorVersionFunctorFactory<T>(): (
  sourceMajor: number,
  sourceMinor: number,
  sourcePatch: number,
  targetMajor: number,
  targetMinor: number,
  targetPatch: number,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
) => Transformation<T> {
  return (
    sourceMajor: number,
    sourceMinor: number,
    sourcePatch: number,
    targetMajor: number,
    targetMinor: number,
    targetPatch: number,
    transform: (input: T) => T,
    inverseTransform?: (input: T) => T
  ) => {
    const source = createVersion(sourceMajor, sourceMinor, sourcePatch);
    const target = createVersion(targetMajor, targetMinor, targetPatch);
    
    return createMajorVersionFunctor(source, target, transform, inverseTransform);
  };
}
```

## Implementation

The categorical version space implementation consists of several parts:

```typescript
/**
 * Version representation
 */
export interface Version {
  major: number;
  minor: number;
  patch: number;
  variant?: string;
}

/**
 * Transformation representation
 */
export interface Transformation<T> {
  source: Version;
  target: Version;
  transform: (input: T) => T;
  isInvertible: boolean;
  inverse?: Transformation<T>;
}

/**
 * Create a version
 * 
 * @param major - Major version number
 * @param minor - Minor version number
 * @param patch - Patch version number
 * @param variant - Variant identifier
 * @returns Version
 */
export function createVersion(
  major: number,
  minor: number,
  patch: number,
  variant?: string
): Version {
  return { major, minor, patch, variant };
}

/**
 * Convert version to string
 * 
 * @param version - Version to convert
 * @returns Version string
 */
export function versionToString(version: Version): string {
  return `${version.major}.${version.minor}.${version.patch}${version.variant ? `-${version.variant}` : ''}`;
}

/**
 * Check if versions are equal
 * 
 * @param a - First version
 * @param b - Second version
 * @returns True if versions are equal
 */
export function versionsEqual(a: Version, b: Version): boolean {
  return a.major === b.major &&
         a.minor === b.minor &&
         a.patch === b.patch &&
         a.variant === b.variant;
}

/**
 * Create an upgrade functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The upgrade functor
 */
export function createUpgradeFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major !== source.major) {
    throw new Error(`Upgrade functor cannot change major version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  if (target.minor < source.minor || (target.minor === source.minor && target.patch < source.patch)) {
    throw new Error(`Upgrade functor must go forward in version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}

/**
 * Create a major version functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The major version functor
 */
export function createMajorVersionFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major <= source.major) {
    throw new Error(`Major version functor must increase major version: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}

/**
 * Create a variant functor
 * 
 * @param source - Source version
 * @param target - Target version
 * @param transform - Transformation function
 * @param inverseTransform - Inverse transformation function
 * @returns The variant functor
 */
export function createVariantFunctor<T>(
  source: Version,
  target: Version,
  transform: (input: T) => T,
  inverseTransform?: (input: T) => T
): Transformation<T> {
  // Validate versions
  if (target.major !== source.major || target.minor !== source.minor || target.patch !== source.patch) {
    throw new Error(`Variant functor must preserve version numbers: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  if (target.variant === source.variant) {
    throw new Error(`Variant functor must change variant: ${versionToString(source)} -> ${versionToString(target)}`);
  }
  
  // Create the functor
  const functor: Transformation<T> = {
    source,
    target,
    transform,
    isInvertible: !!inverseTransform
  };
  
  // Create the inverse functor if provided
  if (inverseTransform) {
    functor.inverse = {
      source: target,
      target: source,
      transform: inverseTransform,
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}

/**
 * Compose transformations
 * 
 * @param first - First transformation
 * @param second - Second transformation
 * @returns Composed transformation
 */
export function composeTransformations<T>(
  first: Transformation<T>,
  second: Transformation<T>
): Transformation<T> {
  // Validate versions
  if (!versionsEqual(first.target, second.source)) {
    throw new Error(`Cannot compose transformations: ${versionToString(first.target)} != ${versionToString(second.source)}`);
  }
  
  // Create the composed functor
  const functor: Transformation<T> = {
    source: first.source,
    target: second.target,
    transform: (input: T) => second.transform(first.transform(input)),
    isInvertible: first.isInvertible && second.isInvertible
  };
  
  // Create the inverse functor if both are invertible
  if (first.isInvertible && second.isInvertible && first.inverse && second.inverse) {
    functor.inverse = {
      source: second.target,
      target: first.source,
      transform: (input: T) => first.inverse!.transform(second.inverse!.transform(input)),
      isInvertible: true,
      inverse: functor
    };
  }
  
  return functor;
}
```

## Benefits of Categorical Version Space

Categorical version space provides several benefits:

1. **Mathematical Rigor**: The categorical approach provides a rigorous mathematical foundation for version management.

2. **Composable Transformations**: Transformations can be composed to form paths between versions, enabling complex transformations.

3. **Invertible Transformations**: Some transformations are invertible, enabling bidirectional conversion between versions.

4. **Path Finding**: Paths between versions can be found, enabling conversion between arbitrary versions.

5. **Version Graph**: The version space forms a directed graph, providing a clear visualization of version relationships.

6. **Functor Factories**: Functor factories simplify functor creation, reducing boilerplate code.

7. **Categorical Laws**: The categorical approach ensures that transformations follow categorical laws, ensuring consistency.

## Challenges and Considerations

There are some challenges and considerations with categorical version space:

1. **Mathematical Complexity**: The categorical approach introduces mathematical complexity that may be unfamiliar to some developers.

2. **Path Finding Complexity**: Finding paths between versions in a large graph can be computationally expensive.

3. **Functor Implementation**: Implementing functors requires careful consideration of edge cases and error handling.

4. **Version Compatibility**: Ensuring compatibility between versions requires careful design of transformations.

5. **Invertibility**: Not all transformations are invertible, which can limit bidirectional conversion.

## Conclusion

Categorical Version Space provides a rigorous mathematical foundation for version management in the SpiceTime Architecture. By structuring the version space as a category, with versions as objects and transformations as morphisms, it creates a directed graph of versions that enables complex transformations and path finding.

The composable nature of functors enables conversion between arbitrary versions, while the invertible nature of some functors enables bidirectional conversion. This categorical approach ensures that transformations follow categorical laws, ensuring consistency and reliability in version management.
