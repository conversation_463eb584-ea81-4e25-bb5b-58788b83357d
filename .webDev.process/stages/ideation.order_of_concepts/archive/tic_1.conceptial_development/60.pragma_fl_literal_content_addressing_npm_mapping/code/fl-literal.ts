/**
 * File Linguistics (FL) Literal Implementation
 * 
 * This module implements the FL literal tag function and related utilities.
 * FL literals interpret filepath pragmas to generate functionality.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as glob from 'glob';

/**
 * FL literal tag function
 * 
 * @param strings - Template strings
 * @param values - Template values
 * @returns The processed component
 */
export function fl(strings: TemplateStringsArray, ...values: any[]): any {
  // Combine strings and values to get the filepath
  const filepath = String.raw(strings, ...values);
  
  // Process the filepath
  return processFilepath(filepath);
}

/**
 * Discover components based on filepath pattern
 * 
 * @param pattern - Glob pattern
 * @returns Array of processed components
 */
fl.discover = function(pattern: string): any[] {
  // Find files matching pattern
  const files = glob.sync(pattern);
  
  // Process each file
  return files.map(file => {
    const component = processFilepath(file);
    
    // Attach filepath for reference
    component.__fl_filepath = file;
    
    return component;
  });
};

/**
 * Generate index file for components
 * 
 * @param directory - Directory to scan
 * @param options - Options for index generation
 */
fl.generateIndex = function(directory: string, options: {
  pattern: string;
  outputPath: string;
  groupBy?: 'componentType' | 'primaryAction';
}): void {
  // Discover components
  const components = fl.discover(`${directory}/${options.pattern}`);
  
  // Group components
  const groupedComponents = groupComponents(components, options.groupBy);
  
  // Generate index file content
  const content = generateIndexContent(groupedComponents);
  
  // Write index file
  fs.writeFileSync(options.outputPath, content);
};

/**
 * Process a filepath to generate a component
 * 
 * @param filepath - Filepath to process
 * @returns Processed component
 */
function processFilepath(filepath: string): any {
  // Extract pragmas from filepath
  const pragmas = extractPragmas(filepath);
  
  // Import the component
  const component = importComponent(filepath);
  
  // Process the component based on pragmas
  return processPragmas(pragmas, component);
}

/**
 * Extract pragmas from filepath
 * 
 * @param filepath - Filepath to extract pragmas from
 * @returns Extracted pragmas
 */
function extractPragmas(filepath: string): {
  primaryAction: string;
  modifiers: string[];
  componentType: string;
  extension: string;
  filepath: string;
} {
  // Extract filename from path
  const filename = path.basename(filepath);
  
  // Split filename into parts
  const parts = filename.split('.');
  
  // Extract extension
  const extension = parts.pop() || '';
  
  // Extract component type
  const componentType = parts.pop() || '';
  
  // Extract primary action
  const primaryAction = parts.shift() || '';
  
  // Extract modifiers
  const modifiers = parts;
  
  return {
    primaryAction,
    modifiers,
    componentType,
    extension,
    filepath
  };
}

/**
 * Import a component from a filepath
 * 
 * @param filepath - Filepath to import from
 * @returns Imported component
 */
function importComponent(filepath: string): any {
  // In a real implementation, this would dynamically import the component
  // For now, we'll just return a mock component
  
  return {
    __filepath: filepath,
    __mocked: true
  };
}

/**
 * Process pragmas to enhance a component
 * 
 * @param pragmas - Extracted pragmas
 * @param component - Component to process
 * @returns Processed component
 */
function processPragmas(pragmas: {
  primaryAction: string;
  modifiers: string[];
  componentType: string;
  extension: string;
  filepath: string;
}, component: any): any {
  // Process primary action
  const withPrimaryAction = processPrimaryAction(pragmas.primaryAction, component);
  
  // Process modifiers
  const withModifiers = pragmas.modifiers.reduce((comp, modifier) => {
    if (isRelationshipModifier(modifier)) {
      return processRelationshipModifier(modifier, comp);
    } else {
      return processBehaviorModifier(modifier, comp);
    }
  }, withPrimaryAction);
  
  // Process component type
  const withComponentType = processComponentType(pragmas.componentType, pragmas.extension, withModifiers);
  
  // Attach pragma metadata
  withComponentType.__fl_pragmas = pragmas;
  
  return withComponentType;
}

/**
 * Check if a modifier is a relationship modifier
 * 
 * @param modifier - Modifier to check
 * @returns True if the modifier is a relationship modifier
 */
function isRelationshipModifier(modifier: string): boolean {
  return ['with', 'on', 'for', 'from', 'to'].includes(modifier);
}

/**
 * Process primary action
 * 
 * @param action - Primary action
 * @param component - Component to process
 * @returns Processed component
 */
function processPrimaryAction(action: string, component: any): any {
  // In a real implementation, this would enhance the component with behavior
  // based on the primary action
  
  // For now, we'll just attach metadata
  return {
    ...component,
    __fl_primaryAction: action
  };
}

/**
 * Process relationship modifier
 * 
 * @param modifier - Relationship modifier
 * @param component - Component to process
 * @returns Processed component
 */
function processRelationshipModifier(modifier: string, component: any): any {
  // In a real implementation, this would enhance the component with behavior
  // based on the relationship modifier
  
  // For now, we'll just attach metadata
  return {
    ...component,
    __fl_relationshipModifiers: [
      ...(component.__fl_relationshipModifiers || []),
      modifier
    ]
  };
}

/**
 * Process behavior modifier
 * 
 * @param modifier - Behavior modifier
 * @param component - Component to process
 * @returns Processed component
 */
function processBehaviorModifier(modifier: string, component: any): any {
  // In a real implementation, this would enhance the component with behavior
  // based on the behavior modifier
  
  // For now, we'll just attach metadata
  return {
    ...component,
    __fl_behaviorModifiers: [
      ...(component.__fl_behaviorModifiers || []),
      modifier
    ]
  };
}

/**
 * Process component type
 * 
 * @param type - Component type
 * @param extension - File extension
 * @param component - Component to process
 * @returns Processed component
 */
function processComponentType(type: string, extension: string, component: any): any {
  // In a real implementation, this would enhance the component with behavior
  // based on the component type and extension
  
  // For now, we'll just attach metadata
  return {
    ...component,
    __fl_componentType: type,
    __fl_extension: extension
  };
}

/**
 * Group components by a property
 * 
 * @param components - Components to group
 * @param groupBy - Property to group by
 * @returns Grouped components
 */
function groupComponents(components: any[], groupBy?: 'componentType' | 'primaryAction'): Record<string, any[]> {
  if (!groupBy) {
    return { default: components };
  }
  
  return components.reduce((groups, component) => {
    const key = component.__fl_pragmas[groupBy];
    
    if (!groups[key]) {
      groups[key] = [];
    }
    
    groups[key].push(component);
    
    return groups;
  }, {} as Record<string, any[]>);
}

/**
 * Generate index file content
 * 
 * @param groupedComponents - Grouped components
 * @returns Index file content
 */
function generateIndexContent(groupedComponents: Record<string, any[]>): string {
  let content = '/**\n * Generated Index File\n */\n\n';
  
  // Add imports
  Object.entries(groupedComponents).forEach(([group, components]) => {
    content += `// ${group} components\n`;
    
    components.forEach(component => {
      const filepath = component.__fl_filepath;
      const filename = path.basename(filepath);
      const nameWithoutExtension = filename.split('.').slice(0, -1).join('.');
      
      // Convert to PascalCase
      const componentName = nameWithoutExtension
        .split('.')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join('');
      
      content += `import ${componentName} from './${filepath}';\n`;
    });
    
    content += '\n';
  });
  
  // Add exports
  content += '// Exports\n';
  
  Object.entries(groupedComponents).forEach(([group, components]) => {
    content += `// ${group} components\n`;
    
    components.forEach(component => {
      const filepath = component.__fl_filepath;
      const filename = path.basename(filepath);
      const nameWithoutExtension = filename.split('.').slice(0, -1).join('.');
      
      // Convert to PascalCase
      const componentName = nameWithoutExtension
        .split('.')
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join('');
      
      content += `export { ${componentName} };\n`;
    });
    
    content += '\n';
  });
  
  return content;
}

export default fl;
