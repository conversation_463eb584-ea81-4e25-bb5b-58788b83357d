# Pragma Handles All Namespacing

## The Key Insight

The most important aspect of our approach is that **pragma handles all namespacing automatically**. The developer doesn't need to:
- Create namespaces
- Import namespaces
- Export namespaces
- Manage namespace relationships
- Inject namespaces into components

Pragma does all of this automatically based on file structure.

## How Pragma Creates Namespaces

Pragma analyzes the file structure and:

1. **Creates namespaces based on directories**:
   - `/theme` creates a `theme` namespace
   - `/auth` creates an `auth` namespace
   - `/counter` creates a `counter` namespace

2. **Populates namespaces based on context files**:
   - `/theme/context.jsx` defines the content of the `theme` namespace
   - `/auth/context.jsx` defines the content of the `auth` namespace
   - `/counter/context.jsx` defines the content of the `counter` namespace

3. **Makes namespaces available where needed**:
   - Files in `/theme` have access to the `theme` namespace
   - Files in `/auth` have access to the `auth` namespace
   - Files in `/counter` have access to the `counter` namespace
   - App-level files have access to all namespaces

4. **Handles namespace inheritance**:
   - `/theme/dark/context.jsx` creates a `dark` namespace that inherits from `theme`
   - Files in `/theme/dark` have access to both `theme` and `dark` namespaces
   - The `dark` namespace can override properties from the `theme` namespace

## What This Means for Developers

Developers can:

1. **Access state and methods directly**:
   ```jsx
   // In a file in /theme
   <button onClick={toggleTheme}>
     Current theme: {theme}
   </button>
   ```

2. **Define state and methods directly**:
   ```jsx
   // In /theme/context.jsx
   theme = 'light'
   
   toggleTheme() {
     theme = theme === 'light' ? 'dark' : 'light'
   }
   ```

3. **Use nested namespaces**:
   ```jsx
   // In a file that has access to multiple namespaces
   <div>
     <p>Theme: {theme}</p>
     <p>User: {auth.user ? auth.user.name : 'Guest'}</p>
     <p>Count: {counter.count}</p>
   </div>
   ```

All without any imports, exports, providers, or hooks.

## The Magic of Pragma

Pragma's namespacing system:

1. **Is automatic** - based on file structure, not explicit code
2. **Is transparent** - developers don't need to think about it
3. **Is efficient** - optimized at build time
4. **Is flexible** - works with any project structure
5. **Is intuitive** - follows natural organization patterns

This is what makes our approach so powerful - developers can focus on pure intent, while pragma handles all the complex namespacing machinery behind the scenes.
