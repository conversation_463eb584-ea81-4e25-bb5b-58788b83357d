# Concept 58: Mapping React Patterns for AI-Driven Development

## Core Insight

We're not coding for humans - we're coding for AI to follow humans when they set concepts. The entire application structure exists by default, and we're simply extending it where needed, file and folder at a time.

## How It Actually Works

1. **Default Application Structure**: The entire app structure exists by default, with standard components, layouts, and functionality.

2. **Extension Through Files**: When we add a file like `ThemeToggleButton.jsx`, we're not creating a new component from scratch - we're indicating that something needs to be extended.

3. **Spec Patches**: In reality, this is done by issuing a spec patch on the default app, and AI applies the patch.

4. **Targeted Updates**: The AI finds the specific script to update (like a nav element or route) and patches that script - it doesn't rewrite it.

5. **Append, Don't Change**: Nothing is changed, only appended. Each generator script is created by the same process repeated over time.

6. **Build-Time Snapshots**: At build time, a snapshot is made for production, which lives in the storage layer as a graph.

## Example: Extending a Theme Toggle

In a traditional approach, you'd write a ThemeToggleButton component from scratch:

```jsx
// Traditional approach
import React from 'react';
import { useTheme } from './ThemeContext';

function ThemeToggleButton() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button onClick={toggleTheme}>
      Switch to {theme === 'light' ? 'dark' : 'light'} mode
    </button>
  );
}

export default ThemeToggleButton;
```

In our approach:

1. The ThemeToggleButton already exists in the default app
2. Its functionality is defined by the theme (CSS and middleware)
3. It has a default place in the page layout
4. When we create a `ThemeToggleButton.jsx` file, we're indicating that we want to extend something that contains this button
5. The AI finds the appropriate script (maybe a nav element) and patches it
6. The patch might look like:

```jsx
// What the developer writes in ThemeToggleButton.jsx
// This is a spec patch, not a complete component
position = 'header-right'
variant = 'outlined'
label = 'Toggle Dark Mode'
```

The AI then finds the nav element that contains the ThemeToggleButton and patches it:

```jsx
// What the AI patches (developer never sees this)
// In NavBar.jsx or similar
<nav>
  <Logo />
  <MenuItems />
  <ThemeToggleButton 
    position="header-right"
    variant="outlined"
    label="Toggle Dark Mode"
  />
  <UserMenu />
</nav>
```

## Coding Concepts and Specs, Not Scripts

We're not writing code in the traditional sense - we're writing concepts and specs that the AI translates into code:

1. **Linguistic Templates**: Using linguistic templates (`l` templates) to express concepts clearly
2. **Functional Compositions**: Specs are executed as functional compositions
3. **Domain Creation**: When English can't express something clearly, AI creates linguistic terms after the meaning is explained
4. **AI-Driven Patching**: AI applies patches to specs on default or previous versions and implementations

## Implications

This approach has profound implications:

1. **Speed**: Development is much faster as we're just specifying changes, not writing full implementations
2. **Consistency**: The default app ensures consistency across the application
3. **Focus on Intent**: Developers focus on what they want to change, not how to implement it
4. **AI Collaboration**: AI becomes a true collaborator, not just a code generator
5. **Evolving System**: The system evolves over time through patches, not rewrites

## Next Steps

This concept leads directly to Concept 59: AI-Driven Development Through Specs, which will explore:

1. How specs are written and interpreted
2. How AI applies patches to existing code
3. How the system evolves over time
4. How linguistic templates enable clear expression of intent
5. How domains are created and extended
