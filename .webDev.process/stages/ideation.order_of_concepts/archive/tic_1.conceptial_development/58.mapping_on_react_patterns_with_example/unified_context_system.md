# Unified Context System: Mapping <PERSON>act Patterns to Structure

## Overview

This concept explores how <PERSON><PERSON>'s core patterns map to our structural approach, preserving <PERSON><PERSON>'s intent while enhancing its capabilities. React's context system, component composition, and hooks are powerful but often lead to verbose, nested code. By mapping these patterns to file structure, we maintain their core benefits while eliminating their drawbacks.

## Context as Scope

Contexts form scope and follow the structure as scopes do. They form a scopedTree and create namespaces in each layer of scope as it evolves through structure. Each context shadows the one in parent - that's a general pattern of how trees are crossed.

React's Context API was designed to solve prop drilling, providing a way to share values across components without passing props through every level. However, this often leads to deeply nested providers. Our approach preserves the intent of context (shared state across components) while eliminating the nesting problem by expressing context relationships through file structure.

## Context Files and Structure

If there's a `context.jsx` file, it's a compound parent component with a provider as a child. Other children, downscope, get that context through function scope, provided by pragma. If a folder does not have context, it does not insert context namespace in the node scope, obviously.

Then, there can be `a.context.jsx` - that's not a provider, but `context.a.jsx` is a provider `a` as `myComponent.provider.a` and in node scope as `scope.context.a` - but component rerenders on `a` and `b`, if there's a `context.b.jsx`, separately.

But if `context.jsx` or `context.shape_a_b.jsx` for being declarative, exports a shape `{a,b}`, it's same scope structure, but rerender is on any change.

This approach maps React's Context API to file structure, where:
- File naming patterns determine context behavior
- Directory structure defines context hierarchy
- File exports define context values
- Rerender behavior is controlled through file organization

## Context as State

Context can have anything - a func or an atomic type like string, or a ref to a string, or an object, or an object or string itself. That context is mutable by any node having access to it, as a sibling or some node using provider as a dep of its package. And that string or object can change any time and cause a rerender. Therefore - context is the state. We need no explicit state - job is done. And the funcs in context are state vars.

React separates context from state, requiring useState/useReducer alongside context. This separation creates unnecessary complexity. By recognizing that context itself can be state, we eliminate this artificial division. React's context updates trigger rerenders just like state updates - we simply leverage this existing behavior more directly.

## Hooks Creation and Usage

We have to stick to React convention - a hook has to be `useBlaMe` and their invocation in a component define the order of state update. But where they come from? Out of context. We have to use `use` in what's intended to be hooks, and stick them in context. And compose them out of other hooks as usual in context file, for sure why not, but that's not very declarative.

A declarative way is to have a separate `hooks.<type pragma>.ts` file. And if `useMe.ts` - we get a func hook called `useMe` in hooks namespace in scope. The hook name overrides name of export, so just have a default export. And we can have a `hook.ts` and the name is set by export, if default, you get `useHook`. And we can have `hooks.obj.ts` and we get a namespace of hooks. If we have two of those, they can be two hooks namespaces or be children of hooks. We can go on forever.

React hooks were designed to reuse stateful logic between components, but their implementation requires careful attention to rules of hooks. Our approach preserves the intent of hooks (reusable stateful logic) while making their organization more declarative through file structure. The hook naming convention is maintained, but the organization becomes more intuitive.

## Namespace Resolution

We need some uniform way to reason structure out of names. I guess there are pragmatic ext and paths in scope structure. And we can have some keynames that can resolve conflicts, like `name_<namespace>` means a namespace in scope. But `.<namespace>` can be a pragma if there's a pragma of that name in scope so we can have reserved names `name` and `pragma` or `n` and `p` for shorthand.

And then some pragmatic helpers like `on`, `of`, `with` and other linguistics toys, monads. This kinda stuff must come through our linguistics capabilities and be expressed in scope, but way down the proto layers.

React has no built-in concept of namespaces, leading to naming conflicts and verbose imports. Our approach introduces namespaces through file structure, making code organization more intuitive while maintaining compatibility with React's module system.

## Implicit Provider Resolution

When a component is declared as dep and used in composition, there's no need to wrap its provider explicitly in structure. That would be extremely confusing. I guess that has to do with composing apps out of composed components in another part of structure.

But we can mix and match compound children, if we provide appropriate provider wrappers. That forms a provider hell and expressed in structure is a nightmare of deeply confused structure. So, when a component is used, its provider comes with it. The nearest implied provider up the parent chain is used. If none of sibling compound children exists, that's where provider is installed in generated JSX.

What props that provider gets? Gets defaults - the context is set in structure. It's possible to have pragmatic props as private scope members as described in concepts, and we can have some expressive mechanism to override defaults by a context file that targets that specific compound. But that will get too complex fast. And people will just create sensible defaults and there are other ways to do what context props do, like state and hooks. Just pick a lane lady, how about using state ;)

So, it'll clean up syntax and structure a lot. And no more provider hell, as those can be completely thrown out of JSX, by simply manipulating the context that gets passed through scopes. It's redundant to complicate syntax with redundant expression. So, each component is provider, technically, but in practice - not.

React's component composition model requires explicit provider wrapping, leading to deeply nested provider trees. Our approach preserves the intent of providers (making context available to components) while eliminating the nesting problem through implicit provider resolution. Components and their providers become conceptually linked, simplifying the mental model.

## Example: Authentication System

To illustrate how these patterns map to structure, consider an authentication system:

```
/auth
  context.jsx           // Base auth context
  context.user.jsx      // User-specific context
  context.roles.jsx     // Roles-specific context
  useAuth.ts            // Main auth hook
  useUser.ts            // User-specific hook
  useRoles.ts           // Roles-specific hook
  LoginForm.jsx         // Login component
  UserProfile.jsx       // Profile component
```

In traditional React, this would require:
1. Creating separate context files
2. Explicitly nesting providers
3. Manually connecting hooks to contexts
4. Ensuring components are wrapped with the right providers

In our approach:
1. File structure defines the context hierarchy
2. Providers are implicitly included when components are used
3. Hooks are automatically connected to their contexts
4. Components just work without explicit provider wrapping

The intent of React's patterns is preserved, but the implementation becomes more declarative and less verbose.

## Conclusion

By mapping React patterns to structure, we preserve their intent while enhancing their capabilities. This approach:

1. Maintains compatibility with React's mental model
2. Eliminates common pain points like provider hell
3. Makes context and state management more intuitive
4. Provides a declarative way to organize hooks
5. Introduces namespaces for better code organization

Most importantly, this approach doesn't fight against React - it enhances React by expressing its patterns through structure rather than syntax. The result is cleaner, more maintainable code that still follows React's core principles.
