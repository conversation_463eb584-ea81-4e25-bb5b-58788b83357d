# Context as Scope

## React Pattern

In React, Context provides a way to pass data through the component tree without having to pass props down manually at every level. It's designed to share data that can be considered "global" for a tree of React components.

```jsx
// React's Context API
const ThemeContext = React.createContext('light');

function App() {
  return (
    <ThemeContext.Provider value="dark">
      <ThemedButton />
    </ThemeContext.Provider>
  );
}

function ThemedButton() {
  const theme = useContext(ThemeContext);
  return <button className={theme}>Themed Button</button>;
}
```

## Structural Mapping

In our system, contexts form scope and follow the structure as scopes do. They form a scopedTree and create namespaces in each layer of scope as it evolves through structure. Each context shadows the one in parent - that's a general pattern of how trees are crossed.

```
/theme
  context.jsx         // Creates theme context
  /button
    context.jsx       // Creates button context that shadows theme context
```

The file `theme/context.jsx` creates a theme context, and `theme/button/context.jsx` creates a button context that shadows the theme context. This creates a scope hierarchy that mirrors the directory structure.

## Implementation

When a context is defined in a file, it automatically becomes part of the scope:

```jsx
// theme/context.jsx
export default function ThemeContext() {
  return {
    theme: 'light',
    toggleTheme: () => {
      this.theme = this.theme === 'light' ? 'dark' : 'light';
    }
  };
}

// theme/button/context.jsx
export default function ButtonContext() {
  // This context shadows the theme context
  // It has access to theme context through prototype chain
  return {
    variant: 'primary',
    setVariant: (variant) => {
      this.variant = variant;
    }
  };
}
```

## Benefits

This approach:
- Eliminates the need for explicit provider nesting
- Creates a clear, hierarchical structure for contexts
- Makes the relationship between contexts explicit through file structure
- Allows for context shadowing, where child contexts can override parent contexts
- Maintains React's mental model of context while simplifying its implementation
