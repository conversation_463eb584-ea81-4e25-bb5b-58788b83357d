# stPragma Integration

This document explains how stPragma integrates with our approach to mapping React patterns to file structure.

## What is stPragma?

stPragma is our high-performance Rust-based pragma engine that:
1. Interprets file extensions as pragmas
2. Transforms code based on these pragmas
3. Generates optimized JavaScript/TypeScript
4. Handles dependency resolution and injection

## File Extensions as Pragmas

In our system, file extensions serve as pragmas that modify behavior:

```
context.jsx                 // Standard context provider
context.user.jsx            // User-specific context provider
useAuth.ts                  // Standard hook
useAuth.withRefresh.ts      // Hook with refresh capability
```

Each extension is processed by stPragma, which applies the corresponding transformation.

## Dynamic Imports

stPragma handles dynamic imports based on file structure:

```jsx
// This code:
import { useAuth } from './auth/useAuth';

// Is transformed to:
import { useAuth } from './auth/useAuth';
import { AuthContext } from './auth/context';
```

The imports are resolved at build time, eliminating the need for explicit imports in your code.

## Provider Injection

stPragma automatically injects providers where needed:

```jsx
// This code:
function App() {
  return <Dashboard />;
}

// Is transformed to:
function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Dashboard />
      </ThemeProvider>
    </AuthProvider>
  );
}
```

The providers are injected based on which contexts each component uses.

## Context Inheritance

stPragma handles context inheritance through the prototype chain:

```jsx
// auth/context.jsx
export default function AuthContext() {
  return {
    // Auth context implementation
  };
}

// auth/user/context.jsx
export default function UserContext() {
  // This is transformed to:
  const authContext = useContext(AuthContext);
  const context = Object.create(authContext);
  
  // Add user-specific properties
  Object.assign(context, {
    // User context implementation
  });
  
  return context;
}
```

The inheritance is handled automatically based on file structure.

## Hook Organization

stPragma organizes hooks based on file naming:

```
useAuth.ts                  // Creates useAuth hook
hooks.form.ts               // Creates hooks.form namespace
hooks.form.validation.ts    // Creates hooks.form.validation namespace
```

The hooks are organized into namespaces based on file naming, making their relationships explicit.

## Implementation Details

stPragma is implemented in Rust for high performance:

```rust
// Simplified Rust implementation
fn process_file(file_path: &str, content: &str) -> String {
    let pragmas = extract_pragmas_from_path(file_path);
    let mut transformed = content.to_string();
    
    for pragma in pragmas {
        transformed = apply_pragma(&pragma, &transformed);
    }
    
    transformed
}

fn extract_pragmas_from_path(path: &str) -> Vec<Pragma> {
    let parts: Vec<&str> = path.split('.').collect();
    let mut pragmas = Vec::new();
    
    // Extract pragmas from file name
    for i in 1..parts.len() - 1 {
        pragmas.push(Pragma {
            name: parts[i].to_string(),
            params: HashMap::new(),
        });
    }
    
    pragmas
}

fn apply_pragma(pragma: &Pragma, content: &str) -> String {
    match pragma.name.as_str() {
        "context" => transform_context(content),
        "use" => transform_hook(content),
        "hooks" => transform_hooks_namespace(content),
        // Other pragmas...
        _ => content.to_string(),
    }
}
```

The actual implementation is more complex, handling dependency resolution, provider injection, and code optimization.

## Integration with Build Process

stPragma integrates with your build process through a webpack or babel plugin:

```javascript
// webpack.config.js
module.exports = {
  // ...
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'stpragma-loader',
          options: {
            // stPragma options
          }
        }
      }
    ]
  }
};
```

This allows stPragma to process your files during the build process, transforming them based on file extensions.

## Runtime Behavior

At runtime, the transformed code behaves like standard React code:
1. Components use standard hooks to access context
2. The context is resolved through React's normal context mechanism
3. If a context is missing, you get the standard React error message
4. No runtime performance penalty compared to manual provider nesting

This creates a seamless developer experience, with all the complexity handled at build time.
