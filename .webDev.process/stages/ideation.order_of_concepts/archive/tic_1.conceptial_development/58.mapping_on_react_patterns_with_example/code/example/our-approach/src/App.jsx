import React from 'react';
import Header from './theme/Header';
import TodoList from './todos/TodoList';
import TodoForm from './todos/TodoForm';
import TodoFilter from './todos/TodoFilter';
import LoginForm from './auth/LoginForm';
import { useAuth } from './auth/useAuth';

function App() {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <LoginForm />;
  }
  
  return (
    <div className="app">
      <Header />
      <main>
        <TodoForm />
        <TodoFilter />
        <TodoList />
      </main>
    </div>
  );
}

export default App;
