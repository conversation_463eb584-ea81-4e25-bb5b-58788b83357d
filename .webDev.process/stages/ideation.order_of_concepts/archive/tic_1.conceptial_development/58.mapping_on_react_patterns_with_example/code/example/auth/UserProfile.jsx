/*
User profile component - Written by developer
<PERSON> intent, no React machinery
*/

// UI and behavior
<div className="user-profile">
  <h2>Welcome, {user.name}</h2>
  <button onClick={logout}>Logout</button>
</div>

/*
What pragma does behind the scenes:
- Creates a functional component
- Accesses the auth namespace (no imports needed)
- Makes user and logout available in this component's scope
- <PERSON><PERSON> re-rendering when user changes
*/
