// Using context namespaces
// File: LoginForm.jsx

function LoginForm() {
  // Access form context directly
  // No imports, no hooks - pragma injects form into scope
  
  // Initialize form on first render
  // In a real implementation, this would use a useEffect-like mechanism
  const loginForm = form.createForm('login', {
    email: '',
    password: ''
  });
  
  // Get validated fields
  const emailField = form.validation.validateEmail('login', 'email');
  const passwordField = form.validation.validateRequired('login', 'password');
  
  const handleSubmit = loginForm.handleSubmit((values) => {
    // Submit logic
    console.log('Submitting:', values);
  });
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>Email</label>
        <input
          name={emailField.name}
          value={emailField.value}
          onChange={emailField.onChange}
          onBlur={emailField.onBlur}
        />
        {emailField.error && <div>{emailField.error}</div>}
      </div>
      
      <div>
        <label>Password</label>
        <input
          type="password"
          name={passwordField.name}
          value={passwordField.value}
          onChange={passwordField.onChange}
          onBlur={passwordField.onBlur}
        />
        {passwordField.error && <div>{passwordField.error}</div>}
      </div>
      
      <button type="submit">Login</button>
    </form>
  );
}
