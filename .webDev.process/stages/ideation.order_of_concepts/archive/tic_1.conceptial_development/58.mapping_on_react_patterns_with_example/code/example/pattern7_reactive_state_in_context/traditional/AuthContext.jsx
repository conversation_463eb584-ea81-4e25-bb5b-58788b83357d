// Traditional approach with <PERSON><PERSON> hooks
import React, { createContext, useState, useContext, useCallback } from 'react';

// Create context
export const AuthContext = createContext();

// Create provider component
export function AuthProvider({ children }) {
  // State managed with React hooks
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Methods that modify state
  const login = useCallback(async (username, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (username && password) {
        setUser({ name: username, role: 'user' });
        setIsLoading(false);
        return true;
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (err) {
      setError(err.message);
      setIsLoading(false);
      return false;
    }
  }, []);
  
  const logout = useCallback(() => {
    setUser(null);
  }, []);
  
  // Context value
  const value = {
    user,
    isLoading,
    error,
    login,
    logout,
    isAuthenticated: !!user
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hooks for consuming context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function useUser() {
  const { user } = useContext(AuthContext);
  if (user === undefined) {
    throw new Error('useUser must be used within an AuthProvider');
  }
  return user;
}

export function useIsAuthenticated() {
  const { isAuthenticated } = useContext(AuthContext);
  if (isAuthenticated === undefined) {
    throw new Error('useIsAuthenticated must be used within an AuthProvider');
  }
  return isAuthenticated;
}

export function useAuthStatus() {
  const { isLoading, error } = useContext(AuthContext);
  if (isLoading === undefined) {
    throw new Error('useAuthStatus must be used within an AuthProvider');
  }
  return { isLoading, error };
}
