/*
Login form component - Written by developer
Pure intent, no React machinery
*/

// Local state
username = ''
password = ''

// UI and behavior
<form onSubmit={(e) => {
  e.preventDefault()
  login(username, password)
}}>
  {error && <div className="error">{error}</div>}

  <div>
    <label>Username</label>
    <input
      value={username}
      onChange={(e) => username = e.target.value}
      disabled={isLoading}
    />
  </div>

  <div>
    <label>Password</label>
    <input
      type="password"
      value={password}
      onChange={(e) => password = e.target.value}
      disabled={isLoading}
    />
  </div>

  <button type="submit" disabled={isLoading}>
    {isLoading ? 'Logging in...' : 'Login'}
  </button>
</form>

/*
What pragma does behind the scenes:
- Creates a functional component
- Sets up local state with useState for username and password
- Accesses the auth namespace (no imports needed)
- Makes login, error, isLoading available in this component's scope
- Handles form submission and input changes
- <PERSON><PERSON> re-rendering when state changes
*/
