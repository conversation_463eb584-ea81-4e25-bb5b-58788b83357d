// Context Namespaces with validation
// File: form/context.validation.jsx

// Context that extends form context
export default function ValidationContext() {
  return {
    // Required field validation
    validateRequired(formId, name) {
      const field = form.getField(formId, name);
      
      if (field.touched && !field.value) {
        const form = form.getForm(formId);
        form.errors[name] = 'This field is required';
        form.notify();
      }
      
      return field;
    },
    
    // Email validation
    validateEmail(formId, name) {
      const field = form.getField(formId, name);
      
      if (field.touched && field.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
          const form = form.getForm(formId);
          form.errors[name] = 'Please enter a valid email';
          form.notify();
        }
      }
      
      return field;
    }
  };
}
