/*
Base context definition
No imports needed - pragma injects scope
*/

// Return the context object directly
return {
  // Common notification system
  listeners: new Set(),
  subscribe(listener) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  },
  notify() {
    this.listeners.forEach(listener => listener())
  },
  
  // Common utility methods
  log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`)
  },
  
  // Common state
  isLoading: false,
  setLoading(value) {
    this.isLoading = value
    this.notify()
  }
}
