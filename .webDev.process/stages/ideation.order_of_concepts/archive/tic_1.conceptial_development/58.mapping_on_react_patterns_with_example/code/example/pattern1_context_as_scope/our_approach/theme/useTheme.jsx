// Our approach - hook defined through file structure
// File: theme/useTheme.jsx

// Hook is defined by the file location
// No imports needed - pragma injects scope
export default function useTheme() {
  // Access theme context (automatically injected by pragma)
  // No need for useContext - pragma handles this
  
  // Return the public API
  return {
    theme: theme.theme,
    toggleTheme: () => theme.toggleTheme()
  };
}

// The pragma system automatically:
// 1. Injects the correct context
// 2. Handles subscription to context changes
// 3. Ensures the hook is used within the correct provider
