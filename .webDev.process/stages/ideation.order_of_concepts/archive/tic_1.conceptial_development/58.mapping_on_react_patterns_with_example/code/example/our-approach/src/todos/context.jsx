export default function TodoContext() {
  // Initialize from localStorage
  const savedTodos = localStorage.getItem('todos');
  const initialTodos = savedTodos 
    ? JSON.parse(savedTodos) 
    : [];
  
  return {
    // State
    todos: initialTodos,
    filter: 'all',
    
    // Methods
    addTodo(text) {
      this.todos = [
        ...this.todos,
        {
          id: Date.now(),
          text,
          completed: false
        }
      ];
      this.saveTodos();
      this.notify();
    },
    
    toggleTodo(id) {
      this.todos = this.todos.map(todo =>
        todo.id === id
          ? { ...todo, completed: !todo.completed }
          : todo
      );
      this.saveTodos();
      this.notify();
    },
    
    deleteTodo(id) {
      this.todos = this.todos.filter(
        todo => todo.id !== id
      );
      this.saveTodos();
      this.notify();
    },
    
    editTodo(id, text) {
      this.todos = this.todos.map(todo =>
        todo.id === id
          ? { ...todo, text }
          : todo
      );
      this.saveTodos();
      this.notify();
    },
    
    setFilter(filter) {
      this.filter = filter;
      this.notify();
    },
    
    getFilteredTodos() {
      return this.todos.filter(todo => {
        if (this.filter === 'all') return true;
        if (this.filter === 'active') 
          return !todo.completed;
        if (this.filter === 'completed') 
          return todo.completed;
        return true;
      });
    },
    
    saveTodos() {
      localStorage.setItem(
        'todos', 
        JSON.stringify(this.todos)
      );
    },
    
    // Notification system
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}
