/*
Base theme context
No imports needed - pragma injects scope
*/

// Return the context object directly
return {
  // Theme state
  theme: 'light',
  colors: {
    primary: '#0070f3',
    secondary: '#ff4081',
    background: '#ffffff',
    text: '#333333'
  },
  
  // Theme methods
  toggleTheme() {
    this.theme = this.theme === 'light' ? 'dark' : 'light'
    
    // Update colors based on theme
    if (this.theme === 'dark') {
      this.colors.background = '#333333'
      this.colors.text = '#ffffff'
    } else {
      this.colors.background = '#ffffff'
      this.colors.text = '#333333'
    }
    
    this.notify()
  },
  
  // Notification system
  listeners: new Set(),
  subscribe(listener) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  },
  notify() {
    this.listeners.forEach(listener => listener())
  }
}
