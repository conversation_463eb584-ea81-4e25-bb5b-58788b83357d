/*
Counter component - Written by developer
Pure intent, no React machinery
*/

// UI and behavior
<div className="counter">
  <h2>Count: {count}</h2>
  <div className="counter-controls">
    <button onClick={decrement}>-</button>
    <button onClick={increment}>+</button>
    <button onClick={reset}>Reset</button>
  </div>
</div>

/*
What pragma does behind the scenes:
- Creates a functional component
- Accesses the counter namespace (no imports needed)
- Makes count, increment, decrement, reset available in this component's scope
- <PERSON><PERSON> re-rendering when count changes
*/
