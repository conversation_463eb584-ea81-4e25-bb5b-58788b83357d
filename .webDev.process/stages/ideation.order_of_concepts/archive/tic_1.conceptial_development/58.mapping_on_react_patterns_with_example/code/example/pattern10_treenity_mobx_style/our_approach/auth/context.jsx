/*
Treenity/MobX-style reactive state in context
This file defines the auth namespace in deps.prod
*/

// Define the auth namespace
deps.prod.auth = {
  // Observable state properties
  user: null,
  isLoading: false,
  error: null,

  // Actions that modify state
  async login(username, password) {
    // State mutations are automatically tracked
    this.isLoading = true
    this.error = null

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (username && password) {
        // State mutations trigger automatic updates
        this.user = { name: username, role: 'user' }
        this.isLoading = false
        return true
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (err) {
      this.error = err.message
      this.isLoading = false
      return false
    }
  },

  logout() {
    // State mutation triggers automatic update
    this.user = null
  },

  // Computed values (automatically cached and reactive)
  get isAuthenticated() {
    return !!this.user
  },

  get userName() {
    return this.user ? this.user.name : 'Guest'
  },

  get userRole() {
    return this.user ? this.user.role : null
  }

  // No need for manual notification system
  // Pragma makes all state automatically observable
}
