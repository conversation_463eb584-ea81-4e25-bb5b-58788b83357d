/*
Theme context
File location defines this as a theme context
No imports, no exports, no React API
*/

// Just the state and behavior
theme = 'light'

colors = {
  primary: '#0070f3',
  secondary: '#ff4081',
  background: '#ffffff',
  text: '#333333'
}

toggleTheme() {
  theme = theme === 'light' ? 'dark' : 'light'
  
  if (theme === 'light') {
    colors.primary = '#0070f3'
    colors.secondary = '#ff4081'
    colors.background = '#ffffff'
    colors.text = '#333333'
  } else {
    colors.primary = '#3291ff'
    colors.secondary = '#ff6b6b'
    colors.background = '#222222'
    colors.text = '#f5f5f5'
  }
}

// Computed properties automatically created
// isDarkMode = theme === 'dark'
