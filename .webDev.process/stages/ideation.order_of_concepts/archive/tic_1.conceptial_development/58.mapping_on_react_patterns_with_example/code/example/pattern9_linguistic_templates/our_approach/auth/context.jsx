/*
Using linguistic templates for even more concise syntax
No imports needed - pragma injects scope
*/

// Using linguistic template for auth state
return l`
  user as nullable state
  with isLoading as boolean state initially false
  with error as nullable string state
  with computed isAuthenticated returning !!user
  with async login(username, password) method
  with logout method
`

/*
This expands to:

return {
  user: null,
  isLoading: false,
  error: null,
  
  get isAuthenticated() {
    return !!this.user
  },
  
  async login(username, password) {
    this.isLoading = true
    this.error = null
    this.notify()
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (username && password) {
        this.user = { name: username, role: 'user' }
        this.isLoading = false
        this.notify()
        return true
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (err) {
      this.error = err.message
      this.isLoading = false
      this.notify()
      return false
    }
  },
  
  logout() {
    this.user = null
    this.notify()
  },
  
  listeners: new Set(),
  subscribe(listener) {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  },
  notify() {
    this.listeners.forEach(listener => listener())
  }
}
*/
