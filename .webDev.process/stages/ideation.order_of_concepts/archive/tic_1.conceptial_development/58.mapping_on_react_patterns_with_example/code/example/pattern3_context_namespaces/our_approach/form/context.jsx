// Context Namespaces pattern
// File: form/context.jsx

// Context with nested functionality
export default function FormContext() {
  return {
    // State
    forms: new Map(),
    
    // Create a form
    createForm(id, initialValues = {}) {
      const form = {
        id,
        values: initialValues,
        errors: {},
        touched: {},
        
        handleChange(e) {
          const { name, value } = e.target;
          this.values[name] = value;
          formContext.notify();
        },
        
        handleBlur(e) {
          const { name } = e.target;
          this.touched[name] = true;
          formContext.notify();
        },
        
        validate() {
          // Validation logic
          return Object.keys(this.errors).length === 0;
        },
        
        handleSubmit(onSubmit) {
          return (e) => {
            e.preventDefault();
            if (this.validate()) {
              onSubmit(this.values);
            }
          };
        }
      };
      
      this.forms.set(id, form);
      this.notify();
      return form;
    },
    
    // Get a form
    getForm(id) {
      return this.forms.get(id);
    },
    
    // Get a field from a form
    getField(formId, name) {
      const form = this.getForm(formId);
      if (!form) return null;
      
      return {
        name,
        value: form.values[name] || '',
        error: form.errors[name],
        touched: form.touched[name] || false,
        onChange: form.handleChange,
        onBlur: form.handleBlur
      };
    },
    
    // Notification system
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}
