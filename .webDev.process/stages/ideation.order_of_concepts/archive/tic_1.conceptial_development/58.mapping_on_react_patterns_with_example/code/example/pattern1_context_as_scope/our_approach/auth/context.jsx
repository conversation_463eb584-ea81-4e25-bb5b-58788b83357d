// Our approach - context defined through file structure
// File: auth/context.jsx

// Context is defined by the file location
// No explicit context creation or provider component
export default function AuthContext() {
  return {
    // State
    user: null,
    
    // Methods
    login(username, password) {
      // Authentication logic
      this.user = { name: username };
      this.notify();
    },
    
    logout() {
      this.user = null;
      this.notify();
    },
    
    // Notification system (handled by pragma)
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}

// The pragma system automatically:
// 1. Creates a React context
// 2. Creates a provider component
// 3. Injects the provider where needed
// 4. Handles context inheritance
