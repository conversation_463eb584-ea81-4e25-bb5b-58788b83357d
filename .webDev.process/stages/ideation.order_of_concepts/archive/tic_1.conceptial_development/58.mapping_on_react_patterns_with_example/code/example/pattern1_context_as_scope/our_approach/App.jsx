// Our approach - no explicit provider wrapping
// File: App.jsx

function App() {
  return (
    // No explicit provider wrapping
    // Providers are injected automatically by the pragma system
    <div className="app">
      <Header />
      <Content />
    </div>
  );
}

export default App;

// The pragma system transforms this to:
// function App() {
//   return (
//     <AuthProvider>
//       <ThemeProvider>
//         <div className="app">
//           <Header />
//           <Content />
//         </div>
//       </ThemeProvider>
//     </AuthProvider>
//   );
// }
