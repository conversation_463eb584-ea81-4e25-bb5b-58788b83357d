// Our approach - hook defined through file structure
// File: auth/useAuth.jsx

// Hook is defined by the file location
// No imports needed - pragma injects scope
export default function useAuth() {
  // Access auth context (automatically injected by pragma)
  // No need for useContext - pragma handles this
  
  // Return the public API
  return {
    user: auth.user,
    login: (username, password) => auth.login(username, password),
    logout: () => auth.logout()
  };
}

// The pragma system automatically:
// 1. Injects the correct context
// 2. Handles subscription to context changes
// 3. Ensures the hook is used within the correct provider
