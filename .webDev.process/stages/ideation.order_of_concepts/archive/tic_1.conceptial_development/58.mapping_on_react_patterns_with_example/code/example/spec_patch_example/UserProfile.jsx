/*
Spec patch for user profile page
*/

// Using linguistic template to express the overall concept
l`user profile page with:
  - avatar at top
  - name below avatar
  - bio section with markdown support
  - social links as icons
  - activity feed at bottom
`

// Specific implementation details
avatar.size = 'large'
avatar.border = true
avatar.borderColor = 'primary'

name.variant = 'h4'
name.fontWeight = 'bold'

bio.maxLength = 500
bio.markdown = true
bio.placeholder = 'Write something about yourself...'

socialLinks = ['twitter', 'github', 'linkedin']
socialLinks.displayAs = 'icons'
socialLinks.size = 'medium'

activityFeed.limit = 10
activityFeed.showDate = true
activityFeed.showTime = false

// The AI will find or create the appropriate components and patch them
// The developer doesn't need to worry about the implementation details
