# Structure-Based React Patterns

This directory contains examples of our structure-based approach to React patterns. Instead of using traditional React patterns with explicit imports, providers, and hooks, we use file structure to define behavior.

## What the Developer Writes vs. What Pragma Handles

### Developer Writes:
- Pure intent focused on business logic and UI
- No imports, no exports
- No React.createContext(), no providers
- No useState, useContext, or other hooks
- No dependency arrays

### Pragma Handles (in high-performance Rust core):
- Creating contexts
- Setting up providers
- Managing state
- Injecting dependencies
- Handling reactivity
- Generating all React machinery
- Optimizing code for performance

The pragma logic runs in a high-performance Rust core within spicetime nodes, which are pre-loaded on devices like phones. This provides many times better performance than JavaScript-based alternatives.

## Key Differences from Traditional React

### 1. No Imports

In traditional React, you'd need:
```jsx
import React, { useState } from 'react';
import { ThemeContext } from './ThemeContext';
```

In our approach:
- No imports needed
- Pragma automatically makes namespaces available
- Pragma analyzes file structure and creates namespaces
- Files in `/theme` automatically have access to theme state and methods

### 2. No Providers

In traditional React, you'd need:
```jsx
<ThemeProvider>
  <AuthProvider>
    <App />
  </AuthProvider>
</ThemeProvider>
```

In our approach:
- No provider wrapping needed
- Pragma handles all provider setup
- Components automatically have access to the contexts they need

### 3. No Hooks

In traditional React, you'd need:
```jsx
const [count, setCount] = useState(0);
const { theme } = useContext(ThemeContext);
```

In our approach:
- Direct access to state: `count`, `theme`
- Direct mutation of state: `count++`, `theme = 'dark'`
- No useState, useContext, or other hooks needed

### 4. No Boilerplate

In traditional React, you'd need:
```jsx
const CounterContext = createContext();

function CounterProvider({ children }) {
  const [count, setCount] = useState(0);

  const increment = () => setCount(count + 1);
  const decrement = () => setCount(count - 1);

  return (
    <CounterContext.Provider value={{ count, increment, decrement }}>
      {children}
    </CounterContext.Provider>
  );
}
```

In our approach:
```jsx
count = 0

increment() {
  count++
}

decrement() {
  count--
}
```

## Examples

- `/theme` - Theme context and components
- `/auth` - Authentication context and components
- `/counter` - Counter context and components
- `/app` - Main application component

Each example shows:
- What the developer writes (pure intent)
- Comments explaining what pragma does behind the scenes
