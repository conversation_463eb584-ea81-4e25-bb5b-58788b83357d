# Spec Patch Examples

These examples demonstrate how development works in our AI-driven approach. Instead of writing complete components from scratch, developers write spec patches that the AI interprets and applies to the existing codebase.

## Key Concepts

1. **Default Application**: The entire app structure exists by default
2. **Spec Patches**: Developers write specs to patch the default app
3. **AI Interpretation**: AI interprets specs and applies patches
4. **Targeted Updates**: Patches are applied to specific parts of the codebase
5. **Append, Don't Change**: Changes are appended, not overwritten

## Examples

### ThemeToggleButton.jsx

This spec patch modifies the existing ThemeToggleButton in the navigation:

```jsx
// Patch specifications
position = 'header-right'
variant = 'outlined'
label = 'Toggle Dark Mode'
icon = 'moon'
showLabel = false
showIcon = true
```

The AI finds the appropriate component (likely the NavBar) and patches it to include these specifications for the ThemeToggleButton.

### UserProfile.jsx

This spec patch defines a user profile page:

```jsx
// Using linguistic template to express the overall concept
l`user profile page with:
  - avatar at top
  - name below avatar
  - bio section with markdown support
  - social links as icons
  - activity feed at bottom
`

// Specific implementation details
avatar.size = 'large'
bio.maxLength = 500
socialLinks = ['twitter', 'github', 'linkedin']
// ...more specifications
```

The AI interprets this spec and creates or patches the necessary components to implement the user profile page according to these specifications.

## How This Differs from Traditional Development

In traditional development:
- Developers write complete components from scratch
- Components must be manually integrated into the application
- Changes require modifying existing code
- Development is slow and error-prone

In our approach:
- Developers write high-level specs
- AI handles the implementation details
- Changes are applied as patches
- Development is fast and focused on intent

## Benefits

1. **Speed**: Development is much faster
2. **Focus**: Developers focus on what they want, not how to implement it
3. **Consistency**: Changes are applied consistently
4. **Evolution**: System evolves naturally without major rewrites
5. **Accessibility**: Domain experts can write specs without deep technical knowledge
