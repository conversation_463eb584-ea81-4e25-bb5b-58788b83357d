/*
Theme context - Generated by pragma
Full React machinery
*/

import React, { createContext, useState, useEffect } from 'react';

// Create context
export const ThemeContext = createContext();

// Create provider
export function ThemeProvider({ children }) {
  // State management
  const [theme, setTheme] = useState('light');
  
  // Methods
  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };
  
  // Context value
  const value = {
    theme,
    toggleTheme
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}
