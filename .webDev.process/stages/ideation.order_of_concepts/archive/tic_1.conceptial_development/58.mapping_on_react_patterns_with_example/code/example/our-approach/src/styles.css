/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s, color 0.3s;
}

body.light {
  background-color: #f5f5f5;
  color: #333;
}

body.dark {
  background-color: #222;
  color: #f5f5f5;
}

.app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid;
}

.header.light {
  border-color: #ddd;
}

.header.dark {
  border-color: #444;
}

.header-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* Theme toggle */
.theme-toggle {
  background: none;
  border: 1px solid;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.light .theme-toggle {
  border-color: #333;
  color: #333;
}

.light .theme-toggle:hover {
  background-color: #333;
  color: #fff;
}

.dark .theme-toggle {
  border-color: #f5f5f5;
  color: #f5f5f5;
}

.dark .theme-toggle:hover {
  background-color: #f5f5f5;
  color: #222;
}

/* User profile */
.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-profile button {
  background: none;
  border: 1px solid;
  padding: 5px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.light .user-profile button {
  border-color: #d32f2f;
  color: #d32f2f;
}

.light .user-profile button:hover {
  background-color: #d32f2f;
  color: #fff;
}

.dark .user-profile button {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.dark .user-profile button:hover {
  background-color: #ff6b6b;
  color: #222;
}

/* Todo form */
.todo-form {
  display: flex;
  margin-bottom: 20px;
}

.todo-form input {
  flex: 1;
  padding: 10px;
  border: 1px solid;
  border-radius: 4px 0 0 4px;
  font-size: 16px;
}

.light .todo-form input {
  border-color: #ddd;
  background-color: #fff;
  color: #333;
}

.dark .todo-form input {
  border-color: #444;
  background-color: #333;
  color: #f5f5f5;
}

.todo-form button {
  padding: 10px 15px;
  border: none;
  border-radius: 0 4px 4px 0;
  background-color: #0070f3;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.todo-form button:hover {
  background-color: #0051a8;
}

/* Todo filter */
.todo-filter {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.todo-filter button {
  padding: 8px 12px;
  border: 1px solid;
  border-radius: 4px;
  background: none;
  cursor: pointer;
  transition: all 0.3s;
}

.light .todo-filter button {
  border-color: #ddd;
  color: #333;
}

.dark .todo-filter button {
  border-color: #444;
  color: #f5f5f5;
}

.todo-filter button.active {
  background-color: #0070f3;
  border-color: #0070f3;
  color: #fff;
}

/* Todo list */
.todo-list {
  list-style: none;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 4px;
  transition: all 0.3s;
}

.light .todo-item {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .todo-item {
  background-color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.todo-item input[type="checkbox"] {
  margin-right: 15px;
  transform: scale(1.2);
}

.todo-text {
  flex: 1;
  cursor: pointer;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  opacity: 0.7;
}

.todo-actions {
  display: flex;
  gap: 10px;
}

.todo-actions button {
  background: none;
  border: 1px solid;
  padding: 5px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.light .todo-actions button:first-child {
  border-color: #0070f3;
  color: #0070f3;
}

.light .todo-actions button:first-child:hover {
  background-color: #0070f3;
  color: #fff;
}

.light .todo-actions button:last-child {
  border-color: #d32f2f;
  color: #d32f2f;
}

.light .todo-actions button:last-child:hover {
  background-color: #d32f2f;
  color: #fff;
}

.dark .todo-actions button:first-child {
  border-color: #3291ff;
  color: #3291ff;
}

.dark .todo-actions button:first-child:hover {
  background-color: #3291ff;
  color: #222;
}

.dark .todo-actions button:last-child {
  border-color: #ff6b6b;
  color: #ff6b6b;
}

.dark .todo-actions button:last-child:hover {
  background-color: #ff6b6b;
  color: #222;
}

/* Login form */
.login-form {
  max-width: 400px;
  margin: 100px auto;
  padding: 30px;
  border-radius: 8px;
  transition: all 0.3s;
}

.light .login-form {
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .login-form {
  background-color: #333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.login-form h2 {
  margin-bottom: 20px;
  text-align: center;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid;
  border-radius: 4px;
  font-size: 16px;
}

.light .form-group input {
  border-color: #ddd;
  background-color: #fff;
  color: #333;
}

.dark .form-group input {
  border-color: #444;
  background-color: #333;
  color: #f5f5f5;
}

.login-form button {
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 4px;
  background-color: #0070f3;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
}

.login-form button:hover {
  background-color: #0051a8;
}

.error {
  color: #d32f2f;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(211, 47, 47, 0.1);
}

.demo-info {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
  opacity: 0.7;
}
