/*
Auth context - Written by developer
<PERSON> intent, no React machinery
*/

// State
user = null
isLoading = false
error = null

// Methods
async login(username, password) {
  isLoading = true
  error = null

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (username && password) {
      user = { name: username }
      isLoading = false
      return true
    } else {
      throw new Error('Invalid credentials')
    }
  } catch (err) {
    error = err.message
    isLoading = false
    return false
  }
}

logout() {
  user = null
}

/*
What pragma does behind the scenes:
- Creates a React context (React.createContext())
- Sets up a provider component with useState for user, isLoading, error
- Implements login and logout to update state
- Creates an 'auth' namespace containing user, isLoading, error, login, logout
- Makes the auth namespace available to components in this directory
- Handles reactivity and re-renders
- Manages async operations
*/
