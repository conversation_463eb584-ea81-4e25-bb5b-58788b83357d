/*
Theme context - Written by developer
<PERSON> intent, no React machinery
*/

// State
theme = 'light'

// Methods
toggleTheme() {
  theme = theme === 'light' ? 'dark' : 'light'
}

/*
What pragma does behind the scenes:
- Creates a React context (React.createContext())
- Sets up a provider component with useState for theme
- Implements toggleTheme to update state
- Creates a 'theme' namespace containing theme and toggleTheme
- Makes the theme namespace available to components in this directory
- Handles reactivity and re-renders
*/
