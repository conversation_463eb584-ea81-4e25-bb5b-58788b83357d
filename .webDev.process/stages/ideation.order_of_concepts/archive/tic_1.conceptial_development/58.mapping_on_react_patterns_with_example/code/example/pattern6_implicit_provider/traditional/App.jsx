// Traditional approach with explicit provider wrapping
import React from 'react';
import { ThemeProvider } from './theme/ThemeContext';
import { AuthProvider } from './auth/AuthContext';
import { DashboardThemeProvider } from './dashboard/theme/ThemeContext';
import Header from './Header';
import Dashboard from './dashboard/Dashboard';
import Footer from './Footer';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <div className="app">
          <Header />
          <main>
            <DashboardThemeProvider>
              <Dashboard />
            </DashboardThemeProvider>
          </main>
          <Footer />
        </div>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
