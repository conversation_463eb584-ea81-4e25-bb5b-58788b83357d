import { useState, useEffect } from 'react';
import { useContext } from 'react';
import { AuthContext } from './context';

export default function useAuth() {
  // Get the context
  const context = useContext(AuthContext);
  
  // Local state that updates when context changes
  const [isAuthenticated, setIsAuthenticated] = useState(context.isAuthenticated);
  const [user, setUser] = useState(context.user);
  
  // Subscribe to context changes
  useEffect(() => {
    const unsubscribe = context.subscribe(() => {
      setIsAuthenticated(context.isAuthenticated);
      setUser(context.user);
    });
    
    return unsubscribe;
  }, [context]);
  
  // Return the public API
  return {
    isAuthenticated,
    user,
    login: (username, password) => context.login(username, password),
    logout: () => context.logout()
  };
}
