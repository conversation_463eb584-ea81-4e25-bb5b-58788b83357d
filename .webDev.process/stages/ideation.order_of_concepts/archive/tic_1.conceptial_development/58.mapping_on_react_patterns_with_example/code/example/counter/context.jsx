/*
Counter context - Written by developer
<PERSON> intent, no React machinery
*/

// State
count = 0

// Methods
increment() {
  count++
}

decrement() {
  count--
}

reset() {
  count = 0
}

/*
What pragma does behind the scenes:
- Creates a React context (React.createContext())
- Sets up a provider component with useState for count
- Implements increment, decrement, reset to update state
- Creates a 'counter' namespace containing count, increment, decrement, reset
- Makes the counter namespace available to components in this directory
- <PERSON><PERSON> reactivity and re-renders
*/
