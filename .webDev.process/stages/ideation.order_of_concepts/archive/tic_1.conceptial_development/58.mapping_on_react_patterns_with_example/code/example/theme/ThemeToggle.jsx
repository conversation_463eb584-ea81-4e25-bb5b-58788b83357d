/*
Theme toggle component - Written by developer
<PERSON> intent, no React machinery
*/

// UI and behavior
<button onClick={toggleTheme}>
  Current theme: {theme}
</button>

/*
What pragma does behind the scenes:
- Creates a functional component
- Accesses the theme namespace (no imports needed)
- Makes theme and toggleTheme available in this component's scope
- <PERSON><PERSON> re-rendering when theme changes
*/
