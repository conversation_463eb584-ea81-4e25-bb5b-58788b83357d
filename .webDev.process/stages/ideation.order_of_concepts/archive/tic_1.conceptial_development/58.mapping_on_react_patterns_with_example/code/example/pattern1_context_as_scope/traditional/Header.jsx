// Traditional approach - explicit context consumption
import React from 'react';
import { useTheme } from './ThemeContext';
import { useAuth } from './AuthContext';

function Header() {
  // Explicitly consume contexts
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  
  return (
    <header className={`header ${theme}`}>
      <h1>My App</h1>
      <div>
        <button onClick={toggleTheme}>
          Switch to {theme === 'light' ? 'dark' : 'light'} mode
        </button>
        {user && (
          <div>
            <span>Welcome, {user.name}</span>
            <button onClick={logout}>Logout</button>
          </div>
        )}
      </div>
    </header>
  );
}

export default Header;
