import { useState, useEffect } from 'react';
import { useContext } from 'react';
import { TodoContext } from './context';

export default function useTodos() {
  // Get the context
  const context = useContext(TodoContext);
  
  // Local state that updates when context changes
  const [todos, setTodos] = useState(
    context.getFilteredTodos()
  );
  const [filter, setFilter] = useState(context.filter);
  
  // Subscribe to context changes
  useEffect(() => {
    const unsubscribe = context.subscribe(() => {
      setTodos(context.getFilteredTodos());
      setFilter(context.filter);
    });
    
    return unsubscribe;
  }, [context]);
  
  // Return the public API
  return {
    todos,
    filter,
    addTodo: (text) => context.addTodo(text),
    toggleTodo: (id) => context.toggleTodo(id),
    deleteTodo: (id) => context.deleteTodo(id),
    editTodo: (id, text) => context.editTodo(id, text),
    setFilter: (filter) => context.setFilter(filter)
  };
}
