# Context Mapping Examples

This document provides concrete examples of how React context patterns map to file structure.

## Basic Context Provider

### React Pattern
```jsx
// ThemeContext.js
const ThemeContext = React.createContext(null);

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('light');
  
  const toggleTheme = useCallback(() => {
    setTheme(t => t === 'light' ? 'dark' : 'light');
  }, []);
  
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

### Structural Mapping
```
/theme
  context.jsx         // Theme context provider
  useTheme.ts         // Theme context consumer hook
```

### Implementation
```jsx
// theme/context.jsx
export default function ThemeContext() {
  return {
    theme: 'light',
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light';
      this.notify();
    },
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}

// theme/useTheme.ts
import { useContext, useState, useEffect } from 'react';
import { ThemeContext } from './context';

export default function useTheme() {
  const context = useContext(ThemeContext);
  const [theme, setTheme] = useState(context.theme);
  
  useEffect(() => {
    const unsubscribe = context.subscribe(() => {
      setTheme(context.theme);
    });
    return unsubscribe;
  }, [context]);
  
  return {
    theme,
    toggleTheme: () => context.toggleTheme()
  };
}
```

## Nested Context Providers

### React Pattern
```jsx
// App.js
function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <UserProvider>
          <Content />
        </UserProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
```

### Structural Mapping
```
/theme
  context.jsx         // Theme context provider
/auth
  context.jsx         // Auth context provider
  /user
    context.jsx       // User context provider
/components
  Content.jsx         // Content component
```

### Implementation
```jsx
// components/Content.jsx
import { useTheme } from '../theme/useTheme';
import { useAuth } from '../auth/useAuth';
import { useUser } from '../auth/user/useUser';

export default function Content() {
  const { theme } = useTheme();
  const { isAuthenticated } = useAuth();
  const { user } = useUser();
  
  if (!isAuthenticated) {
    return <LoginForm />;
  }
  
  return (
    <div className={theme}>
      <h1>Welcome, {user.name}</h1>
      {/* Content */}
    </div>
  );
}
```

When `Content` is used, it automatically gets access to all the contexts it needs without explicit provider wrapping. The providers come with the component and are resolved implicitly.

## Context with Multiple Values

### React Pattern
```jsx
// ThemeContext.js
export function ThemeProvider({ children }) {
  const [colors, setColors] = useState({
    primary: '#0070f3',
    secondary: '#ff4081'
  });
  
  const [fonts, setFonts] = useState({
    body: 'system-ui',
    heading: 'Georgia'
  });
  
  return (
    <ColorsContext.Provider value={{ colors, setColors }}>
      <FontsContext.Provider value={{ fonts, setFonts }}>
        {children}
      </FontsContext.Provider>
    </ColorsContext.Provider>
  );
}
```

### Structural Mapping
```
/theme
  context.colors.jsx  // Colors context provider
  context.fonts.jsx   // Fonts context provider
```

### Implementation
```jsx
// theme/context.colors.jsx
export default function ColorsContext() {
  return {
    primary: '#0070f3',
    secondary: '#ff4081',
    setPrimary(color) {
      this.primary = color;
      this.notify();
    },
    setSecondary(color) {
      this.secondary = color;
      this.notify();
    },
    // Notification system
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}

// theme/context.fonts.jsx
export default function FontsContext() {
  return {
    body: 'system-ui',
    heading: 'Georgia',
    setBody(font) {
      this.body = font;
      this.notify();
    },
    setHeading(font) {
      this.heading = font;
      this.notify();
    },
    // Notification system
    listeners: new Set(),
    subscribe(listener) {
      this.listeners.add(listener);
      return () => this.listeners.delete(listener);
    },
    notify() {
      this.listeners.forEach(listener => listener());
    }
  };
}
```

## Hook Organization

### React Pattern
```jsx
// useForm.js
export function useForm(initialValues) {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  
  // Form logic...
  
  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit
  };
}

// useField.js
export function useField(name, form) {
  const { values, errors, touched, handleChange, handleBlur } = form;
  
  return {
    name,
    value: values[name],
    error: errors[name],
    touched: touched[name],
    onChange: handleChange,
    onBlur: handleBlur
  };
}
```

### Structural Mapping
```
/form
  hooks.ts            // Form hooks namespace
  hooks.field.ts      // Field-specific hooks
  hooks.validation.ts // Validation-specific hooks
```

### Implementation
```jsx
// form/hooks.ts
export function useForm(initialValues) {
  // Form implementation
}

// form/hooks.field.ts
import { useForm } from './hooks';

export function useField(name, formId) {
  const form = useForm(formId);
  // Field implementation
}

// form/hooks.validation.ts
import { useField } from './hooks.field';

export function useRequired(name, formId) {
  const field = useField(name, formId);
  // Validation implementation
}
```

These examples demonstrate how React's patterns map directly to file structure, preserving React's core model while eliminating common pain points like provider nesting and verbose imports.
