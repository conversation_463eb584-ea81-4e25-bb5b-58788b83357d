/**
 * Example implementation of the Multi-Instance Architecture core components
 */

// Instance Registry
export interface InstanceInfo {
  id: string;
  type: 'github-pages' | 'dedicated-server' | 'local-device';
  url: string;
  capabilities: string[];
  status: 'online' | 'offline' | 'degraded';
  lastSeen: number;
  priority: number;
}

export class InstanceRegistry {
  private instances: Map<string, InstanceInfo> = new Map();
  private listeners: Set<(instances: InstanceInfo[]) => void> = new Set();
  
  // Register an instance
  register(instance: InstanceInfo): void {
    this.instances.set(instance.id, instance);
    this.notifyListeners();
  }
  
  // Discover available instances
  async discover(): Promise<InstanceInfo[]> {
    // Local discovery (mDNS, Bluetooth, etc.)
    const localInstances = await this.discoverLocal();
    
    // Remote discovery (DNS, well-known endpoints, etc.)
    const remoteInstances = await this.discoverRemote();
    
    // Combine and deduplicate
    const allInstances = [...localInstances, ...remoteInstances];
    const uniqueInstances = this.deduplicateInstances(allInstances);
    
    // Update registry
    for (const instance of uniqueInstances) {
      this.instances.set(instance.id, instance);
    }
    
    this.notifyListeners();
    
    return Array.from(this.instances.values());
  }
  
  // Get instance by ID
  getInstance(id: string): InstanceInfo | undefined {
    return this.instances.get(id);
  }
  
  // Get all registered instances
  getAllInstances(): InstanceInfo[] {
    return Array.from(this.instances.values());
  }
  
  // Add a listener for registry changes
  addListener(listener: (instances: InstanceInfo[]) => void): void {
    this.listeners.add(listener);
  }
  
  // Remove a listener
  removeListener(listener: (instances: InstanceInfo[]) => void): void {
    this.listeners.delete(listener);
  }
  
  // Notify listeners about registry changes
  private notifyListeners(): void {
    const instances = this.getAllInstances();
    for (const listener of this.listeners) {
      listener(instances);
    }
  }
  
  // Private methods for discovery
  private async discoverLocal(): Promise<InstanceInfo[]> {
    // Implementation for local discovery
    // This is a simplified example
    return [
      {
        id: 'local-device',
        type: 'local-device',
        url: 'http://localhost:8000',
        capabilities: ['static-content', 'data-gateway', 'dynamic-functionality'],
        status: 'online',
        lastSeen: Date.now(),
        priority: 3
      }
    ];
  }
  
  private async discoverRemote(): Promise<InstanceInfo[]> {
    // Implementation for remote discovery
    // This is a simplified example
    return [
      {
        id: 'github-pages',
        type: 'github-pages',
        url: 'https://username.github.io/repo',
        capabilities: ['static-content', 'data-gateway'],
        status: 'online',
        lastSeen: Date.now(),
        priority: 1
      },
      {
        id: 'dedicated-server',
        type: 'dedicated-server',
        url: 'https://example.com',
        capabilities: ['static-content', 'data-gateway', 'dynamic-functionality', 'high-bandwidth'],
        status: 'online',
        lastSeen: Date.now(),
        priority: 2
      }
    ];
  }
  
  private deduplicateInstances(instances: InstanceInfo[]): InstanceInfo[] {
    // Implementation for deduplication
    // This is a simplified example
    const uniqueInstances = new Map<string, InstanceInfo>();
    
    for (const instance of instances) {
      uniqueInstances.set(instance.id, instance);
    }
    
    return Array.from(uniqueInstances.values());
  }
}

// Instance Router
export interface RouteRequest {
  path: string;
  requiredCapabilities?: string[];
  strategy?: RouteStrategy;
  params?: Record<string, string>;
}

export interface RouteResult {
  instanceId: string;
  url: string;
  instance: InstanceInfo;
}

export type RouteStrategy = 'priority' | 'latency' | 'random';

export class InstanceRouter {
  constructor(private registry: InstanceRegistry) {}
  
  // Route a request to the best instance
  async route(request: RouteRequest): Promise<RouteResult> {
    // Get all available instances
    const instances = this.registry.getAllInstances()
      .filter(instance => instance.status !== 'offline')
      .filter(instance => this.hasRequiredCapabilities(instance, request.requiredCapabilities));
    
    if (instances.length === 0) {
      throw new Error('No suitable instances available');
    }
    
    // Select the best instance based on the strategy
    const selectedInstance = this.selectInstance(instances, request.strategy);
    
    // Route the request
    return {
      instanceId: selectedInstance.id,
      url: this.buildUrl(selectedInstance, request),
      instance: selectedInstance
    };
  }
  
  // Check if an instance has the required capabilities
  private hasRequiredCapabilities(instance: InstanceInfo, required: string[] = []): boolean {
    return required.every(cap => instance.capabilities.includes(cap));
  }
  
  // Select the best instance based on the strategy
  private selectInstance(instances: InstanceInfo[], strategy: RouteStrategy = 'priority'): InstanceInfo {
    switch (strategy) {
      case 'priority':
        // Select the instance with the highest priority
        return instances.sort((a, b) => b.priority - a.priority)[0];
        
      case 'latency':
        // Select the instance with the lowest latency
        // This would require latency measurements
        // This is a simplified example
        return instances[0];
        
      case 'random':
        // Select a random instance
        return instances[Math.floor(Math.random() * instances.length)];
        
      default:
        return instances[0];
    }
  }
  
  // Build the URL for the request
  private buildUrl(instance: InstanceInfo, request: RouteRequest): string {
    let url = `${instance.url}${request.path}`;
    
    // Add query parameters if provided
    if (request.params && Object.keys(request.params).length > 0) {
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(request.params)) {
        queryParams.append(key, value);
      }
      url += `?${queryParams.toString()}`;
    }
    
    return url;
  }
}

// Same-Domain Bridge
export interface BridgeMessage {
  type: string;
  payload: any;
  id?: string;
}

export type MessageHandler = (payload: any, origin: string) => any;

export class SameDomainBridge {
  private messageHandlers: Map<string, MessageHandler> = new Map();
  
  constructor() {
    // Set up message listener
    window.addEventListener('message', this.handleMessage.bind(this));
  }
  
  // Send a message to another instance
  sendMessage(targetOrigin: string, message: BridgeMessage): void {
    const iframe = this.findIframeByOrigin(targetOrigin);
    
    if (iframe) {
      // Send to iframe
      iframe.contentWindow.postMessage(message, targetOrigin);
    } else if (window.parent && window.parent !== window) {
      // Send to parent
      window.parent.postMessage(message, targetOrigin);
    } else {
      console.error(`No target found for origin: ${targetOrigin}`);
    }
  }
  
  // Register a message handler
  registerHandler(type: string, handler: MessageHandler): void {
    this.messageHandlers.set(type, handler);
  }
  
  // Unregister a message handler
  unregisterHandler(type: string): void {
    this.messageHandlers.delete(type);
  }
  
  // Handle incoming messages
  private handleMessage(event: MessageEvent): void {
    const { type, payload, id } = event.data as BridgeMessage;
    
    // Find the handler for this message type
    const handler = this.messageHandlers.get(type);
    
    if (handler) {
      // Execute the handler
      Promise.resolve(handler(payload, event.origin))
        .then(response => {
          // Send response if an ID was provided
          if (id) {
            this.sendMessage(event.origin, {
              type: `${type}_response`,
              payload: response,
              id
            });
          }
        })
        .catch(error => {
          // Send error if an ID was provided
          if (id) {
            this.sendMessage(event.origin, {
              type: `${type}_error`,
              payload: { message: error.message },
              id
            });
          }
        });
    }
  }
  
  // Find an iframe by its origin
  private findIframeByOrigin(origin: string): HTMLIFrameElement | null {
    const iframes = Array.from(document.querySelectorAll('iframe'));
    
    for (const iframe of iframes) {
      try {
        if (iframe.src.startsWith(origin)) {
          return iframe;
        }
      } catch (e) {
        // Ignore cross-origin errors
      }
    }
    
    return null;
  }
}

// Example usage in a Gatsby application
export function initializeMultiInstanceArchitecture() {
  // Create the instance registry
  const instanceRegistry = new InstanceRegistry();
  
  // Create the instance router
  const instanceRouter = new InstanceRouter(instanceRegistry);
  
  // Create the same-domain bridge
  const sameDomainBridge = new SameDomainBridge();
  
  // Register the current instance
  instanceRegistry.register({
    id: 'current-instance',
    type: 'github-pages', // or 'dedicated-server' or 'local-device'
    url: window.location.origin,
    capabilities: ['static-content', 'data-gateway'],
    status: 'online',
    lastSeen: Date.now(),
    priority: 1
  });
  
  // Discover other instances
  instanceRegistry.discover().catch(console.error);
  
  // Set up message handlers
  sameDomainBridge.registerHandler('discover_instances', async () => {
    const instances = await instanceRegistry.discover();
    return { instances };
  });
  
  sameDomainBridge.registerHandler('route_request', async (request) => {
    const result = await instanceRouter.route(request);
    return result;
  });
  
  // Expose the services to the window for iframe access
  window.spiceTimeServices = {
    instanceRegistry,
    instanceRouter,
    sameDomainBridge
  };
  
  // Return the services for use in components
  return {
    instanceRegistry,
    instanceRouter,
    sameDomainBridge
  };
}

// Example React component that uses the multi-instance architecture
export function DynamicFunctionality({ functionality, data }) {
  const iframeRef = React.useRef(null);
  const [status, setStatus] = React.useState('loading');
  
  React.useEffect(() => {
    // Handle messages from the iframe
    const handleMessage = (event) => {
      // Verify the origin for security
      if (event.origin !== window.location.origin) return;
      
      const { type, payload } = event.data;
      
      if (type === 'REQUEST_DATA') {
        // Send the data to the iframe
        iframeRef.current.contentWindow.postMessage({
          type: 'DATA_RESPONSE',
          payload: data
        }, '*');
      } else if (type === 'STATUS_UPDATE') {
        setStatus(payload.status);
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [data]);
  
  return (
    <div className="dynamic-functionality">
      <div className="status-indicator">
        Status: {status}
      </div>
      
      <iframe
        ref={iframeRef}
        src={`/dynamic/${functionality}.html`}
        title={`Dynamic ${functionality}`}
        style={{ width: '100%', height: '500px', border: 'none' }}
      />
    </div>
  );
}
