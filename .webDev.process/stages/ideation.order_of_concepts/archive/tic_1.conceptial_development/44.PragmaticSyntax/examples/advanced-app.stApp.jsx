// @section setup
const serviceConfig = {
  endpoints: ['users', 'products', 'orders'],
  security: 'oauth2',
  caching: true
};

const theme = process.env.NODE_ENV === 'production' 
  ? 'corporate' 
  : 'development';

const services = [
  { id: 'api', preset: 'graphql-api', config: { schema: './schema.graphql' } },
  { id: 'auth', preset: 'oauth-provider', config: { providers: ['google', 'github'] } },
  { id: 'db', preset: 'postgres', config: { migrations: 'automatic' } }
];
// @end

// @section helpers
function renderServices() {
  return services.map(service => (
    <Node 
      key={service.id}
      id={service.id} 
      preset={service.preset} 
      config={service.config} 
    />
  ));
}
// @end

// @return

<SpiceTimeApp preset="saas" theme={theme}>
  {renderServices()}
  <Node id="monitoring" preset="prometheus" />
</SpiceTimeApp>
