# Meta Package Architecture

## Overview

The Meta Package provides a bidirectional synchronization mechanism between the repository filesystem structure and a distributed storage layer. It maintains a live representation of the project's temporal and structural dimensions through a specialized folder structure.

## Core Components

1. **Repository Structure**
    - **Present**: Active working files in the repository root
    - **Meta Folder**: Temporal and process projections of the repository

2. **Storage Layer**
    - **Graph DB**: Maintains structural relationships and links
    - **MongoDB**: Stores content and document data
    - **Vector DB**: Manages semantic information and similarity

3. **WebDevProcess Orchestration**
    - Coordinates interactions between Meta Package and Storage Layer
    - Applies process-specific transformations to meta structure

## Workflow

```mermaid
graph TD
    A[Changes to Repository] --> B[Storage Layer Records Changes]
    B --> C[Meta Package Generates Update Patch]
    C --> D[Meta Folder Updated]
    E[Process Stage Activities] --> F[Proposal Creation]
    F --> G[Decision Making Process]
    G --> H[Accepted Proposal]
    H --> I[Future Structure Updated]
    I --> J[Tic Issuance]
    J --> K[Future → Present → Past Rotation]
```

1. Changes are made directly to the repository filesystem
2. Storage layer records these changes across distributed databases
3. Meta package generates patches to update the meta folder
4. Meta folder structure reflects the WebDevProcess profile

## Meta Folder Structure

```
meta/
├── future/         # Deterministic future being built in current tic
├── past/           # Archived previous states
├── stages/         # WebDevProcess stage trees
│   ├── ideation.stage/
│   ├── design.stage/
│   ├── build.stage/
│   ├── test.stage/
│   └── type.stage/
├── proposals/      # Future versions subject to decision process
└── linkages/       # Cross-cutting relationships
```

## Temporal Rotation

When a tic is issued:
1. Future becomes Present
2. Present becomes Past
3. Meta stays with Past
4. New Future initialized with inherited structure
5. Meta initialized from Past with inherited elements

## Stage Interactions

- Each stage maintains a pure representation of its domain
- Stages can be combined into dipoles and tripods via duplex links
- Proposals can be made by any stage
- Accepted proposals update all affected stages

## Permission Structure

- Not all documents are available to all participants
- Permission structure controls access to different parts of meta
- AI assistants review proposals from perspectives of all stages