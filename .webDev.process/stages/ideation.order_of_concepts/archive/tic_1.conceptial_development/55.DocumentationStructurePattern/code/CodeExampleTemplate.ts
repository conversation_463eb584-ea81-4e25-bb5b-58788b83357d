/**
 * CodeExampleTemplate.ts
 * 
 * This file demonstrates the recommended structure and documentation
 * for code examples in the SpiceTime architecture.
 */

/**
 * Interface definition with comprehensive documentation
 * 
 * @template T The type parameter for the interface
 */
export interface ExampleInterface<T> {
  /**
   * A property with a detailed description
   * 
   * @remarks
   * Include any additional remarks about edge cases, performance considerations,
   * or other important details.
   */
  propertyOne: string;
  
  /**
   * Another property with a detailed description
   * 
   * @example
   * ```typescript
   * const example: ExampleInterface<number> = {
   *   propertyOne: "example",
   *   propertyTwo: 42
   * };
   * ```
   */
  propertyTwo: T;
  
  /**
   * A method with detailed parameter and return documentation
   * 
   * @param param1 Description of the first parameter
   * @param param2 Description of the second parameter
   * @returns Description of the return value
   * 
   * @throws {Error} Description of when an error might be thrown
   * 
   * @example
   * ```typescript
   * const result = example.methodOne("input", 42);
   * console.log(result); // Expected output
   * ```
   */
  methodOne(param1: string, param2: T): boolean;
}

/**
 * Class implementation with comprehensive documentation
 * 
 * This class demonstrates the recommended structure for class implementations
 * in the SpiceTime architecture.
 * 
 * @template T The type parameter for the class
 */
export class ExampleClass<T> implements ExampleInterface<T> {
  /**
   * Implementation of propertyOne
   */
  propertyOne: string;
  
  /**
   * Implementation of propertyTwo
   */
  propertyTwo: T;
  
  /**
   * Private property with documentation
   * 
   * @private
   */
  private internalState: Map<string, any>;
  
  /**
   * Constructor with parameter documentation
   * 
   * @param initialPropertyOne Initial value for propertyOne
   * @param initialPropertyTwo Initial value for propertyTwo
   */
  constructor(initialPropertyOne: string, initialPropertyTwo: T) {
    this.propertyOne = initialPropertyOne;
    this.propertyTwo = initialPropertyTwo;
    this.internalState = new Map();
  }
  
  /**
   * Implementation of methodOne
   * 
   * @param param1 Description of the first parameter
   * @param param2 Description of the second parameter
   * @returns Description of the return value
   * 
   * @throws {Error} Description of when an error might be thrown
   */
  methodOne(param1: string, param2: T): boolean {
    // Implementation with comments explaining complex logic
    if (!param1) {
      throw new Error("param1 cannot be empty");
    }
    
    // Store in internal state
    this.internalState.set(param1, param2);
    
    // Return result based on some condition
    return typeof param2 === 'number' && (param2 as unknown as number) > 0;
  }
  
  /**
   * Additional method with documentation
   * 
   * @param key The key to retrieve
   * @returns The value associated with the key, or undefined if not found
   */
  getFromInternalState(key: string): any {
    return this.internalState.get(key);
  }
}

/**
 * Function with comprehensive documentation
 * 
 * @param instance An instance of ExampleInterface
 * @param input Input string to process
 * @returns Processed result
 * 
 * @example
 * ```typescript
 * const example = new ExampleClass("test", 42);
 * const result = processExample(example, "input");
 * console.log(result); // Expected output
 * ```
 */
export function processExample<T>(
  instance: ExampleInterface<T>,
  input: string
): string {
  // Implementation with comments explaining complex logic
  const result = instance.methodOne(input, instance.propertyTwo);
  
  // Return formatted result
  return `Processed ${input} with ${instance.propertyOne}: ${result}`;
}

/**
 * Usage example as a comment at the end of the file
 * 
 * ```typescript
 * // Create an instance
 * const example = new ExampleClass("example", 42);
 * 
 * // Use the instance
 * const result = example.methodOne("test", 42);
 * console.log(result); // true
 * 
 * // Process the example
 * const processed = processExample(example, "input");
 * console.log(processed); // "Processed input with example: true"
 * ```
 */
