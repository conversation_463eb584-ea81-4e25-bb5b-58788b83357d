# Progenitor Node Ecosystem Specification

## Overview

This specification defines how the progenitor node system establishes a conformally equivalent ecosystem across repositories and time layers, ensuring all derivatives remain compatible while allowing for structural differences.

## Core Principles

1. **Progenitor Dependency**: All repositories must include the progenitor node package as a dependency to maintain ecosystem compatibility.
2. **Temporal Layering**: Repositories exist in time layers (past, present, future) with present becoming past when archived.
3. **Conformal Equivalence**: All node extensions must maintain conformal equivalence with the progenitor node.
4. **MIT License Boundary**: Usage of the progenitor node defines the boundary of the MIT license and SpiceTime ecosystem.

## Repository Structure

### Time Layer Transitions

```
Future → Present → Past
           ↓
        Published to npm with:
        - Unique namespace
        - Semantic versioning
        - Preserved node structure
```

When present becomes past:
1. The repository is archived to a git branch
2. It's published to npm with its unique namespace and semver
3. It becomes available as a progenitor dependency for future repositories

## Component Requirements

### React Components

All React components must:
1. Be based on the progenitor node or an extension of it
2. Use the progenitor node API for state management
3. Maintain conformal equivalence with the base component structure

```typescript
// Valid component creation
import { ProgenitorNode } from '@spicetime/progenitor-node';

// Either direct usage
const MyComponent = ProgenitorNode.createComponent({
  // Component definition
});

// Or extension
const ExtendedNode = ProgenitorNode.extend({
  // Extension properties
});

const MyExtendedComponent = ExtendedNode.createComponent({
  // Component definition
});
```

### Configuration Systems

Repositories may use any configuration system, but:
1. To use WebDev process components, they must use or extend the progenitor WebDev system
2. Custom configuration systems break ecosystem compatibility

## Ecosystem Boundaries

### MIT License Boundary

The MIT license applies to:
1. All repositories using the progenitor node
2. All extensions that maintain conformal equivalence

Repositories that:
1. Create their own node system without using the progenitor node
2. Break conformal equivalence with the progenitor node

Are considered outside the SpiceTime ecosystem and not covered by the MIT license.

### Version Space Continuity

The progenitor node ensures all conforming repositories exist in the same version space, allowing:
1. Transformation between any two nodes
2. Projection onto any reference frame
3. Causal connection even between disconnected repositories

## Implementation Requirements

### Progenitor Node API

The progenitor node must expose:
1. Node creation and extension methods
2. Reference frame transformation utilities
3. Component creation factories
4. Version space coordinates

### Publishing Requirements

When publishing a repository as a new progenitor:
1. It must include its full node structure
2. It must expose transformation methods for all node types
3. It must maintain backward compatibility with previous progenitors
4. It must include documentation of all breaking changes

## WebDev Process Integration

The WebDev process is the canonical way to:
1. Manage node extensions
2. Create new components based on the progenitor
3. Ensure conformal equivalence across the ecosystem

Repositories that implement their own node extension mechanisms:
1. Risk breaking conformal equivalence
2. May lose compatibility with the ecosystem
3. Cannot use WebDev process components