# Tripod Formation and Config Composition

## Tripod Config Composition

When stages link to form a tripod, their configs compose:

```typescript
interface TripodConfig {
  id: string;
  focus: 'ideation' | 'design' | 'production';
  stages: {
    ideation: IdeationConfig;
    design: DesignConfig;
    production: ProductionConfig;
  };
  
  // Composition rules
  composition: {
    strategy: 'merge' | 'override' | 'smart';
    conflictResolution: ConflictResolutionStrategy;
  };
  
  // Current rotation state
  rotation: {
    current: 'ideation' | 'design' | 'production';
    history: string[];
  };
}
```

## Config Rotation

As the tripod rotates focus between stages, the configuration adapts:

```typescript
function rotateTripodConfig(
  tripod: TripodConfig, 
  newFocus: 'ideation' | 'design' | 'production'
): TripodConfig {
  // Create new config with rotated focus
  const rotated = {
    ...tripod,
    focus: newFocus,
    rotation: {
      current: newFocus,
      history: [...tripod.rotation.history, newFocus]
    }
  };
  
  // Adjust plugin priorities based on new focus
  // Reconfigure links between stages
  // Update active tools
  
  return rotated;
}
```