# Config Architecture Overview

## Tripodic Configuration Structure

Each process stage (Ideation, Design, Production) has its own configuration domain:

```
                 ┌─────────────┐
                 │  Process    │
                 │  Tripod     │
                 └─────────────┘
                        │
         ┌──────────────┼──────────────┐
         │              │              │
┌────────▼─────┐ ┌──────▼───────┐ ┌────▼────────┐
│  Ideation    │ │    Design    │ │ Production  │
│  Config      │ │    Config    │ │ Config      │
└──────────────┘ └───────────────┘ └─────────────┘
```

Each config domain contains:
- Base configuration for the stage
- Plugin configurations for stage-specific tools
- Link configurations for connections to other stages
- Version variants for different use cases