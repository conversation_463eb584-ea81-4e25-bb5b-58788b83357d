# Dynamic Config Injection

## Config Injection Mechanism

Configurations can be dynamically injected based on the current process stage and context:

```typescript
interface ConfigInjector {
  // Determine which config to inject
  select: (context: ProcessContext) => ConfigSelection;
  
  // Inject the selected config
  inject: (target: any, selection: ConfigSelection) => void;
  
  // Validate the result
  validate: (result: any) => ValidationResult;
}
```

## Subprocess Config Injection

For subprocess-specific configurations:

```typescript
function injectSubprocessConfig(
  process: Process,
  subprocess: Subprocess,
  context: ProcessContext
): Process {
  // Find the appropriate config for this subprocess
  const subprocessConfig = findConfigForSubprocess(subprocess, context);
  
  // Inject the config
  const configuredProcess = {
    ...process,
    config: mergeConfigs(process.config, subprocessConfig)
  };
  
  // Apply transformations based on the new config
  return applyConfigTransformations(configuredProcess);
}
```

This allows different tools and configurations to be used for specific subprocesses (testing, debugging, etc.) without affecting the main process configuration.