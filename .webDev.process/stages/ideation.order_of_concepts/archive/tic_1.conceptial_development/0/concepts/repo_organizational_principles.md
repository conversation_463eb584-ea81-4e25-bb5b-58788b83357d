# Component Organization Pattern

## Core Philosophy

This document outlines our approach to organizing components and types within the WebDev process architecture, balancing abstraction with pragmatism.

## The Snake Terrarium Pattern

We follow what we call the "Snake Terrarium Pattern" - a tripodic approach to component development that follows the IDP (Ideation, Design, Production) structure:

1. **Ideate**: Conceptualize the component's purpose and relationships
2. **Design**: Define API schemas and categorical types
3. **Production**: Create the actual implementation

## Key Principles

### 1. In-Place Component Definition

- Components are defined in-place using JSX pragma
- No artificial separation into a dedicated "components" folder
- Location in the tree reflects the component's conceptual place

```jsx
// Example: Using JSX pragma for in-place component definition
/** @jsx jsx */
import { jsx } from '@emotion/react';
import { ProcessNode } from '../cat_types';

export const ProcessStageView = ({ stage, ...props }) => (
  <div className="process-stage" {...props}>
    {/* Component implementation */}
  </div>
);
```

### 2. Categorical Type Organization

- Each folder contains a `cat_types.ts` module defining categorical structures
- Types implement categorical laws and functors
- Local types are "uplifted" to parent scopes when needed by siblings

```typescript
// Example: cat_types.ts
export interface ProcessNode<T> {
  id: string;
  type: string;
  data: T;
  // Categorical methods
  map: <U>(f: (t: T) => U) => ProcessNode<U>;
  // ...other functorial methods
}
```

### 3. Pragmatic Exposure

- Components are exposed based on usage patterns, not rigid rules
- Domain-specific components can be extended from base components
- APIs are designed for actual use cases, not theoretical purity

### 4. Tripodic Workflow

Our development workflow follows the tripodic structure:
- Start at the root node
- Ideate and design APIs for the current node and its immediate children
- Descend to children and repeat the IDP process
- Stages interact with each other through well-defined interfaces
- Each stage is a reusable component in itself

### 5. Continuous Documentation

- Documentation is created alongside code, not as an afterthought
- Each component includes:
    - API documentation
    - Usage examples
    - Storybook stories
    - Categorical laws it satisfies

## Documentation Tools

### Storybook Integration

We use Storybook as a living documentation and development environment:

- Each component has corresponding `.stories.tsx` files
- Stories demonstrate various states and configurations
- Stories serve as visual regression tests
- Component props are documented using Storybook Controls

```tsx
// Example: Component story
import { Story, Meta } from '@storybook/react';
import { ProcessStageView, ProcessStageViewProps } from './ProcessStageView';

export default {
  title: 'Process/ProcessStageView',
  component: ProcessStageView,
  argTypes: {
    stage: { control: 'object' },
    onStageActivate: { action: 'activated' }
  },
} as Meta;

const Template: Story<ProcessStageViewProps> = (args) => <ProcessStageView {...args} />;

export const Default = Template.bind({});
Default.args = {
  stage: {
    id: 'stage-1',
    name: 'Ideation',
    status: 'active'
  }
};

export const Completed = Template.bind({});
Completed.args = {
  stage: {
    id: 'stage-1',
    name: 'Ideation',
    status: 'completed'
  }
};
```

### Documentation Structure

- **Component API Docs**: Generated from TypeScript types and JSDoc comments
- **Conceptual Docs**: Markdown files explaining patterns and principles
- **Usage Guides**: Examples of component composition and integration
- **Categorical Laws**: Documentation of the mathematical properties components satisfy

## Benefits

- **Clarity**: Component location reflects its conceptual purpose
- **Flexibility**: Easy to extend and adapt components for domain-specific needs
- **Discoverability**: Related components and types are co-located
- **Evolution**: Natural pathways for component growth and refinement
- **Living Documentation**: Storybook provides interactive examples that stay in sync with the code

## Implementation Notes

- Use TypeScript for all type definitions
- React components should be properly typed with PropTypes or TypeScript interfaces
- Document categorical laws that types must satisfy
- Include examples of typical usage patterns
- Maintain Storybook stories for all components