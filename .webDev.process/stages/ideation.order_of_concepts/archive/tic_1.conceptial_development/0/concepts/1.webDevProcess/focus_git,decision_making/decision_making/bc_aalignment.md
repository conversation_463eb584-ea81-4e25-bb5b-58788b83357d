# Blockchain-Aligned Terminology for SpiceTime Decision Model

## Proposal: Align SpiceTime with Blockchain Terminology

To make our emergent gravitational decision model more accessible to blockchain developers, we can align our terminology and workflows with familiar blockchain concepts. This creates an intuitive bridge between SpiceTime's decision model and blockchain governance practices.

## Terminology Mapping

| SpiceTime Concept | Blockchain-Aligned Term | Description |
|-------------------|-------------------------|-------------|
| Hierarchical Authority | Consensus Weight | Authority in the hierarchy maps to voting/consensus weight in blockchain |
| Cross-Tree Connections | Cross-Chain Bridges | Connections between different domains function like cross-chain bridges |
| Decision Propagation | Transaction Propagation | How decisions spread through the system mirrors transaction propagation |
| Authority Concentration | Stake Concentration | Concentration of decision power parallels stake concentration |
| Decision Validation | Block Validation | Process of approving decisions mirrors block validation |
| Decision History | Blockchain | Immutable record of past decisions functions like a blockchain |
| Tripod Tiles | Smart Contracts | Reusable decision patterns function like smart contracts |

## Workflow Alignment

### 1. Proposal-Consensus Flow

```typescript
interface DecisionProposal {
  id: string;
  author: string;
  description: string;
  stakeholders: Stakeholder[];  // "Validators" in blockchain terms
  requiredConsensus: number;    // "Threshold" in blockchain terms
  status: ProposalStatus;       // "Block status" in blockchain terms
}
```

### 2. Stake-Based Influence

```typescript
function calculateStakeholderInfluence(stakeholder: Stakeholder, proposal: Proposal): number {
  // Base influence from position (like stake in PoS)
  const baseInfluence = stakeholder.hierarchyLevel * LEVEL_MULTIPLIER;
  
  // Expertise influence (like reputation in off-chain governance)
  const expertiseInfluence = calculateExpertiseMatch(stakeholder, proposal);
  
  // Historical contribution (like historical mining in PoW)
  const historicalInfluence = stakeholder.contributionHistory.relevantToProposal(proposal);
  
  return baseInfluence + expertiseInfluence + historicalInfluence;
}
```

### 3. Decision Finality

```typescript
enum DecisionStatus {
  PROPOSED,      // "Mempool" in blockchain
  REVIEWING,     // "Pending" in blockchain
  APPROVED,      // "Confirmed" in blockchain
  REJECTED,      // "Rejected" in blockchain
  IMPLEMENTED    // "Finalized" in blockchain
}
```

## Process Alignment

### 1. Improvement Proposal Process

Align our decision-making process with familiar BIP/EIP processes:

1. **Draft** - Initial proposal creation
2. **Review** - Community/stakeholder feedback
3. **Last Call** - Final review period
4. **Accepted/Rejected** - Decision made
5. **Implemented** - Changes applied

### 2. Governance Participation

Align participation models with blockchain governance:

1. **Direct Participation** - Similar to on-chain voting
2. **Delegation** - Similar to delegation in DPoS
3. **Reputation-Based** - Similar to off-chain governance

### 3. Fork Management

Align branch management with blockchain fork terminology:

1. **Soft Fork** - Backward-compatible changes
2. **Hard Fork** - Breaking changes requiring full adoption
3. **Chain Split** - Contentious changes leading to permanent divergence

## UI/UX Alignment

### 1. Dashboard Elements

```typescript
interface DashboardElements {
  pendingProposals: Proposal[];    // "Mempool" in blockchain terms
  activeVotes: Vote[];             // "Active validation" in blockchain terms
  recentDecisions: Decision[];     // "Recent blocks" in blockchain terms
  influenceMetrics: Metrics;       // "Stake metrics" in blockchain terms
}
```

### 2. Visualization

- Represent decision flow as a blockchain-like structure
- Show stakeholder influence as stake distribution
- Visualize cross-domain connections as cross-chain bridges
- Display decision history as an immutable chain

## Benefits of Blockchain-Aligned Terminology

1. **Familiarity**: Blockchain developers immediately understand the model
2. **Conceptual Clarity**: Leverages established mental models
3. **Community Bridge**: Creates common language between SpiceTime and blockchain communities
4. **Adoption Path**: Easier onboarding for blockchain developers
5. **Ecosystem Integration**: Simpler integration with actual blockchain systems

## Implementation Roadmap

1. **Documentation Update**: Revise all documentation to use blockchain-aligned terminology
2. **API Refactoring**: Update method and parameter names to reflect blockchain concepts
3. **UI Redesign**: Incorporate blockchain-inspired visualizations and terminology
4. **Training Materials**: Create transition guides for teams familiar with blockchain concepts
5. **Community Engagement**: Present the aligned model to blockchain communities for feedback

## impl via Tree Scopes
Project Tree
├── root/
│   ├── $scope: core
│   │   └── $lingo: standard
│   │
├── frontend/
│   ├── $scope: ui
│   │   └── $lingo: webdev
│   │
└── blockchain/
├── $scope: consensus
│   └── $lingo: blockchain