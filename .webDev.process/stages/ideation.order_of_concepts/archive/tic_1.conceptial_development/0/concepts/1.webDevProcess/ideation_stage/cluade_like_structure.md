# Claude-like Ideation Structure

## Core Structure

```
ideation/
├── projects/                      # Project containers
│   ├── [project-name]/            # Individual project
│   │   ├── context.md             # Project-wide context
│   │   ├── summaries/             # Shared knowledge artifacts
│   │   │   ├── [concept].summary.md  # Concept summaries
│   │   │   └── [decision].decision.md # Decision records
│   │   └── chats/                 # Conversation threads
│   │       ├── [chat-id]/         # Individual chat
│   │       │   ├── prompt.1.md    # First prompt
│   │       │   ├── response.1.md  # First response
│   │       │   ├── prompt.2.md    # Second prompt
│   │       │   └── ...
│   │       └── ...
│   └── ...
└── context/                       # Global shared context
    ├── [domain].context.md        # Domain-specific context
    └── ...
```

## Dynamic Context Management

Each prompt is self-contained with all necessary context:

```markdown
# prompt.3.md

CONTEXT:
- {{project:context.md}}
- {{summary:auth-system.summary.md}}
- {{context:security.context.md}}

CHAT HISTORY:
- {{chat:prompt.2.md}}
- {{chat:response.2.md}}

PROMPT:
How should we handle token refresh in the OAuth implementation?
```

The context manager resolves these references at runtime, assembling only what's needed for each prompt.

## Summaries as Shared Knowledge

Summaries are the primary knowledge artifacts:

1. **Generated from chats**: After a productive chat, create a summary
2. **Shared across projects**: Reference summaries from any project
3. **Evolve over time**: Update as new insights emerge

## Unstructured Conversations

Chats remain simple prompt/response pairs:
- No predefined structure within chats
- Console-like interaction model
- Each prompt assembles its own context
- No persistent state between prompts (stateless)

## Implementation Transition

As ideas mature:
1. Summaries become specifications
2. Specifications become package structures
3. Package structures become implementation