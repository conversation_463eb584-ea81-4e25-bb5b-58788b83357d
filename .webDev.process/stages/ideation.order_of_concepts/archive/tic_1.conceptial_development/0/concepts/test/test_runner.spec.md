# Test Runner Package Specification

## Overview

The Test Runner package provides a sequential test execution system that respects dependencies between modules. It eliminates the need for mocks by ensuring tests run in the correct order, with dependencies tested before dependents.

## Core Components

### 1. Dependency Inference Engine

The system infers dependencies through multiple methods:

- **Type Definition Analysis**: Parses type files generated by Pragma to extract dependency information
- **Import Statement Analysis**: Analyzes static imports in source files
- **Property Name Matching**: For atomic scripts, matches property names in interfaces to infer dependencies
- **AST Analysis**: Performs abstract syntax tree analysis for dynamic imports and complex patterns

#### Dependency Resolution Algorithm

```typescript
function resolveDependencies(module: string): string[] {
  const deps = new Set<string>();
  
  // Method 1: Parse type definitions
  const typeFile = findTypeFile(module);
  if (typeFile) {
    const typeInfo = parseTypeFile(typeFile);
    deps.add(...typeInfo.dependencies);
  }
  
  // Method 2: Static import analysis
  const imports = findStaticImports(module);
  deps.add(...imports);
  
  // Method 3: Property name matching
  const interfaces = findInterfaces(module);
  for (const iface of interfaces) {
    deps.add(...inferDepsFromProps(iface));
  }
  
  // Method 4: AST analysis for dynamic imports
  const dynamicImports = findDynamicImports(module);
  deps.add(...dynamicImports);
  
  return Array.from(deps);
}
```

### 2. Temporal Dependency Manager

Handles dependencies that reference specific steps in a process:

- **Step Identification**: Identifies temporal steps in modules
- **Step Dependency Mapping**: Maps dependencies between specific steps
- **Execution Scheduling**: Schedules test execution respecting step dependencies

#### Temporal Step Resolution

```typescript
interface TemporalStep {
  moduleId: string;
  stepId: string;
  dependencies: Array<{
    moduleId: string;
    stepId?: string;
  }>;
}

function resolveTemporalSteps(module: string): TemporalStep[] {
  const typeFile = findTypeFile(module);
  if (!typeFile) return [];
  
  const typeInfo = parseTypeFile(typeFile);
  return typeInfo.temporalSteps.map(step => ({
    moduleId: module,
    stepId: step.id,
    dependencies: step.dependencies
  }));
}
```

### 3. Test Documentation Generator

Generates comprehensive documentation for tests:

- **TypeDoc Integration**: Integrates with TypeDoc to document test structure
- **Dependency Visualization**: Creates visual representations of test dependencies
- **Coverage Reporting**: Generates coverage reports with dependency context

#### TypeDoc Module Integration

```typescript
interface TestDocConfig {
  outputDir: string;
  format: 'html' | 'markdown' | 'json';
  includeGraph: boolean;
  includeCoverage: boolean;
}

function generateTestDocs(testResults: TestResult[], config: TestDocConfig): void {
  // Generate TypeDoc compatible documentation
  const typeDocData = convertToTypeDocFormat(testResults);
  
  // Add dependency graph if requested
  if (config.includeGraph) {
    typeDocData.graphs = generateDependencyGraphs(testResults);
  }
  
  // Add coverage information if requested
  if (config.includeCoverage) {
    typeDocData.coverage = generateCoverageReport(testResults);
  }
  
  // Output in requested format
  outputTypeDoc(typeDocData, config);
}
```

### 4. Graph-Based Test Scheduler

Schedules tests based on a dependency graph:

- **Graph Construction**: Builds a directed acyclic graph (DAG) of test dependencies
- **Topological Sorting**: Sorts tests in dependency order
- **Parallel Execution**: Identifies independent test branches for parallel execution
- **Incremental Testing**: Supports running only affected tests when files change

#### Test Execution Scheduler

```typescript
interface TestSchedule {
  sequence: string[][];  // Each inner array can run in parallel
  estimatedDuration: number;
}

function scheduleTests(graph: TestNode[]): TestSchedule {
  // Convert to adjacency list
  const adjacencyList = buildAdjacencyList(graph);
  
  // Perform topological sort
  const sorted = topologicalSort(adjacencyList);
  
  // Group independent tests that can run in parallel
  const parallelGroups = groupIndependentTests(sorted, adjacencyList);
  
  // Estimate duration based on previous runs
  const duration = estimateTestDuration(parallelGroups);
  
  return {
    sequence: parallelGroups,
    estimatedDuration: duration
  };
}
```

## Integration with Pragma

The Test Runner integrates with Pragma through:

1. **Type File Consumption**: Consumes type files generated by Pragma
2. **Extension Recognition**: Recognizes Pragma extensions to infer categorical relationships
3. **Pragma Hooks**: Provides hooks for Pragma to inject custom test logic

## Usage Examples

### Basic Sequential Testing

```typescript
import { TestRunner } from '@pragma/test-runner';

const runner = new TestRunner({
  sourceDir: './src',
  testDir: './tests',
  typeDefsDir: './types'
});

// Run all tests in dependency order
runner.runAll().then(results => {
  console.log(`${results.length} tests completed`);
  console.log(`Success rate: ${results.filter(r => r.success).length / results.length * 100}%`);
});
```

### Temporal Step Testing

```typescript
import { TestRunner } from '@pragma/test-runner';

const runner = new TestRunner({
  sourceDir: './src',
  testDir: './tests',
  typeDefsDir: './types',
  temporalAwareness: true
});

// Run specific temporal step
runner.runStep('natural-transformation', 'laws').then(result => {
  console.log(`Step test ${result.success ? 'passed' : 'failed'}`);
});
```