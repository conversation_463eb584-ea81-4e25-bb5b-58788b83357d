# Integrated Filesystem: Uniting FS Tree and Meta Shadow

This specification details the implementation of an integrated filesystem structure that unites the physical filesystem tree with its meta shadow representation. This unified structure exists simultaneously in Git and a graph database, with the WebDevProcess providing filtered projections for different concerns.

## 1. Core Architecture

### 1.1 Dual-Layer Structure

The integrated filesystem consists of two synchronized layers:

```typescript
interface IntegratedFilesystem {
  physicalLayer: FilesystemTree;    // Actual files and directories on disk
  metaLayer: MetaShadowGraph;       // Extended metadata structure
  synchronizationProtocol: SyncProtocol; // Protocol for keeping layers in sync
  projectionEngine: ProjectionEngine; // Engine for creating filtered views
}

interface FilesystemTree {
  nodes: {
    [path: string]: {
      type: 'file' | 'directory';
      name: string;
      parent: string | null;       // Parent directory path
      children?: string[];         // For directories, paths of children
      content?: Uint8Array;        // For files, actual content
      stats: {                     // Standard filesystem stats
        size: number;
        created: number;
        modified: number;
        accessed: number;
        permissions: number;
      };
    }
  };
}

interface MetaShadowGraph {
  nodes: {
    [id: string]: {
      type: 'file' | 'directory' | 'virtual' | 'concept' | 'relationship';
      physicalPath?: string;      // Path in physical layer (if applicable)
      metadata: {
        [key: string]: any;       // Extensible metadata
      };
      relations: {
        [relationId: string]: {
          type: string;           // Type of relationship
          target: string;         // ID of target node
          properties: {           // Relationship properties
            [key: string]: any;
          };
        }
      };
      version: {
        current: string;          // Current version ID
        history: string[];        // Previous version IDs
      };
      phase: 'ideation' | 'design' | 'production';
    }
  };
}
```

### 1.2 Synchronization Protocol

The synchronization protocol ensures consistency between the physical filesystem and the meta shadow:

```typescript
interface SyncProtocol {
  events: {
    physicalToMeta: {
      fileCreated: (path: string) => void;
      fileModified: (path: string) => void;
      fileDeleted: (path: string) => void;
      directoryCreated: (path: string) => void;
      directoryDeleted: (path: string) => void;
      fileMoved: (oldPath: string, newPath: string) => void;
    };
    metaToPhysical: {
      nodeCreated: (id: string) => void;
      nodeModified: (id: string) => void;
      nodeDeleted: (id: string) => void;
      relationshipCreated: (id: string, type: string, sourceId: string, targetId: string) => void;
      relationshipDeleted: (id: string) => void;
    };
  };
  reconciliation: {
    conflictDetection: (physicalState: any, metaState: any) => Conflict[];
    conflictResolution: (conflicts: Conflict[]) => Resolution[];
    applyResolutions: (resolutions: Resolution[]) => void;
  };
  consistency: {
    validateConsistency: () => ConsistencyReport;
    repairInconsistencies: (report: ConsistencyReport) => void;
    scheduleConsistencyCheck: (interval: number) => void;
  };
}
```

### 1.3 Projection Engine

The projection engine creates filtered views of the integrated filesystem:

```typescript
interface ProjectionEngine {
  createProjection: (config: ProjectionConfig) => Projection;
  updateProjection: (projectionId: string, config: Partial<ProjectionConfig>) => Projection;
  deleteProjection: (projectionId: string) => void;
  applyProjection: (projectionId: string) => ProjectedView;
}

interface ProjectionConfig {
  id: string;
  name: string;
  filterCriteria: {
    nodeTypes?: string[];         // Types of nodes to include
    relationshipTypes?: string[]; // Types of relationships to include
    phases?: ('ideation' | 'design' | 'production')[];
    metadataFilters?: {           // Filters based on metadata
      [key: string]: any;
    };
    pathPatterns?: string[];      // File path patterns to include
  };
  transformations: {
    groupBy?: string[];           // Properties to group by
    sortBy?: string;              // Property to sort by
    sortDirection?: 'asc' | 'desc';
    flatten?: boolean;            // Whether to flatten directory structure
    aggregations?: {              // Aggregations to apply
      [property: string]: 'sum' | 'average' | 'count' | 'min' | 'max';
    };
  };
  presentation: {
    viewType: 'tree' | 'graph' | 'table' | 'board';
    nodeRepresentation: {
      [nodeType: string]: {
        icon?: string;            // Icon to represent this node type
        color?: string;           // Color to represent this node type
        template?: string;        // Display template
      }
    };
    relationshipRepresentation: {
      [relationType: string]: {
        lineStyle?: 'solid' | 'dashed' | 'dotted';
        lineColor?: string;
        lineWidth?: number;
        label?: string;
      }
    };
  };
}
```

## 2. Git Integration

### 2.1 Version Control Mapping

The integrated filesystem maps to Git structures:

```typescript
interface GitMapping {
  fileToBlob: {
    mapFileToBlob: (filePath: string) => string;  // Maps file to Git blob ID
    mapBlobToFile: (blobId: string) => string;    // Maps Git blob ID to file
  };
  directoryToTree: {
    mapDirectoryToTree: (dirPath: string) => string;  // Maps directory to Git tree ID
    mapTreeToDirectory: (treeId: string) => string;   // Maps Git tree ID to directory
  };
  metaToGit: {
    storeMetaInGit: (metaNode: any) => string;    // Stores meta node in Git
    loadMetaFromGit: (id: string) => any;         // Loads meta node from Git
  };
  commitMapping: {
    createCommitForChanges: (changes: Change[]) => string;  // Creates Git commit
    mapCommitToChanges: (commitId: string) => Change[];     // Maps Git commit to changes
  };
}

interface GitExtensions {
  metaReferences: {
    addMetaRef: (path: string, metaId: string) => void;     // Adds meta reference to Git
    getMetaRef: (path: string) => string;                   // Gets meta reference from Git
    updateMetaRef: (path: string, metaId: string) => void;  // Updates meta reference
  };
  metaProperties: {
    addMetaProperty: (path: string, key: string, value: any) => void;
    getMetaProperty: (path: string, key: string) => any;
    updateMetaProperty: (path: string, key: string, value: any) => void;
  };
  metaHistory: {
    getMetaHistory: (metaId: string) => HistoryEntry[];
    compareMetaVersions: (metaId1: string, metaId2: string) => Diff;
  };
}
```

### 2.2 Branching and Merging

The system handles branching and merging of both physical and meta layers:

```typescript
interface BranchingMerging {
  createBranch: (name: string, source: string) => void;
  deleteBranch: (name: string) => void;
  checkoutBranch: (name: string) => void;
  mergeBranch: (source: string, target: string) => MergeResult;
  resolveConflicts: (conflicts: Conflict[]) => void;
  
  metaBranching: {
    branchMetaLayer: (branchName: string) => void;
    mergeMetaLayer: (sourceBranch: string, targetBranch: string) => MergeResult;
    resolveMetaConflicts: (conflicts: MetaConflict[]) => void;
  };
  
  syncBranching: {
    syncBranchOperations: (operation: 'create' | 'delete' | 'checkout' | 'merge', params: any) => void;
    ensureBranchConsistency: (branchName: string) => void;
  };
}
```

## 3. Graph Database Integration

### 3.1 Graph Schema

The system defines a schema for the graph database representation:

```typescript
interface GraphSchema {
  nodeLabels: {
    File: {
      properties: {
        path: { type: 'string', indexed: true, unique: true },
        name: { type: 'string', indexed: true },
        extension: { type: 'string', indexed: true },
        size: { type: 'number' },
        created: { type: 'number' },
        modified: { type: 'number' },
        phase: { type: 'string', enum: ['ideation', 'design', 'production'] },
        version: { type: 'string' }
      }
    };
    Directory: {
      properties: {
        path: { type: 'string', indexed: true, unique: true },
        name: { type: 'string', indexed: true },
        created: { type: 'number' },
        modified: { type: 'number' }
      }
    };
    Concept: {
      properties: {
        id: { type: 'string', indexed: true, unique: true },
        name: { type: 'string', indexed: true },
        description: { type: 'string' },
        phase: { type: 'string', enum: ['ideation', 'design', 'production'] },
        created: { type: 'number' },
        modified: { type: 'number' }
      }
    };
    MetaNode: {
      properties: {
        id: { type: 'string', indexed: true, unique: true },
        type: { type: 'string', indexed: true },
        physicalPath: { type: 'string', nullable: true, indexed: true },
        phase: { type: 'string', enum: ['ideation', 'design', 'production'] },
        created: { type: 'number' },
        modified: { type: 'number' }
      }
    };
  };
  
  relationshipTypes: {
    CONTAINS: {
      source: ['Directory'],
      target: ['Directory', 'File'],
      properties: {}
    };
    IMPLEMENTS: {
      source: ['File'],
      target: ['Concept'],
      properties: {
        completeness: { type: 'number' }
      }
    };
    DEPENDS_ON: {
      source: ['File', 'Concept'],
      target: ['File', 'Concept'],
      properties: {
        type: { type: 'string', enum: ['import', 'reference', 'inheritance'] },
        strength: { type: 'number' }
      }
    };
    VERSION_OF: {
      source: ['MetaNode'],
      target: ['MetaNode'],
      properties: {
        version: { type: 'string' },
        changeType: { type: 'string', enum: ['major', 'minor', 'patch'] }
      }
    };
    META_SHADOW: {
      source: ['MetaNode'],
      target: ['File', 'Directory'],
      properties: {}
    };
  };
}
```

### 3.2 Graph Queries

The system defines queries for extracting information from the graph:

```typescript
interface GraphQueries {
  // Path-based queries
  getNodeByPath: (path: string) => any;
  getSubtree: (rootPath: string, depth: number) => any;
  findPathsBetween: (sourcePath: string, targetPath: string) => any[];
  
  // Relationship queries
  getDependencies: (nodePath: string, direct: boolean) => any[];
  getDependents: (nodePath: string, direct: boolean) => any[];
  getRelationships: (nodePath: string, types: string[]) => any[];
  
  // Meta queries
  getMetaForPath: (path: string) => any;
  getVersionHistory: (nodeId: string) => any[];
  getPhaseTransitions: (nodeId: string) => any[];
  
  // Complex queries
  getImpactAnalysis: (nodePath: string) => any;
  findCycles: () => any[];
  getComponentCohesion: (componentPath: string) => number;
}
```

### 3.3 Graph Synchronization

The system synchronizes changes between the filesystem and the graph database:

```typescript
interface GraphSync {
  initializeGraph: (filesystemRoot: string) => void;
  watchForChanges: (callback: (changes: Change[]) => void) => void;
  
  applyFileSystemChange: (change: Change) => void;
  applyMetaChange: (change: MetaChange) => void;
  
  validateGraphConsistency: () => InconsistencyReport;
  repairGraph: (issues: Issue[]) => void;
  
  exportGraph: (format: 'json' | 'csv' | 'graphml') => string;
  importGraph: (data: string, format: 'json' | 'csv' | 'graphml') => void;
}
```

## 4. WebDevProcess Projections

### 4.1 Phase-Specific Projections

The system creates phase-specific projections of the integrated filesystem:

```typescript
interface PhaseProjections {
  ideationProjection: {
    config: ProjectionConfig;
    focus: 'concepts' | 'requirements' | 'prototypes';
    visualization: 'mindmap' | 'graph' | 'outline';
    conceptFilters: {
      status: ('proposed' | 'approved' | 'rejected')[];
      categories: string[];
      priority: ('high' | 'medium' | 'low')[];
    };
  };
  
  designProjection: {
    config: ProjectionConfig;
    focus: 'architecture' | 'components' | 'interfaces';
    visualization: 'diagram' | 'tree' | 'matrix';
    designFilters: {
      componentTypes: string[];
      patterns: string[];
      status: ('draft' | 'reviewed' | 'approved')[];
    };
  };
  
  productionProjection: {
    config: ProjectionConfig;
    focus: 'implementation' | 'testing' | 'deployment';
    visualization: 'filesystem' | 'dependency-graph' | 'metrics';
    codeFilters: {
      languages: string[];
      testCoverage: [number, number];  // [min, max]
      complexity: [number, number];    // [min, max]
    };
  };
}
```

### 4.2 Concern-Based Projections

The system creates projections based on specific concerns:

```typescript
interface ConcernProjections {
  createConcernProjection: (concern: string) => ProjectionConfig;
  
  predefinedConcerns: {
    security: ProjectionConfig;
    performance: ProjectionConfig;
    maintainability: ProjectionConfig;
    testability: ProjectionConfig;
    documentation: ProjectionConfig;
  };
  
  customConcernBuilder: {
    defineMetadataFilters: (filters: Record<string, any>) => void;
    defineNodeTypes: (types: string[]) => void;
    defineRelationships: (relationships: string[]) => void;
    defineVisualization: (visualization: any) => void;
    saveConcern: (name: string) => ProjectionConfig;
  };
}
```

### 4.3 Role-Based Projections

The system creates projections based on user roles:

```typescript
interface RoleProjections {
  developerProjection: {
    config: ProjectionConfig;
    codeOwnership: boolean;        // Show code ownership
    focusAreas: string[];          // Areas of focus
    metrics: string[];             // Metrics to display
  };
  
  architectProjection: {
    config: ProjectionConfig;
    structuralView: boolean;       // Show structural view
    dependencyAnalysis: boolean;   // Show dependency analysis
    qualityMetrics: boolean;       // Show quality metrics
  };
  
  managerProjection: {
    config: ProjectionConfig;
    progressView: boolean;         // Show progress view
    resourceAllocation: boolean;   // Show resource allocation
    deadlineTracking: boolean;     // Show deadline tracking
  };
  
  testerProjection: {
    config: ProjectionConfig;
    testCoverage: boolean;         // Show test coverage
    qualityGates: boolean;         // Show quality gates
    issueTracking: boolean;        // Show issue tracking
  };
}
```

## 5. Meta Shadow Implementation

### 5.1 Meta Properties

The system defines meta properties that extend physical filesystem entities:

```typescript
interface MetaProperties {
  // Design properties
  design: {
    pattern: string;               // Design pattern applied
    responsibility: string;        // Component responsibility
    interfaces: string[];          // Exposed interfaces
    contracts: {                   // Interface contracts
      [interface: string]: {
        inputs: any[];
        outputs: any[];
        preconditions: string[];
        postconditions: string[];
      }
    };
  };
  
  // Process properties
  process: {
    phase: 'ideation' | 'design' | 'production';
    status: string;                // Current status
    reviewStatus: 'pending' | 'reviewed' | 'approved';
    owners: string[];              // Component owners
    priority: 'high' | 'medium' | 'low';
  };
  
  // Quality properties
  quality: {
    complexity: number;            // Complexity metric
    cohesion: number;              // Cohesion metric
    coupling: number;              // Coupling metric
    testCoverage: number;          // Test coverage percentage
    issueCount: number;            // Number of issues
  };
  
  // Versioning properties
  versioning: {
    semanticVersion: string;       // Semantic version
    releaseStatus: 'unreleased' | 'alpha' | 'beta' | 'release';
    changeLog: {                   // Change log entries
      [version: string]: {
        changes: string[];
        author: string;
        date: number;
      }
    };
  };
}
```

### 5.2 Virtual Node Types

The system defines virtual nodes that exist only in the meta shadow:

```typescript
interface VirtualNodeTypes {
  Concept: {
    id: string;
    name: string;
    description: string;
    category: string;
    status: 'proposed' | 'approved' | 'rejected';
    implementations: string[];     // Paths that implement this concept
    dependencies: string[];        // Concept dependencies
  };
  
  Interface: {
    id: string;
    name: string;
    description: string;
    methods: {
      [name: string]: {
        parameters: any[];
        returnType: any;
        description: string;
      }
    };
    implementations: string[];     // Paths that implement this interface
    consumers: string[];           // Paths that consume this interface
  };
  
  Component: {
    id: string;
    name: string;
    description: string;
    type: string;
    subcomponents: string[];       // Subcomponent IDs
    interfaces: string[];          // Provided interface IDs
    dependencies: string[];        // Required interface IDs
  };
  
  Requirement: {
    id: string;
    name: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    status: 'proposed' | 'approved' | 'in-progress' | 'completed';
    implementations: string[];     // Paths that implement this requirement
    tests: string[];               // Paths that test this requirement
  };
}
```

### 5.3 Relationship Types

The system defines relationship types between nodes:

```typescript
interface RelationshipTypes {
  CodeRelationships: {
    Imports: {
      source: string;              // Importing file
      target: string;              // Imported file
      importType: 'static' | 'dynamic';
      cardinality: 'one' | 'many';
    };
    
    Extends: {
      source: string;              // Extending entity
      target: string;              // Extended entity
      extensionType: 'inheritance' | 'interface' | 'mixin';
    };
    
    References: {
      source: string;              // Referencing entity
      target: string;              // Referenced entity
      referenceType: 'call' | 'use' | 'create';
      cardinality: 'one' | 'many';
    };
  };
  
  ArchitecturalRelationships: {
    Composes: {
      source: string;              // Container component
      target: string;              // Contained component
      cardinalityMin: number;
      cardinalityMax: number | 'unbounded';
    };
    
    DependsOn: {
      source: string;              // Dependent component
      target: string;              // Dependency component
      dependencyType: 'required' | 'optional';
      strength: 'strong' | 'weak';
    };
    
    Provides: {
      source: string;              // Provider component
      target: string;              // Provided interface
      visibility: 'public' | 'protected' | 'private';
    };
    
    Consumes: {
      source: string;              // Consumer component
      target: string;              // Consumed interface
      usage: 'frequent' | 'occasional' | 'rare';
    };
  };
  
  ProcessRelationships: {
    Implements: {
      source: string;              // Implementation entity
      target: string;              // Implemented concept/requirement
      completeness: number;        // Percentage complete (0-100)
      quality: number;             // Quality score (0-100)
    };
    
    Tests: {
      source: string;              // Test entity
      target: string;              // Tested entity
      coverageType: 'unit' | 'integration' | 'e2e';
      coveragePercentage: number;
    };
    
    Succeeds: {
      source: string;              // Newer version
      target: string;              // Previous version
      changeType: 'major' | 'minor' | 'patch';
      changes: string[];           // Description of changes
    };
  };
}
```

## 6. Implementation Mechanics

### 6.1 Filesystem Monitoring

The system monitors filesystem changes:

```typescript
interface FilesystemMonitoring {
  watchMechanisms: {
    fsWatcher: {
      paths: string[];             // Paths to watch
      recursive: boolean;          // Watch recursively
      excludePatterns: string[];   // Patterns to exclude
    };
    
    gitHooks: {
      preCommit: boolean;          // Pre-commit hook
      postCommit: boolean;         // Post-commit hook
      preCheckout: boolean;        // Pre-checkout hook
      postCheckout: boolean;       // Post-checkout hook
    };
    
    ide: {
      fileChangeEvents: boolean;   // IDE file change events
      saveEvents: boolean;         // IDE save events
      refactorEvents: boolean;     // IDE refactor events
    };
  };
  
  changeProcessing: {
    batchChanges: boolean;         // Batch changes before processing
    batchInterval: number;         // Interval for batching (ms)
    prioritizeChanges: boolean;    // Prioritize important changes
    maxConcurrentProcessing: number; // Max number of concurrent processings
  };
  
  notificationSystem: {
    changeSubscribers: {
      [path: string]: {
        callback: (change: Change) => void;
        recursive: boolean;
      }[];
    };
    
    notifyChange: (change: Change) => void;
    registerChangeListener: (path: string, callback: (change: Change) => void, options?: any) => void;
    unregisterChangeListener: (path: string, callback: (change: Change) => void) => void;
  };
}
```

### 6.2 Meta Shadow Storage

The system stores the meta shadow in both Git and a graph database:

```typescript
interface MetaShadowStorage {
  persistence: {
    git: {
      metaDirectory: string;       // Directory for meta files
      metaFileNaming: (id: string) => string; // Naming function for meta files
      serializeMetaNode: (node: any) => string; // Serialization function
      deserializeMetaNode: (content: string) => any; // Deserialization function
    };
    
    graphDb: {
      connectionSettings: {
        uri: string;
        username: string;
        password: string;
      };
      batchSize: number;           // Size of batches for bulk operations
      retryStrategy: {             // Strategy for retrying failed operations
        maxRetries: number;
        retryInterval: number;     // ms
      };
    };
  };
  
  consistency: {
    syncInterval: number;          // Interval for full synchronization (ms)
    checkConsistency: () => InconsistencyReport;
    repairConsistency: (report: InconsistencyReport) => void;
    forceFullSync: () => Promise<void>;
  };
  
  caching: {
    enabled: boolean;
    cacheSize: number;             // Number of items to cache
    ttl: number;                   // Time to live (ms)
    evictionPolicy: 'lru' | 'lfu' | 'fifo';
    persistCache: boolean;         // Persist cache to disk
  };
}
```

### 6.3 Projection Implementation

The system implements projections:

```typescript
interface ProjectionImplementation {
  runtime: {
    computeProjection: (config: ProjectionConfig) => ProjectedView;
    updateProjectedView: (projectionId: string, changes: Change[]) => ProjectedView;
    renderProjection: (projection: ProjectedView, renderer: Renderer) => void;
  };
  
  filtering: {
    applyNodeFilters: (nodes: any[], filters: any) => any[];
    applyRelationshipFilters: (relationships: any[], filters: any) => any[];
    applyMetadataFilters: (nodes: any[], filters: any) => any[];
  };
  
  transformation: {
    groupNodes: (nodes: any[], groupBy: string[]) => any[];
    flattenStructure: (nodes: any[]) => any[];
    computeAggregations: (nodes: any[], aggregations: any) => any;
    applySort: (nodes: any[], sortBy: string, direction: 'asc' | 'desc') => any[];
  };
  
  presentation: {
    renderers: {
      tree: (data: any) => any;    // Tree visualization
      graph: (data: any) => any;   // Graph visualization
      table: (data: any) => any;   // Table visualization
      board: (data: any) => any;   // Board visualization
    };
    styleNodes: (nodes: any[], styling: any) => any[];
    styleRelationships: (relationships: any[], styling: any) => any[];
  };
}
```

## 8. Dynamic Scope Assembly and Filtering

### 8.1 Node Object Structure

Each node in the filesystem contains a single unified object that gets filtered differently based on development phase:

```typescript
interface NodeObject {
  // Base properties (always available)
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory' | 'virtual';

  // Ideation properties (prefixed or marked with .id)
  id: {
    concepts: Concept[];
    responsibilities: string[];
    stakeholders: string[];
    requirements: Requirement[];
    notes: string[];
    relations: Relation[];
  };

  // Design properties (with . prefix)
  .design: {
    architecture: ArchitectureSpec;
    interfaces: Interface[];
    contracts: Contract[];
    dataStructures: DataStructure[];
    algorithms: Algorithm[];
    patterns: Pattern[];
  };

  // Production properties (entire object)
  implementation: any;              // Actual implementation
  tests: Test[];
  docs: Documentation[];
  deploymentConfig: DeploymentConfig;
  monitoring: MonitoringConfig;
  
  // Meta properties for all phases
  meta: {
    phase: 'ideation' | 'design' | 'production';
    version: string;
    owner: string;
    contributors: string[];
    lastModified: number;
    status: string;
  };
}
```

### 8.2 Phase-Based Filtering

The system dynamically filters node objects based on the current IDP phase:

```typescript
interface PhaseFiltering {
  ideationFilter: (node: NodeObject) => FilteredNode;
  designFilter: (node: NodeObject) => FilteredNode;
  productionFilter: (node: NodeObject) => FilteredNode;
  
  applyFilter: (node: NodeObject, phase: 'ideation' | 'design' | 'production') => FilteredNode;
  
  // Implementation examples
  ideationFilterImpl: (node: NodeObject) => {
    // Return only .id marked properties
    return {
      id: node.id,
      name: node.name,
      path: node.path,
      type: node.type,
      // Only include ideation properties
      concepts: node.id.concepts,
      responsibilities: node.id.responsibilities,
      // Include meta information
      meta: node.meta
    };
  };
  
  designFilterImpl: (node: NodeObject) => {
    // Return id properties and . prefixed properties
    return {
      id: node.id,
      name: node.name,
      path: node.path, 
      type: node.type,
      // Include ideation properties
      concepts: node.id.concepts,
      responsibilities: node.id.responsibilities,
      // Include design properties with . prefix
      architecture: node['.design'].architecture,
      interfaces: node['.design'].interfaces,
      // Include meta information
      meta: node.meta
    };
  };
  
  productionFilterImpl: (node: NodeObject) => {
    // Return entire object
    return node;
  };
}
```

### 8.3 Dynamic Scope Assembly

The system dynamically assembles scope based on pragma comments and yield statements:

```typescript
interface DynamicScopeAssembly {
  // Event triggers for scope reassembly
  triggers: {
    pragmaCommentChanged: (path: string, newValue: string) => void;
    yieldStatementAdded: (path: string, yieldStatement: string) => void;
    editorStageChanged: (path: string, newStage: 'ideation' | 'design' | 'production') => void;
    nodeResponded: (requestId: string, response: any) => void;
  };
  
  // Scope assembly process
  assembleScope: (nodePath: string, stage: 'ideation' | 'design' | 'production') => Scope;
  
  // Implementation example
  assembleScopeImpl: (nodePath: string, stage: 'ideation' | 'design' | 'production') => {
    const node = getNode(nodePath);
    const filteredNode = applyFilter(node, stage);
    
    // Assemble scope based on node's area of responsibility
    const scope = {
      // Include the filtered node itself
      [node.name]: filteredNode,
      
      // Include parent nodes (filtered appropriately)
      ...getFilteredParents(nodePath, stage),
      
      // Include dependency nodes within area of responsibility
      ...getFilteredDependencies(nodePath, stage),
      
      // Include API functions for cross-boundary communication
      api: buildApiForNode(nodePath, stage)
    };
    
    return scope;
  };
}
```

### 8.4 Yield-Based Interaction Model

The system implements a yield-based interaction model for cross-boundary communication:

```typescript
interface YieldInteractionModel {
  // Yield statement handling
  handleYield: (nodePath: string, yieldStatement: string) => Promise<any>;
  
  // Implementation example
  handleYieldImpl: async (nodePath: string, yieldStatement: string) => {
    // Parse yield statement to extract function and arguments
    const { func, args } = parseYieldStatement(yieldStatement);
    
    // Determine if call is within node's responsibility area
    const isLocalResponsibility = isWithinResponsibilityArea(nodePath, func);
    
    if (isLocalResponsibility) {
      // Execute locally if within responsibility area
      const result = await executeLocalFunction(func, args);
      return result;
    } else {
      // Create patch/request for external node
      const requestId = generateRequestId();
      const request = {
        id: requestId,
        source: nodePath,
        target: determineTargetNode(func),
        function: func,
        arguments: args,
        timestamp: Date.now()
      };
      
      // Send request to target node
      sendNodeRequest(request);
      
      // Return promise that will resolve when target responds
      return new Promise((resolve) => {
        registerResponseHandler(requestId, (response) => {
          resolve(response);
        });
      });
    }
  };
}
```

### 8.5 Responsibility Areas and Role Management

The system manages responsibility areas and roles:

```typescript
interface ResponsibilityManagement {
  // Define responsibility areas
  defineResponsibilityArea: (name: string, pathPatterns: string[]) => void;
  
  // Assign roles to developers
  assignRole: (developerId: string, responsibility: string) => void;
  
  // Check if node is within developer's responsibility
  isNodeInResponsibility: (nodePath: string, developerId: string) => boolean;
  
  // Get all nodes in a responsibility area
  getNodesInResponsibility: (responsibility: string) => string[];
  
  // Handle cross-boundary requests
  handleCrossBoundaryRequest: (request: NodeRequest) => void;
  
  // Implementation example
  handleCrossBoundaryRequestImpl: (request: NodeRequest) => {
    // Determine responsible developers
    const responsibleDevs = getResponsibleDevelopers(request.target);
    
    // Create notification for each responsible developer
    for (const devId of responsibleDevs) {
      createDeveloperNotification(devId, {
        type: 'node-request',
        requestId: request.id,
        source: request.source,
        function: request.function,
        arguments: request.arguments,
        timestamp: request.timestamp,
        actions: [
          { name: 'Accept', handler: () => acceptRequest(request) },
          { name: 'Reject', handler: () => rejectRequest(request) },
          { name: 'Negotiate', handler: () => startNegotiation(request) }
        ]
      });
    }
  };
}
```

### 8.6 Incremental Update Process

The system implements an incremental update process:

```typescript
interface IncrementalUpdateProcess {
  // Update node based on yield result
  updateNodeFromYield: (nodePath: string, yieldResult: any) => void;
  
  // Refresh editor scope
  refreshEditorScope: (nodePath: string) => void;
  
  // Implementation example
  updateNodeFromYieldImpl: (nodePath: string, yieldResult: any) => {
    // Get current node
    const node = getNode(nodePath);
    
    // Apply updates based on yield result
    const updatedNode = applyYieldResult(node, yieldResult);
    
    // Save updated node
    saveNode(nodePath, updatedNode);
    
    // Refresh editor scope to reflect changes
    refreshEditorScope(nodePath);
    
    // Notify dependent nodes about changes
    notifyDependents(nodePath, yieldResult);
  };
  
  refreshEditorScopeImpl: (nodePath: string) => {
    // Get current editing stage
    const stage = getCurrentEditingStage(nodePath);
    
    // Reassemble scope based on current stage
    const newScope = assembleScope(nodePath, stage);
    
    // Update global space with new scope variables
    updateGlobalSpace(newScope);
    
    // Refresh editor UI
    refreshEditorUI(nodePath);
  };
}
```

### 8.7 Stage Transition Process

The system handles transitions between IDP stages:

```typescript
interface StageTransitionProcess {
  // Handle stage transition through pragma comment
  handleStageTransition: (nodePath: string, newStage: 'ideation' | 'design' | 'production') => void;
  
  // Implementation example
  handleStageTransitionImpl: (nodePath: string, newStage: 'ideation' | 'design' | 'production') => {
    // Get current node
    const node = getNode(nodePath);
    
    // Update node's meta phase
    node.meta.phase = newStage;
    saveNode(nodePath, node);
    
    // Reassemble scope based on new stage
    const newScope = assembleScope(nodePath, newStage);
    
    // Update global space with new scope variables
    updateGlobalSpace(newScope);
    
    // Refresh editor UI with appropriate filters
    refreshEditorUI(nodePath);
    
    // Record transition in version history
    recordStageTransition(nodePath, node.meta.phase, newStage);
  };
}
```
