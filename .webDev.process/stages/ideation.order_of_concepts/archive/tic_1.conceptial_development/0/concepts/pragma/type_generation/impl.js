/**
 * @module Pragma/TypeGenerator
 *
 * Generates categorical type definitions from source files
 */
/**
 * Generates type definitions for all modules in a directory
 */
export function generateTypes(config) {
    // Implementation would:
    // 1. Parse all source files
    // 2. Extract imports/exports
    // 3. Build categorical structure
    // 4. Generate type files
    return Promise.resolve({});
}
/**
 * Resolves types from file exports rather than filenames
 */
export function resolveTypesFromExports(sourceFile, importedName) {
    // Parse the file to extract exported types
    const exports = getFileExports(sourceFile);
    // If specific import requested, filter to just that one
    const targetExports = importedName
        ? exports.filter(exp => exp.name === importedName)
        : exports;
    return targetExports.map(exp => ({
        name: exp.name,
        category: inferCategoryFromName(exp.name),
        isDefault: exp.isDefault,
        typeParameters: exp.typeParameters
    }));
}
/**
 * Infers imports needed based on file extensions
 */
export function inferImportsFromExtensions(filename) {
    // Parse extensions like functors.afr,bfr.type.Afr,Bfr.ts
    const parts = filename.split('.');
    const imports = [];
    // Find extension groups (comma-separated values)
    for (let i = 0; i < parts.length - 1; i++) {
        if (parts[i].includes(',')) {
            // This is a grouped extension like afr,bfr
            const extensionGroup = parts[i].split(',');
            // Find corresponding type names if available
            const typeNames = parts[i + 1]?.includes(',')
                ? parts[i + 1].split(',')
                : [];
            // Create import for each extension
            extensionGroup.forEach((ext, index) => {
                imports.push({
                    extension: ext,
                    typeName: typeNames[index] || inferTypeNameFromExtension(ext)
                });
            });
        }
    }
    return imports;
}
/**
 * Resolves the full evolution chain of a type
 */
export function resolveTypeEvolutionChain(baseName) {
    // Find all files matching the base name with sequential markers
    const files = findFilesWithPattern(`${baseName}.(\\d+|tic_\\d+).*\\.ts`);
    // Sort by sequence number
    files.sort((a, b) => {
        const seqA = extractSequenceNumber(a);
        const seqB = extractSequenceNumber(b);
        return seqA - seqB;
    });
    // Resolve types from each file in sequence
    return files.map(file => ({
        ...resolveTypesFromExports(file)[0],
        sequenceNumber: extractSequenceNumber(file)
    }));
}
/**
 * Extracts sequence number from filename
 */
export function extractSequenceNumber(filename) {
    // Extract from numeric format (BaseType.1.type.ts)
    const numMatch = filename.match(/\.(\d+)\./);
    if (numMatch)
        return parseInt(numMatch[1]);
    // Extract from tic format (Process.tic_2.type.ts)
    const ticMatch = filename.match(/\.tic_(\d+)\./);
    if (ticMatch)
        return parseInt(ticMatch[1]);
    return 0; // Base/initial version
}
// Helper functions (would be implemented in full version)
function getFileExports(sourceFile) { return []; }
function inferCategoryFromName(name) { return undefined; }
function inferTypeNameFromExtension(ext) { return ''; }
function findFilesWithPattern(pattern) { return []; }
//# sourceMappingURL=impl.js.map