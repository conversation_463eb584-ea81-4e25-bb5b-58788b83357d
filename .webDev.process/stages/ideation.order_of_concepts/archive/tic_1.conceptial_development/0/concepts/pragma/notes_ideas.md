## local packages names
- local packages are imported by local names, import error "@utils/errors".
- local package names are named as their paths from packages folder
- that means - if package is moved, webstorm will not adjust names and naming convention be broken, in unpredictable ways
- this should be addressed by pragma - it has to do its own indexing, 

## package, or any folder deps are declared in either index file or a separate deps file
- index exports package/folder api as default export
- if deps declared in index, it will export deps object
- if deps file used -guess what happens
- these deps are inserted into local scope as namespaces
- for b ootstrap early impl - each child script will import just one file - scope from index file
- that means - index constructs scope, exports it as scope, including imported deps, from deps if provided
- if not, inferred from package.json
- if its not a package - two options, make a package with package json that has just the deps, or
create deps.ts file, or when pragma matures, declare in index with some syntax like
```md
## deps
path/outside/my/scope
go up left error
error in sibling
error in sibling to right
```
- but normal ts syntax should be accepted by default - if extended syntax is used - itll be tagged as such in comments
- spicetime files are normal extensions like ts,js,jsx - all extended syntax is in special comments
- pragma can make it look just as xmass color looking as ts does - for effect, and clarity, and provide linters, 
which are grammar books, from elementary school

## pragma runtime
- with pragma maturing thru versions - it will implement workflow wgere a dev writes '@pragma ideation', and context 
changes and hes in pragma. Ideation file, just keeps adding notes, like i do here
- when these interrupts get stacked up, a nice breadcrumb mechanism and navigation be very helpful
- there also should be option to do a note wo changing context, like @note need to prune pragma or @todo in pragma ideation
- these leaves a clear record what happened and from where, and prbly why, at least a good start on why, 
but subject to writing skills.the point is - not a syllable is lost to silliness
- such off tangent comments and entries should be put out of sight, in meta somewhere, for record, but current file
should remain clean.that will provide tangents wo changing context. which is a large cognitive load.

## when folders can be projected on local folder, by adding extentions 
- when it specified by package profile - which is a pragma variant, and indicated by name extention in parent folder, 
or upstream
- whats the difference between pragmas and file extensions - file extensions are specified as such by pragmas.pragmas dont
declare other pragmas
- but both staclk up sequentially - form a linguistic pipe. executable. what it does depends on what it says, and 
the context, such as type of term it leads to, and where it started