"use strict";
// The categorical time process that transforms sequences into zippered forests
class CategoricalTimeProcess {
    // Transform pure sequences into structured trees
    static sequenceToTree(sequence) {
        // Use monadic pattern to fold sequence into tree
        return sequence.reduce((tree, item) => {
            // Detect recursion points and create appropriate structure
            return tree.addNode(item);
        }, new TreeNode(null));
    }
    // Create zippered forest from multiple sequences
    static sequencesToForest(sequences) {
        // Transform each sequence to a tree
        const trees = sequences.map(seq => this.sequenceToTree(seq));
        // Create initial forest
        const forest = new ZipperedForest(trees);
        // Discover and create zippers between related nodes
        return forest.discoverRelations((a, b) => this.areRelated(a, b));
    }
    // Process that builds identity through recursion
    static buildIdentity(seed, steps) {
        let current = seed;
        const sequence = [seed];
        // Generate sequence with potential recursion
        for (let i = 0; i < steps; i++) {
            current = this.step(current, sequence);
            sequence.push(current);
        }
        // Transform to tree, folding recursive references
        return this.sequenceToTree(sequence);
    }
}
// Zippered Forest - a forest with relations between trees
class ZipperedForest {
    trees;
    zippers;
    relations;
    constructor(trees, zippers = [], relations = []) {
        this.trees = trees;
        this.zippers = zippers;
        this.relations = relations;
    }
    // Discover relations between nodes in different trees
    discoverRelations(relationPredicate) {
        const newRelations = [];
        // For each pair of trees
        for (let i = 0; i < this.trees.length; i++) {
            for (let j = i + 1; j < this.trees.length; j++) {
                // Find all relations between nodes in these trees
                const treeRelations = this.findRelationsBetween(this.trees[i], this.trees[j], relationPredicate);
                newRelations.push(...treeRelations);
            }
        }
        return new ZipperedForest(this.trees, this.zippers, [...this.relations, ...newRelations]);
    }
}
//# sourceMappingURL=tic_5.categorical_spacetime_expansion.js.map