"use strict";
// Transactional dynamics of time links
class TimeLinkDynamics {
    // Model the directional constraints of time links
    static modelConstraints() {
        return {
            // Past cannot receive mutations from present or future
            pastCanReceiveMutations: false,
            // Present can receive mutations from past (through memory/history)
            presentCanReceiveMutationsFromPast: true,
            // Present cannot receive mutations from future
            presentCanReceiveMutationsFromFuture: false,
            // Future can be freely mutated based on present
            futureCanReceiveMutations: true
        };
    }
    // Digital control system for time propagation
    static createControlSystem() {
        return new TimeControlSystem({
            // Forward propagation rate (present → future)
            forwardRate: 1.0,
            // Backward influence rate (past → present)
            backwardRate: 0.5,
            // Damping factor for oscillations
            damping: 0.3,
            // Feedback mechanisms
            feedback: {
                // How strongly past affects present decisions
                pastInfluence: 0.7,
                // How strongly future projections affect present
                futureInfluence: 0.4
            }
        });
    }
    // Partial differential equations for time propagation
    static propagationEquations() {
        return {
            // How past influences present (memory, learning)
            pastToPresent: (past, dt) => {
                return (node) => {
                    // Extract relevant history from past
                    const history = past.getRelevantHistory(node);
                    // Apply historical influence with decay
                    return node.applyHistory(history, {
                        decayFactor: Math.exp(-0.1 * dt)
                    });
                };
            },
            // How present projects to future
            presentToFuture: (present, dt) => {
                return (node) => {
                    // Project current state forward
                    return present.projectForward(node, dt);
                };
            }
        };
    }
}
// Control system for time dynamics
class TimeControlSystem {
    parameters;
    constructor(parameters) {
        this.parameters = parameters;
    }
    // Apply control to time propagation
    control(time) {
        // Apply forward propagation
        const controlledFuture = this.forwardPropagate(time.getPresent(), time.getFuture());
        // Apply backward influence
        const controlledPresent = this.backwardInfluence(time.getPast(), time.getPresent());
        // Create new time state with controlled nodes
        return time.withNodes(time.getPast(), controlledPresent, controlledFuture);
    }
}
//# sourceMappingURL=transactional_dynamics_of_links.especially_time_lionks.js.map