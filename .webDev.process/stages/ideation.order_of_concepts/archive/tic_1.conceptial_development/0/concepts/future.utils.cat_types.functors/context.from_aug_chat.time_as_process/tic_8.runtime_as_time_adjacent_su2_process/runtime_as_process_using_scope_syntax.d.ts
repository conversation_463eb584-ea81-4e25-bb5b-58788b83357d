declare const reactComponent: {
    visibleApis: {
        getUser: () => {
            id: number;
            name: string;
        };
        getTheme: () => string;
    };
    plannedFuture: {
        nextState: () => string;
    };
    currentState: string;
};
declare const process: {
    accessPast(query: any): any;
    accessFuture(query: any): any;
    execute(fn: any): any;
    dayDream(scenario: any, dreamFn: any): any;
};
declare const dreamResult: any;
//# sourceMappingURL=runtime_as_process_using_scope_syntax.d.ts.map