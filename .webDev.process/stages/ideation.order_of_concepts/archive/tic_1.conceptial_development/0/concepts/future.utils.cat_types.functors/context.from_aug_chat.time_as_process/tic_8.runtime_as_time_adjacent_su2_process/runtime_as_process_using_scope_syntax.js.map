{"version": 3, "file": "runtime_as_process_using_scope_syntax.js", "sourceRoot": "", "sources": ["runtime_as_process_using_scope_syntax.ts"], "names": [], "mappings": ";AAAA,+DAA+D;AAC/D,SAAS,oBAAoB,CAAC,eAAe;IACzC,kEAAkE;IAClE,MAAM,OAAO,GAAG;QACZ,oCAAoC;QACpC,QAAQ,EAAE,eAAe,CAAC,WAAW;QAErC,sCAAsC;QACtC,UAAU,EAAE,eAAe,CAAC,aAAa;QAEzC,oBAAoB;QACpB,OAAO,EAAE,eAAe,CAAC,YAAY;KACxC,CAAC;IAEF,kEAAkE;IAClE,OAAO;QACH,wCAAwC;QACxC,UAAU,CAAC,KAAK;YACZ,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;QACvC,CAAC;QAED,8CAA8C;QAC9C,YAAY,CAAC,KAAK;YACd,OAAO,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;QACzC,CAAC;QAED,mCAAmC;QACnC,OAAO,CAAC,EAAE;YACN,yCAAyC;YACzC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,4CAA4C;QAC5C,QAAQ,CAAC,QAAQ,EAAE,OAAO;YACtB,gCAAgC;YAChC,MAAM,YAAY,GAAG,EAAC,GAAG,OAAO,EAAE,GAAG,QAAQ,EAAC,CAAC;YAE/C,qCAAqC;YACrC,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;KACJ,CAAC;AACN,CAAC;AAED,gBAAgB;AAChB,MAAM,cAAc,GAAG;IACnB,WAAW,EAAE;QACT,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAC,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAC,CAAC;QACtC,QAAQ,EAAE,GAAG,EAAE,CAAC,MAAM;KACzB;IACD,aAAa,EAAE;QACX,SAAS,EAAE,GAAG,EAAE,CAAC,SAAS;KAC7B;IACD,YAAY,EAAE,MAAM;CACvB,CAAC;AAEF,yBAAyB;AACzB,MAAM,OAAO,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;AAErD,4CAA4C;AAC5C,OAAO,CAAC,OAAO,CAAC;IACZ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;IACpC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,wBAAwB;AAClE,CAAC,CAAC,CAAC;AAEH,4CAA4C;AAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAChC,EAAC,OAAO,EAAE,aAAa,EAAC,EACxB;IACI,OAAO,2BAA2B,IAAI,CAAC,OAAO,EAAE,CAAC;AACrD,CAAC,CACJ,CAAC;AACF,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,wCAAwC"}