# Time Process Specification

## 1. The Time Process

### 1.1 Definition

The Time Process is the fundamental sequence manager in SpiceTime:

- It maintains a discrete sequence of state changes (tics)
- It establishes causal relationships between events
- It provides the foundation for all other processes

```javascript
// Basic Time Process structure
const timeProcess = {
  id: "time_process_123",
  currentTic: 0,
  sequence: [],
  attachedProcesses: new Map()
};
```

### 1.2 Core Functions

The Time Process performs these essential functions:

1. **Tic Generation**: Creates discrete time units
2. **Sequence Management**: Maintains ordered event history
3. **Causal Enforcement**: Ensures events respect dependencies

```javascript
// Time Process core functions
function advanceTic(timeProcess) {
  timeProcess.currentTic++;
  timeProcess.sequence.push({
    tic: timeProcess.currentTic,
    timestamp: Date.now()
  });
  
  // Notify attached processes
  for (const process of timeProcess.attachedProcesses.values()) {
    process.onTic(timeProcess.currentTic);
  }
}
```

### 1.3 Time Process Context

The Time Process maintains a context that includes:

1. **Current Tic**: The current position in the sequence
2. **Sequence History**: Record of past tics and events
3. **Causal Graph**: Dependencies between events

## 2. Process Attachment to Time Process

### 2.1 Attachment Mechanism

Other processes attach to the Time Process through a defined interface:

```javascript
// Attaching a process to the Time Process
function attachProcess(timeProcess, process) {
  // Register the process
  timeProcess.attachedProcesses.set(process.id, process);
  
  // Initialize the process with current temporal context
  process.initialize({
    currentTic: timeProcess.currentTic,
    sequence: timeProcess.sequence
  });
  
  return process;
}
```

### 2.2 Types of Attached Processes

Processes attach to the Time Process in specific relationships:

1. **Runtime Process**: Executes code within temporal context
2. **State Process**: Manages state changes over time
3. **Event Process**: Handles event propagation in temporal order

### 2.3 Context Inheritance

Attached processes inherit context from the Time Process:

```javascript
// Context inheritance example
function initializeProcess(process, timeContext) {
  process.context = {
    temporal: {
      currentTic: timeContext.currentTic,
      sequence: timeContext.sequence
    },
    // Process-specific context
    local: {}
  };
}
```

## 3. Runtime Process

### 3.1 Runtime Process Definition

The Runtime Process is a specific type that attaches to the Time Process:

```javascript
// Runtime Process structure
const runtimeProcess = {
  id: "runtime_123",
  type: "runtime",
  context: {
    temporal: {/* inherited from Time Process */},
    execution: {/* runtime-specific context */}
  },
  
  // Called when Time Process advances
  onTic(tic) {
    // Update temporal context
    this.context.temporal.currentTic = tic;
    
    // Execute pending tasks
    this.executePendingTasks();
  }
};
```

### 3.2 Runtime Process Functions

The Runtime Process:

1. **Executes** code within the temporal context
2. **Maintains** its own execution state
3. **Follows** the temporal rules of its parent Time Process

```javascript
// Runtime Process execution
function executeInRuntime(runtimeProcess, task) {
  // Bind task to runtime context
  const boundTask = task.bind(runtimeProcess.context);
  
  // Execute within current temporal context
  return boundTask();
}
```

## 4. Other Process Types

### 4.1 Gateway Process

The Gateway Process manages communication within temporal context:

```javascript
// Gateway Process structure
const gatewayProcess = {
  id: "gateway_123",
  type: "gateway",
  channels: new Map(),
  
  // Called when Time Process advances
  onTic(tic) {
    // Process incoming messages in temporal order
    this.processMessages(tic);
  }
};
```

### 4.2 Agent Process

The Agent Process handles decision-making within temporal context:

```javascript
// Agent Process structure
const agentProcess = {
  id: "agent_123",
  type: "agent",
  goals: [],
  strategies: [],
  
  // Called when Time Process advances
  onTic(tic) {
    // Make decisions based on current context
    this.evaluateStrategies();
  }
};
```

## 5. Process Interaction

Processes interact through well-defined interfaces:

```javascript
// Process interaction example
function connectProcesses(sourceProcess, targetProcess) {
  // Create a channel between processes
  const channel = {
    source: sourceProcess.id,
    target: targetProcess.id,
    messages: []
  };
  
  // Register channel with both processes
  sourceProcess.channels.set(targetProcess.id, channel);
  targetProcess.channels.set(sourceProcess.id, channel);
}
```

## 6. Implementation Approach

The implementation follows these principles:

1. **Separation of Concerns**: Each process type has a specific role
2. **Temporal Consistency**: All processes follow the same temporal rules
3. **Context Isolation**: Processes have limited access to each other's context
4. **Event-Driven**: Processes respond to tic events from the Time Process