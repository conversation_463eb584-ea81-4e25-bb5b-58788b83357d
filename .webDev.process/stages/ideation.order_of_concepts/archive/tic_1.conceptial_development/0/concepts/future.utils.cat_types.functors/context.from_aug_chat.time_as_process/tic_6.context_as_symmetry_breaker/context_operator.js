"use strict";
// Context as a symmetry-breaking operator that structures time
class ContextOperator {
    contextRules;
    constructor(contextRules) {
        this.contextRules = contextRules;
    }
    // Transform a pure dependency graph into structured trees
    structureGraph(graph) {
        // Start with raw dependency links
        const links = graph.getAllLinks();
        // Categorize links based on context rules
        const categorizedLinks = this.categorizeLinks(links);
        // Extract tree structure (parent-child) links
        const treeLinks = categorizedLinks.get('parentToChild') || [];
        // Build trees from parent-child links
        const trees = this.buildTreesFromLinks(treeLinks);
        // Create other relationship types (SU2, SU3, zippers)
        const relationships = this.createRelationships(categorizedLinks, trees);
        return new ForestStructure(trees, relationships);
    }
    // Categorize links based on context rules
    categorizeLinks(links) {
        const categorized = new Map();
        for (const link of links) {
            for (const rule of this.contextRules) {
                const type = rule.categorize(link);
                if (type) {
                    if (!categorized.has(type)) {
                        categorized.set(type, []);
                    }
                    categorized.get(type).push(link);
                    break; // First matching rule wins
                }
            }
        }
        return categorized;
    }
}
//# sourceMappingURL=context_operator.js.map