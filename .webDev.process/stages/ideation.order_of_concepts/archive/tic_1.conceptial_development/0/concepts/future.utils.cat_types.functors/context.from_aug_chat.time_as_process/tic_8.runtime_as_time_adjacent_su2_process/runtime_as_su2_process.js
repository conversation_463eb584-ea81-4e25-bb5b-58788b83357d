"use strict";
// SU2 Runtime Process - attached to present stage/proto
class SU2RuntimeProcess {
    attachmentPoint;
    scope;
    context;
    constructor(attachmentPoint, // The "present" when process was created
    scope, // Visible scope from attachment point
    context // Process execution context
    ) {
        this.attachmentPoint = attachmentPoint;
        this.scope = scope;
        this.context = context;
    }
    // Access past through public APIs only
    accessPast(query) {
        // Can only see what past explicitly exposes
        const pastView = this.scope.getPastPublicView();
        // Execute query against limited past view
        return pastView.executeQuery(query);
    }
    // Access future as it was planned at creation time
    accessFuture(query) {
        // Can only see meta.future - the planned future
        const metaFuture = this.scope.getMetaFuture();
        // Cannot see what actually happened in future
        return metaFuture.executeQuery(query);
    }
    // Execute in the dynamic context of attachment point
    execute(input) {
        // JS dynamic binding naturally handles the context
        return this.context.evaluateInScope(input, this.scope, { attachmentPoint: this.attachmentPoint });
    }
    // Inject custom context for out-of-context analysis
    injectContext(customContext) {
        // Create new process with injected context
        return new SU2RuntimeProcess(this.attachmentPoint, this.scope, this.context.withCustomContext(customContext));
    }
    // Day dreaming - out of context analysis
    dayDream(scenario) {
        // Create temporary detached context
        const dreamContext = this.context.createDetachedContext({
            baseReality: this.scope,
            dreamScenario: scenario,
            attachmentPoint: this.attachmentPoint
        });
        // Run simulation in detached context
        return dreamContext.simulate();
    }
}
// Runtime scope visible from attachment point
class RuntimeScope {
    pastPublicApis;
    presentState;
    metaFuture;
    constructor(pastPublicApis, presentState, metaFuture) {
        this.pastPublicApis = pastPublicApis;
        this.presentState = presentState;
        this.metaFuture = metaFuture;
    }
    // Get limited view of past through public APIs
    getPastPublicView() {
        return new PastPublicView(this.pastPublicApis);
    }
    // Get meta.future - the planned future at creation time
    getMetaFuture() {
        return this.metaFuture;
    }
    // Get current state at attachment point
    getPresentState() {
        return this.presentState;
    }
}
// React string runtime implementation
class ReactStringRuntime extends SU2RuntimeProcess {
    constructor(attachmentPoint, browserContext) {
        super(attachmentPoint, new ReactRuntimeScope(attachmentPoint), new ReactRuntimeContext(browserContext));
    }
    // Render React string in browser
    render(reactString) {
        return this.execute({
            type: 'render',
            content: reactString,
            target: this.context.getTarget()
        });
    }
    // Implement day dreaming for React components
    componentDayDream(alternateProps) {
        return this.dayDream({
            type: 'component-simulation',
            alternateProps,
            renderTarget: 'virtual'
        });
    }
}
// Context for dynamic evaluation
class RuntimeContext {
    evaluator;
    customContexts;
    constructor(evaluator, customContexts = []) {
        this.evaluator = evaluator;
        this.customContexts = customContexts;
    }
    // Evaluate input in given scope
    evaluateInScope(input, scope, options) {
        // Apply all custom contexts
        const enrichedInput = this.customContexts.reduce((acc, ctx) => ctx.enrichInput(acc, scope), input);
        // Evaluate with dynamic binding
        return this.evaluator.evaluate(enrichedInput, scope, options);
    }
    // Add custom context for analysis
    withCustomContext(customContext) {
        return new RuntimeContext(this.evaluator, [...this.customContexts, customContext]);
    }
    // Create detached context for day dreaming
    createDetachedContext(options) {
        return new DetachedContext(this.evaluator.clone(), options);
    }
}
//# sourceMappingURL=runtime_as_su2_process.js.map