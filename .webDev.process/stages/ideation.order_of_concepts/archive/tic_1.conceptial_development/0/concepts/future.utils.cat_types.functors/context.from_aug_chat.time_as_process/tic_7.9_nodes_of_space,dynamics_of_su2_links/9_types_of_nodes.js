"use strict";
// The 10 possible node types based on temporal position and interaction
var TimeNodeType;
(function (TimeNodeType) {
    // Present-based nodes
    TimeNodeType[TimeNodeType["PRESENT_OBSERVING_PAST"] = 0] = "PRESENT_OBSERVING_PAST";
    TimeNodeType[TimeNodeType["PRESENT_OBSERVING_PRESENT"] = 1] = "PRESENT_OBSERVING_PRESENT";
    TimeNodeType[TimeNodeType["PRESENT_PROJECTING_FUTURE"] = 2] = "PRESENT_PROJECTING_FUTURE";
    // Past-based nodes
    TimeNodeType[TimeNodeType["PAST_REFLECTING_ON_PAST"] = 3] = "PAST_REFLECTING_ON_PAST";
    TimeNodeType[TimeNodeType["PAST_REFLECTING_ON_PRESENT"] = 4] = "PAST_REFLECTING_ON_PRESENT";
    TimeNodeType[TimeNodeType["PAST_PROJECTING_FUTURE"] = 5] = "PAST_PROJECTING_FUTURE";
    // Future-based nodes
    TimeNodeType[TimeNodeType["FUTURE_REFLECTING_ON_PAST"] = 6] = "FUTURE_REFLECTING_ON_PAST";
    TimeNodeType[TimeNodeType["FUTURE_REFLECTING_ON_PRESENT"] = 7] = "FUTURE_REFLECTING_ON_PRESENT";
    TimeNodeType[TimeNodeType["FUTURE_PROJECTING_NEAR"] = 8] = "FUTURE_PROJECTING_NEAR";
    TimeNodeType[TimeNodeType["FUTURE_PROJECTING_FAR"] = 9] = "FUTURE_PROJECTING_FAR"; // Long-term future projection
})(TimeNodeType || (TimeNodeType = {}));
// Node factory that creates the appropriate type of node
class TimeSpaceNodeFactory {
    createNode(type, context) {
        switch (type) {
            case TimeNodeType.PRESENT_OBSERVING_PAST:
                return new PresentObservingPastNode(context);
            case TimeNodeType.PRESENT_PROJECTING_FUTURE:
                return new PresentProjectingFutureNode(context);
            case TimeNodeType.PAST_REFLECTING_ON_PRESENT:
                return new PastReflectingOnPresentNode(context);
            case TimeNodeType.FUTURE_PROJECTING_NEAR:
                return new FutureProjectingNearNode(context);
            // ... other node types
            default:
                throw new Error(`Unknown node type: ${type}`);
        }
    }
}
// Future tree with multiple scenarios
class FutureTree {
    basePresent;
    scenarios;
    constructor(basePresent, scenarios = new Map()) {
        this.basePresent = basePresent;
        this.scenarios = scenarios;
    }
    addScenario(name, futureNode) {
        this.scenarios.set(name, futureNode);
    }
    // Get a specific future scenario
    getScenario(name) {
        return this.scenarios.get(name);
    }
    // Create a structured tree of future possibilities
    buildTree() {
        const root = new TreeNode(this.basePresent);
        // Add each scenario as a child
        for (const [name, node] of this.scenarios.entries()) {
            root.addChild(new TreeNode(node, { name }));
        }
        return root;
    }
}
//# sourceMappingURL=9_types_of_nodes.js.map