// Context as a symmetry-breaking operator that structures time
class ContextOperator<T> {
    constructor(
        private contextRules: ContextRule<T>[]
    ) {}

    // Transform a pure dependency graph into structured trees
    structureGraph(graph: DependencyGraph<T>): ForestStructure<T> {
        // Start with raw dependency links
        const links = graph.getAllLinks();

        // Categorize links based on context rules
        const categorizedLinks = this.categorizeLinks(links);

        // Extract tree structure (parent-child) links
        const treeLinks = categorizedLinks.get('parentToChild') || [];

        // Build trees from parent-child links
        const trees = this.buildTreesFromLinks(treeLinks);

        // Create other relationship types (SU2, SU3, zippers)
        const relationships = this.createRelationships(
            categorizedLinks,
            trees
        );

        return new ForestStructure(trees, relationships);
    }

    // Categorize links based on context rules
    private categorizeLinks(links: Link<T>[]): Map<RelationType, Link<T>[]> {
        const categorized = new Map<RelationType, Link<T>[]>();

        for (const link of links) {
            for (const rule of this.contextRules) {
                const type = rule.categorize(link);
                if (type) {
                    if (!categorized.has(type)) {
                        categorized.set(type, []);
                    }
                    categorized.get(type)!.push(link);
                    break; // First matching rule wins
                }
            }
        }

        return categorized;
    }
}

// Context rule that determines link types
interface ContextRule<T> {
    categorize(link: Link<T>): RelationType | null;
}

// Types of relationships that context can create
type RelationType =
    | 'parentToChild'  // Tree structure
    | 'SU2'            // Binary relationship
    | 'SU3'            // Ternary relationship
    | 'zipper'         // Cross-tree relation
    | 'sequential';    // Time-ordered relation