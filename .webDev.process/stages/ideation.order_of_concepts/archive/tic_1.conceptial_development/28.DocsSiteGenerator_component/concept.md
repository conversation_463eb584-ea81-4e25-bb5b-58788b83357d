# DocsSiteGenerator Component

## Overview

The DocsSiteGenerator is a component that generates a unified documentation site for the entire Spicetime Architecture. Instead of maintaining separate documentation for each package, this component provides a centralized system that automatically generates and aggregates documentation from all packages into a cohesive site.

## Problem Statement

Currently, documentation is scattered across different packages with inconsistent structure and formatting. This makes it difficult for developers to:

1. Find the documentation they need
2. Understand how different packages relate to each other
3. Maintain documentation as the codebase evolves
4. Ensure consistent quality and structure across all documentation

## Solution

Create a unified documentation site generator that:

1. Automatically extracts API documentation from TypeScript code (implementation layer)
2. Aggregates markdown documentation from all packages (specifications layer)
3. Presents the stream of concepts from ideation in a discoverable form
4. Generates a dynamic site with context-aware navigation and styling
5. Provides search functionality across all documentation
6. Automatically deploys when code or documentation changes
7. Adapts to user context and questions through an agent-driven discovery mechanism

## Key Features

1. **Unified Structure**: Standardized documentation structure across all packages
2. **Layered Documentation**: Clear separation between implementation (API docs), specifications, and ideation concepts
3. **Historical Layers**: Temporal dimension showing the evolution of ideas and implementations over time
4. **Automated API Docs**: Automatic generation of API documentation from TypeScript code
5. **Markdown Processing**: Support for markdown files with frontmatter for usage guides and examples
6. **Dynamic Discovery**: Agent-driven discovery mechanism that builds routes dynamically based on user context and questions
7. **Concept Exploration**: Interactive exploration of the ideation stream, showing why decisions were made, when, and by whom
8. **Adaptive Navigation**: Navigation that evolves based on usage patterns, creating an emergent structure
9. **Context Awareness**: Content presentation that adapts to different user types and contexts
10. **Search Functionality**: Full-text search across all documentation
11. **Automated Deployment**: Automatic deployment to GitHub Pages when code or documentation changes
12. **Route Caching**: Caching of dynamically generated routes to improve performance and create a default site structure

## Implementation Approach

The DocsSiteGenerator will be implemented as a standalone component with these parts:

1. **Documentation Structure**: Standard directory structure for package documentation
2. **TypeDoc Integration**: Configuration for generating API documentation from TypeScript
3. **Gatsby Site**: Static site generator for creating the documentation site
4. **Build Scripts**: Scripts for building and deploying the documentation
5. **GitHub Actions**: Workflows for automating the documentation process

### Documentation Structure

Each package should follow this documentation structure:

```
package-name/
├── docs/
│   ├── README.md             # Overview of the package
│   ├── usage/                # Usage guides
│   │   ├── getting-started.md
│   │   └── ...
│   ├── api/                  # API documentation (auto-generated)
│   │   └── ...
│   └── examples/             # Code examples
│       ├── example1.md
│       └── ...
└── package.json              # Contains metadata for the package
```

### TypeDoc Integration

TypeDoc will be used to generate API documentation from TypeScript code. A standard TypeDoc configuration will be provided for all packages.

### Gatsby Site

A Gatsby site will be created to:

1. Aggregate documentation from all packages
2. Provide consistent navigation and styling
3. Enable search functionality
4. Generate a static site for deployment

### Build Scripts

Build scripts will be provided for:

1. Generating API documentation for a single package
2. Building the entire documentation site
3. Serving the documentation site locally for development

### GitHub Actions

GitHub Actions workflows will be created to:

1. Automatically build the documentation site when code or documentation changes
2. Deploy the documentation site to GitHub Pages

## Component Location

The DocsSiteGenerator component will be located at:

```
packages/components/DocsSiteGenerator/
```

## Dependencies

The DocsSiteGenerator will depend on:

1. TypeDoc for API documentation generation
2. Gatsby for static site generation
3. React for UI components
4. GitHub Actions for automation

## Timeline

1. **Phase 1**: Create the documentation structure and TypeDoc configuration
2. **Phase 2**: Implement the Gatsby site
3. **Phase 3**: Create build scripts and GitHub Actions workflows
4. **Phase 4**: Test with existing packages
5. **Phase 5**: Deploy the documentation site

## Success Criteria

The DocsSiteGenerator will be considered successful if:

1. All packages have consistent documentation structure
2. API documentation is automatically generated from TypeScript code
3. The documentation site provides a unified experience
4. Documentation is automatically updated when code changes
5. Developers can easily find the documentation they need

## Next Steps

1. Create the standard documentation structure
2. Implement the TypeDoc configuration
3. Create the Gatsby site
4. Implement build scripts and GitHub Actions workflows
5. Test with existing packages
