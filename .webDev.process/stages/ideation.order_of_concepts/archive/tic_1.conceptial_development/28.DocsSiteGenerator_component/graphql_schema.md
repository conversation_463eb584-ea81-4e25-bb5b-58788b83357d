# GraphQL Schema for DocSiteGenerator

## Overview

This document defines the GraphQL schema for the DocSiteGenerator service architecture. The schema enables dynamic routing, content generation, and site rebuilding based on user interactions and structure changes. It serves as the foundation for the adaptive, agent-driven documentation system.

## Service Architecture

The DocSiteGenerator consists of several microservices that work together:

1. **Content Service**: Manages documentation content (markdown, code, metadata)
2. **Structure Service**: Handles the documentation structure and routing
3. **Agent Service**: Processes user queries and generates dynamic routes
4. **Build Service**: Triggers Gatsby rebuilds when structure changes
5. **History Service**: Manages temporal aspects of documentation
6. **User Context Service**: Tracks and manages user context

These services communicate through a GraphQL API defined by this schema.

## Schema Definition

```graphql
# Root Query type
type Query {
  # Content queries
  content(id: ID!): Content
  contentByPath(path: String!): Content
  contentSearch(query: String!, limit: Int = 10): [Content]
  contentVersion(id: ID!, version: String!): Content
  
  # Structure queries
  structure: Structure
  route(path: String!): Route
  dynamicRoute(query: String!, context: UserContextInput): Route
  
  # User context queries
  userContext(id: ID!): UserContext
  
  # History queries
  contentHistory(id: ID!): [ContentVersion]
  structureHistory: [StructureVersion]
  conceptEvolution(conceptId: ID!): ConceptEvolution
  
  # Build status queries
  buildStatus: BuildStatus
  buildHistory(limit: Int = 10): [Build]
}

# Root Mutation type
type Mutation {
  # Content mutations
  updateContent(input: ContentInput!): Content
  deleteContent(id: ID!): Boolean
  
  # Structure mutations
  updateStructure(input: StructureInput!): Structure
  createRoute(input: RouteInput!): Route
  updateRoute(id: ID!, input: RouteInput!): Route
  deleteRoute(id: ID!): Boolean
  
  # User context mutations
  updateUserContext(id: ID!, input: UserContextInput!): UserContext
  
  # Build mutations
  triggerBuild(input: BuildInput): Build
  cancelBuild(id: ID!): Boolean
  
  # Agent mutations
  processUserQuery(query: String!, context: UserContextInput): AgentResponse
}

# Subscription type for real-time updates
type Subscription {
  contentUpdated(id: ID): Content
  structureUpdated: Structure
  buildStatusChanged: BuildStatus
  routeGenerated(queryId: ID!): Route
}

# Content types
interface Content {
  id: ID!
  path: String!
  title: String!
  description: String
  type: ContentType!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  versions: [ContentVersion]
  currentVersion: String!
}

type MarkdownContent implements Content {
  id: ID!
  path: String!
  title: String!
  description: String
  type: ContentType!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  versions: [ContentVersion]
  currentVersion: String!
  markdown: String!
  frontmatter: JSONObject
  html: String
  tableOfContents: JSONObject
}

type ApiContent implements Content {
  id: ID!
  path: String!
  title: String!
  description: String
  type: ContentType!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  versions: [ContentVersion]
  currentVersion: String!
  apiData: JSONObject!
  sourceFile: String
  packageName: String
}

type CodeContent implements Content {
  id: ID!
  path: String!
  title: String!
  description: String
  type: ContentType!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  versions: [ContentVersion]
  currentVersion: String!
  code: String!
  language: String!
  highlightedCode: String
}

type ConceptContent implements Content {
  id: ID!
  path: String!
  title: String!
  description: String
  type: ContentType!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  versions: [ContentVersion]
  currentVersion: String!
  markdown: String!
  frontmatter: JSONObject
  html: String
  conceptId: ID!
  stage: String
  status: ConceptStatus
  relatedConcepts: [Concept]
}

enum ContentType {
  MARKDOWN
  API
  CODE
  CONCEPT
  JSX
}

enum ConceptStatus {
  DRAFT
  PROPOSED
  ACCEPTED
  IMPLEMENTED
  DEPRECATED
}

type ContentVersion {
  id: ID!
  contentId: ID!
  version: String!
  data: JSONObject!
  createdAt: DateTime!
  createdBy: String
  commitId: String
  commitMessage: String
}

input ContentInput {
  id: ID
  path: String!
  title: String!
  description: String
  type: ContentType!
  markdown: String
  code: String
  language: String
  apiData: JSONObject
  frontmatter: JSONObject
  conceptId: ID
  stage: String
  status: ConceptStatus
}

# Structure types
type Structure {
  id: ID!
  version: String!
  updatedAt: DateTime!
  routes: [Route]
  navigation: [NavigationItem]
}

type Route {
  id: ID!
  path: String!
  component: String!
  exact: Boolean
  content: Content
  context: JSONObject
  metadata: JSONObject
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  dynamicallyGenerated: Boolean
  generatedFrom: String
  userContextId: ID
}

type NavigationItem {
  id: ID!
  title: String!
  path: String
  children: [NavigationItem]
  order: Int
  metadata: JSONObject
}

type StructureVersion {
  id: ID!
  version: String!
  structure: JSONObject!
  createdAt: DateTime!
  createdBy: String
  commitId: String
  commitMessage: String
}

input StructureInput {
  routes: [RouteInput]
  navigation: [NavigationItemInput]
}

input RouteInput {
  path: String!
  component: String!
  exact: Boolean
  contentId: ID
  context: JSONObject
  metadata: JSONObject
}

input NavigationItemInput {
  id: ID
  title: String!
  path: String
  children: [NavigationItemInput]
  order: Int
  metadata: JSONObject
}

# User context types
type UserContext {
  id: ID!
  role: String
  expertiseLevel: Int
  interests: [String]
  viewPreferences: JSONObject
  interactionHistory: [Interaction]
  currentFocus: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Interaction {
  id: ID!
  type: InteractionType!
  data: JSONObject!
  timestamp: DateTime!
  routePath: String
  contentId: ID
  query: String
}

enum InteractionType {
  PAGE_VIEW
  SEARCH
  QUERY
  NAVIGATION
  FEEDBACK
}

input UserContextInput {
  role: String
  expertiseLevel: Int
  interests: [String]
  viewPreferences: JSONObject
  currentFocus: String
}

# Build types
type Build {
  id: ID!
  status: BuildStatus!
  startedAt: DateTime!
  completedAt: DateTime
  triggeredBy: String
  structureVersion: String
  log: String
  error: String
}

enum BuildStatus {
  QUEUED
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

input BuildInput {
  structureVersion: String
  incremental: Boolean
}

# History types
type ConceptEvolution {
  id: ID!
  conceptId: ID!
  stages: [ConceptStage]
  contributors: [Contributor]
  timeline: [TimelineEvent]
  relatedConcepts: [RelatedConcept]
}

type ConceptStage {
  id: ID!
  stage: String!
  status: ConceptStatus!
  content: Content
  startDate: DateTime!
  endDate: DateTime
}

type Contributor {
  id: ID!
  name: String!
  contributions: [Contribution]
}

type Contribution {
  id: ID!
  type: ContributionType!
  description: String
  timestamp: DateTime!
  contentId: ID
  commitId: String
}

enum ContributionType {
  CREATED
  UPDATED
  REVIEWED
  APPROVED
  IMPLEMENTED
}

type TimelineEvent {
  id: ID!
  timestamp: DateTime!
  type: TimelineEventType!
  description: String
  actor: String
  contentId: ID
  commitId: String
  metadata: JSONObject
}

enum TimelineEventType {
  CONCEPT_CREATED
  CONCEPT_UPDATED
  STATUS_CHANGED
  DECISION_MADE
  IMPLEMENTATION_STARTED
  IMPLEMENTATION_COMPLETED
}

type RelatedConcept {
  id: ID!
  conceptId: ID!
  title: String!
  relationship: RelationshipType!
  strength: Float
}

enum RelationshipType {
  PARENT
  CHILD
  INSPIRED_BY
  REPLACED_BY
  RELATED
}

# Agent types
type AgentResponse {
  id: ID!
  query: String!
  response: String
  suggestedRoute: Route
  suggestedContent: [Content]
  suggestedQueries: [String]
  context: JSONObject
  timestamp: DateTime!
}

# Scalar types
scalar DateTime
scalar JSONObject
```

## Key Schema Components

### Content Management

The schema defines different types of content:

- **MarkdownContent**: Documentation written in markdown
- **ApiContent**: API documentation generated from TypeScript
- **CodeContent**: Code examples and snippets
- **ConceptContent**: Ideation concepts with their metadata

Content is versioned, allowing for historical exploration:

```graphql
type ContentVersion {
  id: ID!
  contentId: ID!
  version: String!
  data: JSONObject!
  createdAt: DateTime!
  createdBy: String
  commitId: String
  commitMessage: String
}
```

### Dynamic Routing

The schema enables dynamic route generation based on user queries:

```graphql
type Query {
  dynamicRoute(query: String!, context: UserContextInput): Route
}

type Mutation {
  processUserQuery(query: String!, context: UserContextInput): AgentResponse
}
```

Routes can be created, updated, and deleted:

```graphql
type Mutation {
  createRoute(input: RouteInput!): Route
  updateRoute(id: ID!, input: RouteInput!): Route
  deleteRoute(id: ID!): Boolean
}
```

### Structure Management

The documentation structure is managed through:

```graphql
type Structure {
  id: ID!
  version: String!
  updatedAt: DateTime!
  routes: [Route]
  navigation: [NavigationItem]
}
```

Structure changes trigger builds:

```graphql
type Mutation {
  updateStructure(input: StructureInput!): Structure
  triggerBuild(input: BuildInput): Build
}
```

### User Context

User context is tracked to personalize the documentation experience:

```graphql
type UserContext {
  id: ID!
  role: String
  expertiseLevel: Int
  interests: [String]
  viewPreferences: JSONObject
  interactionHistory: [Interaction]
  currentFocus: String
  createdAt: DateTime!
  updatedAt: DateTime!
}
```

### Historical Exploration

The schema supports exploring the evolution of concepts:

```graphql
type Query {
  contentHistory(id: ID!): [ContentVersion]
  structureHistory: [StructureVersion]
  conceptEvolution(conceptId: ID!): ConceptEvolution
}
```

### Agent Interaction

The agent service processes user queries and generates responses:

```graphql
type Mutation {
  processUserQuery(query: String!, context: UserContextInput): AgentResponse
}

type AgentResponse {
  id: ID!
  query: String!
  response: String
  suggestedRoute: Route
  suggestedContent: [Content]
  suggestedQueries: [String]
  context: JSONObject
  timestamp: DateTime!
}
```

## Service Interactions

### Dynamic Route Generation Flow

1. User submits a query through the UI
2. The query is sent to the Agent Service via `processUserQuery` mutation
3. Agent Service analyzes the query and user context
4. Agent Service generates a response with suggested routes and content
5. Structure Service creates a new route based on the agent's suggestion
6. Build Service triggers a Gatsby rebuild to include the new route
7. User is redirected to the new route when the build completes

```graphql
mutation ProcessUserQuery($query: String!, $context: UserContextInput) {
  processUserQuery(query: $query, context: $context) {
    id
    suggestedRoute {
      id
      path
      component
    }
    suggestedContent {
      id
      title
      path
    }
  }
}
```

### Structure Change Rebuild Flow

1. Structure Service detects a change to the documentation structure
2. Structure Service updates the structure via `updateStructure` mutation
3. Build Service is notified of the structure change
4. Build Service triggers a Gatsby rebuild via `triggerBuild` mutation
5. Build Service monitors the build status
6. When the build completes, the new structure is available to users

```graphql
mutation UpdateStructureAndBuild($input: StructureInput!) {
  updateStructure(input: $input) {
    id
    version
  }
  triggerBuild(input: { incremental: true }) {
    id
    status
  }
}
```

### Historical Exploration Flow

1. User requests to see the evolution of a concept
2. History Service retrieves the concept evolution via `conceptEvolution` query
3. UI displays the timeline of the concept's development
4. User selects a specific point in time
5. Content Service retrieves the content version via `contentVersion` query
6. UI displays the content as it existed at that point in time

```graphql
query ExploreConceptHistory($conceptId: ID!, $version: String) {
  conceptEvolution(conceptId: $conceptId) {
    stages {
      stage
      status
      startDate
    }
    timeline {
      timestamp
      type
      description
    }
  }
  contentVersion(id: $conceptId, version: $version) {
    data
    createdAt
    createdBy
  }
}
```

## Implementation Considerations

### Gatsby Integration

The Build Service needs to integrate with Gatsby's build process:

1. **Incremental Builds**: Use Gatsby's incremental build feature to rebuild only affected pages
2. **Dynamic Page Creation**: Generate pages dynamically based on routes in the Structure Service
3. **Build Hooks**: Set up webhooks to trigger builds when structure changes
4. **Build Caching**: Cache build artifacts to speed up rebuilds

### Real-time Updates

The Subscription type enables real-time updates:

```graphql
type Subscription {
  contentUpdated(id: ID): Content
  structureUpdated: Structure
  buildStatusChanged: BuildStatus
  routeGenerated(queryId: ID!): Route
}
```

This allows the UI to update in real-time when:
- Content is updated
- Structure changes
- Build status changes
- New routes are generated

### JSX Generation

The schema includes support for JSX content:

```graphql
enum ContentType {
  MARKDOWN
  API
  CODE
  CONCEPT
  JSX
}
```

This enables the Agent Service to generate React components dynamically based on user queries, which can then be included in the documentation site.

### Caching Strategy

To optimize performance:

1. **Route Caching**: Cache commonly used routes
2. **Content Caching**: Cache content that doesn't change frequently
3. **Query Caching**: Cache results of common queries
4. **Build Caching**: Cache build artifacts to speed up rebuilds

### Security Considerations

The schema should be implemented with security in mind:

1. **Authentication**: Require authentication for mutations
2. **Authorization**: Implement role-based access control
3. **Rate Limiting**: Limit the number of requests per user
4. **Input Validation**: Validate all input to prevent injection attacks

## Next Steps

1. Implement the GraphQL schema in a Node.js service
2. Create the microservices for each component
3. Set up the Gatsby integration
4. Develop the agent service for processing user queries
5. Implement the UI for interacting with the services
