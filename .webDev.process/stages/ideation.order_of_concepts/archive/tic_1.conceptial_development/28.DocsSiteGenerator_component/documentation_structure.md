# Documentation Structure

This document outlines the standard documentation structure for packages in the Spicetime Architecture. Following this structure ensures that documentation is consistent across all packages and can be automatically processed by the DocsSiteGenerator.

## Package Documentation Structure

Each package should follow this documentation structure:

```
package-name/
├── docs/
│   ├── README.md             # Overview of the package
│   ├── usage/                # Usage guides
│   │   ├── getting-started.md
│   │   ├── advanced-usage.md
│   │   └── ...
│   ├── api/                  # API documentation (auto-generated)
│   │   ├── index.md
│   │   ├── classes/
│   │   ├── interfaces/
│   │   ├── functions/
│   │   └── ...
│   └── examples/             # Code examples
│       ├── basic-example.md
│       ├── advanced-example.md
│       └── ...
└── package.json              # Contains metadata for the package
```

## Documentation Files

### README.md

The README.md file provides an overview of the package. It should include:

- Package name and description
- Installation instructions
- Basic usage example
- Key features
- Links to more detailed documentation

Example:

```markdown
---
title: "Cat Types"
description: "TypeScript implementation of category theory concepts"
---

# Cat Types

A TypeScript implementation of category theory concepts for functional programming.

## Installation

```bash
npm install @future/cat_types
```

## Overview

The cat_types package provides a TypeScript implementation of category theory concepts for functional programming. It includes implementations of categories, functors, and monads, as well as utilities for working with these concepts in a type-safe way.

## Key Features

- **Categories**: Create and work with categories
- **Functors**: Map between categories
- **Monads**: Handle effects like optionality and errors
- **Lenses**: Focus on and modify parts of data structures
- **Type Safety**: Strong type safety for all operations

## Usage

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects
const stringObj = createCatObject('', 'String');
const numberObj = createCatObject(0, 'Number');

// Add objects to the category
category.addObject(stringObj);
category.addObject(numberObj);

// Create a morphism
const stringToNumber = new BaseMorphism(
  stringObj,
  numberObj,
  (s: string) => parseFloat(s)
);

// Add the morphism to the category
category.addMorphism(stringToNumber);

// Use the morphism
const result = stringToNumber.apply('42'); // 42
```

## Documentation

- [Usage Guides](./usage/): Detailed guides on how to use the package
- [API Reference](./api/): Complete API documentation
- [Examples](./examples/): Code examples showing how to use the package

## License

MIT
```

### Usage Guides

Usage guides provide detailed information on how to use the package. They should include:

- Step-by-step instructions
- Code examples
- Explanations of concepts
- Best practices

Example (getting-started.md):

```markdown
---
title: "Getting Started with Cat Types"
description: "Learn how to get started with the Cat Types package"
---

# Getting Started with Cat Types

This guide will help you get started with the Cat Types package.

## Installation

```bash
npm install @future/cat_types
```

## Basic Concepts

Cat Types implements several key concepts from category theory:

- **Categories**: Collections of objects and morphisms
- **Morphisms**: Transformations between objects
- **Functors**: Mappings between categories
- **Monads**: Special kinds of functors with additional structure

## Creating a Category

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects
const stringObj = createCatObject('', 'String');
const numberObj = createCatObject(0, 'Number');

// Add objects to the category
category.addObject(stringObj);
category.addObject(numberObj);

// Create a morphism
const stringToNumber = new BaseMorphism(
  stringObj,
  numberObj,
  (s: string) => parseFloat(s)
);

// Add the morphism to the category
category.addMorphism(stringToNumber);

// Use the morphism
const result = stringToNumber.apply('42'); // 42
```

## Next Steps

- [Working with Categories](./working-with-categories.md)
- [Working with Functors](./working-with-functors.md)
- [Working with Monads](./working-with-monads.md)
- [API Reference](../api/)
- [Examples](../examples/)
```

### Examples

Examples provide practical code examples showing how to use the package. They should include:

- Complete, working code examples
- Explanations of what the code does
- Use cases for the example

Example (basic-example.md):

```markdown
---
title: "Basic Example"
description: "A basic example of using the Cat Types package"
---

# Basic Example

This example demonstrates the basic usage of the Cat Types package.

## Creating a Category

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects
const stringObj = createCatObject('', 'String');
const numberObj = createCatObject(0, 'Number');
const booleanObj = createCatObject(false, 'Boolean');

// Add objects to the category
category.addObject(stringObj);
category.addObject(numberObj);
category.addObject(booleanObj);

// Create morphisms
const stringToNumber = new BaseMorphism(
  stringObj,
  numberObj,
  (s: string) => parseFloat(s)
);

const numberToBoolean = new BaseMorphism(
  numberObj,
  booleanObj,
  (n: number) => n > 0
);

// Add morphisms to the category
category.addMorphism(stringToNumber);
category.addMorphism(numberToBoolean);

// Compose morphisms
const stringToBoolean = category.compose(stringToNumber, numberToBoolean);

// Use the composed morphism
const result = stringToBoolean.apply('42'); // true
const result2 = stringToBoolean.apply('0'); // false
const result3 = stringToBoolean.apply('-10'); // false

console.log(result, result2, result3); // true false false
```

## Explanation

This example demonstrates:

1. Creating a category with three objects: String, Number, and Boolean
2. Creating morphisms between these objects
3. Composing morphisms to create a new morphism
4. Using the composed morphism to transform values

The `stringToBoolean` morphism is a composition of `stringToNumber` and `numberToBoolean`. It takes a string, converts it to a number, and then checks if the number is greater than 0.
```

## Frontmatter

All markdown files should include frontmatter with at least these fields:

```yaml
---
title: "Title of the Document"
description: "Brief description of the document"
---
```

Additional fields can be included as needed:

```yaml
---
title: "Title of the Document"
description: "Brief description of the document"
author: "Author Name"
date: "2023-01-01"
tags: ["tag1", "tag2"]
---
```

## API Documentation

API documentation is automatically generated from TypeScript code using TypeDoc. The documentation is generated in the `docs/api` directory and includes:

- Classes
- Interfaces
- Functions
- Types
- Variables

## Documentation Site Structure

The documentation site generated by the DocsSiteGenerator will have this structure:

```
/                           # Home page with overview of all packages
├── packages/               # List of all packages
│   ├── package-name/       # Package page
│   │   ├── usage/          # Usage guides for the package
│   │   │   ├── getting-started/
│   │   │   └── ...
│   │   ├── api/            # API documentation for the package
│   │   │   ├── classes/
│   │   │   ├── interfaces/
│   │   │   └── ...
│   │   └── examples/       # Examples for the package
│   │       ├── basic-example/
│   │       └── ...
│   └── ...
├── search/                 # Search page
└── about/                  # About page
```

## Navigation

The documentation site will include:

1. **Top Navigation**: Links to home, packages, search, and about pages
2. **Side Navigation**: Package-specific navigation with links to usage guides, API documentation, and examples
3. **Breadcrumbs**: Show the current location in the documentation hierarchy

## Search

The documentation site will include a search function that allows users to search across all documentation. The search will include:

1. Package names and descriptions
2. Usage guide titles and content
3. API documentation
4. Example titles and content

## Conclusion

Following this documentation structure ensures that documentation is consistent across all packages and can be automatically processed by the DocsSiteGenerator. This makes it easier for developers to find and use the documentation they need.
