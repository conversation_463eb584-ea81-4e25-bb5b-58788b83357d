# Time Structure: Origin Concept as Summary of Past, Branch Clocks

## Overview

This concept explores how time is structured in the SpiceTime architecture, introducing the ideas of time layers, origin concepts as summaries of past concepts, and branch-specific clocks. This model extends beyond version control to provide a general framework for understanding time in the system.

## Key Insights

1. **Time Layers**: The system organizes time into distinct layers, each representing a snapshot of understanding at a particular point.
   - Each layer summarizes previous concepts
   - Conflicts between concepts are resolved in favor of later concepts
   - The result is deposited as "Concept 0" - the origin concept

2. **Past/Present/Future Structure**: The main branch is restructured to reflect temporal relationships:
   - `main/past`: Contains historical concepts and implementations
   - `main/present`: Contains the current summarized concepts and implementations
   - `main/future`: Contains proposed changes and future directions

3. **Branch Clocks**: Each branch has its own clock that doesn't necessarily tick at every tick of the parent branch.
   - Branch names reflect the branching tree structure
   - Each branch has its own time structure (past/present/future)
   - Branches are synchronized at specific points, not continuously

4. **Proposals as Future Versions**: New proposals are created by generating a future version in a branch.
   - This allows exploration of ideas without disrupting the present
   - Accepted proposals can be merged into the present
   - Rejected proposals remain as historical artifacts

## Implementation Approach

1. **Repository Structure**: Restructure the repository to reflect the temporal model:
   ```
   /main
     /past      # Historical concepts and implementations
     /present   # Current summarized concepts and implementations
     /future    # Proposed changes and future directions
   /branches
     /feature-x
       /past
       /present
       /future
     /feature-y
       /past
       /present
       /future
   ```

2. **Concept Summarization**: Create a process for summarizing concepts from the past into the origin concept:
   - Analyze all past concepts
   - Resolve conflicts in favor of later concepts
   - Generate a comprehensive origin concept that captures the current understanding
   - Place this in the present directory

3. **Branch Synchronization**: Define mechanisms for synchronizing branches:
   - Branches tick according to their own internal clock
   - Synchronization points are defined where branches align
   - Changes can flow between branches at these synchronization points

4. **Time Visualization**: Create tools to visualize the temporal structure:
   - Show the relationships between branches
   - Highlight synchronization points
   - Display the evolution of concepts over time

## Universal Time Model

This git branch time model can be extended to time in general:

1. **Relative Time**: Time is relative to the observer (branch)
2. **Discrete Ticks**: Time advances in discrete ticks, not continuously
3. **Synchronization Points**: Different time streams synchronize at specific points
4. **Causal Relationships**: Changes in one time stream can affect others, but only at synchronization points

This model will be essential when designing `time.rfr`, providing a foundation for understanding temporal relationships in the system.

## Benefits

1. **Historical Context**: Maintains a clear record of how ideas evolved
2. **Parallel Exploration**: Allows multiple directions to be explored simultaneously
3. **Conflict Resolution**: Provides a structured approach to resolving conflicts
4. **Temporal Reasoning**: Enables reasoning about temporal relationships in the system

## Relationship to Other Concepts

This concept builds on the "Restructuring Design from Ideation Concepts" (Concept 19) and provides a foundation for the "Categorical Structure of ST Bootstrap" (Concept 21).
