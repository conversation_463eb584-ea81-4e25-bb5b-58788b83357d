/**
 * Structure Merger Implementation
 * 
 * This module implements the structure merger for the SpiceTime Architecture.
 * It provides functions for merging structures, extracting substructures, and updating substructures.
 */

/**
 * Deep merge two objects
 * 
 * @param target - Target object
 * @param source - Source object
 * @returns Merged object
 */
export function deepMerge<T extends Record<string, any>, U extends Record<string, any>>(
  target: T,
  source: U
): T & U {
  // Create a new object with target properties
  const output = { ...target } as T & U;
  
  // If both target and source are objects
  if (isObject(target) && isObject(source)) {
    // Iterate over source properties
    Object.keys(source).forEach(key => {
      // If the property is an object
      if (isObject(source[key])) {
        // If the property doesn't exist in target, copy it
        if (!(key in target)) {
          output[key] = source[key];
        } else {
          // Otherwise, recursively merge
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        // For non-object properties, copy from source
        output[key] = source[key];
      }
    });
  }
  
  return output;
}

/**
 * Check if a value is an object
 * 
 * @param item - Value to check
 * @returns True if the value is an object
 */
export function isObject(item: any): item is Record<string, any> {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

/**
 * Merge multiple structures
 * 
 * @param structures - Structures to merge
 * @returns Merged structure
 */
export function mergeStructures<T extends Record<string, any>>(
  ...structures: T[]
): T {
  // Start with an empty object
  let result = {} as T;
  
  // Merge each structure
  for (const structure of structures) {
    result = deepMerge(result, structure);
  }
  
  return result;
}

/**
 * Merge structures with conflict resolution
 * 
 * @param structures - Structures to merge
 * @param resolver - Conflict resolver function
 * @returns Merged structure
 */
export function mergeStructuresWithResolver<T extends Record<string, any>>(
  structures: T[],
  resolver: (key: string, targetValue: any, sourceValue: any) => any
): T {
  // Start with an empty object
  let result = {} as T;
  
  // Merge each structure
  for (const structure of structures) {
    result = deepMergeWithResolver(result, structure, resolver);
  }
  
  return result;
}

/**
 * Deep merge two objects with conflict resolution
 * 
 * @param target - Target object
 * @param source - Source object
 * @param resolver - Conflict resolver function
 * @returns Merged object
 */
export function deepMergeWithResolver<T extends Record<string, any>, U extends Record<string, any>>(
  target: T,
  source: U,
  resolver: (key: string, targetValue: any, sourceValue: any) => any
): T & U {
  // Create a new object with target properties
  const output = { ...target } as T & U;
  
  // If both target and source are objects
  if (isObject(target) && isObject(source)) {
    // Iterate over source properties
    Object.keys(source).forEach(key => {
      // If the property is an object
      if (isObject(source[key])) {
        // If the property doesn't exist in target, copy it
        if (!(key in target)) {
          output[key] = source[key];
        } else {
          // If both properties are objects, recursively merge
          if (isObject(target[key])) {
            output[key] = deepMergeWithResolver(target[key], source[key], resolver);
          } else {
            // Otherwise, use resolver
            output[key] = resolver(key, target[key], source[key]);
          }
        }
      } else {
        // If the property exists in target and is different
        if (key in target && target[key] !== source[key]) {
          // Use resolver
          output[key] = resolver(key, target[key], source[key]);
        } else {
          // Otherwise, copy from source
          output[key] = source[key];
        }
      }
    });
  }
  
  return output;
}

/**
 * Apply a partial structure to a base structure
 * 
 * @param base - Base structure
 * @param partial - Partial structure
 * @returns Updated structure
 */
export function applyPartial<T extends Record<string, any>>(
  base: T,
  partial: Partial<T>
): T {
  return deepMerge(base, partial as T);
}

/**
 * Extract a substructure from a structure
 * 
 * @param structure - Structure to extract from
 * @param path - Path to extract
 * @returns Extracted substructure
 */
export function extractSubstructure<T extends Record<string, any>>(
  structure: T,
  path: string[]
): any {
  // Start at root
  let current: any = structure;
  
  // Navigate to path
  for (const part of path) {
    // If path doesn't exist, return null
    if (!current[part]) {
      return null;
    }
    
    // Move to next level
    current = current[part];
  }
  
  return current;
}

/**
 * Update a substructure in a structure
 * 
 * @param structure - Structure to update
 * @param path - Path to update
 * @param value - New value
 * @returns Updated structure
 */
export function updateSubstructure<T extends Record<string, any>>(
  structure: T,
  path: string[],
  value: any
): T {
  // Create a copy of the structure
  const result = { ...structure };
  
  // Start at root
  let current = result;
  
  // Navigate to path
  for (let i = 0; i < path.length - 1; i++) {
    const part = path[i];
    
    // Create path if it doesn't exist
    if (!current[part]) {
      current[part] = {};
    }
    
    // Move to next level
    current = current[part];
  }
  
  // Update value at final path
  const finalPart = path[path.length - 1];
  current[finalPart] = value;
  
  return result;
}

/**
 * Delete a substructure from a structure
 * 
 * @param structure - Structure to update
 * @param path - Path to delete
 * @returns Updated structure
 */
export function deleteSubstructure<T extends Record<string, any>>(
  structure: T,
  path: string[]
): T {
  // Create a copy of the structure
  const result = { ...structure };
  
  // Start at root
  let current = result;
  
  // Navigate to path
  for (let i = 0; i < path.length - 1; i++) {
    const part = path[i];
    
    // If path doesn't exist, return original structure
    if (!current[part]) {
      return structure;
    }
    
    // Move to next level
    current = current[part];
  }
  
  // Delete value at final path
  const finalPart = path[path.length - 1];
  delete current[finalPart];
  
  return result;
}

/**
 * Check if a structure contains a path
 * 
 * @param structure - Structure to check
 * @param path - Path to check
 * @returns True if the structure contains the path
 */
export function hasPath<T extends Record<string, any>>(
  structure: T,
  path: string[]
): boolean {
  // Start at root
  let current: any = structure;
  
  // Navigate to path
  for (const part of path) {
    // If path doesn't exist, return false
    if (!current[part]) {
      return false;
    }
    
    // Move to next level
    current = current[part];
  }
  
  return true;
}

/**
 * Get all paths in a structure
 * 
 * @param structure - Structure to get paths from
 * @returns Array of paths
 */
export function getAllPaths<T extends Record<string, any>>(
  structure: T
): string[][] {
  const paths: string[][] = [];
  
  // Recursive function to traverse structure
  function traverse(obj: any, path: string[] = []) {
    // Add current path
    if (path.length > 0) {
      paths.push([...path]);
    }
    
    // If object, traverse properties
    if (isObject(obj)) {
      for (const key of Object.keys(obj)) {
        traverse(obj[key], [...path, key]);
      }
    }
  }
  
  // Start traversal
  traverse(structure);
  
  return paths;
}

/**
 * Diff two structures
 * 
 * @param a - First structure
 * @param b - Second structure
 * @returns Difference object
 */
export function diffStructures<T extends Record<string, any>, U extends Record<string, any>>(
  a: T,
  b: U
): {
  added: string[][];
  removed: string[][];
  changed: Array<{ path: string[]; oldValue: any; newValue: any }>;
} {
  const added: string[][] = [];
  const removed: string[][] = [];
  const changed: Array<{ path: string[]; oldValue: any; newValue: any }> = [];
  
  // Get all paths
  const pathsA = getAllPaths(a);
  const pathsB = getAllPaths(b);
  
  // Find added paths
  for (const path of pathsB) {
    if (!hasPath(a, path)) {
      added.push(path);
    }
  }
  
  // Find removed paths
  for (const path of pathsA) {
    if (!hasPath(b, path)) {
      removed.push(path);
    }
  }
  
  // Find changed paths
  for (const path of pathsA) {
    if (hasPath(b, path)) {
      const valueA = extractSubstructure(a, path);
      const valueB = extractSubstructure(b, path);
      
      // If values are different and not objects
      if (!isObject(valueA) && !isObject(valueB) && valueA !== valueB) {
        changed.push({
          path,
          oldValue: valueA,
          newValue: valueB
        });
      }
    }
  }
  
  return { added, removed, changed };
}

export default {
  deepMerge,
  isObject,
  mergeStructures,
  mergeStructuresWithResolver,
  deepMergeWithResolver,
  applyPartial,
  extractSubstructure,
  updateSubstructure,
  deleteSubstructure,
  hasPath,
  getAllPaths,
  diffStructures
};
