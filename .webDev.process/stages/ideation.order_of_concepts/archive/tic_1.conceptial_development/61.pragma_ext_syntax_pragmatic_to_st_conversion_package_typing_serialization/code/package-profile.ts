/**
 * Package Profile Implementation
 * 
 * This module implements package profiles for the SpiceTime Architecture.
 * It provides functions for creating, serializing, and applying package profiles.
 */

import * as fs from 'fs';
import * as path from 'path';
import * as glob from 'glob';

/**
 * Package profile interface
 */
export interface PackageProfile {
  name: string;
  version: string;
  exports?: Record<string, string | ExportDefinition>;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  peerDependencies?: Record<string, string>;
  types?: Record<string, TypeDefinition>;
  structure?: Record<string, StructureDefinition>;
  __root?: boolean;
}

/**
 * Export definition interface
 */
export interface ExportDefinition {
  path: string;
  description?: string;
}

/**
 * Type definition interface
 */
export interface TypeDefinition {
  path: string;
  description?: string;
}

/**
 * Structure definition interface
 */
export interface StructureDefinition {
  type: 'directory' | 'file';
  contentType?: string;
  children?: Record<string, StructureDefinition>;
}

/**
 * Project interface
 */
export interface Project {
  name: string;
  version: string;
  packages: Record<string, Package>;
}

/**
 * Package interface
 */
export interface Package {
  name: string;
  version: string;
  profile: PackageProfile;
}

/**
 * Serialize a package profile to JSON
 * 
 * @param profile - Package profile to serialize
 * @returns Serialized JSON
 */
export function serializePackageProfile(profile: PackageProfile): string {
  return JSON.stringify(profile, null, 2);
}

/**
 * Deserialize JSON to a package profile
 * 
 * @param json - JSON to deserialize
 * @returns Deserialized package profile
 */
export function deserializePackageProfile(json: string): PackageProfile {
  return JSON.parse(json);
}

/**
 * Hydrate a project from filesystem to memory
 * 
 * @param directory - Directory to hydrate from
 * @returns Hydrated project
 */
export function hydrate(directory: string): Project {
  // Find all package profiles
  const profiles = findPackageProfiles(directory);
  
  // Build project structure
  const project: Project = {
    name: '',
    version: '',
    packages: {}
  };
  
  // Process each profile
  for (const profile of profiles) {
    // Extract package name and version
    const { name, version } = profile;
    
    // Add to project
    project.packages[name] = {
      name,
      version,
      profile
    };
    
    // If root package, set project name and version
    if (profile.__root) {
      project.name = name;
      project.version = version;
    }
  }
  
  return project;
}

/**
 * Rehydrate a project from memory to filesystem
 * 
 * @param project - Project to rehydrate
 * @param directory - Directory to rehydrate to
 */
export function rehydrate(project: Project, directory: string): void {
  // Create directory if it doesn't exist
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }
  
  // Process each package
  for (const packageName in project.packages) {
    const pkg = project.packages[packageName];
    
    // Get package directory
    const packageDir = path.join(directory, packageName);
    
    // Create package directory if it doesn't exist
    if (!fs.existsSync(packageDir)) {
      fs.mkdirSync(packageDir, { recursive: true });
    }
    
    // Write package profile
    const profilePath = path.join(packageDir, 'index.ts');
    fs.writeFileSync(profilePath, generatePackageProfile(pkg.profile));
    
    // Generate package files
    generatePackageFiles(pkg.profile, packageDir);
  }
}

/**
 * Find all package profiles in a directory
 * 
 * @param directory - Directory to search
 * @returns Found package profiles
 */
function findPackageProfiles(directory: string): PackageProfile[] {
  // Find all index.ts files
  const indexFiles = glob.sync(`${directory}/**/index.ts`);
  
  // Extract package profiles
  const profiles: PackageProfile[] = [];
  
  for (const indexFile of indexFiles) {
    // Read file
    const content = fs.readFileSync(indexFile, 'utf8');
    
    // Check if it contains a package profile
    if (content.includes('__package')) {
      // Extract package profile
      const profile = extractPackageProfile(content);
      
      // Add to profiles
      profiles.push(profile);
    }
  }
  
  return profiles;
}

/**
 * Extract package profile from file content
 * 
 * @param content - File content
 * @returns Extracted package profile
 */
function extractPackageProfile(content: string): PackageProfile {
  // In a real implementation, this would parse the file and extract the package profile
  // For now, we'll just return a mock profile
  return {
    name: 'ui-components',
    version: '1.0.0',
    exports: {}
  };
}

/**
 * Generate package profile code
 * 
 * @param profile - Package profile
 * @returns Generated code
 */
function generatePackageProfile(profile: PackageProfile): string {
  // In a real implementation, this would generate code for the package profile
  // For now, we'll just return a mock string
  return `export const __package = ${JSON.stringify(profile, null, 2)};`;
}

/**
 * Generate package files
 * 
 * @param profile - Package profile
 * @param directory - Directory to generate files in
 */
function generatePackageFiles(profile: PackageProfile, directory: string): void {
  // In a real implementation, this would generate files based on the package profile
  // For now, we'll just log a message
  console.log(`Generating files for ${profile.name} in ${directory}`);
}

/**
 * Apply a package profile to a component
 * 
 * @param component - Component to apply profile to
 * @param profile - Profile to apply
 * @returns Enhanced component
 */
export function applyProfile(component: any, profile: any): any {
  // Apply behavior
  const withBehavior = profile.behavior
    ? applyBehavior(component, profile.behavior)
    : component;
  
  // Apply styles
  const withStyles = profile.styles
    ? applyStyles(withBehavior, profile.styles)
    : withBehavior;
  
  // Apply other properties
  return Object.entries(profile)
    .filter(([key]) => !['behavior', 'styles'].includes(key))
    .reduce((comp, [key, value]) => {
      if (typeof value === 'object' && value !== null) {
        // Recursively apply nested profiles
        return { ...comp, [key]: applyProfile(comp[key] || {}, value) };
      }
      return comp;
    }, withStyles);
}

/**
 * Apply behavior to a component
 * 
 * @param component - Component to apply behavior to
 * @param behavior - Behavior to apply
 * @returns Enhanced component
 */
function applyBehavior(component: any, behavior: Record<string, any>): any {
  // In a real implementation, this would enhance the component with behavior
  // For now, we'll just attach metadata
  return {
    ...component,
    __behavior: behavior
  };
}

/**
 * Apply styles to a component
 * 
 * @param component - Component to apply styles to
 * @param styles - Styles to apply
 * @returns Enhanced component
 */
function applyStyles(component: any, styles: Record<string, any>): any {
  // In a real implementation, this would enhance the component with styles
  // For now, we'll just attach metadata
  return {
    ...component,
    __styles: styles
  };
}

/**
 * Merge package profiles
 * 
 * @param base - Base profile
 * @param extension - Extension profile
 * @returns Merged profile
 */
export function mergeProfiles(base: PackageProfile, extension: Partial<PackageProfile>): PackageProfile {
  return deepMerge(base, extension as PackageProfile);
}

/**
 * Deep merge objects
 * 
 * @param target - Target object
 * @param source - Source object
 * @returns Merged object
 */
export function deepMerge<T extends Record<string, any>, U extends Record<string, any>>(
  target: T,
  source: U
): T & U {
  const output = { ...target } as T & U;
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          output[key] = source[key];
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        output[key] = source[key];
      }
    });
  }
  
  return output;
}

/**
 * Check if value is an object
 * 
 * @param item - Value to check
 * @returns True if the value is an object
 */
export function isObject(item: any): item is Record<string, any> {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

export default {
  serializePackageProfile,
  deserializePackageProfile,
  hydrate,
  rehydrate,
  applyProfile,
  mergeProfiles,
  deepMerge,
  isObject
};
