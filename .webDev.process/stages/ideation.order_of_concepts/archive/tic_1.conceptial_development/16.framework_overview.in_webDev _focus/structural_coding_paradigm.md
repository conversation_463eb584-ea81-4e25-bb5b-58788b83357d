# Structural Coding Paradigm

## Filepath as Code
- File paths become literal JavaScript statements
- Most paths represent constant declarations of functions/scripts
- The structure itself is executable code
- Conditional logic can be embedded in the file structure

## React as Runtime Environment
- Each node becomes a React component (a package type)
- Components react to state/props at runtime
- The structure itself evolves dynamically during execution
- Process functors extend the Node.js process object

## Evolution vs. Destruction
- Start with existing structures rather than replacing them
- Respect the past while building the future
- Make incremental changes in parallel processes
- Guide evolution through value-based loss functions

## Meta-Programming Tools
- Builder views to manage structural programming
- Tools that extend the process to code in the names of views
- A recursive system of extensions that builds upon itself
- The ultimate building view directs the process of process extensions

## Homoiconic Approach
- Code is data and data is code
- The filesystem itself becomes part of this homoiconicity
- Structure and behavior are unified
- The naming conventions are executable
