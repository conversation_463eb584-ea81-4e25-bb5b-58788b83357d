# Package Profile JSON Template

This document provides a template and explanation for the `package.profile.json` file, which serves as the source of truth for package configuration in our system.

## Template Structure

```json
{
  "name": "example-package",
  "version": "1.0.0",
  "type": "component",
  "description": "Example package demonstrating the profile structure",
  
  "structure": {
    "folders": [
      {
        "name": "src",
        "alias": "@src",
        "structure": [
          {
            "name": "components",
            "alias": "@components"
          },
          {
            "name": "hooks",
            "alias": "@hooks"
          },
          {
            "name": "utils",
            "alias": "@utils"
          }
        ]
      },
      {
        "name": "tests",
        "alias": "@tests"
      },
      {
        "name": "docs",
        "alias": "@docs"
      }
    ],
    "files": [
      {
        "name": "index.ts",
        "template": "default-export"
      },
      {
        "name": "README.md",
        "template": "component-readme"
      }
    ]
  },
  
  "dependencies": {
    "shared": ["@shared/utils", "@shared/types"],
    "external": {
      "production": ["react", "react-dom"],
      "development": ["typescript", "vite"]
    }
  },
  
  "capabilities": ["ui", "state-management"],
  
  "metadata": {
    "owner": "frontend-team",
    "domain": "user-interface",
    "stage": "development"
  }
}
```

## Field Explanations

### Basic Information
- `name`: Package identifier
- `version`: Current version
- `type`: Package type (component, service, utility, etc.)
- `description`: Brief description of the package

### Structure
- `folders`: Directory structure with aliases for path mapping
- `files`: Key files with optional templates
- `alias`: Path aliases used for TypeScript path mapping and imports

### Dependencies
- `shared`: Internal dependencies from the monorepo
- `external`: External dependencies from npm, separated by environment

### Capabilities
- List of functionalities this package provides

### Metadata
- Additional information for organization and management

## Usage

This profile is maintained by the `package_profiles` utility and serves as the single source of truth for:

1. Directory structure generation
2. TypeScript path configuration
3. Package.json dependency management
4. WebDev process stage configuration

The profile is intentionally agnostic of WebDev stages, as stage-specific configurations are derived from this base profile by the WebDev process.