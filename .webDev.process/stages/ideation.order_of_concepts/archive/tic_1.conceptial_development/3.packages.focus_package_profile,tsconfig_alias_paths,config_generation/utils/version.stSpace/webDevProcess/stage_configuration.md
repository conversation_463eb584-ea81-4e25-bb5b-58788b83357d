# WebDev Process Stage Configuration

This document explains how the WebDev process consumes package profiles to generate stage-specific configurations.

## Stage Definition

The WebDev process defines standard stages for package development:

```json
{
  "stages": {
    "ideation": {
      "directory": "meta/ideation",
      "artifacts": ["specs", "diagrams"],
      "tools": ["conceptMapper"]
    },
    "design": {
      "directory": "meta/design",
      "artifacts": ["mockups", "prototypes"],
      "tools": ["figma"]
    },
    "development": {
      "directory": "packages",
      "artifacts": ["source", "tests"],
      "tools": ["typescript", "vite"]
    },
    "testing": {
      "directory": "packages",
      "artifacts": ["tests", "coverage"],
      "tools": ["vitest", "playwright"]
    },
    "production": {
      "directory": "dist",
      "artifacts": ["bundles", "types"],
      "tools": ["vite", "typescript"]
    }
  }
}
```

## Stage-Specific Profile Generation

For each package profile, the WebDev process generates stage-specific configurations:

```typescript
function generateStageConfig(packageProfile, stage) {
  // Base configuration from the stage definition
  const stageConfig = { ...stages[stage] };
  
  // Extend with package-specific information
  return {
    ...stageConfig,
    package: {
      name: packageProfile.name,
      type: packageProfile.type,
      structure: adaptStructureForStage(packageProfile.structure, stage),
      dependencies: filterDependenciesForStage(packageProfile.dependencies, stage)
    },
    // Stage-specific configurations
    config: generateConfigForStage(packageProfile, stage)
  };
}
```

## Example: Development Stage Configuration

For a component package in the development stage, the WebDev process might generate:

```json
{
  "stage": "development",
  "directory": "packages/example-component",
  "artifacts": ["source", "tests"],
  "tools": ["typescript", "vite"],
  "package": {
    "name": "example-component",
    "type": "component",
    "structure": {
      "folders": [
        {
          "name": "src",
          "alias": "@src",
          "structure": [...]
        },
        {
          "name": "tests",
          "alias": "@tests"
        }
      ]
    }
  },
  "config": {
    "typescript": {
      "compilerOptions": {
        "baseUrl": ".",
        "paths": {
          "@src/*": ["src/*"],
          "@components/*": ["src/components/*"],
          "@hooks/*": ["src/hooks/*"],
          "@utils/*": ["src/utils/*"],
          "@tests/*": ["tests/*"]
        }
      }
    },
    "vite": {
      "build": {
        "outDir": "../../dist/example-component"
      }
    }
  }
}
```

## Workflow

1. The WebDev process loads the package profile from `package.profile.json`
2. It identifies the current stage of the package
3. It generates stage-specific configuration by combining:
    - Standard stage definition
    - Package-specific profile information
    - Tool-specific configurations from the Config package
4. The resulting configuration is used to:
    - Generate or update TypeScript configurations
    - Configure build tools
    - Set up testing environments
    - Prepare for the next stage transition

This approach ensures consistency across packages while allowing for stage-specific optimizations.