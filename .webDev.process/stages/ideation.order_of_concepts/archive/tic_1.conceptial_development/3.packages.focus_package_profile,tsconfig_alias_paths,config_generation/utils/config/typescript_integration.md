# TypeScript Configuration Integration

## TypeScript Config Schema

```typescript
// TypeScript config manager
interface TsConfigManager {
  // Create a base tsconfig
  createBase(options?: Partial<TsConfigOptions>): TsConfig;
  
  // Extend a tsconfig
  extend(base: TsConfig, overrides?: Partial<TsConfigOptions>): TsConfig;
  
  // Resolve a tsconfig with all its extends
  resolveExtends(configPath: string): Promise<TsConfig>;
  
  // Generate path mappings
  generatePaths(baseUrl: string, packages: string[]): Record<string, string[]>;
}

// Simplified tsconfig interface
interface TsConfig {
  extends?: string | string[];
  compilerOptions?: {
    target?: string;
    module?: string;
    outDir?: string;
    rootDir?: string;
    baseUrl?: string;
    paths?: Record<string, string[]>;
    [key: string]: any;
  };
  include?: string[];
  exclude?: string[];
  references?: Array<{path: string}>;
  [key: string]: any;
}
```

## Usage Examples

1. **Creating Extended Configs**

```typescript
// Create a base config
const baseConfig = tsConfigManager.createBase({
  target: "es2020",
  module: "esnext"
});

// Extend for a specific package
const packageConfig = tsConfigManager.extend(baseConfig, {
  outDir: "./dist",
  rootDir: "./src"
});
```

2. **Resolving Config Inheritance**

```typescript
// Resolve a config with all its extends
const resolvedConfig = await tsConfigManager.resolveExtends("./tsconfig.json");
```

3. **Working with Path Mappings**

```typescript
// Generate path mappings for monorepo
const paths = tsConfigManager.generatePaths("./", ["packages/*"]);
```