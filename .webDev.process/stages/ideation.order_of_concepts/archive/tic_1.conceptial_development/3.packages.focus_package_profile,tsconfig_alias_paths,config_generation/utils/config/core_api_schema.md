# Config Utility Core Schema

## Minimal Interface

```typescript
// Core configuration interface
interface ConfigManager {
  // Read configuration
  read<T = any>(path: string): Promise<T>;
  
  // Write configuration
  write<T = any>(path: string, data: T): Promise<void>;
  
  // Merge configurations
  merge<T = any>(base: T, override: T, options?: MergeOptions): T;
  
  // Resolve configuration inheritance
  resolve<T = any>(path: string): Promise<T>;
  
  // Convert between formats
  convert(data: any, sourceFormat: string, targetFormat: string): any;
}

// Merge options
interface MergeOptions {
  strategy?: 'deep' | 'shallow' | 'replace';
  customMergers?: Record<string, (base: any, override: any) => any>;
  arrayStrategy?: 'concat' | 'replace' | 'merge';
}

// Config resolver options
interface ResolveOptions {
  cache?: boolean;
  maxDepth?: number;
  baseDir?: string;
}
```

## Extension Points

1. **Format Handlers**
    - Register handlers for different file formats (JSON, YAML, etc.)
    - Pluggable architecture for custom formats

2. **Merge Strategies**
    - Custom merge strategies for specific config types
    - Special handling for arrays, functions, and complex objects

3. **Resolution Hooks**
    - Pre/post resolution hooks for validation and transformation
    - Environment-specific resolution (dev, prod, test)