# Package Profile System Architecture

## Overview

The Package Profile System provides a structured approach to managing package configurations across different development stages. This document outlines the core components and their interactions.

## Core Components

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Package        │────►│  WebDev         │────►│  Config         │
│  Profiles       │     │  Process        │     │  Package        │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  profile.json   │     │  stage configs  │     │  functionality  │
│  (source of     │     │  (stage-based)  │     │  configurations │
│   truth)        │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │  TypeScript     │
                                               │  Build System   │
                                               │                 │
                                               └─────────────────┘
```

### 1. Package Profiles

- **Source of truth** for package configuration
- Maintained by `package_profiles` utility
- Defines a single profile per package, agnostic of WebDev stages
- Stored in `package.profile.json` files

### 2. WebDev Process

- Has profiles for each stage of a typical package
- Orchestrates the transition between development stages
- Uses package profiles as a base for stage-specific configurations
- Manages the lifecycle of package development

### 3. Config Package

- Contains configurations for all functionalities
- These functionalities correspond to WebDev process stages
- Doesn't know about WebDev process or package profiles directly
- Provides reusable configuration templates

### 4. TypeScript Configuration

- TSConfigs and path mappings are generated from package profiles
- The build stage extends TSConfigs with paths supplied by package_profiles
- Orchestrated by WebDev process

## Workflow

1. Package profiles define structure and metadata
2. WebDev process consumes profiles to generate stage-specific configurations
3. Config package provides functionality-specific configuration templates
4. TypeScript build system consumes the generated configurations

This separation of concerns allows for flexibility while maintaining consistency across packages.