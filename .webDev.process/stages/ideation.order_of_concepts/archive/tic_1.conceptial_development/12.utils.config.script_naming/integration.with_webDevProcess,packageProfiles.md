# Naming Convention Integration with Package Profiles

This document explains how the Naming Convention system integrates with Package Profiles and the WebDev process.

## Integration Points

### 1. Package Profile Extension

Package profiles can specify naming conventions:

```yaml
# component.profile.yaml
name: ReactComponent
version: 1.0.0
type: component
extends: BaseComponent

# Naming convention reference
namingConventions: dualTypesWithTests

structure:
  folders:
    - name: src
      alias: @src
      structure:
        - name: components
          alias: @components
    - name: tests
      alias: @tests
    - name: types
      alias: @types
```

Alternatively, inline naming convention definition:

```yaml
# component.profile.yaml
namingConventions:
  atomicPatterns:
    - camelCase
    - PascalCase
  structurePatterns:
    - mirror-test
    - types-directory
```

### 2. WebDev Process Integration

The WebDev process applies naming conventions during stage transitions:

```typescript
async function transitionToDevStage(packageProfile: PackageProfile): Promise<void> {
  // Get naming conventions from profile
  const namingProfile = await namingManager.getProfile(
    packageProfile.namingConventions || 'default'
  );
  
  // Generate structure based on naming conventions
  await structureBuilder.buildWithNamingConventions(
    packageProfile,
    namingProfile,
    './packages/my-package'
  );
  
  // Generate and apply config patches
  const patches = namingManager.generatePatches(
    namingProfile,
    './packages/my-package'
  );
  await configManager.applyPatches(patches, './packages/my-package');
}
```

### 3. Config Utility Integration

The Config utility applies naming convention patches:

```typescript
class ConfigManager {
  // Existing methods...
  
  async applyPatches(patches: ConfigPatch[], targetDir: string): Promise<void> {
    for (const patch of patches) {
      const configPath = this.getConfigPath(patch.tool, targetDir);
      
      // Read existing config or create default
      const existingConfig = await this.read(configPath).catch(() => ({}));
      
      // Apply patch
      const updatedConfig = this.merge(existingConfig, patch.patch);
      
      // Write updated config
      await this.write(configPath, updatedConfig);
    }
  }
  
  private getConfigPath(tool: string, targetDir: string): string {
    switch (tool) {
      case 'typescript':
        return path.join(targetDir, 'tsconfig.json');
      case 'jest':
        return path.join(targetDir, 'jest.config.js');
      case 'eslint':
        return path.join(targetDir, '.eslintrc.js');
      default:
        throw new Error(`Unknown tool: ${tool}`);
    }
  }
}
```

## Workflow Example

1. Developer creates a new package with a specific naming convention:

```typescript
await packageProfileManager.generatePackage({
  name: "data-processor",
  version: "1.0.0",
  profile: "ReactComponent",
  targetDir: "./packages/data-processor",
  options: {
    namingConventions: "dualTypesWithTests"
  }
});
```

2. The package profile manager:
    - Loads the ReactComponent profile
    - Retrieves the dualTypesWithTests naming convention
    - Generates the package structure according to both profiles
    - Applies configuration patches from the naming convention

3. The resulting package has:
    - A structure that follows the ReactComponent profile
    - TypeScript path mappings for src, tests, and types
    - Jest configuration for testing the mirrored structure
    - ESLint rules that enforce the naming conventions

4. When the developer runs validation:

```typescript
const validationResult = await packageProfileManager.validatePackage(
  "./packages/data-processor",
  "ReactComponent"
);

if (!validationResult.valid) {
  console.log("Validation issues:", validationResult.issues);
}
```

The validation checks if:
- Each source file has a corresponding test file
- Each source file has a type definition (either adjacent or in types directory)
- All files follow the naming conventions

## Benefits

This integration provides several benefits:

1. **Consistency**: Ensures consistent naming and structure across packages
2. **Automation**: Automatically generates appropriate configurations
3. **Validation**: Provides tools to validate compliance with conventions
4. **Flexibility**: Allows for different conventions for different package types
5. **Integration**: Works seamlessly with existing Package Profiles and WebDev process