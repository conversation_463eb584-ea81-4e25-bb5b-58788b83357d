name: dualTypesWithTests
description: "Dual type definition with mirrored test structure"

atomicPatterns:
- camelCase
- PascalCase
- typeSuffix

structurePatterns:
- mirror-test
- adjacent-types
- types-directory

configPatches:
- tool: typescript
  patch:
  compilerOptions:
  baseUrl: "."
  paths:
  "@types/*": ["types/*"]
  "@src/*": ["src/*"]
  "@tests/*": ["tests/*"]

- tool: jest
  patch:
  moduleNameMapper:
  "^@src/(.*)$": "<rootDir>/src/$1"
  "^@types/(.*)$": "<rootDir>/types/$1"
  "^@tests/(.*)$": "<rootDir>/tests/$1"
  testMatch:
  - "<rootDir>/tests/**/*.ts"

- tool: eslint
  patch:
  rules:
  "@typescript-eslint/naming-convention": [
  "error",
  {
  "selector": "default",
  "format": ["camelCase"]
  },
  {
  "selector": "typeLike",
  "format": ["PascalCase"]
  },
  {
  "selector": "variable",
  "format": ["camelCase", "PascalCase", "UPPER_CASE"]
  }
  ]

validationRules:
- name: "Test file existence"
  rule: "For each src/**/*.ts, tests/**/*.ts must exist"
  severity: "warning"

- name: "Type definition"
  rule: "For each src/**/*.ts, either src/**/*.type.ts or types/**/*.type.ts must exist"
  severity: "warning"