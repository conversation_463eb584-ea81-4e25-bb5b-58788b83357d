# React Integration Details

## Component-Level Scope Access

React components can access scope terms without explicit imports:

```jsx
// UserProfile.tsx
function UserProfile() {
  // Access scope terms directly
  const userData = db.getUserData(currentUser.id);
  
  logger.info(`Rendering profile for ${currentUser.name}`);
  
  return (
    <div className={config.theme === 'dark' ? 'dark-mode' : 'light-mode'}>
      <h1>{currentUser.name}'s Profile</h1>
      {/* Component implementation */}
    </div>
  );
}
```

## Custom Hooks

The system provides custom hooks for more explicit scope access:

```jsx
import { useScope, useDynamicScope } from '@scope-typing/react';

function FeatureComponent() {
  // Get specific scope terms
  const { logger, config } = useScope(['logger', 'config']);
  
  // Get dynamic scope that might change during component lifecycle
  const currentUser = useDynamicScope('$currentUser');
  
  // Use scope terms
  logger.debug('Rendering FeatureComponent');
  
  return config.features.newUI ? <NewUI user={currentUser} /> : <LegacyUI user={currentUser} />;
}
```

## Scope Provider Configuration

The ScopeProvider component can be configured with different strategies:

```jsx
<ScopeProvider 
  config={scopeConfig}
  strategy="hierarchical" // or "flat", "hybrid"
  dynamicResolution="render" // or "mount", "memo"
  devTools={process.env.NODE_ENV === 'development'}
>
  <App />
</ScopeProvider>
```

## Development Tools Integration

The React integration includes development tools:

1. **React DevTools Extension**: Inspect scope terms in React DevTools
2. **Scope Debugger**: Visualize scope inheritance and resolution
3. **Performance Monitoring**: Track scope access patterns and optimize
4. **Hot Reloading**: Preserve scope state during hot module replacement