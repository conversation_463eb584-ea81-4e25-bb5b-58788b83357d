# TypeScript Path Aliases as Unified Scope Builder

## Overview

This document describes how TypeScript path aliases can unify separate stage-based tree structures into a single coherent scope system, eliminating the need for multiple stage-specific tsconfig files.

## The Problem: Fragmented Stage Structures

Previously, our architecture required:
- Separate tree structures for different stages (test, design, ideation, implementation)
- Stage-specific tsconfig files with `baseUrl` pointing to each stage's tree base
- Complex import patterns when referencing across stages
- Maintaining multiple configuration files as the project evolved

For example:
- Test specs in `meta/stages/test/`
- Design documents in `meta/design.stage/`
- Ideation concepts in `meta/ideation.stage/`

Each requiring its own tsconfig with different base URLs and import patterns.

## The Solution: Unified Scope Builder

Using TypeScript path aliases, we can unify all stages under a single tsconfig:

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@meta.test/*": ["meta/stages/test/*"],
      "@meta.design/*": ["meta/design.stage/*"],
      "@meta.ideation/*": ["meta/ideation.stage/*"],
      "@meta.impl/*": ["meta/impl.stage/*"]
    }
  }
}
```

## Key Benefits

1. **Single Configuration**: One tsconfig serves as the scope builder for all stages
2. **Unified Namespace**: All stages accessible through a consistent pattern
3. **Cross-Stage References**: Easily reference content across different stages
4. **Simplified Maintenance**: Update one configuration file instead of many
5. **Explicit Stage Context**: Imports clearly indicate which stage they're from

## Usage Examples

```typescript
// Import from test stage
import { runTest } from "@meta.test/utils/runner";

// Import from design stage
import { ComponentSpec } from "@meta.design/components/button";

// Import from ideation stage
import { Concept } from "@meta.ideation/concepts/categorical_types";

// Cross-stage reference
import { TestCase } from "@meta.test/cases";
import { DesignSpec } from "@meta.design/specs";
```

## Implementation Strategy

1. Create a single root tsconfig.json with path mappings for all stages
2. Remove stage-specific tsconfig files or have them extend the root config
3. Update imports to use the new namespace pattern
4. Ensure IDE support by configuring it to use the root tsconfig

## Integration with Project Structure

This approach transforms our filesystem-based stage separation into a logical namespace separation while maintaining the physical structure:

```
meta/
├── stages/
│   └── test/           <- @meta.test/*
├── design.stage/       <- @meta.design/*
├── ideation.stage/     <- @meta.ideation/*
└── impl.stage/         <- @meta.impl/*
```

The physical separation of stages is preserved, but logically unified through the TypeScript module resolution system.