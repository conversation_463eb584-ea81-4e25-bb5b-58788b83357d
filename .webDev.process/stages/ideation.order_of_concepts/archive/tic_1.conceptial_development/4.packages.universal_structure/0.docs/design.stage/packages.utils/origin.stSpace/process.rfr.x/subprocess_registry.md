# Subprocess Registry

## Overview

The Subprocess Registry provides a mechanism for subprocesses to register with parent processes in the `process.rfr.x` system. This registry enables parent processes to track, manage, and communicate with their subprocesses efficiently.

## Core Concepts

- **Parent-Child Process Relationship**: Processes can spawn subprocesses that inherit context from their parent
- **Registration**: Subprocesses must register with their parent to establish the relationship
- **Lifecycle Management**: The registry tracks subprocess creation and termination
- **Domain Organization**: Subprocesses can be organized and queried by domain

## Implementation

### Subprocess Registry Interface

```typescript
/** Registry for subprocesses to check in with parent process */
export interface SubprocessRegistry {
  /** All registered subprocesses */
  subprocesses: Map<string, Process>;
  
  /** Register a subprocess with the parent */
  register(subprocess: Process): void;
  
  /** Unregister a subprocess */
  unregister(subprocessId: string): void;
  
  /** Get a subprocess by ID */
  getSubprocess(subprocessId: string): Process | undefined;
  
  /** Get all subprocesses */
  getAllSubprocesses(): Process[];
  
  /** Get subprocesses by domain */
  getSubprocessesByDomain(domain: string): Process[];
}
```

### Default Implementation

```typescript
/** Implementation of subprocess registry */
export class ProcessSubprocessRegistry implements SubprocessRegistry {
  subprocesses = new Map<string, Process>();
  
  constructor(private parentProcess: Process) {}
  
  register(subprocess: Process): void {
    this.subprocesses.set(subprocess.id, subprocess);
    // Notify parent of new subprocess via messageHub
    this.parentProcess.messageHub.publish('subprocess:registered', subprocess);
  }
  
  unregister(subprocessId: string): void {
    const subprocess = this.subprocesses.get(subprocessId);
    if (subprocess) {
      this.subprocesses.delete(subprocessId);
      // Notify parent of removed subprocess
      this.parentProcess.messageHub.publish('subprocess:unregistered', subprocess);
    }
  }
  
  getSubprocess(subprocessId: string): Process | undefined {
    return this.subprocesses.get(subprocessId);
  }
  
  getAllSubprocesses(): Process[] {
    return Array.from(this.subprocesses.values());
  }
  
  getSubprocessesByDomain(domain: string): Process[] {
    return this.getAllSubprocesses().filter(
      process => process.domain === domain
    );
  }
}
```

## Integration with Process Creation

The subprocess registry is integrated into the process creation functor:

```typescript
function createProcess<T extends ProcessConfig>(config: T): Process<T> {
  // ... other initialization code ...
  
  const process: Process<T> = {
    // ... other properties ...
    
    // Subprocess registry
    subprocessRegistry: new ProcessSubprocessRegistry(process),
    
    // Process methods for subprocess management
    createSubprocess: function(name, subConfig) {
      const subprocess = createSubprocess({
        name,
        parentProcess: this
      }, subConfig);
      
      // Register the subprocess
      this.subprocessRegistry.register(subprocess);
      
      return subprocess;
    },
    
    getSubprocesses: function() {
      return this.subprocessRegistry.getAllSubprocesses();
    },
    
    getSubprocess: function(id) {
      return this.subprocessRegistry.getSubprocess(id);
    }
  };
  
  return process;
}
```

## Reference Frame Considerations

Subprocesses inherit the reference frame of their parent process by default, but can be positioned relative to the parent through rotation and translation:

```typescript
function createSubprocess(baseConfig, customConfig) {
  // Inherit reference frame from parent
  const referenceFrame = deriveReferenceFrame(
    baseConfig.parentProcess.referenceFrame,
    baseConfig.rotationAxis || [0, 0, 1],  // Default z-axis rotation
    baseConfig.rotationAngle || 0,
    baseConfig.translation || [0, 0, 0]
  );
  
  // Create process with derived reference frame
  return createProcess({
    ...customConfig,
    referenceFrame,
    parent: baseConfig.parentProcess.id
  });
}
```

## Event Communication

Subprocesses communicate with their parent process through the message hub:

1. **Registration Events**: When a subprocess registers or unregisters
2. **State Change Events**: When a subprocess changes state
3. **Completion Events**: When a subprocess completes its execution

```typescript
// Example of subprocess state change notification
subprocess.messageHub.publish('state:changed', {
  processId: subprocess.id,
  previousState: previousState,
  currentState: subprocess.state
});

// Parent process can subscribe to subprocess events
parentProcess.messageHub.subscribe('subprocess:*', (event, data) => {
  console.log(`Subprocess event: ${event}`, data);
});
```

## Usage Examples

### Creating and Managing Subprocesses

```typescript
// Create a parent process
const parentProcess = createProcess({
  name: "MainProcess",
  domain: "project"
});

// Create a subprocess
const subprocess = parentProcess.createSubprocess("ChildProcess", {
  domain: "task"
});

// Get all subprocesses
const allSubprocesses = parentProcess.getSubprocesses();

// Get subprocesses by domain
const taskSubprocesses = parentProcess.subprocessRegistry.getSubprocessesByDomain("task");
```

### Specialized Subprocess Types

Different types of subprocesses can be created for specific purposes:

1. **Configuration Subprocesses**: Manage configuration inheritance
2. **Resource Subprocesses**: Handle resource allocation and management
3. **Task Subprocesses**: Execute specific tasks within the parent process
4. **Monitor Subprocesses**: Observe and report on process metrics

## Future Considerations

- **Hierarchical Subprocess Trees**: Support for deeper nesting of subprocesses
- **Cross-Process Communication**: Enhanced patterns for subprocess collaboration
- **Subprocess Migration**: Moving subprocesses between parent processes
- **Subprocess Templates**: Reusable subprocess configurations
