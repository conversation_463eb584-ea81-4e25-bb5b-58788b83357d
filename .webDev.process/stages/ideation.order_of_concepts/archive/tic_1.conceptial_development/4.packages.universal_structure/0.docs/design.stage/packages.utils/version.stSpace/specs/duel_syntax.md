# Dual Syntax Specification

## Overview

The dual syntax system allows developers to seamlessly mix TypeScript and pragmatic syntax within the same file, sharing scope and variables between them. This creates a unified script where each syntax can be used where it's most natural.

## Key Concepts

1. **Shared Scope**: Variables defined in either syntax are available in the other
2. **Complementary Strengths**: Use each syntax where it's most expressive
3. **Single Flow**: The code executes as a single sequence regardless of syntax used

## Example of Shared Scope

```typescript
// Start with TypeScript
function processUsers(rawData) {
  // Complex filtering in TypeScript
  const activeUsers = rawData.filter(u => 
    u.status === 'active' && 
    u.lastLogin > Date.now() - (30 * 24 * 60 * 60 * 1000)
  );
  
  /* @pragma
  // Now use pragmatic syntax, accessing the activeUsers variable
  transform activeUsers to include
    activity score based on login frequency
    risk level based on permission access
  */
  
  // Back to TypeScript, using variables potentially modified by pragma
  return activeUsers.map(user => ({
    ...user,
    displayName: `${user.firstName} ${user.lastName}`
  }));
}
```

## Syntax Characteristics

### Pragmatic Syntax
- Natural language-like commands
- Excellent for data transformations and pipelines
- More concise for certain operations
- Can access any variable in scope

### TypeScript Syntax
- Full type safety and tooling support
- Better for complex logic and algorithms
- Familiar syntax for most developers
- Creates variables that pragmatic syntax can use

## File Extensions

- `.st.ts`: TypeScript is primary with pragmatic blocks in comments
- `.ts.st`: Pragmatic syntax is primary with TypeScript blocks in comments

## Comment Markers

- `/* @pragma ... */` - Marks pragmatic syntax in TypeScript files
- `/* @typescript ... */` - Marks TypeScript syntax in pragmatic files

## Compilation Process

The compiler:
1. Parses the entire file as a single script
2. Identifies syntax blocks and their types
3. Ensures variables are properly shared between syntax types
4. Generates a unified JavaScript output
5. Maintains a single execution flow

This approach allows developers to use the most appropriate syntax for each part of their code while maintaining a cohesive program with shared state.

## Implementation Details

### Comment Markers

- `/* @pragma ... */` - Marks pragmatic syntax in TypeScript files
- `/* @typescript ... */` - Marks TypeScript syntax in pragmatic files

### Pragma Processor

```typescript
function processDualSyntax(sourceCode: string): string {
  // Parse source code
  const ast = parseCode(sourceCode);
  
  // Extract syntax blocks
  const { mainSyntax, commentSyntax } = extractSyntaxBlocks(ast);
  
  // Merge syntaxes
  const unifiedAst = mergeSyntaxes(mainSyntax, commentSyntax);
  
  // Generate output
  return generateCode(unifiedAst);
}
```

### Build Integration

The dual syntax processor integrates with the build pipeline:

1. Runs before TypeScript compilation
2. Processes all files with dual syntax
3. Outputs unified TypeScript files
4. Passes to standard TypeScript compiler

## Usage Examples

### Component Definition

```typescript
// @pragma-mode: dual

// Direct scope access (pragmatic)
function UserProfile() {
  const { user, auth } = context;
  
  /* @typescript
  // Traditional props and hooks (TypeScript)
  function UserProfile(props: UserProfileProps) {
    const { user } = useUser();
    const { isLoggedIn } = useAuth();
  */
  
  function updateUsername(newName) {
    context.user.name = newName;
  }
  
  return (
    <div>
      <h2>{user.name}</h2>
      <button onClick={() => updateUsername("Jane Doe")}>
        Change Name
      </button>
    </div>
  );
}
```

### Unified Output

```typescript
// Generated unified code
function UserProfile(props?: UserProfileProps) {
  // Combined approach
  const { user, auth } = context;
  const { isLoggedIn } = auth;
  
  function updateUsername(newName) {
    context.user.name = newName;
  }
  
  return (
    <div>
      <h2>{user.name}</h2>
      <button onClick={() => updateUsername("Jane Doe")}>
        Change Name
      </button>
    </div>
  );
}
```