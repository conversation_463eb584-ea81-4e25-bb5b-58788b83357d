# WebDev Process Root Components API

This document defines the core React components and types for the root of the webDev process system.

## Core Components

### `ProcessWorkspace`

The top-level container for the entire webDev process workspace.

```typescript
interface ProcessWorkspaceProps {
  /** Unique identifier for this workspace */
  id: string;
  /** Display name for the workspace */
  name: string;
  /** Current active process in the workspace */
  activeProcessId?: string;
  /** List of all processes in this workspace */
  processes: Process[];
  /** Pipeline configuration for workspace-level transformations */
  pipeline?: PipelineConfig;
  /** Handler for when a process is selected */
  onProcessSelect?: (processId: string) => void;
  /** Handler for creating a new process */
  onProcessCreate?: (processTemplate?: ProcessTemplate) => void;
  /** Custom class name */
  className?: string;
}

/**
 * ProcessWorkspace is the top-level container for all webDev processes.
 * It manages the collection of processes and provides navigation between them.
 */
const ProcessWorkspace: React.FC<ProcessWorkspaceProps> = ({
  id,
  name,
  processes,
  activeProcessId,
  pipeline,
  onProcessSelect,
  onProcessCreate,
  className,
}) => {
  // Implementation details
};
```

### `ProcessNavigator`

Provides navigation between different processes in the workspace.

```typescript
interface ProcessNavigatorProps {
  /** List of all processes */
  processes: Process[];
  /** Currently active process ID */
  activeProcessId?: string;
  /** Handler for when a process is selected */
  onProcessSelect?: (processId: string) => void;
  /** Handler for creating a new process */
  onProcessCreate?: (processTemplate?: ProcessTemplate) => void;
  /** Display mode for the navigator */
  displayMode?: 'compact' | 'expanded' | 'detailed';
  /** Custom class name */
  className?: string;
}

/**
 * ProcessNavigator provides a UI for navigating between different processes
 * in the workspace, with options for creating new processes.
 */
const ProcessNavigator: React.FC<ProcessNavigatorProps> = ({
  processes,
  activeProcessId,
  onProcessSelect,
  onProcessCreate,
  displayMode = 'expanded',
  className,
}) => {
  // Implementation details
};
```

### `ProcessView`

Displays and manages a single process with its stages.

```typescript
interface ProcessViewProps {
  /** The process to display */
  process: Process;
  /** Whether this process is currently active */
  isActive?: boolean;
  /** Pipeline configuration for process-level transformations */
  pipeline?: PipelineConfig;
  /** Handler for stage activation */
  onStageActivate?: (stageId: string) => void;
  /** Handler for process status change */
  onStatusChange?: (status: ProcessStatus) => void;
  /** Custom class name */
  className?: string;
}

/**
 * ProcessView displays a single process with all its stages and provides
 * interactions for managing the process lifecycle.
 */
const ProcessView: React.FC<ProcessViewProps> = ({
  process,
  isActive = false,
  pipeline,
  onStageActivate,
  onStatusChange,
  className,
}) => {
  // Implementation details
};
```

### `ProcessStageList`

Displays the list of stages in a process.

```typescript
interface ProcessStageListProps {
  /** Stages to display */
  stages: ProcessStage[];
  /** ID of the currently active stage */
  activeStageId?: string;
  /** Handler for stage activation */
  onStageActivate?: (stageId: string) => void;
  /** Display mode for the stages */
  displayMode?: 'linear' | 'network' | 'kanban';
  /** Custom class name */
  className?: string;
}

/**
 * ProcessStageList displays all stages in a process with their relationships
 * and provides navigation between stages.
 */
const ProcessStageList: React.FC<ProcessStageListProps> = ({
  stages,
  activeStageId,
  onStageActivate,
  displayMode = 'linear',
  className,
}) => {
  // Implementation details
};
```

### `PipeMiddleware`

Provides pipeline processing capabilities for data transformations.

```typescript
interface PipeMiddlewareProps {
  /** Path or sentence to select the pipe function */
  select: string;
  /** Children to be processed through the pipe */
  children: React.ReactNode;
  /** Pipeline configuration */
  config?: PipelineConfig;
  /** Handler for pipe execution events */
  onPipeEvent?: (event: PipeEvent) => void;
}

/**
 * PipeMiddleware provides a way to process data through transformation pipelines.
 * It can be used to transform data at various levels of the component hierarchy.
 */
const PipeMiddleware: React.FC<PipeMiddlewareProps> = ({
  select,
  children,
  config,
  onPipeEvent,
}) => {
  // Implementation details
};
```

## Core Types

```typescript
/** Status of a process or stage */
type ProcessStatus = 'draft' | 'active' | 'paused' | 'completed' | 'archived';

/** A template for creating new processes */
interface ProcessTemplate {
  id: string;
  name: string;
  description: string;
  stages: ProcessStageTemplate[];
}

/** The main process entity */
interface Process {
  id: string;
  name: string;
  description: string;
  status: ProcessStatus;
  stages: ProcessStage[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, unknown>;
}

/** A stage within a process */
interface ProcessStage {
  id: string;
  name: string;
  description: string;
  status: ProcessStatus;
  order: number;
  dependencies: string[]; // IDs of stages this depends on
  artifacts: ProcessArtifact[];
  metadata: Record<string, unknown>;
}

/** A template for creating process stages */
interface ProcessStageTemplate {
  id: string;
  name: string;
  description: string;
  defaultOrder: number;
  defaultDependencies: string[];
  artifactTypes: string[];
}

/** An artifact produced during a process stage */
interface ProcessArtifact {
  id: string;
  name: string;
  type: string;
  url: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, unknown>;
}

/** Pipeline configuration for data transformations */
interface PipelineConfig {
  /** Pipeline stages in execution order */
  stages: PipeStage[];
  /** Monitoring configuration */
  monitoring?: {
    /** Called when a step completes */
    onStepComplete?: (stepId: string, duration: number) => void;
    /** Called when the pipeline completes */
    onPipelineComplete?: (duration: number) => void;
    /** Called when an error occurs */
    onError?: (error: Error) => void;
  };
}

/** A stage in a pipeline */
interface PipeStage {
  /** Unique identifier for this stage */
  id: string;
  /** Priority of this stage (higher numbers execute first) */
  priority?: number;
  /** Optional condition function to determine if this stage should execute */
  condition?: (context: any) => boolean;
  /** The processing function for this stage */
  process: (context: any, next: () => Promise<any>) => Promise<any>;
}

/** Pipe morphism for transforming data */
interface PipeMorphism<T, U> {
  /** Transform input data */
  (input: T): Promise<U>;
  /** Metadata about the transformation */
  metadata?: {
    /** Whether this transformation preserves structure */
    preserves?: boolean;
    /** Whether this transformation is invertible */
    invertible?: boolean;
    /** Additional properties */
    [key: string]: any;
  };
}

/** Event emitted during pipe execution */
interface PipeEvent {
  /** Unique identifier for this event */
  id: string;
  /** Type of event */
  type: 'function.enter' | 'function.exit' | 'function.error' | 'data.transform' | 'function.skip';
  /** Timestamp when the event occurred */
  timestamp: number;
  /** Context information */
  context: {
    /** Name of the function */
    functionName: string;
    /** Arguments passed to the function */
    args: unknown[];
    /** Result of the function (if available) */
    result?: unknown;
    /** Error that occurred (if any) */
    error?: Error;
    /** Duration of execution in milliseconds */
    duration?: number;
  };
  /** Additional metadata */
  meta?: Record<string, unknown>;
}
```

## Pipe Utilities

```typescript
/**
 * Creates a pipeline of transformation functions
 */
const pipe = <T>(...fns: Array<(arg: T) => T>) =>
  (value: T): T =>
    fns.reduce((acc, fn) => fn(acc), value);

/**
 * Creates a pipe morphism with metadata
 */
const createPipeMorphism = <T, U>(
  transformFn: (input: T) => Promise<U>,
  metadata?: {
    preserves?: boolean;
    invertible?: boolean;
    [key: string]: any;
  }
): PipeMorphism<T, U> => {
  const pipeFn = async (input: T): Promise<U> => {
    return transformFn(input);
  };
  
  pipeFn.metadata = metadata;
  return pipeFn;
};

/**
 * Creates a project management pipeline
 */
const createProjectPipeline = <T>(
  scheme: 'agile' | 'kanban',
  orgTheme: any
): PipeMorphism<T, T> => {
  return createPipeMorphism(
    async (input: T) => {
      // Pipeline implementation based on scheme
      const pipeline = {
        agile: createAgilePipeline(orgTheme),
        kanban: createKanbanPipeline(orgTheme)
      }[scheme];

      return pipeline.run(input);
    },
    {
      preserves: true,
      invertible: false
    }
  );
};
```

## Component Relationships

- `ProcessWorkspace` contains one or more `Process` entities
- `ProcessNavigator` provides navigation between `Process` entities
- `ProcessView` displays a single `Process` with its `ProcessStage` entities
- `ProcessStageList` displays the `ProcessStage` entities for a `Process`
- `PipeMiddleware` can be used at any level to transform data through pipelines

## Usage Example

```tsx
// Basic workspace setup
<ProcessWorkspace
  id="main-workspace"
  name="Web Development Projects"
  processes={processes}
  activeProcessId={activeProcessId}
  pipeline={workspacePipeline}
  onProcessSelect={handleProcessSelect}
  onProcessCreate={handleProcessCreate}
>
  <ProcessNavigator
    processes={processes}
    activeProcessId={activeProcessId}
    onProcessSelect={handleProcessSelect}
    onProcessCreate={handleProcessCreate}
    displayMode="expanded"
  />
  
  {activeProcess && (
    <ProcessView
      process={activeProcess}
      isActive={true}
      pipeline={processPipeline}
      onStageActivate={handleStageActivate}
      onStatusChange={handleProcessStatusChange}
    />
  )}
</ProcessWorkspace>

// Using pipe middleware
<PipeMiddleware select="hocs/auth/middleware">
  <PipeMiddleware select="in TopNav find all buttons with auth">
    <Button onClick={handleAction} />
  </PipeMiddleware>
</PipeMiddleware>

// Creating a custom pipeline
const myPipeline: PipelineConfig = {
  stages: [
    {
      id: 'validation',
      priority: 100,
      process: async (context, next) => {
        // Validate input
        if (!context.input) throw new Error('Invalid input');
        return next();
      }
    },
    {
      id: 'transformation',
      priority: 50,
      condition: (context) => context.shouldTransform,
      process: async (context, next) => {
        context.transformed = true;
        return next();
      }
    },
    {
      id: 'enrichment',
      priority: 25,
      process: async (context, next) => {
        // Add additional data
        context.enriched = true;
        return next();
      }
    }
  ],
  monitoring: {
    onStepComplete: (stepId, duration) => 
      console.log(`Step ${stepId} completed in ${duration}ms`),
    onPipelineComplete: (duration) => 
      console.log(`Pipeline completed in ${duration}ms`),
    onError: (error) => 
      console.error('Pipeline error:', error)
  }
};
```