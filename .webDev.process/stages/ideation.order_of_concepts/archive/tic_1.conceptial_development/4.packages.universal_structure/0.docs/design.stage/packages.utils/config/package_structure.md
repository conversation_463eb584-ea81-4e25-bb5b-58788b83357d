# Config Utility Package Structure

## Overview

The Config utility package provides a standardized approach to configuration management across the repository. This document outlines the structure and organization of the package.

## Directory Structure

```
utils/config/
├── package.json       # Package metadata
├── tsconfig.json      # TypeScript configuration
├── lib/               # Main code
│   ├── index.ts       # Main entry point
│   ├── api/           # Core API functions
│   │   ├── index.ts   # API exports
│   │   └── types.ts   # Type definitions
│   │
│   ├── converters/    # File format converters
│   │   ├── yaml.ts    # YAML converter
│   │   └── json.ts    # JSON converter
│   │
│   ├── resolvers/     # Config resolution logic
│   │   ├── index.ts   # Main resolver exports
│   │   ├── vite.ts    # Vite-specific resolver
│   │   └── babel.ts   # Babel-specific resolver
│   │
│   ├── composer/      # Configuration composition
│   │   ├── index.ts   # Composer exports
│   │   └── factory.ts # Factory functions
│   │
│   └── utils/         # Utility functions
│       ├── deep-merge.ts  # Deep merge implementation
│       └── parent-finder.ts # Parent config finder
│
├── templates/         # Reference templates
│   ├── vite.js        # Vite config template
│   ├── babel.js       # Babel config template
│   └── tsconfig.json  # TypeScript config template
│
└── docs/              # Documentation
    ├── README.md      # Overview documentation
    ├── api.md         # API documentation
    ├── examples.md    # Usage examples
    └── roadmap.md     # Development roadmap
```

## Core Components

1. **API Layer** (`lib/api/`)
    - Core functions for reading, writing, and merging configs
    - Type definitions for configuration objects

2. **Converters** (`lib/converters/`)
    - File format conversion utilities
    - Support for YAML, JSON, JS formats

3. **Resolvers** (`lib/resolvers/`)
    - Tool-specific configuration resolution logic
    - Special handling for tools like Vite, Babel, etc.

4. **Composers** (`lib/composer/`)
    - Configuration composition utilities
    - Factory functions for creating tool-specific configs

5. **Utilities** (`lib/utils/`)
    - Helper functions used across the package
    - Deep merge implementation
    - Parent config finder

## Key Functions

- `readConfig(filePath)` - Read configuration from file
- `writeConfig(filePath, config)` - Write configuration to file
- `mergeConfigs(configType, base, override)` - Merge configurations
- `composeConfigs(configType, configs)` - Compose multiple configurations
- `createConfigFactory(configType)` - Create a config factory

## Pre-defined Factories

- `createViteConfig(options)` - Create Vite configuration
- `createBabelConfig(options)` - Create Babel configuration
- `createTsConfig(options)` - Create TypeScript configuration
- `createEslintConfig(options)` - Create ESLint configuration