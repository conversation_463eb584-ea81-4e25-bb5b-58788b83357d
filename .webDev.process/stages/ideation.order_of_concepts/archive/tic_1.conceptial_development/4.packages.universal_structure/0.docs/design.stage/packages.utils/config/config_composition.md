# Configuration Composition

This document outlines our approach to configuration composition in the utils/config package.

## Overview

Modern JavaScript tooling often requires complex configuration files that need to be:
- Shared across projects
- Extended for specific use cases
- Composed from multiple sources
- Environment-aware

Our configuration composition system provides a unified API for handling these requirements across different tools, whether they natively support composition (like Vite) or not.

## Core Concepts

### 1. Configuration Merging

We support three levels of configuration merging:

- **Native Merging**: Using the tool's own merging function when available (e.g., Vite's `mergeConfig`)
- **Special Handling**: Custom merging logic for tools with special requirements (e.g., Babel's presets/plugins)
- **Deep Merging**: Our default approach for tools without native support

### 2. Configuration Factories

We provide factory functions that create tool-specific configuration composers:

```typescript
// Create a config factory for a specific tool
const eslintConfigFactory = createConfigFactory('eslint', defaultEslintConfig);

// Use the factory to create a composed config
const finalConfig = eslintConfigFactory({
  extensions: [
    require('./eslint-react'),
    require('./eslint-typescript')
  ],
  environment: 'production'
});
```

### 3. Composition Sources

Configurations can be composed from multiple sources:

- **Base Config**: The starting point for composition
- **Extensions**: Additional configurations to be merged
- **Parent Config**: A higher-level configuration to inherit from
- **Environment Overrides**: Environment-specific settings

## Usage Examples

### Basic Usage

```typescript
import { createConfigFactory } from '@utils/config';

// Create a webpack config composer
const webpackConfigFactory = createConfigFactory('webpack');

// Compose a configuration
const config = webpackConfigFactory({
  base: require('./webpack.base.js'),
  extensions: [
    require('./webpack.react.js'),
    { devtool: 'source-map' }
  ]
});
```

### Using Pre-defined Factories

```typescript
import { createViteConfig, createTsConfig } from '@utils/config';

// Vite configuration
const viteConfig = createViteConfig({
  extensions: [
    { plugins: [reactPlugin()] },
    { build: { outDir: 'dist' } }
  ],
  environment: process.env.NODE_ENV
});

// TypeScript configuration
const tsConfig = createTsConfig({
  extensions: [
    require('./tsconfig.paths.json'),
    { compilerOptions: { strict: true } }
  ]
});
```

### Environment-Specific Configuration

```typescript
const config = createConfigFactory('webpack')({
  base: {
    entry: './src/index.js',
    environments: {
      development: {
        devtool: 'eval-source-map',
        mode: 'development'
      },
      production: {
        optimization: { minimize: true },
        mode: 'production'
      }
    }
  },
  environment: process.env.NODE_ENV
});
```

## Extending the System

### Adding Native Mergers

To add support for a tool with native merging capabilities:

```typescript
// In api/index.ts
const nativeMergers = {
  // Existing mergers...
  webpack: (base, override) => {
    try {
      const { merge } = require('webpack-merge');
      return merge(base, override);
    } catch (e) {
      return deepMerge(base, override);
    }
  }
};
```

### Adding Special Handlers

For tools that need special merging logic:

```typescript
// In api/index.ts
const specialHandlers = {
  // Existing handlers...
  jest: (base, override) => {
    const result = deepMerge(base, override);
    
    // Special handling for Jest's transform and moduleNameMapper
    if (result.transform) {
      // Custom transform merging logic
    }
    
    return result;
  }
};
```

## Best Practices

1. **Prefer Composition Over Inheritance**: Use small, focused config extensions instead of deep inheritance chains
2. **Document Extensions**: Each config extension should be well-documented
3. **Keep Base Configs Minimal**: Base configs should only include essential settings
4. **Use Environment Variables Sparingly**: Only override what's necessary for each environment
5. **Test Composed Configs**: Verify that composed configurations work as expected