# Runtime Behavior Specification

## Overview

This document details how the pragma coordinates system behaves at runtime, including process instantiation, scope management, and transaction handling through spacetime.

## Dual Syntax System

SpiceTime implements a dual syntax approach that allows developers to work with both TypeScript and SpiceTime pragmatic syntax:

### File Extensions
- `.st.ts`: TypeScript is primary, SpiceTime pragmas in comments
- `.ts.st`: SpiceTime is primary, TypeScript in comments

### Compilation Process
- Both syntaxes get converted to plain JS/TS
- The result is fed to the TypeScript compiler
- Both share the same module environment with `globalThis` as scope

### Pragmatic Syntax
- Not exactly TypeScript syntax
- Evolves through versions
- Uses linguistic pipes to compose terms of scope
- Resembles natural speech patterns

The pragma sequence in the file determines how the syntax is interpreted, but both syntaxes operate within the same runtime environment.

## Process Lifecycle

### Instantiation

1. **Coordinate Resolution**:
    - System resolves the pragma coordinates to a position in type space
    - Determines the appropriate process type and implementation

2. **Scope Initialization**:
    - Creates the initial scope based on configuration
    - Injects public, protected, and private parts

3. **Timeline Creation**:
    - Establishes a timeline based on the specified type
    - Sets up event handlers and state transitions

### Execution

1. **Transaction Processing**:
    - System processes transactions that move the process through spacetime
    - Each transaction can update state and trigger events

2. **State Management**:
    - Maintains state according to scope visibility rules
    - Updates affected components when state changes

3. **Event Propagation**:
    - Propagates events through the component hierarchy
    - Triggers re-renders as needed

## Spacetime Transactions

### Transaction Structure

```typescript
interface Transaction {
  id: string;
  type: TransactionType;
  payload: any;
  timestamp: number;
  coordinates: [number, number, number];
  targetCoordinates?: [number, number, number];
}
```

### Transaction Processing

```typescript
function processTransaction(transaction: Transaction) {
  // Get current process state
  const currentState = getProcessState(transaction.coordinates);
  
  // Apply transaction
  const newState = applyTransaction(currentState, transaction);
  
  // Update process state
  updateProcessState(transaction.targetCoordinates || transaction.coordinates, newState);
  
  // Trigger events
  emitStateChangeEvents(transaction, currentState, newState);
}
```

## Scope Management at Runtime

### Global Scope Access

```javascript
// Access public parts directly
const theme = globalThis.theme;

// Access protected parts through scope
const _state = scope._state;

// Access private parts through privateParts
const internal = privateParts.internal;
```

### Component Scope Access

```javascript
function Button() {
  // Direct access to context
  const { primaryColor } = context.theme;
  
  // Update context
  function updateColor(color) {
    context.theme.primaryColor = color;
    // Triggers re-render automatically
  }
  
  return (
    <button 
      style={{ backgroundColor: primaryColor }}
      onClick={() => updateColor("#ff0000")}
    >
      Click Me
    </button>
  );
}
```

## Implementation Details

### Runtime Scope Injection

```javascript
// Generated at runtime for each module
(function injectScope(module) {
  // Get module path
  const modulePath = getModulePath(module);
  
  // Resolve scope configuration for this path
  const scopeConfig = resolveScopeConfig(modulePath);
  
  // Create context object
  const context = createContext(scopeConfig);
  
  // Inject public parts into globalThis
  Object.entries(context.public).forEach(([key, value]) => {
    Object.defineProperty(globalThis, key, {
      value,
      enumerable: true,
      configurable: false
    });
  });
  
  // Create protected scope
  const scope = Object.create(null);
  Object.entries(context.protected).forEach(([key, value]) => {
    Object.defineProperty(scope, key, {
      value,
      enumerable: false,
      configurable: false
    });
  });
  
  // Create private parts
  const privateParts = Object.create(null);
  Object.entries(context.private).forEach(([key, value]) => {
    Object.defineProperty(privateParts, key, {
      value,
      enumerable: false,
      configurable: false
    });
  });
  
  // Expose to module
  module.exports = { scope, privateParts };
})(module);
```

### Change Detection

```javascript
// Proxy-based change detection
function createReactiveContext(context) {
  return new Proxy(context, {
    set(target, property, value) {
      const oldValue = target[property];
      target[property] = value;
      
      // If value changed, trigger updates
      if (oldValue !== value) {
        triggerUpdates(property, oldValue, value);
      }
      
      return true;
    }
  });
}
```

## Example: Full Runtime Flow

```javascript
// 1. Process initialization with coordinates
// @pragma-process: component
// @pragma-coordinates: [0, 1, 0]

// 2. Scope injection (generated at runtime)
const context = {
  theme: { primaryColor: "#0066cc", secondaryColor: "#f5f5f5" },
  user: { name: "John", role: "admin" }
};

// 3. Component definition
function UserProfile() {
  // 4. Direct context access
  const { name, role } = context.user;
  
  // 5. Event handler that updates context
  function updateRole(newRole) {
    // 6. Context update
    context.user.role = newRole;
    // 7. Automatic re-render triggered
  }
  
  // 8. Render with context values
  return (
    <div>
      <h2>{name}</h2>
      <p>Role: {role}</p>
      <button onClick={() => updateRole("superadmin")}>
        Promote
      </button>
    </div>
  );
}
```