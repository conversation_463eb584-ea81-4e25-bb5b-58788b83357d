# TypeDoc Configuration Examples

## Basic Configuration

```javascript
const { createTypedocConfig } = require('@utils/config/docs/tools/typedoc/config');

const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api"
  }
});

// Write to file
const { writeConfig } = require('@utils/config');
writeConfig('./typedoc.json', typedocConfig);
```

## With Extensions

```javascript
const { createTypedocConfig } = require('@utils/config/docs/tools/typedoc/config');

const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api",
    excludePrivate: true
  },
  extensions: [
    // Add Markdown plugin
    {
      plugin: ["typedoc-plugin-markdown"],
      theme: "markdown"
    },
    // Add Mermaid diagrams
    {
      plugin: ["typedoc-plugin-mermaid"]
    },
    // Customize categories
    {
      categorizeByGroup: true,
      categoryOrder: ["Core", "API", "Models", "Utilities", "*"]
    }
  ]
});
```

## With Environment-Specific Settings

```javascript
const { createTypedocConfig } = require('@utils/config/docs/tools/typedoc/config');

const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api",
    environments: {
      development: {
        excludePrivate: false,
        excludeProtected: false
      },
      production: {
        excludePrivate: true,
        excludeProtected: true,
        excludeExternals: true
      }
    }
  },
  environment: process.env.NODE_ENV
});
```

## For Monorepo Packages

```javascript
const { createTypedocConfig } = require('@utils/config/docs/tools/typedoc/config');

const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: [
      "packages/core/src/index.ts",
      "packages/client/src/index.ts",
      "packages/server/src/index.ts"
    ],
    out: "docs/api",
    name: "Project Documentation"
  },
  extensions: [
    {
      plugin: ["typedoc-plugin-markdown"],
      readme: "README.md",
      includeVersion: true
    }
  ]
});
```