# Documentation Configuration Usage Guide

This guide demonstrates how to use the documentation configuration utilities to set up and generate documentation for your SpiceTime architecture packages.

## Quick Start

### Setting Up TypeDoc

1. Install required dependencies:

```bash
npm install --save-dev typedoc typedoc-plugin-markdown
```

2. Create a TypeDoc configuration:

```javascript
// typedoc.config.js
const { createTypedocConfig } = require('@utils/config/docs');

const config = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api"
  }
});

module.exports = config;
```

3. Add script to package.json:

```json
{
  "scripts": {
    "docs:generate": "typedoc --options ./typedoc.config.js"
  }
}
```

4. Generate documentation:

```bash
npm run docs:generate
```

## Advanced Usage

### Composing Multiple Documentation Tools

```javascript
// docs.config.js
const { createTypedocConfig } = require('@utils/config/docs');

// TypeDoc configuration
const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api"
  },
  extensions: [
    { plugin: ["typedoc-plugin-markdown", "typedoc-plugin-mermaid"] }
  ]
});

// Export all configurations
module.exports = {
  typedoc: typedocConfig,
  // Add other tool configurations as needed
};
```

### Environment-Specific Configurations

```javascript
// docs.config.js
const { createTypedocConfig } = require('@utils/config/docs');

const isDev = process.env.NODE_ENV === 'development';

const typedocConfig = createTypedocConfig({
  base: {
    entryPoints: ["src/index.ts"],
    out: "docs/api",
    excludePrivate: !isDev,
    excludeProtected: !isDev
  }
});

module.exports = typedocConfig;
```

### Monorepo Setup

For a monorepo with multiple packages:

```javascript
// docs.config.js
const { createTypedocConfig } = require('@utils/config/docs');
const fs = require('fs');
const path = require('path');

// Find all packages with a src/index.ts file
const packagesDir = path.join(__dirname, 'packages');
const packages = fs.readdirSync(packagesDir)
  .filter(pkg => {
    const indexPath = path.join(packagesDir, pkg, 'src/index.ts');
    return fs.existsSync(indexPath);
  });

// Create entry points for each package
const entryPoints = packages.map(pkg => 
  path.join('packages', pkg, 'src/index.ts')
);

// Create TypeDoc configuration
const typedocConfig = createTypedocConfig({
  base: {
    entryPoints,
    out: "docs/api",
    name: "Project API Documentation"
  }
});

module.exports = typedocConfig;
```

## Documentation Structure Best Practices

1. **Keep documentation close to code**
    - Place documentation files in the same repository as the code
    - Use relative links between documentation files

2. **Use consistent formatting**
    - Follow Markdown best practices
    - Use headings consistently (H1 for title, H2 for sections, etc.)
    - Include code examples with syntax highlighting

3. **Update documentation with code changes**
    - Add documentation updates to the same PR as code changes
    - Use CI to validate documentation links and formatting

4. **Generate API documentation automatically**
    - Use TypeDoc to generate API documentation from code comments
    - Keep manually written documentation focused on concepts and usage