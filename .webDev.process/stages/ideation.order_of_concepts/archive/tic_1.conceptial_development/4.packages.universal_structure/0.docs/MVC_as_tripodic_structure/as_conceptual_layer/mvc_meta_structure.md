# MVC Meta Structure

## Core Concept

The MVC pattern exists as a conceptual overlay within the meta structure, not as a physical organization in the working repository. This creates a "forest of trees" where:

1. The primary process stages (Ideation-Design-Production) form the main tree
2. MVC components form three parallel trees that mirror the main structure
3. Links between trees are maintained through markdown references, not filesystem symlinks

## Forest Structure

```
meta/
├── ideation.stage/          # Main process tree
│   ├── concepts/
│   │   └── feature-a/
│   └── ...
├── design.stage/
│   └── ...
├── production.stage/
│   └── ...
└── mvc/                     # MVC forest (parallel trees)
    ├── model/               # Model tree
    │   ├── ideation.links/  # Links to ideation stage
    │   │   └── feature-a.md # Contains markdown links
    │   ├── design.links/
    │   └── production.links/
    ├── view/                # View tree
    │   ├── ideation.links/
    │   ├── design.links/
    │   └── production.links/
    └── controller/          # Controller tree
        ├── ideation.links/
        ├── design.links/
        └── production.links/
```

## Link Structure

Links are maintained in markdown files, not as filesystem symlinks:

```markdown
# Model Links for Feature A

## Ideation Stage
- [Domain Model Concept](../../../ideation.stage/concepts/feature-a/domain-model.md)
- [Entity Relationships](../../../ideation.stage/concepts/feature-a/entity-relationships.md)

## Design Stage
- [Data Schema](../../../design.stage/specs/feature-a/data-schema.md)
- [API Contracts](../../../design.stage/specs/feature-a/api-contracts.md)
```