
# MVC Meta Implementation

## Link Management

The MVC meta structure uses markdown links rather than filesystem symlinks to:

1. **Avoid Git Issues**: Git doesn't handle symlinks consistently across platforms
2. **Enable Rich Metadata**: Markdown links can include context, descriptions, and tags
3. **Support Future Extensions**: Links can evolve to include universal hashes or IPFS addresses

## Example Link File

```markdown
# Controller Links for Authentication Flow

## Ideation Stage
- [User Flow](../../../ideation.stage/concepts/auth/user-flow.md) - Controls the authentication process
- [Decision Points](../../../ideation.stage/concepts/auth/decision-points.md) - Branch logic for auth flow

## Design Stage
- [State Machine](../../../design.stage/specs/auth/state-machine.md) - Auth state transitions
- [API Integration](../../../design.stage/specs/auth/api-integration.md) - Controller-API interface

## Production Stage
- [Auth Controller](../../../production.stage/src/controllers/auth.controller.ts) - Implementation
- [Middleware](../../../production.stage/src/middleware/auth.middleware.ts) - Request processing
```

## Universal Addressing

Future implementation will support:

1. **Content-Addressable Links**: Using hashes of file content
2. **IPFS Integration**: Distributed addressing for resilient linking
3. **Semantic Linking**: Linking by concept rather than just by file