# Visualization and Dashboard Tools

## Mobile-First Dashboards

### Portainer
- Docker Swarm visualization and management
- Mobile-responsive interface
- Service health monitoring
- Deployment via:
  ```yaml
  portainer:
    image: portainer/portainer-ce:latest
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    deploy:
      placement:
        constraints: [node.role == manager]
  ```

### Grafana Mobile
- Custom dashboards for agent activity
- Real-time metrics visualization
- Alert notifications on mobile
- Implementation:
  ```yaml
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
    environment:
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    deploy:
      placement:
        constraints: [node.role == manager]
  ```

## Domain Visualization

### Neo4j Graph Visualization
- Visual representation of domain relationships
- Agent capability mapping
- Interactive exploration of security zones
- Mobile-optimized graph views

### D3.js Custom Visualizations
- Real-time domain forest visualization
- Capability flow mapping
- Security zone boundary visualization
- Responsive design for mobile interfaces