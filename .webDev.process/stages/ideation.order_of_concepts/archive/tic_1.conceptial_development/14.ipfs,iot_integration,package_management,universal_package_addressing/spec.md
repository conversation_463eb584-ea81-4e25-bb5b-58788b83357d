# HashPM Specification

## 1. Core Concepts

### 1.1 Content Addressing

Packages are identified by a cryptographic hash of their normalized content:

- **Hash Algorithm**: BLAKE3 (chosen for speed and security)
- **Hash Format**: Base58-encoded hash prefixed with `hashpm://`
- **Content Normalization**: Deterministic serialization of package contents excluding volatile metadata
- **Subresource Integrity**: All dependencies are referenced by their content hashes

### 1.2 Logical-Physical Decoupling

- **Logical Names**: Human-readable identifiers used in source code
- **Physical Addresses**: Content hashes that determine storage location
- **Mapping Layer**: Bidirectional translation between names and hashes

### 1.3 Context-Specific Naming

- **Project Context**: Each project defines its own name-to-hash mappings
- **Organizational Context**: Organizations can maintain consistent mappings across projects
- **Global Context**: Optional global registry for common package names

## 2. System Architecture

### 2.1 Content Store

- **Local Cache**: Content-addressed storage of package contents
- **Deduplication**: Automatic sharing of identical content across packages
- **Integrity Verification**: Validation of content against declared hashes
- **Garbage Collection**: Removal of unused content based on reference tracking

### 2.2 Mapping Registry

- **Local Mappings**: Project-specific name-to-hash mappings in `.hashpm.json`
- **Organizational Mappings**: Shared mappings across multiple projects
- **Resolution Algorithm**: Cascading resolution from local to organizational to global contexts
- **Version Mapping**: Optional semantic version ranges mapped to specific content hashes

### 2.3 Compatibility Layer

- **NPM Compatibility**: Generation of compatible package.json with resolved dependencies
- **Yarn/PNPM Support**: Hooks for popular package managers
- **Build Tool Integration**: Plugins for webpack, vite, esbuild, etc.
- **IDE Support**: Extensions for VS Code, WebStorm, etc.

### 2.4 Distribution Network

- **Centralized Registry**: Optional centralized registry for discovery
- **P2P Distribution**: Direct peer-to-peer package sharing
- **Content Delivery**: Integration with existing CDNs
- **Private Registries**: Support for organization-specific private registries

## 3. Package Format

### 3.1 Package Structure

```
<package-hash>/
├── .hashpm.json       # HashPM metadata
├── package.json       # Standard package.json (for compatibility)
├── dist/              # Compiled code
├── src/               # Source code
└── types/             # TypeScript declarations
```

### 3.2 HashPM Metadata

```json
{
  "hash": "hashpm://QmZ9myDs5aZtSqNBcwxs7zNpx4LcEfSvTcPwcKLHR5wqSa",
  "name": "@example/package",
  "version": "1.0.0",
  "dependencies": {
    "react": "hashpm://QmT17Qm1QnxdtxL3WsxyVuURczuCTqWxNgz3Jy1oMYLV54"
  },
  "mappings": {
    "react": "hashpm://QmT17Qm1QnxdtxL3WsxyVuURczuCTqWxNgz3Jy1oMYLV54"
  }
}
```

## 4. Import Resolution

### 4.1 Source Code Imports

```typescript
// Standard import (resolved through mappings)
import { Component } from 'react';

// Direct hash import
import { Component } from 'hashpm://QmT17Qm1QnxdtxL3WsxyVuURczuCTqWxNgz3Jy1oMYLV54';

// Subpath import
import { useState } from 'react/hooks';
```

### 4.2 TypeScript Configuration

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "react": ["/.hashpm/QmT17Qm1QnxdtxL3WsxyVuURczuCTqWxNgz3Jy1oMYLV54"]
    },
    "plugins": [
      { "name": "hashpm-typescript-plugin" }
    ]
  }
}
```

### 4.3 Resolution Algorithm

1. Check if import is a direct hash reference
2. Look up in local project mappings
3. Check organizational mappings
4. Query global registry
5. Fall back to traditional resolution

## 5. Security Model

### 5.1 Trust Mechanisms

- **Content Verification**: Automatic validation of package content against hash
- **Author Signatures**: Optional cryptographic signatures by package authors
- **Trust Chains**: Hierarchical trust relationships between packages
- **Reputation System**: Community-driven package quality metrics

### 5.2 Vulnerability Management

- **Immutable References**: Packages cannot be modified after publication
- **Advisory System**: Centralized database of known vulnerabilities
- **Automated Scanning**: Integration with security scanning tools
- **Update Verification**: Cryptographic proof of package lineage

## 6. Compatibility Considerations

### 6.1 NPM Ecosystem Integration

- **Registry Proxy**: Transparent proxy for npm registry requests
- **Package Transformation**: On-the-fly conversion between formats
- **Dependency Resolution**: Hybrid resolution supporting both systems
- **Publishing Bridge**: Two-way publishing to both systems

### 6.2 Build System Integration

- **Webpack Loader**: Custom loader for hash-based imports
- **Babel Plugin**: Source transformation for compatibility
- **ESBuild Plugin**: Direct integration with modern bundlers
- **PostCSS Integration**: Support for CSS dependencies

### 6.3 Monorepo Support

- **Workspace Detection**: Automatic discovery of local packages
- **Local Linking**: Hash-based linking of local packages
- **Consistent Versioning**: Ensuring consistent dependency versions
- **Build Caching**: Leveraging content hashes for optimal caching