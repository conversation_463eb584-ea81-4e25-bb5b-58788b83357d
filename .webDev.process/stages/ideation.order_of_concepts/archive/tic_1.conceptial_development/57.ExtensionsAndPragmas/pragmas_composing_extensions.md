# Pragmas Composing Extensions

## Overview

This concept explores how pragmas compose extensions in the SpiceTime architecture. Pragmas are special directives that modify the behavior of the system, enabling powerful composition patterns that would be difficult or impossible to achieve with standard language features. By composing extensions through pragmas, we create a flexible, expressive system that can adapt to a wide variety of use cases.

## Core Principles

### 1. Pragmas as Behavior Modifiers

Pragmas modify the behavior of the system:

- **Component Pragmas**: Modify component behavior
  - `@provider`: Creates a provider component
  - `@consumer`: Creates a consumer component
  - `@memo`: Memoizes a component

- **Hook Pragmas**: Modify hook behavior
  - `@effect`: Creates an effect hook
  - `@state`: Creates a state hook
  - `@memo`: Memoizes a hook result

- **Type Pragmas**: Modify type behavior
  - `@nominal`: Creates a nominal type
  - `@branded`: Creates a branded type
  - `@opaque`: Creates an opaque type

### 2. Extension Composition through Pragmas

Pragmas compose extensions by modifying their behavior:

- **Sequential Composition**:
  - `@provider @memo Component`: Creates a memoized provider component
  - `@effect @memo hook`: Creates a memoized effect hook

- **Parametric Composition**:
  - `@provider(scope='auth') Component`: Creates a provider in the auth scope
  - `@effect(deps=['a', 'b']) hook`: Creates an effect with specific dependencies

- **Nested Composition**:
  - `@provider(@memo Component)`: Creates a provider with a memoized inner component
  - `@effect(@memo hook)`: Creates an effect with a memoized inner hook

### 3. Pragma Resolution Rules

Rules for resolving pragmas in the system:

1. **Order Matters**: Pragmas are applied in the order they are specified
2. **Innermost First**: Nested pragmas are resolved from innermost to outermost
3. **Parametric Override**: Parameters override default behavior
4. **Conflict Resolution**: Conflicts are resolved by explicit pragma declarations

## Implementation Examples

### Example 1: Component Pragmas

```jsx
// @provider
function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  
  const login = useCallback((username, password) => {
    // Login logic
    setUser({ username });
  }, []);
  
  const logout = useCallback(() => {
    // Logout logic
    setUser(null);
  }, []);
  
  const value = { user, login, logout };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// @consumer
function useAuth() {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// @provider @memo
function UserProvider({ children }) {
  // Implementation
}
```

### Example 2: Hook Pragmas

```jsx
// @effect
function useDocumentTitle(title) {
  useEffect(() => {
    document.title = title;
    
    return () => {
      document.title = 'App';
    };
  }, [title]);
}

// @state
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => {
    setCount(c => c + 1);
  }, []);
  
  const decrement = useCallback(() => {
    setCount(c => c - 1);
  }, []);
  
  return { count, increment, decrement };
}

// @effect(deps=['value'])
function useLogger(value, label) {
  useEffect(() => {
    console.log(`${label}: ${value}`);
  }, [value]); // Note: deps pragma overrides this
}
```

### Example 3: Type Pragmas

```typescript
// @nominal
type UserId = string;

// @branded
type Email = string & { __brand: 'email' };

// @opaque
type Password = string; // Implementation is hidden

// @nominal @branded
type SessionId = string & { __brand: 'session' };
```

## Pragmatic Implementation

### Pragma Parser

The pragma system parses pragmas from comments or decorators:

```typescript
function parsePragmas(source: string): Pragma[] {
  const pragmas: Pragma[] = [];
  const pragmaRegex = /\/\/\s*@(\w+)(?:\(([^)]*)\))?|@(\w+)(?:\(([^)]*)\))?/g;
  
  let match;
  while ((match = pragmaRegex.exec(source)) !== null) {
    const name = match[1] || match[3];
    const params = match[2] || match[4];
    
    pragmas.push({
      name,
      params: params ? parseParams(params) : {}
    });
  }
  
  return pragmas;
}

function parseParams(paramsString: string): Record<string, any> {
  // Parse parameter string into object
  // e.g., "scope='auth', memo=true" -> { scope: 'auth', memo: true }
  // Implementation details omitted for brevity
  return {};
}
```

### Pragma Application

Pragmas are applied to transform code:

```typescript
function applyPragmas(source: string, pragmas: Pragma[]): string {
  let result = source;
  
  for (const pragma of pragmas) {
    switch (pragma.name) {
      case 'provider':
        result = transformToProvider(result, pragma.params);
        break;
      case 'consumer':
        result = transformToConsumer(result, pragma.params);
        break;
      case 'memo':
        result = transformToMemo(result, pragma.params);
        break;
      case 'effect':
        result = transformToEffect(result, pragma.params);
        break;
      case 'state':
        result = transformToState(result, pragma.params);
        break;
      // Other pragmas...
    }
  }
  
  return result;
}

function transformToProvider(source: string, params: Record<string, any>): string {
  // Transform a component into a provider
  // Implementation details omitted for brevity
  return source;
}

// Other transform functions...
```

### Usage Example

```typescript
// Process a file
const filePath = '/components/auth/AuthProvider.jsx';
const source = readFile(filePath);
const pragmas = parsePragmas(source);
const transformed = applyPragmas(source, pragmas);

// Write transformed code
writeFile(filePath.replace('.jsx', '.transformed.jsx'), transformed);
```

## Design Patterns

### 1. Pragma Composition

Pragmas can be composed to create complex behaviors:

```jsx
// @provider @memo @log
function ComplexProvider({ children }) {
  // Implementation
}
```

This creates a provider that is memoized and logs its renders.

### 2. Pragma Inheritance

Pragmas can inherit behavior from parent pragmas:

```jsx
// @provider(scope='auth')
function AuthProvider({ children }) {
  // Implementation
}

// @provider(extends='auth', scope='user')
function UserProvider({ children }) {
  // Implementation
}
```

The UserProvider inherits behavior from the AuthProvider.

### 3. Pragma Overriding

Child pragmas can override parent pragmas:

```jsx
// @effect(deps=['a', 'b'])
function useEffect1() {
  useEffect(() => {
    // Implementation
  }, ['c', 'd']); // Overridden by pragma
}

// @effect(deps=[], override=false)
function useEffect2() {
  useEffect(() => {
    // Implementation
  }, ['c', 'd']); // Not overridden by pragma
}
```

## Example Implementation

### Pragma Definitions

```typescript
// Provider pragma
const providerPragma = {
  name: 'provider',
  transform: (component, params) => {
    const { scope = 'default' } = params;
    
    return `
      function ${component.name}({ children }) {
        ${component.body}
        
        return (
          <${scope}Context.Provider value={value}>
            {children}
          </${scope}Context.Provider>
        );
      }
    `;
  }
};

// Consumer pragma
const consumerPragma = {
  name: 'consumer',
  transform: (hook, params) => {
    const { scope = 'default' } = params;
    
    return `
      function ${hook.name}() {
        const context = useContext(${scope}Context);
        
        if (!context) {
          throw new Error('${hook.name} must be used within a ${scope}Provider');
        }
        
        return context;
      }
    `;
  }
};

// Memo pragma
const memoPragma = {
  name: 'memo',
  transform: (component, params) => {
    return `
      const ${component.name} = React.memo(function ${component.name}(props) {
        ${component.body}
      });
    `;
  }
};
```

### Generated Code

```jsx
// Original code with pragmas
// @provider(scope='auth')
function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  
  const login = useCallback((username, password) => {
    // Login logic
    setUser({ username });
  }, []);
  
  const logout = useCallback(() => {
    // Logout logic
    setUser(null);
  }, []);
  
  const value = { user, login, logout };
}

// Generated code
function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  
  const login = useCallback((username, password) => {
    // Login logic
    setUser({ username });
  }, []);
  
  const logout = useCallback(() => {
    // Logout logic
    setUser(null);
  }, []);
  
  const value = { user, login, logout };
  
  return (
    <authContext.Provider value={value}>
      {children}
    </authContext.Provider>
  );
}
```

## Conclusion

By using pragmas to compose extensions, we create a flexible, expressive system that can adapt to a wide variety of use cases. Pragmas modify the behavior of components, hooks, and types, enabling powerful composition patterns that would be difficult or impossible to achieve with standard language features.

The system is designed to be intuitive and declarative, with pragmas specified directly in the code as comments or decorators. This approach makes the code self-documenting and easy to understand, while still providing the full power of the pragma system.

Pragmas are a key part of the SpiceTime architecture, providing a foundation for the Context Scoping Tree pattern and enabling seamless integration of components, hooks, and utilities across the system. By composing extensions through pragmas, we create a cohesive, integrated system that is both powerful and easy to use.
