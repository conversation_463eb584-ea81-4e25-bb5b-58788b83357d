# React Component Mapping

## Overview

The React Component Mapping system is a core component of the WebDev Process MVP that enables declarative UI development through pragmatic file extensions. It maps file extensions directly to React components, implements file system-based CSS (fsCss) for structural styling, provides behavioral theming for dynamic customization, and offers a template adaptation system for rapid development.

## Key Features

### Pragmatic Extensions

File extensions serve as pragmas that define component behavior:

```
Button.component.tsx                 // Basic component
Button.component.withState.tsx       // Component with state management
Button.component.withState.styled.tsx // Component with state and styling
```

These extensions are parsed and interpreted by the stPragma system to generate the appropriate React component with the specified behaviors.

### fsCss (File System CSS)

fsCss uses the filesystem structure to define styling:

```
components/
├── Button/
│   ├── Button.component.tsx
│   ├── Button.styles.css
│   └── Button.theme.css
├── Card/
│   ├── Card.component.tsx
│   ├── Card.styles.css
│   └── Card.theme.css
└── theme/
    ├── colors.css
    ├── typography.css
    └── spacing.css
```

The structure of the filesystem directly maps to the structure of the CSS, enabling intuitive organization and easy navigation.

### Behavioral Theming

Behavioral theming dynamically adjusts styling based on component behavior:

```typescript
// Button.component.withState.styled.tsx
const Button = ({ children, ...props }) => {
  const [state, setState] = useState('default');
  
  return (
    <button 
      className={`button button--${state}`} 
      onMouseEnter={() => setState('hover')}
      onMouseLeave={() => setState('default')}
      {...props}
    >
      {children}
    </button>
  );
};
```

The styling is automatically applied based on the component's state, creating a dynamic and responsive UI.

### Template Adaptation

The template adaptation system enables rapid customization of components:

```typescript
// Template definition
const buttonTemplate = createTemplate('Button', {
  variants: ['primary', 'secondary', 'tertiary'],
  sizes: ['small', 'medium', 'large'],
  states: ['default', 'hover', 'active', 'disabled']
});

// Template usage
const PrimaryButton = buttonTemplate.create({
  variant: 'primary',
  size: 'medium'
});
```

Templates can be quickly adapted to create new components with different styles and behaviors.

## Implementation

### Pragma to React Mapping

The mapping from pragmas to React components is implemented through a mapping system:

```typescript
const componentMappings = {
  'component': createBasicComponent,
  'withState': addStateToComponent,
  'styled': addStylesToComponent,
  'withRouter': addRouterToComponent,
  'withContext': addContextToComponent
};

function mapPragmaToComponent(filename) {
  const extensions = filename.split('.').slice(1, -1);
  let component = createEmptyComponent();
  
  for (const ext of extensions) {
    if (componentMappings[ext]) {
      component = componentMappings[ext](component);
    }
  }
  
  return component;
}
```

### fsCss Implementation

fsCss is implemented through a CSS-in-JS system that reads from the filesystem:

```typescript
function generateCssFromFileSystem(componentPath) {
  const stylesPath = `${componentPath}.styles.css`;
  const themePath = `${componentPath}.theme.css`;
  
  const styles = fs.existsSync(stylesPath) ? fs.readFileSync(stylesPath, 'utf8') : '';
  const theme = fs.existsSync(themePath) ? fs.readFileSync(themePath, 'utf8') : '';
  
  return {
    styles,
    theme
  };
}
```

### Behavioral Theming Implementation

Behavioral theming is implemented through a state-based styling system:

```typescript
function addStylesToComponent(component) {
  return (props) => {
    const { state } = useComponentState();
    const styles = useStyles(state);
    
    return component({
      ...props,
      style: {
        ...props.style,
        ...styles
      }
    });
  };
}
```

### Template Adaptation Implementation

Template adaptation is implemented through a template factory system:

```typescript
function createTemplate(name, options) {
  return {
    create: (config) => {
      const component = createBasicComponent(name);
      
      if (config.variant) {
        component.variant = config.variant;
      }
      
      if (config.size) {
        component.size = config.size;
      }
      
      return component;
    }
  };
}
```

## Integration with stPragma

The React Component Mapping system integrates with stPragma through the pragma operators:

```typescript
const reactComponentPragma = createPragmaOperator({
  name: 'component',
  transform: (node) => {
    const filename = node.file.name;
    const component = mapPragmaToComponent(filename);
    
    return {
      ...node,
      component
    };
  }
});
```

## Integration with Time Management

The React Component Mapping system integrates with the Time Management system to enable temporal navigation of component versions:

```typescript
function getComponentVersion(componentPath, version) {
  const versionSpace = getVersionSpace();
  return versionSpace.getVersion(componentPath, version);
}
```

## Conclusion

The React Component Mapping system provides a powerful mechanism for declarative UI development through pragmatic file extensions. By mapping file extensions directly to React components, implementing file system-based CSS, providing behavioral theming, and offering a template adaptation system, it enables rapid development and customization of UI components in the WebDev Process MVP.
