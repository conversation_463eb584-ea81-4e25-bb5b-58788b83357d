# Time Management

## Overview

The Time Management system is a core component of the WebDev Process MVP that provides temporal navigation, versioning, archiving, and restructuring capabilities. It implements the Version Space concept for categorical representation of project versions, integrates with Git for distributed versioning, and provides tools for archiving and restructuring projects based on temporal patterns.

## Key Features

### Version Space

Version Space is a categorical representation of project versions that enables navigation through different versions of a project:

```
VersionSpace/
├── v1.0.0/
│   ├── components/
│   ├── pages/
│   └── utils/
├── v1.1.0/
│   ├── components/
│   ├── pages/
│   └── utils/
└── v2.0.0/
    ├── components/
    ├── pages/
    └── utils/
```

Version Space allows developers to navigate between different versions of a project, compare changes, and understand the evolution of the codebase.

### Archiving

The archiving system automatically archives old versions of files and directories based on time.rfr:

```typescript
// time.rfr configuration
const timeConfig = {
  archiveAfter: '30d',
  archivePath: './archive',
  keepVersions: 5,
  compressArchive: true
};
```

Archived files and directories are stored in a compressed format to save space while maintaining accessibility.

### Restructuring

The restructuring system reorganizes projects based on temporal patterns:

```typescript
// Restructuring rule
const restructuringRule = {
  pattern: 'components/*.component.tsx',
  condition: 'lastModified > 30d && !accessed',
  action: 'move',
  destination: 'archive/components'
};
```

Restructuring helps maintain a clean and organized codebase by moving unused or outdated files to appropriate locations.

### Git Integration

The Time Management system integrates with Git for distributed versioning:

```typescript
// Git integration
const gitConfig = {
  repository: './repository',
  branch: 'main',
  commitMessage: 'Auto-commit: {message}',
  pushAfterCommit: true
};
```

Git integration ensures that all changes are properly tracked and can be shared across a distributed team.

## Implementation

### Version Space Implementation

Version Space is implemented as a categorical structure using forestry_cat_types:

```typescript
function createVersionSpace(rootPath) {
  const category = new ConcreteCategory();
  const versionSpace = createForestFunctor('versionSpace', category);
  
  // Create a tree for each version
  const versions = getVersions(rootPath);
  
  for (const version of versions) {
    const versionTree = createTreeFunctor(version, category, {});
    versionSpace.addTree(version, versionTree);
  }
  
  return versionSpace;
}
```

### Archiving Implementation

Archiving is implemented through a time-based system that monitors file access and modification times:

```typescript
function archiveFiles(config) {
  const files = getFiles(config.pattern);
  
  for (const file of files) {
    const stats = fs.statSync(file);
    const lastModified = new Date(stats.mtime);
    const now = new Date();
    
    const diffDays = (now - lastModified) / (1000 * 60 * 60 * 24);
    
    if (diffDays > config.archiveAfter) {
      archiveFile(file, config.archivePath);
    }
  }
}
```

### Restructuring Implementation

Restructuring is implemented through a rule-based system that applies actions based on conditions:

```typescript
function restructureProject(rules) {
  for (const rule of rules) {
    const files = getFiles(rule.pattern);
    
    for (const file of files) {
      if (evaluateCondition(file, rule.condition)) {
        applyAction(file, rule.action, rule.destination);
      }
    }
  }
}
```

### Git Integration Implementation

Git integration is implemented through a wrapper around the Git CLI:

```typescript
function gitCommit(message) {
  execSync(`git add .`);
  execSync(`git commit -m "${message}"`);
  
  if (gitConfig.pushAfterCommit) {
    execSync(`git push origin ${gitConfig.branch}`);
  }
}
```

## Integration with stPragma

The Time Management system integrates with stPragma through the time.rfr pragma:

```typescript
const timeRfrPragma = createPragmaOperator({
  name: 'time.rfr',
  transform: (node) => {
    const timeConfig = parseTimeConfig(node.file.content);
    
    return {
      ...node,
      timeConfig
    };
  }
});
```

## Integration with React Component Mapping

The Time Management system integrates with the React Component Mapping system to enable temporal navigation of component versions:

```typescript
function getComponentVersion(componentPath, version) {
  const versionSpace = getVersionSpace();
  return versionSpace.getVersion(componentPath, version);
}
```

## Conclusion

The Time Management system provides powerful capabilities for temporal navigation, versioning, archiving, and restructuring in the WebDev Process MVP. By implementing the Version Space concept, integrating with Git, and providing tools for archiving and restructuring, it enables developers to manage the evolution of their codebase effectively and efficiently.
