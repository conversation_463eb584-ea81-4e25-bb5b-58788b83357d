# Kernel Priority Synchronization in React Context

## Overview

This concept defines how the SpiceTime kernel controls React component scheduling through priority synchronization and context management. The kernel acts as a gatekeeper, controlling when autoexec wrappers can proceed, while React handles rendering after receiving properly hooked contexts.

## Architecture Flow

### 1. State Change Detection
```
TreenityTree Context Change → Autoexec Wrapper Triggered → Kernel Scheduling Request
```

### 2. Kernel Gatekeeper Pattern
```
Autoexec Wrapper:
├── Detects context change
├── Schedules itself with kernel
├── **WAITS** for kernel approval
├── Proceeds only when kernel gives go-ahead
└── Returns wrapped function result
```

### 3. React Integration Point
```
For React Pragmas:
├── Wrapper waits for kernel approval
├── Calls: scope.context = createContext(scope.context)
├── Returns component with hooked context
└── React takes over scheduling from there
```

## Implementation Details

### Autoexec Wrapper Behavior

**Non-React Nodes:**
```javascript
// Standard autoexec wrapper
const wrappedFunction = autoexec((context) => {
  // 1. Context change detected
  // 2. Schedule with kernel
  kernel.scheduleTask(taskId, priority);
  
  // 3. WAIT for kernel approval
  await kernel.waitForApproval(taskId);
  
  // 4. Execute function
  return originalFunction(context);
});
```

**React Component Nodes:**
```javascript
// React pragma wrapper
const wrappedComponent = autoexec((context) => {
  // 1. Context change detected
  // 2. Schedule with kernel
  kernel.scheduleTask(taskId, priority);
  
  // 3. WAIT for kernel approval
  await kernel.waitForApproval(taskId);
  
  // 4. Hook context into React
  scope.context = createContext(scope.context);
  
  // 5. Return component with hooked context
  return ComponentFunction(hookedContext);
});
```

### Context Processing Pipeline

#### Root Pragma Context Creation
```javascript
// In each folder's root pragma
const processContext = (contextDefinition) => {
  // 1. Create treenityTree from context definition
  const reactiveContext = t.treenityTree.create(contextDefinition);
  
  // 2. Preserve parent context
  reactiveContext._parent = parentScope.context;
  
  // 3. Make deeply reactive
  return makeDeepReactive(reactiveContext);
};

// Usage in folder
scope.context = processContext({
  users: [],
  filters: { status: 'active' },
  selectedUser: null
});
```

#### Component Pragma Context Hooking
```javascript
// In component/comp pragma
const hookReactContext = (treenityContext) => {
  // Convert treenityTree to React context
  const ReactContext = createContext(treenityContext.getSnapshot());
  
  // Create provider wrapper
  const ContextProvider = ({ children }) => {
    const [state, setState] = useState(treenityContext.getSnapshot());
    
    useEffect(() => {
      // Subscribe to treenityTree changes
      const unsubscribe = treenityContext.subscribe((changes) => {
        setState(treenityContext.getSnapshot());
      });
      return unsubscribe;
    }, []);
    
    return (
      <ReactContext.Provider value={state}>
        {children}
      </ReactContext.Provider>
    );
  };
  
  return { ReactContext, ContextProvider };
};
```

### Kernel Priority Control

#### Task Scheduling
```javascript
class SpiceTimeKernel {
  scheduleTask(taskId, priority) {
    // Add to priority queue
    this.taskQueue.enqueue({ taskId, priority, timestamp: Date.now() });
    
    // Process queue if not already processing
    if (!this.isProcessing) {
      this.processQueue();
    }
  }
  
  async waitForApproval(taskId) {
    return new Promise((resolve) => {
      this.approvalCallbacks.set(taskId, resolve);
    });
  }
  
  giveApproval(taskId) {
    const callback = this.approvalCallbacks.get(taskId);
    if (callback) {
      callback();
      this.approvalCallbacks.delete(taskId);
    }
  }
}
```

#### Priority Processing
```javascript
processQueue() {
  this.isProcessing = true;
  
  const processNext = () => {
    if (this.taskQueue.isEmpty()) {
      this.isProcessing = false;
      return;
    }
    
    const task = this.taskQueue.dequeue();
    
    // Give approval based on priority and resources
    if (this.canExecuteNow(task)) {
      this.giveApproval(task.taskId);
    } else {
      // Re-queue for later
      this.taskQueue.enqueue(task);
    }
    
    // Continue processing
    setTimeout(processNext, 0);
  };
  
  processNext();
}
```

## Context Namespace Management

### Scope Context Structure
```javascript
// Each node's scope.context structure
scope.context = {
  // Local context (treenityTree)
  users: [],
  filters: {},
  
  // Parent context reference (preserved)
  _parent: parentScope.context,
  
  // Injected contexts (from providers)
  injected: {
    // Controlled injection points
  }
};
```

### Provider Pragma Implementation

#### Current Problem
```javascript
// someNode.provider creates access issue
scope.context.someNode = someNodeScope.context;
// Problem: Host node now has access to someNode's parent context
```

#### Proposed Solution: Context Isolation
```javascript
// Provider pragma with isolation
const createIsolatedProvider = (sourceNode, targetNode) => {
  // Create isolated copy of source context
  const isolatedContext = {
    // Only public API of source context
    ...sourceNode.scope.context.getPublicAPI(),
    
    // No parent context access
    _parent: null,
    
    // Controlled injection metadata
    _source: sourceNode.id,
    _injectedAt: Date.now()
  };
  
  // Inject into target under controlled namespace
  targetNode.scope.context.providers = targetNode.scope.context.providers || {};
  targetNode.scope.context.providers[sourceNode.id] = isolatedContext;
};
```

#### Alternative: Scoped Injection
```javascript
// someNode.provider.scoped - only specific properties
someNode.provider.scoped({
  expose: ['users', 'filters'],  // Only these properties
  as: 'userProvider',            // Inject as this name
  readonly: true                 // No mutations allowed
});

// Results in:
scope.context.userProvider = {
  users: [...],     // Read-only copy
  filters: {...}    // Read-only copy
};
```

#### Alternative: API-Only Injection
```javascript
// someNode.provider.api - only methods, no state
someNode.provider.api({
  methods: ['createUser', 'updateUser', 'deleteUser'],
  as: 'userAPI'
});

// Results in:
scope.context.userAPI = {
  createUser: (data) => someNode.createUser(data),
  updateUser: (id, data) => someNode.updateUser(id, data),
  deleteUser: (id) => someNode.deleteUser(id)
};
```

## Benefits

### 1. Clean Separation of Concerns
- **Kernel**: Controls task scheduling and priorities
- **React**: Handles rendering after receiving hooked contexts
- **No Conflict**: Different levels of scheduling

### 2. Granular Priority Control
- Kernel decides when wrappers can proceed
- React gets clean, properly prepared contexts
- Perfect priority control before React involvement

### 3. True Reactivity Without Reducers
- TreenityTree provides deep reactivity
- Direct state manipulation through setters
- Compositional reducers only when needed
- Context-based state management

### 4. Provider Security
- Controlled context injection
- No accidental parent context exposure
- API-only or scoped property injection options

## Implementation Strategy

### Phase 1: Pure JavaScript Implementation
```javascript
// Start with pure JS/TS implementation
// Prove the concept works
// Refine the API and patterns
```

### Phase 2: Rust Conversion
```rust
// Convert kernel to Rust when patterns are stable
// Keep JavaScript for React integration
// Rust handles priority scheduling and resource management
```

### Phase 3: Optimization
```javascript
// Optimize based on real-world usage
// Fine-tune priority algorithms
// Add performance monitoring
```

## Index File Orchestration

### Application Bootstrap
```javascript
// index.js - Application entry point
import { SpiceTimeKernel } from './kernel';
import { initializeContexts } from './contexts';
import { App } from './App';

// 1. Initialize kernel
const kernel = new SpiceTimeKernel();

// 2. Initialize context system
initializeContexts(kernel);

// 3. Start application with kernel control
kernel.startApplication(() => {
  ReactDOM.render(<App />, document.getElementById('root'));
});
```

## Conclusion

This architecture provides complete control over React component scheduling while maintaining clean separation between kernel task scheduling and React rendering. The gatekeeper pattern ensures priority control, while the context hooking mechanism provides seamless React integration with true reactivity through treenityTrees.

The provider system with controlled injection maintains security while enabling flexible context sharing between nodes. The entire system can be implemented in pure JavaScript initially and converted to Rust for performance optimization once the patterns are proven.
