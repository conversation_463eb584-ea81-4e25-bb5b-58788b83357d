# React Integration

## Overview

This document explores how Pragmatic Behavior Components (PBCs) integrate with React to create a powerful development experience that combines behavior-driven development with component-driven UI development. It covers integration patterns, reactive architecture alignment, and styling approaches.

## Integration Patterns

PBCs can be integrated with React in several ways:

### 1. PBC as React Component

PBCs can be directly used as React components:

```tsx
// Using a PBC as a React component
const UserList = () => {
  // Component state and hooks
  const [users, setUsers] = useState([]);
  
  return (
    <FilterCollection
      items={users}
      entityName="users"
      properties={['name', 'email', 'role']}
      onFilterChange={(filteredUsers) => {
        // Handle filtered results
      }}
    />
  );
};
```

This approach is ideal for:
- Direct use of PBC behavior in UI components
- Simple integration with existing React applications
- Clear separation between behavior and presentation

### 2. PBC as Higher-Order Component

PBCs can be used as higher-order components to enhance existing components:

```tsx
// Using a PBC as a higher-order component
const FilterableUserList = withFilterBehavior(UserList, {
  entityName: 'users',
  properties: ['name', 'email', 'role']
});
```

This approach is ideal for:
- Enhancing existing components with behavior
- Composition of multiple behaviors
- Maintaining separation of concerns

### 3. PBC as Custom Hook

PBCs can be used as custom hooks to provide behavior to components:

```tsx
// Using a PBC as a custom hook
const UserList = () => {
  const [users, setUsers] = useState([]);
  
  const { 
    filters, 
    setFilters, 
    filteredItems 
  } = useFilterBehavior(users, {
    entityName: 'users',
    properties: ['name', 'email', 'role']
  });
  
  return (
    <div>
      <FilterControls 
        properties={['name', 'email', 'role']}
        filters={filters}
        onFilterChange={setFilters}
      />
      <UserTable users={filteredItems} />
    </div>
  );
};
```

This approach is ideal for:
- Maximum flexibility in UI implementation
- Fine-grained control over behavior integration
- Reuse of behavior across multiple components

### 4. PBC as Decorator

PBCs can be used as decorators for class components:

```tsx
// Using a PBC as a decorator
@FilterBehavior({
  entityName: 'users',
  properties: ['name', 'email', 'role']
})
class UserList extends React.Component {
  // Component implementation
}
```

This approach is ideal for:
- Class-based component architectures
- Multiple behavior composition
- Clear behavior declaration

## Reactive Architecture Alignment

React's component model and our reactive architecture align naturally:

### 1. State Management

```tsx
// React component with reactive state
const UserList = () => {
  // Create reactive context
  const context = useContext(UserContext);
  
  // Initialize component
  useEffect(() => {
    // Set up reactive state
    context.filters = { status: 'active' };
  }, []);
  
  // Computed properties (reactive)
  // These will automatically update when dependencies change
  const filteredUsers = context.users.filter(user => 
    user.status === context.filters.status
  );
  
  return (
    <div>
      <FilterControls 
        filters={context.filters}
        onFilterChange={(filters) => {
          context.filters = filters;
        }}
      />
      <UserTable users={filteredUsers} />
    </div>
  );
};
```

### 2. Effect Management

```tsx
// React component with reactive effects
const UserList = () => {
  const context = useContext(UserContext);
  
  // This effect runs when the component mounts
  useEffect(() => {
    // Register reactive computation
    context.registerComputation('filteredUsers', () => {
      return context.users.filter(user => 
        user.status === context.filters.status
      );
    });
    
    // Cleanup when component unmounts
    return () => {
      context.unregisterComputation('filteredUsers');
    };
  }, []);
  
  return (
    <div>
      <UserTable users={context.filteredUsers} />
    </div>
  );
};
```

### 3. Context Integration

```tsx
// Integrating React Context with our reactive context
const UserProvider = ({ children }) => {
  // Create reactive context
  const reactiveContext = createReactiveContext({
    users: [],
    filters: {},
    sortCriteria: { property: 'name', direction: 'ascending' }
  });
  
  // Create React context value
  const contextValue = {
    ...reactiveContext,
    // Additional methods
    fetchUsers: async () => {
      const users = await api.getUsers();
      reactiveContext.users = users;
    },
    selectUser: (user) => {
      reactiveContext.selectedUser = user;
    }
  };
  
  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};
```

## Styling Integration

PBCs can include styling that integrates with React components:

### 1. CSS-in-JS Integration

```tsx
// PBC with styled-components integration
const FilterCollection = createPBC({
  name: 'FilterCollection',
  // Other PBC properties...
  
  react: {
    component: ({ items, properties, onFilterChange }) => {
      // Component implementation
      
      return (
        <Container>
          <FilterBar>
            <PropertySelect>
              {/* Select options */}
            </PropertySelect>
            <OperatorSelect>
              {/* Select options */}
            </OperatorSelect>
            <ValueInput>
              {/* Input field */}
            </ValueInput>
          </FilterBar>
        </Container>
      );
    },
    
    // Styled components
    styles: {
      Container: styled.div`
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      `,
      FilterBar: styled.div`
        display: flex;
        align-items: center;
        padding: 0.5rem;
        background-color: #f3f4f6;
        border-radius: 0.25rem;
      `,
      PropertySelect: styled.select`
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
      `,
      OperatorSelect: styled.select`
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        margin-left: 0.5rem;
      `,
      ValueInput: styled.input`
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        padding: 0.25rem 0.5rem;
        margin-left: 0.5rem;
      `
    }
  }
});
```

### 2. Tailwind CSS Integration

```tsx
// PBC with Tailwind CSS integration
const FilterCollection = createPBC({
  name: 'FilterCollection',
  // Other PBC properties...
  
  react: {
    component: ({ items, properties, onFilterChange }) => {
      // Component implementation
      
      return (
        <div className="flex flex-col gap-2">
          <div className="flex items-center p-2 bg-gray-100 rounded">
            <select className="border rounded px-2 py-1">
              {/* Select options */}
            </select>
            <select className="border rounded px-2 py-1 ml-2">
              {/* Select options */}
            </select>
            <input className="border rounded px-2 py-1 ml-2" />
          </div>
        </div>
      );
    }
  },
  
  // Tailwind classes
  styling: {
    container: "flex flex-col gap-2",
    filterBar: "flex items-center p-2 bg-gray-100 rounded",
    propertySelect: "border rounded px-2 py-1",
    operatorSelect: "border rounded px-2 py-1 ml-2",
    valueInput: "border rounded px-2 py-1 ml-2"
  }
});
```

### 3. Theme Integration

```tsx
// PBC with theme integration
const FilterCollection = createPBC({
  name: 'FilterCollection',
  // Other PBC properties...
  
  react: {
    component: ({ items, properties, onFilterChange, theme }) => {
      // Component implementation
      
      return (
        <div className={theme.container}>
          <div className={theme.filterBar}>
            <select className={theme.propertySelect}>
              {/* Select options */}
            </select>
            <select className={theme.operatorSelect}>
              {/* Select options */}
            </select>
            <input className={theme.valueInput} />
          </div>
        </div>
      );
    }
  },
  
  // Default styling that can be overridden by themes
  defaultStyling: {
    container: "flex flex-col gap-2",
    filterBar: "flex items-center p-2 bg-gray-100 rounded",
    propertySelect: "border rounded px-2 py-1",
    operatorSelect: "border rounded px-2 py-1 ml-2",
    valueInput: "border rounded px-2 py-1 ml-2"
  }
});
```

## Complete Example: User Management System

Let's explore a complete example of a User Management System built with PBCs and React:

### 1. Define PBCs

```typescript
// FilterUsers PBC
const FilterUsers = createPBC({
  name: 'FilterUsers',
  behavior: {
    type: "FilterOperation",
    entity: "users",
    filter: {
      property: "${propertyName}",
      operator: "${operator}",
      value: "${value}"
    }
  },
  // Other PBC properties...
});

// SortUsers PBC
const SortUsers = createPBC({
  name: 'SortUsers',
  behavior: {
    type: "SortOperation",
    entity: "users",
    sort: {
      property: "${propertyName}",
      direction: "${direction}"
    }
  },
  // Other PBC properties...
});

// PaginateUsers PBC
const PaginateUsers = createPBC({
  name: 'PaginateUsers',
  behavior: {
    type: "PaginationOperation",
    entity: "users",
    pagination: {
      pageSize: "${pageSize}",
      currentPage: "${currentPage}"
    }
  },
  // Other PBC properties...
});
```

### 2. Compose PBCs

```typescript
// Compose PBCs into a UserListBehavior
const UserListBehavior = composePBCs([
  FilterUsers,
  SortUsers,
  PaginateUsers
]);
```

### 3. Create React Context

```typescript
// Create React context for user management
const UserContext = createContext(null);

// Create provider component
const UserProvider = ({ children }) => {
  // Create reactive context
  const reactiveContext = createReactiveContext({
    users: [],
    filters: {},
    sortCriteria: { property: 'name', direction: 'ascending' },
    pagination: { pageSize: 10, currentPage: 1 }
  });
  
  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      const users = await api.getUsers();
      reactiveContext.users = users;
    };
    
    fetchUsers();
  }, []);
  
  // Create context value
  const contextValue = {
    ...reactiveContext,
    // Additional methods
    createUser: async (user) => {
      const newUser = await api.createUser(user);
      reactiveContext.users = [...reactiveContext.users, newUser];
    },
    updateUser: async (user) => {
      const updatedUser = await api.updateUser(user);
      reactiveContext.users = reactiveContext.users.map(u => 
        u.id === updatedUser.id ? updatedUser : u
      );
    },
    deleteUser: async (userId) => {
      await api.deleteUser(userId);
      reactiveContext.users = reactiveContext.users.filter(u => 
        u.id !== userId
      );
    }
  };
  
  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};
```

### 4. Create React Components

```tsx
// UserList component using PBCs
const UserList = () => {
  const context = useContext(UserContext);
  
  // Use the composed behavior
  const { 
    filteredUsers,
    sortedUsers,
    paginatedUsers,
    totalPages
  } = useUserListBehavior(context);
  
  return (
    <div className="user-list">
      <div className="filters">
        <FilterControls 
          properties={['name', 'email', 'role', 'status']}
          filters={context.filters}
          onFilterChange={(filters) => {
            context.filters = filters;
          }}
        />
      </div>
      
      <div className="sorting">
        <SortControls 
          properties={['name', 'email', 'role', 'status']}
          sortCriteria={context.sortCriteria}
          onSortChange={(sortCriteria) => {
            context.sortCriteria = sortCriteria;
          }}
        />
      </div>
      
      <div className="user-table">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Role</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {paginatedUsers.map(user => (
              <tr key={user.id}>
                <td>{user.name}</td>
                <td>{user.email}</td>
                <td>{user.role}</td>
                <td>{user.status}</td>
                <td>
                  <button onClick={() => context.selectUser(user)}>
                    Edit
                  </button>
                  <button onClick={() => context.deleteUser(user.id)}>
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="pagination">
        <PaginationControls 
          currentPage={context.pagination.currentPage}
          totalPages={totalPages}
          onPageChange={(page) => {
            context.pagination.currentPage = page;
          }}
        />
      </div>
    </div>
  );
};

// UserForm component
const UserForm = () => {
  const context = useContext(UserContext);
  const [user, setUser] = useState({
    name: '',
    email: '',
    role: 'user',
    status: 'active'
  });
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (user.id) {
      context.updateUser(user);
    } else {
      context.createUser(user);
    }
    setUser({
      name: '',
      email: '',
      role: 'user',
      status: 'active'
    });
  };
  
  // When a user is selected for editing
  useEffect(() => {
    if (context.selectedUser) {
      setUser(context.selectedUser);
    }
  }, [context.selectedUser]);
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>Name</label>
        <input
          value={user.name}
          onChange={(e) => setUser({ ...user, name: e.target.value })}
        />
      </div>
      
      <div>
        <label>Email</label>
        <input
          value={user.email}
          onChange={(e) => setUser({ ...user, email: e.target.value })}
        />
      </div>
      
      <div>
        <label>Role</label>
        <select
          value={user.role}
          onChange={(e) => setUser({ ...user, role: e.target.value })}
        >
          <option value="admin">Admin</option>
          <option value="user">User</option>
          <option value="guest">Guest</option>
        </select>
      </div>
      
      <div>
        <label>Status</label>
        <select
          value={user.status}
          onChange={(e) => setUser({ ...user, status: e.target.value })}
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="pending">Pending</option>
        </select>
      </div>
      
      <button type="submit">
        {user.id ? 'Update User' : 'Create User'}
      </button>
    </form>
  );
};

// Main UserManagement component
const UserManagement = () => {
  return (
    <UserProvider>
      <div className="user-management">
        <h1>User Management</h1>
        
        <div className="user-form-container">
          <h2>Create/Edit User</h2>
          <UserForm />
        </div>
        
        <div className="user-list-container">
          <h2>User List</h2>
          <UserList />
        </div>
      </div>
    </UserProvider>
  );
};
```

### 5. Custom Hook Implementation

```typescript
// Custom hook for UserListBehavior
const useUserListBehavior = (context) => {
  // Computed properties (reactive)
  
  // Filter users
  const filteredUsers = useMemo(() => {
    return context.users.filter(user => {
      // Apply filters
      for (const [property, filter] of Object.entries(context.filters)) {
        if (!filter) continue;
        
        const { operator, value } = filter;
        const userValue = user[property];
        
        switch (operator) {
          case 'equals':
            if (userValue !== value) return false;
            break;
          case 'contains':
            if (!userValue.includes(value)) return false;
            break;
          case 'startsWith':
            if (!userValue.startsWith(value)) return false;
            break;
          case 'endsWith':
            if (!userValue.endsWith(value)) return false;
            break;
        }
      }
      
      return true;
    });
  }, [context.users, context.filters]);
  
  // Sort users
  const sortedUsers = useMemo(() => {
    return [...filteredUsers].sort((a, b) => {
      const { property, direction } = context.sortCriteria;
      const factor = direction === 'ascending' ? 1 : -1;
      
      if (typeof a[property] === 'string') {
        return a[property].localeCompare(b[property]) * factor;
      } else {
        return (a[property] - b[property]) * factor;
      }
    });
  }, [filteredUsers, context.sortCriteria]);
  
  // Paginate users
  const paginatedUsers = useMemo(() => {
    const { pageSize, currentPage } = context.pagination;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return sortedUsers.slice(startIndex, endIndex);
  }, [sortedUsers, context.pagination]);
  
  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(sortedUsers.length / context.pagination.pageSize);
  }, [sortedUsers, context.pagination.pageSize]);
  
  return {
    filteredUsers,
    sortedUsers,
    paginatedUsers,
    totalPages
  };
};
```

## Bidirectional Updates with React

Our system supports bidirectional updates between React components and other formats:

### 1. React Component to Pragmatic Structure

When a developer modifies a React component:

```tsx
// Modified React component
const UserList = () => {
  const context = useContext(UserContext);
  
  // Added export functionality
  const handleExport = () => {
    const csv = convertToCSV(context.filteredUsers);
    downloadCSV(csv, 'users.csv');
  };
  
  return (
    <div className="user-list">
      {/* Existing component content */}
      
      {/* New export button */}
      <button onClick={handleExport}>
        Export to CSV
      </button>
    </div>
  );
};
```

The system can extract the new behavior:

```typescript
// Updated Pragmatic Structure
{
  type: "Component",
  name: "UserList",
  behaviors: [
    // Existing behaviors...
    
    // New behavior
    {
      type: "ExportOperation",
      entity: "users",
      format: "CSV",
      filename: "users.csv"
    }
  ]
}
```

### 2. Pragmatic Structure to Gherkin

The updated pragmatic structure can generate new Gherkin scenarios:

```gherkin
Feature: User Management

  # Existing scenarios...
  
  Scenario: Export Users to CSV
    Given I have filtered the user list
    When I click the "Export to CSV" button
    Then a CSV file named "users.csv" should be downloaded
    And the CSV should contain all filtered users
```

### 3. Gherkin to React Component

Changes in Gherkin can flow back to React components:

```gherkin
Feature: User Management

  # Existing scenarios...
  
  Scenario: Export Users to CSV
    Given I have filtered the user list
    When I click the "Export to CSV" button
    Then a CSV file named "users.csv" should be downloaded
    And the CSV should contain all filtered users
    And the CSV should include headers for all columns
```

The system can update the React component:

```tsx
// Updated React component
const UserList = () => {
  const context = useContext(UserContext);
  
  // Updated export functionality with headers
  const handleExport = () => {
    const headers = ['Name', 'Email', 'Role', 'Status'];
    const csv = convertToCSV(context.filteredUsers, headers);
    downloadCSV(csv, 'users.csv');
  };
  
  return (
    <div className="user-list">
      {/* Existing component content */}
      
      {/* Export button */}
      <button onClick={handleExport}>
        Export to CSV
      </button>
    </div>
  );
};
```

## Conclusion

The integration of Pragmatic Behavior Components with React creates a powerful development experience that combines the best of behavior-driven development and component-driven UI development. By leveraging React's component model and our reactive architecture, we can create applications that are both well-behaved and beautiful.

The bidirectional transformation system ensures that changes in any format propagate to all others, maintaining consistency across specifications, implementation, and UI components. This enables developers to work in their preferred format while ensuring that all representations stay in sync.

The result is a development workflow that is more efficient, more maintainable, and more aligned with business requirements.
