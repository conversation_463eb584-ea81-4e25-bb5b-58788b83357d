# Bidirectional Transformations

## Overview

Bidirectional transformations are the cornerstone of our vision, enabling developers to work in their preferred format without becoming detached from the underlying pragmatic structure. This document explores how these transformations work, their benefits, and implementation strategies.

## Core Principles

1. **Lossless Transformation**: Information is preserved across all transformations
2. **Round-Trip Consistency**: A→B→A transformations result in equivalent representations
3. **Incremental Updates**: Changes in one format propagate efficiently to others
4. **Conflict Resolution**: Clear strategies for handling conflicting changes
5. **Format-Specific Optimizations**: Each format leverages its unique strengths

## Transformation Paths

```
                      ┌─────────────────────┐
                      │  OpenAPI/Swagger    │
                      └────────▲────────────┘
                               │
┌─────────────────┐      ┌─────┴─────────┐      ┌─────────────────┐
│   Gherkin       │◄────►│   Pragmatic   │◄────►│  JSON Schema    │
└─────────────────┘      │   Structure   │      └─────────────────┘
                         └─────────┬─────┘
                                   │
                         ┌─────────▼─────────┐
                         │  Implementation   │
                         │      Code         │
                         └───────────────────┘
```

### Gherkin ↔ Pragmatic Structure

**Gherkin to Pragmatic Structure**:
```gherkin
Feature: User Management
  Scenario: Filter users by status
    Given there are users with different statuses
    When I filter users by status "active"
    Then I should see only active users in the results
```

↓ Transformation ↓

```typescript
// Pragmatic Structure
{
  type: "FilterOperation",
  entity: "users",
  filter: {
    property: "status",
    value: "active"
  },
  expectation: {
    result: "filtered",
    condition: "all",
    property: "status",
    value: "active"
  }
}
```

**Pragmatic Structure to Gherkin**:
The system can regenerate equivalent Gherkin from the pragmatic structure, potentially with variations in natural language while preserving the semantic meaning.

### Pragmatic Structure ↔ Implementation Code

**Pragmatic Structure to Implementation**:
```typescript
// Pragmatic Structure
{
  type: "FilterOperation",
  entity: "users",
  filter: {
    property: "status",
    value: "active"
  }
}
```

↓ Transformation ↓

```typescript
// Implementation Code
export default function filterUsers(context) {
  // Reactive implementation - no async/await
  // Filter is applied whenever context.filters or context.users changes
  context.filters = { status: context.params.status };
  
  // Filtered results are automatically computed
  context.filteredUsers = context.users.filter(user => 
    user.status === context.filters.status
  );
}
```

**Implementation to Pragmatic Structure**:
The system analyzes the implementation code to extract the underlying pragmatic structure, identifying patterns like filtering, sorting, and pagination.

## Benefits of Bidirectional Transformations

1. **Format Freedom**: Developers can work in their preferred format
2. **Consistency**: All representations stay in sync
3. **Traceability**: Clear connections between specifications and implementation
4. **Flexibility**: Changes can be made at any level of abstraction
5. **Collaboration**: Different team members can work in different formats

## Implementation Strategies

### 1. Lens-Based Transformations

Lenses provide a formal framework for bidirectional transformations:

```typescript
interface Lens<S, T> {
  get: (source: S) => T;
  put: (source: S, target: T) => S;
}

// Example: Gherkin to Pragmatic Structure lens
const gherkinToPragmaticLens: Lens<GherkinFeature, PragmaticStructure> = {
  get: (gherkin) => {
    // Extract pragmatic structure from Gherkin
    return extractPragmaticStructure(gherkin);
  },
  put: (gherkin, pragmatic) => {
    // Update Gherkin based on pragmatic structure changes
    return updateGherkin(gherkin, pragmatic);
  }
};
```

### 2. Incremental Transformation

For efficiency, transformations are performed incrementally:

```typescript
function transformIncrementally(source, target, changes) {
  // Only transform the parts that have changed
  const affectedParts = identifyAffectedParts(source, changes);
  
  // Apply transformations to affected parts
  const transformedParts = affectedParts.map(part => 
    transformPart(part, target)
  );
  
  // Merge transformed parts back into the target
  return mergeParts(target, transformedParts);
}
```

### 3. Conflict Resolution

When conflicts arise between different representations:

```typescript
function resolveConflicts(sourceChanges, targetChanges) {
  // Identify conflicting changes
  const conflicts = findConflicts(sourceChanges, targetChanges);
  
  // Apply resolution strategies
  conflicts.forEach(conflict => {
    if (conflict.type === 'property') {
      // Property conflicts use latest change
      resolvePropertyConflict(conflict);
    } else if (conflict.type === 'structural') {
      // Structural conflicts may require user intervention
      promptUserForResolution(conflict);
    }
  });
  
  // Return resolved changes
  return mergeChanges(sourceChanges, targetChanges);
}
```

## Real-World Example: Full Cycle

Let's follow a complete cycle of bidirectional transformations:

1. **Start with Gherkin**:
```gherkin
Feature: Product Management
  Scenario: Sort products by price
    Given there are products with different prices
    When I sort products by "price" in "ascending" order
    Then I should see products in order from cheapest to most expensive
```

2. **Transform to Pragmatic Structure**:
```typescript
{
  type: "SortOperation",
  entity: "products",
  sort: {
    property: "price",
    direction: "ascending"
  },
  expectation: {
    result: "ordered",
    property: "price",
    direction: "ascending"
  }
}
```

3. **Generate Implementation Code**:
```typescript
// products.sort.by_price.ts
export default function sortProductsByPrice(context) {
  // Reactive implementation - no async/await
  // Create reactive sort criteria
  context.sortCriteria = { 
    property: 'price', 
    direction: context.params.direction || 'ascending'
  };
  
  // Sorted results are automatically computed
  // when either products or sortCriteria change
  context.sortedProducts = [...context.products].sort((a, b) => {
    const factor = context.sortCriteria.direction === 'ascending' ? 1 : -1;
    return (a.price - b.price) * factor;
  });
}
```

4. **Developer Modifies Implementation**:
```typescript
// Modified implementation with additional functionality
export default function sortProductsByPrice(context) {
  // Reactive implementation with added limit functionality
  context.sortCriteria = { 
    property: 'price', 
    direction: context.params.direction || 'ascending',
    limit: context.params.limit
  };
  
  // Sort products
  const sorted = [...context.products].sort((a, b) => {
    const factor = context.sortCriteria.direction === 'ascending' ? 1 : -1;
    return (a.price - b.price) * factor;
  });
  
  // Apply optional limit
  context.sortedProducts = context.sortCriteria.limit 
    ? sorted.slice(0, context.sortCriteria.limit) 
    : sorted;
}
```

5. **Update Pragmatic Structure**:
```typescript
{
  type: "SortOperation",
  entity: "products",
  sort: {
    property: "price",
    direction: "ascending"
  },
  limit: {
    type: "optional",
    value: "number"
  },
  expectation: {
    result: "ordered",
    property: "price",
    direction: "ascending"
  }
}
```

6. **Regenerate Gherkin**:
```gherkin
Feature: Product Management
  Scenario: Sort products by price
    Given there are products with different prices
    When I sort products by "price" in "ascending" order
    Then I should see products in order from cheapest to most expensive
    
  Scenario: Sort products by price with limit
    Given there are products with different prices
    When I sort products by "price" in "ascending" order with limit 5
    Then I should see the 5 cheapest products in order
```

This cycle demonstrates how changes in one format propagate to all others, maintaining consistency while allowing developers to work in their preferred format.

## Conclusion

Bidirectional transformations provide the foundation for our vision, enabling seamless workflows across different specification formats and implementation code. By maintaining consistency across all representations, they address the fundamental challenge of keeping specifications and implementation in sync throughout the development lifecycle.
