# Pragmatic Behavior Component (PBC) Pattern

## Overview

The Pragmatic Behavior Component (PBC) pattern represents a paradigm shift in how we think about software components. While traditional components focus on UI elements or technical functionality, PBCs encapsulate behavior patterns that can be reused, composed, and shared across projects. This document explores the PBC pattern, its structure, composition models, and integration with React.

## Core Principles

1. **Behavior First**: PBCs focus on behavior patterns rather than implementation details
2. **Composition Over Inheritance**: PBCs can be composed to create complex behaviors
3. **Separation of Concerns**: Behavior is separated from presentation
4. **Bidirectional Mapping**: PBCs map between natural language and implementation
5. **Reactive Architecture**: PBCs operate within a reactive system

## PBC Structure

A Pragmatic Behavior Component consists of:

```
┌─────────────────────────────────────────────┐
│ Pragmatic Behavior Component                │
├─────────────────────────────────────────────┤
│ ● Behavioral Pattern (what it does)         │
│ ● Linguistic Template (how it's expressed)  │
│ ● Implementation Pattern (how it's built)   │
│ ● Validation Rules (how it's verified)      │
│ ● Styling (how it looks)                    │
└─────────────────────────────────────────────┘
```

### Behavioral Pattern

The behavioral pattern defines what the component does in abstract terms:

```typescript
// FilterCollection behavioral pattern
{
  type: "FilterOperation",
  entity: "${entityName}",
  filter: {
    property: "${propertyName}",
    operator: "${operator}",
    value: "${value}"
  },
  result: {
    type: "filtered",
    entity: "${entityName}"
  }
}
```

### Linguistic Template

The linguistic template defines how the behavior is expressed in natural language:

```typescript
// FilterCollection linguistic template
{
  gherkin: {
    when: [
      "I filter ${entityName} by ${propertyName} ${operator} ${value}",
      "I search for ${entityName} with ${propertyName} ${operator} ${value}"
    ],
    then: [
      "I should see only ${entityName} where ${propertyName} ${operator} ${value}",
      "the results should only include ${entityName} where ${propertyName} ${operator} ${value}"
    ]
  }
}
```

### Implementation Pattern

The implementation pattern defines how the behavior is implemented in code:

```typescript
// FilterCollection implementation pattern
export default function filterCollection(context) {
  // Extract parameters
  const { property, operator, value } = context.params;
  
  // Set up reactive filter
  context.filters = { [property]: { operator, value } };
  
  // Apply filter (reactive)
  context.filteredItems = context.items.filter(item => {
    const itemValue = item[property];
    
    switch (operator) {
      case 'equals':
        return itemValue === value;
      case 'contains':
        return itemValue.includes(value);
      case 'greaterThan':
        return itemValue > value;
      case 'lessThan':
        return itemValue < value;
      default:
        return false;
    }
  });
}
```

### Validation Rules

The validation rules define how the behavior is verified:

```typescript
// FilterCollection validation rules
{
  rules: [
    {
      condition: "context.items.length > 0",
      message: "Collection must not be empty"
    },
    {
      condition: "context.params.property in context.items[0]",
      message: "Filter property must exist in collection items"
    },
    {
      condition: "['equals', 'contains', 'greaterThan', 'lessThan'].includes(context.params.operator)",
      message: "Operator must be one of: equals, contains, greaterThan, lessThan"
    }
  ]
}
```

### Styling (for UI Components)

For PBCs that have a UI representation, styling defines how they look:

```typescript
// FilterCollection styling
{
  styling: {
    container: "flex flex-col gap-2",
    filterBar: "flex items-center p-2 bg-gray-100 rounded",
    filterInput: "border rounded px-2 py-1 ml-2",
    resultList: "mt-4 divide-y divide-gray-200"
  }
}
```

## Composition Models

PBCs can be composed in several ways:

### 1. Sequential Composition

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Filter      │ ──► │ Sort        │ ──► │ Paginate    │
└─────────────┘     └─────────────┘     └─────────────┘
```

Example:
```typescript
// Sequential composition
const FilterSortPaginate = composeSequential([
  FilterCollection,
  SortCollection,
  PaginateCollection
]);
```

### 2. Nested Composition

```
┌───────────────────────────────────────┐
│ Authentication                         │
│  ┌────────────────────────────────┐   │
│  │ Authorization                  │   │
│  │  ┌─────────────────────────┐   │   │
│  │  │ Resource Access         │   │   │
│  │  └─────────────────────────┘   │   │
│  └────────────────────────────────┘   │
└───────────────────────────────────────┘
```

Example:
```typescript
// Nested composition
const SecureResourceAccess = composeNested({
  outer: Authentication,
  middle: Authorization,
  inner: ResourceAccess
});
```

### 3. Aspect-Oriented Composition

```
┌─────────────────────────────────────────┐
│ Logging Aspect                          │
└─────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│ Core Behavior                           │
└─────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│ Error Handling Aspect                   │
└─────────────────────────────────────────┘
```

Example:
```typescript
// Aspect-oriented composition
const LoggedSecureOperation = composeWithAspects(
  CoreOperation,
  [LoggingAspect, ErrorHandlingAspect]
);
```

## React Integration

PBCs can be seamlessly integrated with React components:

### 1. PBC as React Component

```tsx
// Using a PBC as a React component
const UserList = () => {
  // Component state and hooks
  const [users, setUsers] = useState([]);
  
  return (
    <FilterCollection
      items={users}
      entityName="users"
      properties={['name', 'email', 'role']}
      onFilterChange={(filteredUsers) => {
        // Handle filtered results
      }}
    />
  );
};
```

### 2. PBC as Higher-Order Component

```tsx
// Using a PBC as a higher-order component
const FilterableUserList = withFilterBehavior(UserList, {
  entityName: 'users',
  properties: ['name', 'email', 'role']
});
```

### 3. PBC as Custom Hook

```tsx
// Using a PBC as a custom hook
const UserList = () => {
  const [users, setUsers] = useState([]);
  
  const { 
    filters, 
    setFilters, 
    filteredItems 
  } = useFilterBehavior(users, {
    entityName: 'users',
    properties: ['name', 'email', 'role']
  });
  
  return (
    <div>
      <FilterControls 
        properties={['name', 'email', 'role']}
        filters={filters}
        onFilterChange={setFilters}
      />
      <UserTable users={filteredItems} />
    </div>
  );
};
```

### 4. PBC as Decorator

```tsx
// Using a PBC as a decorator
@FilterBehavior({
  entityName: 'users',
  properties: ['name', 'email', 'role']
})
class UserList extends React.Component {
  // Component implementation
}
```

## PBC Registry and Discovery

PBCs are organized in a registry for discovery and reuse:

```
┌─────────────────────┐     ┌─────────────────────┐
│ Global Registry     │     │ Project Registry    │
├─────────────────────┤     ├─────────────────────┤
│ Core PBCs           │     │ Project-specific    │
│ Community PBCs      │◄───►│ Module-specific     │
│ Industry Standards  │     │ Team-specific       │
└─────────────────────┘     └─────────────────────┘
```

The registry provides:
- Search and discovery
- Versioning and compatibility
- Documentation and examples
- Composition suggestions

## Example: Complete PBC Definition

Here's a complete example of a FilterCollection PBC:

```typescript
// FilterCollection PBC
export const FilterCollection = createPBC({
  name: 'FilterCollection',
  description: 'Filters a collection based on property values',
  
  // Behavioral pattern
  behavior: {
    type: "FilterOperation",
    entity: "${entityName}",
    filter: {
      property: "${propertyName}",
      operator: "${operator}",
      value: "${value}"
    },
    result: {
      type: "filtered",
      entity: "${entityName}"
    }
  },
  
  // Linguistic template
  linguistics: {
    gherkin: {
      when: [
        "I filter ${entityName} by ${propertyName} ${operator} ${value}",
        "I search for ${entityName} with ${propertyName} ${operator} ${value}"
      ],
      then: [
        "I should see only ${entityName} where ${propertyName} ${operator} ${value}",
        "the results should only include ${entityName} where ${propertyName} ${operator} ${value}"
      ]
    }
  },
  
  // Implementation pattern
  implementation: (context) => {
    // Extract parameters
    const { property, operator, value } = context.params;
    
    // Set up reactive filter
    context.filters = { [property]: { operator, value } };
    
    // Apply filter (reactive)
    context.filteredItems = context.items.filter(item => {
      const itemValue = item[property];
      
      switch (operator) {
        case 'equals':
          return itemValue === value;
        case 'contains':
          return itemValue.includes(value);
        case 'greaterThan':
          return itemValue > value;
        case 'lessThan':
          return itemValue < value;
        default:
          return false;
      }
    });
  },
  
  // Validation rules
  validation: {
    rules: [
      {
        condition: "context.items.length > 0",
        message: "Collection must not be empty"
      },
      {
        condition: "context.params.property in context.items[0]",
        message: "Filter property must exist in collection items"
      },
      {
        condition: "['equals', 'contains', 'greaterThan', 'lessThan'].includes(context.params.operator)",
        message: "Operator must be one of: equals, contains, greaterThan, lessThan"
      }
    ]
  },
  
  // React integration
  react: {
    component: ({ items, properties, onFilterChange }) => {
      // React component implementation
      const [property, setProperty] = useState(properties[0]);
      const [operator, setOperator] = useState('equals');
      const [value, setValue] = useState('');
      
      // Apply filter when inputs change
      useEffect(() => {
        const filteredItems = items.filter(item => {
          const itemValue = item[property];
          
          switch (operator) {
            case 'equals':
              return itemValue === value;
            case 'contains':
              return itemValue.includes(value);
            case 'greaterThan':
              return itemValue > value;
            case 'lessThan':
              return itemValue < value;
            default:
              return false;
          }
        });
        
        onFilterChange(filteredItems);
      }, [items, property, operator, value, onFilterChange]);
      
      return (
        <div className="flex flex-col gap-2">
          <div className="flex items-center p-2 bg-gray-100 rounded">
            <select 
              value={property} 
              onChange={e => setProperty(e.target.value)}
              className="border rounded px-2 py-1"
            >
              {properties.map(prop => (
                <option key={prop} value={prop}>{prop}</option>
              ))}
            </select>
            
            <select 
              value={operator} 
              onChange={e => setOperator(e.target.value)}
              className="border rounded px-2 py-1 ml-2"
            >
              <option value="equals">equals</option>
              <option value="contains">contains</option>
              <option value="greaterThan">greater than</option>
              <option value="lessThan">less than</option>
            </select>
            
            <input
              type="text"
              value={value}
              onChange={e => setValue(e.target.value)}
              className="border rounded px-2 py-1 ml-2"
              placeholder="Value"
            />
          </div>
        </div>
      );
    },
    
    // Higher-order component
    hoc: (Component, options) => {
      return (props) => {
        // HOC implementation
        const [filteredItems, setFilteredItems] = useState(props.items);
        
        return (
          <>
            <FilterCollection
              items={props.items}
              properties={options.properties}
              onFilterChange={setFilteredItems}
            />
            <Component {...props} items={filteredItems} />
          </>
        );
      };
    },
    
    // Custom hook
    hook: (items, options) => {
      const [filters, setFilters] = useState({});
      
      // Apply filters reactively
      const filteredItems = useMemo(() => {
        // Filter implementation
        return items.filter(item => {
          // Apply filters
          return true; // Simplified
        });
      }, [items, filters]);
      
      return { filters, setFilters, filteredItems };
    }
  },
  
  // Styling
  styling: {
    container: "flex flex-col gap-2",
    filterBar: "flex items-center p-2 bg-gray-100 rounded",
    filterInput: "border rounded px-2 py-1 ml-2",
    resultList: "mt-4 divide-y divide-gray-200"
  }
});
```

## Conclusion

The Pragmatic Behavior Component pattern represents a powerful approach to software development that bridges the gap between conceptual design and implementation. By encapsulating behavior patterns in reusable, composable components, PBCs enable developers to work at a higher level of abstraction while maintaining consistency across all representations. The integration with React demonstrates how PBCs can be seamlessly incorporated into existing frameworks and workflows.
