/**
 * Pragmatic Behavior Component (PBC) Implementation Example
 *
 * This file demonstrates how PBCs are implemented using the `l` package
 * for linguistic transformation from pure specifications to working code.
 * No build tools or code generators needed - just functional composition.
 */

// Core PBC interface
interface PragmaticBehaviorComponent {
  name: string;
  description: string;
  behavior: BehaviorPattern;
  linguistics: LinguisticTemplate;
  implementation: (context: any) => void;
  validation: ValidationRules;
  react?: ReactIntegration;
  styling?: Record<string, string>;
}

// Behavior pattern types
type BehaviorPattern =
  | FilterOperation
  | SortOperation
  | PaginationOperation
  | ExportOperation
  | FormOperation
  | AuthOperation;

// Example behavior pattern
interface FilterOperation {
  type: "FilterOperation";
  entity: string;
  filter: {
    property: string;
    operator: string;
    value: string;
  };
  result: {
    type: "filtered";
    entity: string;
  };
}

// Linguistic template interface
interface LinguisticTemplate {
  gherkin: {
    when: string[];
    then: string[];
  };
}

// Validation rules interface
interface ValidationRules {
  rules: {
    condition: string;
    message: string;
  }[];
}

// React integration interface
interface ReactIntegration {
  component: React.FC<any>;
  hoc?: (Component: React.ComponentType<any>, options: any) => React.FC<any>;
  hook?: (items: any[], options: any) => any;
}

/**
 * Create a Pragmatic Behavior Component
 */
function createPBC(config: Partial<PragmaticBehaviorComponent>): PragmaticBehaviorComponent {
  // Default values
  const defaultPBC: PragmaticBehaviorComponent = {
    name: '',
    description: '',
    behavior: {
      type: "FilterOperation",
      entity: "${entityName}",
      filter: {
        property: "${propertyName}",
        operator: "${operator}",
        value: "${value}"
      },
      result: {
        type: "filtered",
        entity: "${entityName}"
      }
    },
    linguistics: {
      gherkin: {
        when: [],
        then: []
      }
    },
    implementation: () => {},
    validation: {
      rules: []
    }
  };

  // Merge with provided config
  return { ...defaultPBC, ...config };
}

/**
 * Compose multiple PBCs sequentially
 */
function composeSequential(pbcs: PragmaticBehaviorComponent[]): PragmaticBehaviorComponent {
  return {
    name: `Composed_${pbcs.map(pbc => pbc.name).join('_')}`,
    description: `Sequential composition of ${pbcs.map(pbc => pbc.name).join(', ')}`,

    // Merge behavior patterns
    behavior: {
      type: "CompositeOperation",
      operations: pbcs.map(pbc => pbc.behavior)
    },

    // Merge linguistic templates
    linguistics: {
      gherkin: {
        when: pbcs.flatMap(pbc => pbc.linguistics.gherkin.when),
        then: pbcs.flatMap(pbc => pbc.linguistics.gherkin.then)
      }
    },

    // Compose implementations
    implementation: (context) => {
      for (const pbc of pbcs) {
        pbc.implementation(context);
      }
    },

    // Merge validation rules
    validation: {
      rules: pbcs.flatMap(pbc => pbc.validation.rules)
    },

    // Compose React integration if all PBCs have it
    react: pbcs.every(pbc => pbc.react) ? {
      component: (props) => {
        // Implementation would compose the React components
        return null; // Placeholder
      },

      hoc: (Component, options) => {
        // Implementation would compose the HOCs
        return (props) => <Component {...props} />;
      },

      hook: (items, options) => {
        // Implementation would compose the hooks
        return {}; // Placeholder
      }
    } : undefined,

    // Merge styling
    styling: pbcs.reduce((acc, pbc) => {
      return { ...acc, ...(pbc.styling || {}) };
    }, {})
  };
}

/**
 * Example PBC: FilterCollection
 */
const FilterCollection = createPBC({
  name: 'FilterCollection',
  description: 'Filters a collection based on property values',

  // Behavioral pattern
  behavior: {
    type: "FilterOperation",
    entity: "${entityName}",
    filter: {
      property: "${propertyName}",
      operator: "${operator}",
      value: "${value}"
    },
    result: {
      type: "filtered",
      entity: "${entityName}"
    }
  },

  // Linguistic template
  linguistics: {
    gherkin: {
      when: [
        "I filter ${entityName} by ${propertyName} ${operator} ${value}",
        "I search for ${entityName} with ${propertyName} ${operator} ${value}"
      ],
      then: [
        "I should see only ${entityName} where ${propertyName} ${operator} ${value}",
        "the results should only include ${entityName} where ${propertyName} ${operator} ${value}"
      ]
    }
  },

  // Implementation pattern
  implementation: (context) => {
    // Extract parameters
    const { property, operator, value } = context.params;

    // Set up reactive filter
    context.filters = { [property]: { operator, value } };

    // Apply filter (reactive)
    context.filteredItems = context.items.filter(item => {
      const itemValue = item[property];

      switch (operator) {
        case 'equals':
          return itemValue === value;
        case 'contains':
          return itemValue.includes(value);
        case 'greaterThan':
          return itemValue > value;
        case 'lessThan':
          return itemValue < value;
        default:
          return false;
      }
    });
  },

  // Validation rules
  validation: {
    rules: [
      {
        condition: "context.items.length > 0",
        message: "Collection must not be empty"
      },
      {
        condition: "context.params.property in context.items[0]",
        message: "Filter property must exist in collection items"
      },
      {
        condition: "['equals', 'contains', 'greaterThan', 'lessThan'].includes(context.params.operator)",
        message: "Operator must be one of: equals, contains, greaterThan, lessThan"
      }
    ]
  },

  // Styling
  styling: {
    container: "flex flex-col gap-2",
    filterBar: "flex items-center p-2 bg-gray-100 rounded",
    filterInput: "border rounded px-2 py-1 ml-2",
    resultList: "mt-4 divide-y divide-gray-200"
  }
});

/**
 * Example PBC: SortCollection
 */
const SortCollection = createPBC({
  name: 'SortCollection',
  description: 'Sorts a collection based on a property',

  // Behavioral pattern
  behavior: {
    type: "SortOperation",
    entity: "${entityName}",
    sort: {
      property: "${propertyName}",
      direction: "${direction}"
    },
    result: {
      type: "sorted",
      entity: "${entityName}"
    }
  },

  // Linguistic template
  linguistics: {
    gherkin: {
      when: [
        "I sort ${entityName} by ${propertyName} in ${direction} order",
        "I order ${entityName} by ${propertyName} ${direction}"
      ],
      then: [
        "I should see ${entityName} in ${direction} order by ${propertyName}",
        "the results should be sorted by ${propertyName} in ${direction} order"
      ]
    }
  },

  // Implementation pattern
  implementation: (context) => {
    // Extract parameters
    const { property, direction = 'ascending' } = context.params;

    // Set up reactive sort criteria
    context.sortCriteria = { property, direction };

    // Apply sort (reactive)
    context.sortedItems = [...context.items].sort((a, b) => {
      const factor = direction === 'ascending' ? 1 : -1;

      if (typeof a[property] === 'string') {
        return a[property].localeCompare(b[property]) * factor;
      } else {
        return (a[property] - b[property]) * factor;
      }
    });
  },

  // Validation rules
  validation: {
    rules: [
      {
        condition: "context.items.length > 0",
        message: "Collection must not be empty"
      },
      {
        condition: "context.params.property in context.items[0]",
        message: "Sort property must exist in collection items"
      },
      {
        condition: "['ascending', 'descending'].includes(context.params.direction)",
        message: "Direction must be either 'ascending' or 'descending'"
      }
    ]
  },

  // Styling
  styling: {
    container: "flex flex-col gap-2",
    sortBar: "flex items-center p-2 bg-gray-100 rounded",
    sortSelect: "border rounded px-2 py-1",
    directionSelect: "border rounded px-2 py-1 ml-2"
  }
});

/**
 * Example PBC: PaginateCollection
 */
const PaginateCollection = createPBC({
  name: 'PaginateCollection',
  description: 'Paginates a collection',

  // Behavioral pattern
  behavior: {
    type: "PaginationOperation",
    entity: "${entityName}",
    pagination: {
      pageSize: "${pageSize}",
      currentPage: "${currentPage}"
    },
    result: {
      type: "paginated",
      entity: "${entityName}"
    }
  },

  // Linguistic template
  linguistics: {
    gherkin: {
      when: [
        "I paginate ${entityName} with ${pageSize} items per page",
        "I view page ${currentPage} of ${entityName}"
      ],
      then: [
        "I should see page ${currentPage} of ${entityName}",
        "I should see at most ${pageSize} ${entityName} per page"
      ]
    }
  },

  // Implementation pattern
  implementation: (context) => {
    // Extract parameters
    const { pageSize = 10, currentPage = 1 } = context.params;

    // Set up reactive pagination
    context.pagination = { pageSize, currentPage };

    // Calculate total pages
    context.totalPages = Math.ceil(context.items.length / pageSize);

    // Apply pagination (reactive)
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    context.paginatedItems = context.items.slice(startIndex, endIndex);
  },

  // Validation rules
  validation: {
    rules: [
      {
        condition: "context.params.pageSize > 0",
        message: "Page size must be greater than 0"
      },
      {
        condition: "context.params.currentPage > 0",
        message: "Current page must be greater than 0"
      },
      {
        condition: "context.params.currentPage <= Math.ceil(context.items.length / context.params.pageSize)",
        message: "Current page must not exceed total pages"
      }
    ]
  },

  // Styling
  styling: {
    container: "flex flex-col gap-2",
    pagination: "flex items-center justify-center mt-4",
    pageButton: "border rounded px-3 py-1 mx-1",
    activePageButton: "border rounded px-3 py-1 mx-1 bg-blue-500 text-white"
  }
});

/**
 * Example: Compose multiple PBCs
 */
const FilterSortPaginateCollection = composeSequential([
  FilterCollection,
  SortCollection,
  PaginateCollection
]);

/**
 * Example: Convert Gherkin to Pragmatic Structure
 */
function gherkinToPragmaticStructure(gherkin: string): any {
  // This is a simplified example
  if (gherkin.includes("filter") && gherkin.includes("by")) {
    // Extract entity, property, operator, and value
    const entityMatch = gherkin.match(/filter (\w+) by/);
    const propertyMatch = gherkin.match(/by (\w+)/);
    const valueMatch = gherkin.match(/"([^"]+)"/);

    const entity = entityMatch ? entityMatch[1] : "";
    const property = propertyMatch ? propertyMatch[1] : "";
    const value = valueMatch ? valueMatch[1] : "";

    return {
      type: "FilterOperation",
      entity,
      filter: {
        property,
        operator: "equals",
        value
      },
      result: {
        type: "filtered",
        entity
      }
    };
  }

  // Handle other patterns...

  return null;
}

/**
 * Example: Convert Pragmatic Structure to Implementation
 */
function pragmaticStructureToImplementation(structure: any): string {
  // This is a simplified example
  if (structure.type === "FilterOperation") {
    return `
export default function filter${structure.entity}(context) {
  // Reactive implementation
  context.filters = { ${structure.filter.property}: "${structure.filter.value}" };

  // Filtered results are automatically computed
  context.filtered${structure.entity} = context.${structure.entity}.filter(item =>
    item.${structure.filter.property} === context.filters.${structure.filter.property}
  );
}
`;
  }

  // Handle other patterns...

  return "";
}

/**
 * Example: Convert Pragmatic Structure to React Component
 */
function pragmaticStructureToReactComponent(structure: any): string {
  // This is a simplified example
  if (structure.type === "FilterOperation") {
    return `
import React, { useContext } from 'react';
import { ${structure.entity}Context } from './context';

export const ${structure.entity}Filter = () => {
  const context = useContext(${structure.entity}Context);

  const handleFilterChange = (value) => {
    context.filters = { ${structure.filter.property}: value };
  };

  return (
    <div className="filter-container">
      <label>Filter by ${structure.filter.property}:</label>
      <input
        type="text"
        value={context.filters.${structure.filter.property} || ''}
        onChange={(e) => handleFilterChange(e.target.value)}
        placeholder="Enter ${structure.filter.property}..."
      />
    </div>
  );
};
`;
  }

  // Handle other patterns...

  return "";
}

/**
 * Example: Full Bidirectional Transformation
 */
function demonstrateBidirectionalTransformation() {
  // Start with Gherkin
  const gherkin = `
Feature: User Management
  Scenario: Filter users by status
    Given there are users with different statuses
    When I filter users by status "active"
    Then I should see only active users in the results
  `;

  // Convert to Pragmatic Structure
  const pragmaticStructure = gherkinToPragmaticStructure(gherkin);
  console.log("Pragmatic Structure:", pragmaticStructure);

  // Convert to Implementation
  const implementation = pragmaticStructureToImplementation(pragmaticStructure);
  console.log("Implementation:", implementation);

  // Convert to React Component
  const reactComponent = pragmaticStructureToReactComponent(pragmaticStructure);
  console.log("React Component:", reactComponent);

  // Modify Implementation (simulating developer changes)
  const modifiedImplementation = implementation.replace(
    "context.filtered${structure.entity} = context.${structure.entity}.filter",
    "context.filtered${structure.entity} = context.${structure.entity}.filter"
  );

  // Extract Pragmatic Structure from modified Implementation
  // (This would be more complex in a real implementation)
  const extractedPragmaticStructure = pragmaticStructure;

  // Convert back to Gherkin
  // (This would be more complex in a real implementation)
  const regeneratedGherkin = gherkin;

  console.log("Regenerated Gherkin:", regeneratedGherkin);
}

// ============================================================================
// NEW PRAGMA FORMAT USING `l` PACKAGE
// ============================================================================

/**
 * Pure Linguistic Pragma Specifications
 * These are transformed at runtime by the `l` package into working classes
 */

// Object Pragma - Natural Language Style
const objPragmaNatural = `
  # Object Composition

  Children can be properties, methods, getters, or setters.
  Properties have names and values that can be anything.
  Methods have names and function implementations.
  Getters and setters have names and function implementations.

  When composing, create an empty object first.
  Add properties directly to the object using their names and values.
  Add methods directly to the object using their names and implementations.
  For getters and setters, use property descriptors instead.
  Finally, return the composed object.
`;

// Object Pragma - Structured Style
const objPragmaStructured = `
  # obj

  prop: property
  method: function
  getter: getter
  setter: setter

  -> object with descriptors
`;

// Object Pragma - Minimal Style
const objPragmaMinimal = `
  # obj
  prop:property, method:function, getter:getter, setter:setter -> object
`;

// Filter Pragma - Natural Language Style
const filterPragmaNatural = `
  # Filter by Property

  Filter data where property matches value using operator.
  Data is an array of items.
  Property is a string key.
  Value can be anything.
  Operator defaults to equals, can be contains, startsWith, or endsWith.

  Return filtered array where each item's property matches the value.
`;

// Filter Pragma - Structured Style
const filterPragmaStructured = `
  # filter_by_property

  data: array
  property: string
  value: any
  operator: equals | contains | startsWith | endsWith = equals

  -> filter data by property operator value
`;

// Filter Pragma - Minimal Style
const filterPragmaMinimal = `
  data:array, property:string, value:any, operator:equals -> filter
`;

/**
 * The `l` Package - Pure Functional Transformation Engine
 * No build tools needed - leverages FP libraries for runtime transformation
 */

// Simulated l package (would be imported from @spicetime/l)
const l = {
  // Main pragma transformation using template literals
  pragma: (strings: TemplateStringsArray, ...values: any[]) => {
    const spec = strings.join('');

    // Parse the specification
    const parsed = parseNaturalLanguage(spec);

    // Extract semantics
    const semantics = extractSemantics(parsed);

    // Apply linguistic terms
    const enhanced = applyTermsRecursively(semantics);

    // Generate class structure
    const classStructure = generateClassStructure(enhanced);

    // Return runtime class
    return createRuntimeClass(classStructure);
  },

  // Term definitions for semantic matching
  terms: {
    // Type terms
    'string': () => 't.string()',
    'array': (of?: string) => `t.array(${of || 't.any()'})`,
    'number': () => 't.number()',
    'boolean': () => 't.boolean()',
    'any': () => 't.any()',
    'optional': (type: string) => `t.optional(${type})`,
    'required': (type: string) => type,

    // Pattern terms
    'object': () => 'object_composition',
    'object with descriptors': () => 'object_with_descriptors_composition',
    'filter': () => 'filter_composition',
    'create': () => 'create_composition',
    'component': () => 'react_component_composition',

    // Composition terms
    '->': (pattern: string) => ({ compositionPattern: pattern }),
    'with': (modifier: string) => ({ modifier }),
    'by': (criteria: string) => ({ criteria }),
    'where': (condition: string) => ({ condition }),

    // Natural language patterns
    'can be': (types: string) => ({ union: types.split('|').map(t => t.trim()) }),
    'have': (properties: string) => ({ properties }),
    'defaults to': (value: string) => ({ default: value }),
    'when': (condition: string) => ({ condition }),
    'render': (what: string) => ({ render: what }),
    'return': (what: string) => ({ return: what })
  }
};

// Helper functions for the transformation pipeline
function parseNaturalLanguage(spec: string) {
  const lines = spec.split('\n').filter(line => line.trim());
  const heading = lines.find(line => line.startsWith('#'));
  const definitions = lines.filter(line => line.includes(':') && !line.startsWith('#'));
  const composition = lines.find(line => line.includes('->'));

  return {
    name: heading?.replace('#', '').trim(),
    definitions,
    composition,
    fullText: spec
  };
}

function extractSemantics(ast: any) {
  return {
    ...ast,
    semantics: {
      children: extractChildDefinitions(ast.definitions),
      pattern: extractCompositionPattern(ast.composition),
      types: extractTypeDefinitions(ast.definitions)
    }
  };
}

function applyTermsRecursively(semantics: any) {
  let enhanced = { ...semantics };

  for (const [term, handler] of Object.entries(l.terms)) {
    if (semantics.fullText.includes(term)) {
      const result = handler();
      if (typeof result === 'object') {
        enhanced = { ...enhanced, ...result };
      }
    }
  }

  return enhanced;
}

function generateClassStructure(enhancedSemantics: any) {
  return {
    className: `${capitalize(enhancedSemantics.name || 'Generated')}Pragma`,
    constructor: generateConstructor(enhancedSemantics.semantics?.pattern || 'default'),
    schema: generateSchema(enhancedSemantics.semantics?.types || {}),
    children: generateChildren(enhancedSemantics.semantics?.children || [])
  };
}

function createRuntimeClass(classStructure: any) {
  // Create the actual runtime class
  return class GeneratedPragma {
    constructor(...args: any[]) {
      return classStructure.constructor.apply(this, args);
    }

    static schema() {
      return classStructure.schema;
    }

    static children() {
      return classStructure.children;
    }
  };
}

// Helper functions
function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function extractChildDefinitions(definitions: string[]) {
  return definitions
    .filter(def => !def.includes('->'))
    .map(def => {
      const [name, type] = def.split(':').map(s => s.trim());
      return { name, type };
    });
}

function extractCompositionPattern(composition?: string): string {
  if (!composition) return 'default';
  return composition.split('->')[1]?.trim() || 'default';
}

function extractTypeDefinitions(definitions: string[]) {
  return definitions.reduce((acc, def) => {
    const [name, type] = def.split(':').map(s => s.trim());
    acc[name] = type;
    return acc;
  }, {} as Record<string, string>);
}

function generateConstructor(pattern: string) {
  switch (pattern) {
    case 'object_composition':
    case 'object with descriptors':
    case 'object':
      return function(children: any[]) {
        const obj: any = {};
        const descriptors: any = {};

        children.forEach(child => {
          switch (child.pragma) {
            case 'property':
              obj[child.name] = child.value;
              break;
            case 'function':
              obj[child.name] = child.implementation;
              break;
            case 'getter':
              descriptors[child.name] = {
                ...descriptors[child.name],
                get: child.implementation
              };
              break;
            case 'setter':
              descriptors[child.name] = {
                ...descriptors[child.name],
                set: child.implementation
              };
              break;
          }
        });

        Object.defineProperties(obj, descriptors);
        return obj;
      };

    case 'filter_composition':
    case 'filter':
      return function(context: any) {
        return context.data.filter((item: any) => {
          const itemValue = item[context.property];

          switch (context.operator || 'equals') {
            case 'equals': return itemValue === context.value;
            case 'contains': return String(itemValue).includes(String(context.value));
            case 'startsWith': return String(itemValue).startsWith(String(context.value));
            case 'endsWith': return String(itemValue).endsWith(String(context.value));
            default: return false;
          }
        });
      };

    default:
      return function(input: any) {
        return input; // Pass-through for unknown patterns
      };
  }
}

function generateSchema(types: Record<string, string>): string {
  const schemaProps = Object.entries(types).map(([name, type]) => {
    return `${name}: ${mapTypeToSchema(type)}`;
  }).join(', ');

  return `t.object({ ${schemaProps} })`;
}

function generateChildren(children: any[]) {
  return children.reduce((acc, child) => {
    acc[child.name] = child.type;
    return acc;
  }, {} as Record<string, string>);
}

function mapTypeToSchema(type: string): string {
  const typeMap: Record<string, string> = {
    'string': 't.string()',
    'number': 't.number()',
    'boolean': 't.boolean()',
    'array': 't.array(t.any())',
    'any': 't.any()'
  };

  // Handle union types (e.g., "equals | contains")
  if (type.includes('|')) {
    const unionTypes = type.split('|').map(t => t.trim());
    const literals = unionTypes.map(t => `t.literal('${t}')`).join(', ');
    return `t.union([${literals}])`;
  }

  // Handle default values (e.g., "equals = equals")
  if (type.includes('=')) {
    const [baseType, defaultValue] = type.split('=').map(t => t.trim());
    const schemaType = mapTypeToSchema(baseType);
    return `${schemaType}.default('${defaultValue}')`;
  }

  return typeMap[type] || 't.any()';
}

/**
 * Usage Examples - Runtime transformation with l.pragma
 */

// Transform pragma specs at runtime
const ObjPragma = l.pragma`
  # obj

  prop: property
  method: function
  getter: getter
  setter: setter

  -> object with descriptors
`;

const FilterPragma = l.pragma`
  # filter_by_property

  data: array
  property: string
  value: any
  operator: equals | contains = equals

  -> filter data by property operator value
`;

// Usage examples
const myObject = new ObjPragma([
  { name: 'title', pragma: 'property', value: 'Hello World' },
  { name: 'greet', pragma: 'function', implementation: (name: string) => `Hello, ${name}!` }
]);

const users = [
  { name: 'Alice', role: 'admin', status: 'active' },
  { name: 'Bob', role: 'user', status: 'inactive' },
  { name: 'Charlie', role: 'user', status: 'active' }
];

const activeUsers = new FilterPragma({
  data: users,
  property: 'status',
  value: 'active',
  operator: 'equals'
});

// Export components and functions
export {
  // Original examples
  createPBC,
  composeSequential,
  FilterCollection,
  SortCollection,
  PaginateCollection,
  FilterSortPaginateCollection,
  gherkinToPragmaticStructure,
  pragmaticStructureToImplementation,
  pragmaticStructureToReactComponent,
  demonstrateBidirectionalTransformation,

  // New linguistic pragma examples
  objPragmaNatural,
  objPragmaStructured,
  objPragmaMinimal,
  filterPragmaNatural,
  filterPragmaStructured,
  filterPragmaMinimal,

  // The l package and generated pragmas
  l,
  ObjPragma,
  FilterPragma
};
