# stPragma Core System - Summary

## Overview

The stPragma Core System is the foundational mechanism that powers the SpiceTime architecture's pragmatic approach to code organization and composition. It transforms file extensions and directory structures into a rich, typed API that enables declarative programming through filesystem structure. Each API is a validatable schema with runtime type checking and validation capabilities. This concept builds upon and refines earlier ideas from concepts #23 (cat_types as universal type system), #61 (pragma ext syntax), and #58 (mapping on react patterns).

## Key Components

### 1. Scope Construction

stPragma builds hierarchical scopes that mirror the filesystem structure:

- Scopes contain terms that represent files, directories, and their exported values
- Terms are organized in a hierarchical namespace that mirrors the filesystem
- Each term in the scope includes static validation functions:
  - **is**: A static function returning a boolean that checks if a value is of the term's type
  - **validate**: A static function returning a boolean that performs deeper validation of a value
- Visibility rules control which terms are accessible to which parts of the codebase
- Private terms (prefixed with `_`) are only accessible within their own node
- Public terms are accessible to all downstream nodes

### 2. Pragma Operators

Pragma operators transform node scopes into node APIs:

- Operators are functions that take a node scope and return a node API
- Operators are composed based on file extensions
- Operators can be chained to create complex transformations
- Operators can be defined at any level of the hierarchy
- Operators can access the scope of their parent nodes
- Operators can define their own terms in the scope

### 3. Type System Integration

stPragma integrates deeply with the cat_types system to provide type safety across the entire architecture:

- Types are defined in `.type.ts` files, which contain both static TypeScript types and runtime types
- Static types are collected into a parallel `t` namespace that mirrors the term scope structure
- Runtime types are collected into a parallel `rt` namespace that mirrors the term scope structure
- Private (`_*.type.ts`) and public (`.type.ts`) type scopes are maintained separately
- `.pragma.type` files define both static and runtime types for pragmas
- Type definitions are primarily in `.pragma` nodes that define pragmas
- Other nodes only define types for non-pragmatic elements
- Type checking ensures correct composition of pragmas
- Namespaced interfaces enable dot notation access to nested structures
- Each type in the `rt` namespace includes runtime methods (`is`, `validate`, `create`)
- APIs are validatable schemas with runtime type checking

### 4. Context Management

stPragma provides a context management system for sharing state between nodes:

- Context is passed down the hierarchy from parent to child nodes
- Context can be modified by nodes at any level
- Context is accessible to all operators within a node
- Context can be used to share state between different parts of the application
- Context is typed based on the node's scope

### 5. Linguistic Integration

stPragma integrates with the linguistics system to enable linguistic programming:

- Linguistic literals (`l` tagged template literals) are parsed and interpreted
- Linguistic patterns are mapped to functional programming pipes
- Pragmatic extensions are translated into linguistic expressions
- Parse and interpret pragmatic extensions
- Translate between different syntactic forms
- Provide a rich vocabulary for expressing intent through filesystem structure

### 6. Runtime Type System

stPragma includes a comprehensive runtime type system that provides validation and creation capabilities for all types. This system is implemented through the `.type.ts` files, which define both static and runtime types:

```typescript
// In _.pragma.type.ts
// Static type definitions (t namespace)
export interface User {
  id: string;
  name: string;
  email: string;
}

export interface Component {
  name: string;
  props: Record<string, any>;
  render: () => any;
}

// Runtime type definitions (rt namespace)
export const User = rt.object({
  id: rt.string().min(1),
  name: rt.string().min(1),
  email: rt.string().email()
});

export const Component = rt.object({
  name: rt.string().min(1),
  props: rt.record(rt.string(), rt.any()),
  render: rt.function()
});
```

The runtime type system is based on a factory/component/instance pattern:

```typescript
// RTYPE is the factory for runtime types
class RTYPE {
  // Core type definitions
  string() { /* ... */ }
  number() { /* ... */ }
  boolean() { /* ... */ }
  object(shape) { /* ... */ }
  // Other type definitions...
  
  // Create a new rt instance
  extend(types) {
    const extended = new RTYPE();
    Object.assign(extended, this);
    Object.assign(extended, types);
    return extended;
  }
}

// rt is an instance of RTYPE
const rt = new RTYPE();

// Each node extends its parent's rt
function createScope(parentScope) {
  const scope = { ...parentScope };
  
  // Extend rt with new types
  if (parentScope?.rt) {
    scope.rt = parentScope.rt.extend({});
  } else {
    scope.rt = new RTYPE();
  }
  
  // Extend t with new types
  scope.t = { ...parentScope?.t };
  
  return scope;
}
```

Usage in a component file:

```typescript
// Using runtime types
const user = rt.User.create({
  name: 'John Doe',
  email: '<EMAIL>'
});

if (rt.User.validate(user)) {
  console.log('Valid user:', user);
} else {
  console.log('Invalid user');
}

// Type guard usage
function processData(data: unknown) {
  if (rt.User.is(data)) {
    // TypeScript knows data is a User
    console.log(data.name);
  } else if (rt.Component.is(data)) {
    // TypeScript knows data is a Component
    console.log(data.name);
  }
}
```

The runtime type system enables:

1. **Type Guards**: Check if values are of the correct type using `is` methods
2. **Deep Validation**: Validate values against business rules using `validate` methods
3. **Safe Creation**: Create valid instances of types with default values using `create` methods
4. **Schema Validation**: Validate data against schemas at runtime
5. **API Validation**: Ensure APIs conform to their specifications

## Applications

The stPragma system enables:

1. **Declarative API Design**: Define APIs through filesystem structure
2. **Composition Through Naming**: Compose functionality by naming files with pragmatic extensions
3. **Type-Safe Integration**: Ensure type safety across component boundaries
4. **Context-Aware Reactivity**: Build reactive systems that respond to context changes
5. **Dependency Injection**: Inject dependencies through the scope system
6. **Validatable Schemas**: Create APIs that are validatable schemas with runtime type checking
7. **Runtime Type Safety**: Validate data at runtime using `is`, `validate`, and `create` methods

## Implementation

The stPragma system is implemented as a set of TypeScript functions and types:

```typescript
/**
 * Creates a scope for a node
 * 
 * @param node - The node to create a scope for
 * @param parentScope - The parent scope
 * @returns The node scope
 */
function createScope(node: Node, parentScope?: Scope): Scope {
  // Implementation details...
}

/**
 * Creates a pragma operator
 * 
 * @param options - The operator options
 * @returns The pragma operator
 */
function createPragmaOperator(options: PragmaOperatorOptions): PragmaOperator {
  // Implementation details...
}

/**
 * Applies pragma operators to a node
 * 
 * @param node - The node to apply operators to
 * @param operators - The operators to apply
 * @returns The node API
 */
function applyPragmaOperators(node: Node, operators: PragmaOperator[]): NodeAPI {
  // Implementation details...
}
```

## Conclusion

The stPragma Core System provides a powerful foundation for the SpiceTime architecture, enabling declarative programming through filesystem structure, type-safe integration, and context-aware reactivity. By transforming file extensions and directory structures into a rich, typed API, it enables developers to express their intent through the organization of their code, rather than through explicit configuration or boilerplate.
