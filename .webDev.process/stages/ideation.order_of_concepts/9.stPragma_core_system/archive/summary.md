# stPragma Core System - Summary

## Overview

The stPragma Core System is the foundational mechanism that powers the SpiceTime architecture's pragmatic approach to code organization and composition. It transforms file extensions and directory structures into a rich, typed API that enables declarative programming through filesystem structure. Each API is a validatable schema with runtime type checking and validation capabilities. This concept builds upon and refines earlier ideas from concepts #23 (cat_types as universal type system), #61 (pragma ext syntax), and #58 (mapping on react patterns).

## Key Components

### 1. Scope Construction

stPragma builds and manages scopes that propagate through the filesystem structure:

- **Public Scope**: Accessible to all downstream nodes (no prefix)
- **Private Scope**: Accessible only to the local node (`_` prefix)
- **Protected Scope**: Accessible to local and direct child nodes (`.` prefix)

These scopes contain terms that represent files, directories, and their exported values, creating a hierarchical namespace that mirrors the filesystem structure. Each term in the scope includes static validation functions:

- **is**: A static function returning a boolean that checks if a value is of the term's type
- **validate**: A static function returning a boolean that performs deeper validation of a value

### 2. Pragma Operators

Pragma operators are functions that transform local node scopes into node APIs:

```typescript
type PragmaOperator<T extends NodeScope, R extends NodeAPI> = (scope: T) => R;
```

These operators are composed through functional programming patterns to create complex behaviors from simple building blocks.

### 3. Type System Integration

stPragma integrates deeply with the cat_types system to provide type safety across the entire architecture:

- Types are defined in `.type.ts` files
- Static types are collected into a parallel `t` namespace that mirrors the term scope structure
- Runtime types with validation methods are available in a parallel `rt` namespace
- Private (`_*.type.ts`) and public (`.type.ts`) type scopes are maintained separately
- Runtime types are defined in `_.pragma.rt.ts` files
- Runtime types are collected into a parallel `rt` namespace that mirrors the term scope structure
- Type checking ensures correct composition of pragmas
- Namespaced interfaces enable dot notation access to nested structures
- Pragma types expose their children as namespaces with dot notation access
- Each type in the `rt` namespace includes runtime methods (`is`, `validate`, `create`)
- APIs are validatable schemas with runtime type checking

### 4. Context Management

stPragma provides a context mechanism that allows for:

- Sharing state between nodes
- Reactive updates when state changes
- Context inheritance through the scope hierarchy

### 5. Dependency Management

Dependencies are managed through:

- Explicit declarations in `_.deps.ts` or `_.deps.json` files
- Automatic synchronization with package.json
- Scoped visibility based on prefix conventions

## Implementation Approach

The stPragma system is implemented as a decorator function that:

1. Wraps module functions to inject scopes
2. Manages two-phase invocation for proper context handling
3. Provides access to both public and private APIs
4. Integrates with the filesystem to build scopes from directory structures

```typescript
export function stPragma(options: {
    stage?: 'ideation' | 'design' | 'production';
    metaSync?: boolean;
}) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        // Implementation details...
    };
}
```

## Relationship to Other Components

### forestry_cat_types

stPragma uses forestry_cat_types to:
- Represent scopes as trees in a forest
- Manage relationships between different parts of the codebase
- Provide categorical operations on the scope structure

### linguistics

stPragma leverages linguistics to:
- Parse and interpret pragmatic extensions
- Translate between different syntactic forms
- Provide a rich vocabulary for expressing intent through filesystem structure

### 6. Runtime Type System

stPragma includes a comprehensive runtime type system that provides validation and creation capabilities for all types. This system is implemented through the `_.pragma.rt` pragma, which defines runtime types for a node:

```typescript
// Static type definitions in the t namespace
namespace t {
  export interface User {
    id: string;
    name: string;
    email: string;
  }

  export interface Component {
    name: string;
    props: Record<string, any>;
    render: () => any;
  }
}

// Runtime type operations in the T namespace
namespace T {
  export const User = {
    is(value: any): value is t.User {
      return typeof value === 'object' && value !== null &&
             typeof value.id === 'string' &&
             typeof value.name === 'string' &&
             typeof value.email === 'string';
    },
    validate(value: any): boolean {
      if (!T.User.is(value)) {
        return false;
      }

      // Additional validation logic
      if (value.id.length === 0) {
        return false;
      }

      if (value.name.length === 0) {
        return false;
      }

      if (!value.email.includes('@')) {
        return false;
      }

      return true;
    },
    create(props: Partial<t.User>): t.User {
      // Default values
      const defaults: t.User = {
        id: '',
        name: '',
        email: ''
      };

      // Merge defaults with provided props
      const user = { ...defaults, ...props };

      // Validate the created user
      if (!T.User.validate(user)) {
        throw new Error('Invalid User created');
      }

      return user;
    }
  };

  export const Component = {
    is(value: any): value is t.Component {
      return typeof value === 'object' && value !== null &&
             typeof value.name === 'string' &&
             typeof value.props === 'object' &&
             typeof value.render === 'function';
    },
    validate(value: any): boolean {
      if (!T.Component.is(value)) {
        return false;
      }

      // Additional validation logic
      if (value.name.length === 0) {
        return false;
      }

      return true;
    },
    create(props: Partial<t.Component>): t.Component {
      // Default values
      const defaults: t.Component = {
        name: '',
        props: {},
        render: () => null
      };

      // Merge defaults with provided props
      const component = { ...defaults, ...props };

      // Validate the created component
      if (!T.Component.validate(component)) {
        throw new Error('Invalid Component created');
      }

      return component;
    }
  };
}
```

The runtime type system enables:

1. **Type Guards**: Check if values are of the correct type using `is` methods
2. **Deep Validation**: Validate values against business rules using `validate` methods
3. **Safe Creation**: Create valid instances of types with default values using `create` methods
4. **Schema Validation**: Validate data against schemas at runtime
5. **API Validation**: Ensure APIs conform to their specifications

## Applications

The stPragma system enables:

1. **Declarative API Design**: Define APIs through filesystem structure
2. **Composition Through Naming**: Compose functionality by naming files with pragmatic extensions
3. **Type-Safe Integration**: Ensure type safety across component boundaries
4. **Context-Aware Reactivity**: Build reactive systems that respond to context changes
5. **Dependency Injection**: Inject dependencies through the scope system
6. **Validatable Schemas**: Create APIs that are validatable schemas with runtime type checking
7. **Runtime Type Safety**: Validate data at runtime using `is`, `validate`, and `create` methods

## Future Directions

1. **Rust Implementation**: Develop a high-performance Rust implementation
2. **WebAssembly Integration**: Enable cross-language interoperability through WebAssembly
3. **IDE Integration**: Create IDE plugins for visualizing and manipulating pragma structures
4. **Automated Testing**: Generate tests from pragma specifications
5. **Documentation Generation**: Generate documentation from pragma structures

## Conclusion

The stPragma Core System provides the foundation for the SpiceTime architecture's approach to code organization and composition. By leveraging file extensions and directory structures as a declarative programming interface, it enables powerful patterns while maintaining type safety and performance.
