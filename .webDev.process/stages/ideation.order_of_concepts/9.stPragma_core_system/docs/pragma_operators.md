# Pragma Operators in stPragma

## Overview

Pragma operators are the core functions that transform local node scopes into node APIs in the stPragma system. They encapsulate the logic for interpreting pragmatic extensions and composing functionality based on filesystem structure. This document explains the concept of pragma operators, their composition patterns, and how they are used in the stPragma system.

## Pragma Operator Definition

A pragma operator is a function that takes a node scope as input and returns a node API:

```typescript
type PragmaOperator<T extends NodeScope, R extends NodeAPI> = (scope: T) => R;
```

Pragma operators are defined in `_.pragma.ts` files and are used to generate the API for a node based on its scope.

## Composition Patterns

Pragma operators can be composed in several ways to create complex behaviors from simple building blocks:

### Chaining

Chaining involves sequential application of pragma operators:

```typescript
function chainPragmas<T, U, V>(
  first: PragmaOperator<T, U>,
  second: PragmaOperator<U, V>
): PragmaOperator<T, V> {
  return (scope: T) => second(first(scope));
}
```

Example usage:

```typescript
// Define pragma operators
const objPragma = createObjectPragma();
const propPragma = createPropertyPragma();

// Chain them together
const objPropPragma = chainPragmas(objPragma, propPragma);

// Apply the chained pragma
const api = objPropPragma(scope);
```

### Mixing

Mixing involves combining the results of multiple pragma operators:

```typescript
function mixPragmas<T, U>(
  pragmas: PragmaOperator<T, U>[]
): PragmaOperator<T, U> {
  return (scope: T) => {
    const results = pragmas.map(pragma => pragma(scope));
    return Object.assign({}, ...results);
  };
}
```

Example usage:

```typescript
// Define pragma operators
const statePragma = createStatePragma();
const propsPragma = createPropsPragma();
const methodsPragma = createMethodsPragma();

// Mix them together
const componentPragma = mixPragmas([statePragma, propsPragma, methodsPragma]);

// Apply the mixed pragma
const api = componentPragma(scope);
```

### Inheritance

Inheritance involves extending a base pragma operator with additional functionality:

```typescript
function extendPragma<T, U>(
  base: PragmaOperator<T, U>,
  extension: (api: U, scope: T) => U
): PragmaOperator<T, U> {
  return (scope: T) => {
    const baseApi = base(scope);
    return extension(baseApi, scope);
  };
}
```

Example usage:

```typescript
// Define base pragma operator
const basePragma = createBasePragma();

// Define extension
const extension = (api, scope) => {
  // Add additional functionality
  api.newMethod = () => {
    // Implementation
  };
  return api;
};

// Extend the base pragma
const extendedPragma = extendPragma(basePragma, extension);

// Apply the extended pragma
const api = extendedPragma(scope);
```

## Common Pragma Operators

The stPragma system includes several common pragma operators:

### Object Pragma

Creates an object from child nodes with property pragmas:

```typescript
function createObjectPragma(): PragmaOperator<NodeScope, Record<string, any>> {
  return (scope: NodeScope) => {
    const obj = {};
    
    // Find all child nodes with property pragmas
    const propNodes = scope.children.filter(child => 
      child.pragmas.includes('prop')
    );
    
    // Add properties to the object
    for (const propNode of propNodes) {
      const propName = propNode.name;
      Object.defineProperty(obj, propName, {
        get: () => propNode.api,
        enumerable: true,
        configurable: false
      });
    }
    
    return obj;
  };
}
```

### Component Pragma

Creates a React-like component from a node:

```typescript
function createComponentPragma(): PragmaOperator<NodeScope, Component> {
  return (scope: NodeScope) => {
    // Extract props, state, and methods from scope
    const props = extractProps(scope);
    const state = extractState(scope);
    const methods = extractMethods(scope);
    
    // Create the component function
    const component = (componentProps: any) => {
      // Implementation
    };
    
    // Add static properties
    component.displayName = scope.name;
    component.propTypes = props.types;
    component.defaultProps = props.defaults;
    
    return component;
  };
}
```

### Context Pragma

Creates a context provider and consumer:

```typescript
function createContextPragma(): PragmaOperator<NodeScope, Context> {
  return (scope: NodeScope) => {
    // Create context object
    const context = {
      Provider: (props: any) => {
        // Implementation
      },
      Consumer: (props: any) => {
        // Implementation
      },
      useContext: () => {
        // Implementation
      }
    };
    
    return context;
  };
}
```

## Pragma Operator Implementation

Pragma operators are implemented in `_.pragma.ts` files:

```typescript
// _.pragma.ts
export default function(scope: NodeScope): NodeAPI {
  // Determine which pragma to use based on file extensions
  const pragmas = determinePragmas(scope);
  
  // Compose pragma operators
  const composedPragma = composePragmas(pragmas);
  
  // Apply the composed pragma
  return composedPragma(scope);
}
```

## Integration with linguistics

Pragma operators integrate with the linguistics package to parse and interpret pragmatic extensions:

```typescript
import { parse } from '@future/linguistics';

function determinePragmas(scope: NodeScope): PragmaOperator[] {
  // Get file extensions
  const extensions = scope.file.name.split('.').slice(1);
  
  // Parse extensions using linguistics
  const pragmaTerms = parse(extensions.join('_'));
  
  // Map terms to pragma operators
  return pragmaTerms.map(term => getPragmaOperator(term.name));
}
```

## Conclusion

Pragma operators are the core functions that transform local node scopes into node APIs in the stPragma system. By composing pragma operators in different ways, developers can create complex behaviors from simple building blocks, enabling a declarative approach to API design through filesystem structure.
