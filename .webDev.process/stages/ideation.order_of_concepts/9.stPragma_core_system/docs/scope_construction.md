# Scope Construction in stPragma

## Overview

Scope construction is a fundamental aspect of the stPragma system. It defines how terms from files and directories are organized into hierarchical namespaces that propagate through the filesystem structure. This document explains the scope construction process, visibility rules, and how scopes are used in the stPragma system.

## Scope Types

stPragma defines three types of scopes with different visibility rules:

### Public Scope

- **Prefix**: None (e.g., `file.ts`)
- **Visibility**: Accessible to all downstream nodes
- **Purpose**: Expose API to all child nodes

Public scope terms propagate down through the entire subtree, making them accessible to all descendant nodes.

### Private Scope

- **Prefix**: `_` (e.g., `_file.ts`)
- **Visibility**: Accessible only to the local node
- **Purpose**: Internal implementation details

Private scope terms are only accessible within the current node and are not propagated to child nodes.

### Protected Scope

- **Prefix**: `.` (e.g., `.file.ts`)
- **Visibility**: Accessible to local and direct child nodes
- **Purpose**: Limited API exposure

Protected scope terms are accessible within the current node and its immediate children, but not to deeper descendants.

## Scope Construction Process

The scope construction process involves several steps:

1. **File Discovery**: Scan the filesystem to identify all files and directories
2. **Term Extraction**: Extract terms from files based on their exports
3. **Scope Assignment**: Assign terms to scopes based on filename prefixes
4. **Scope Propagation**: Propagate scopes through the filesystem hierarchy
5. **Scope Injection**: Inject scopes into module globals

### File Discovery

The file discovery process scans the filesystem to identify all files and directories:

```typescript
function discoverFiles(rootDir: string): FileInfo[] {
  // Implementation details...
}
```

### Term Extraction

Terms are extracted from files based on their exports:

```typescript
function extractTerms(file: FileInfo): Term[] {
  // Implementation details...
}
```

### Scope Assignment

Terms are assigned to scopes based on filename prefixes:

```typescript
function assignToScope(term: Term, filename: string): Scope {
  if (filename.startsWith('_')) {
    return 'private';
  } else if (filename.startsWith('.')) {
    return 'protected';
  } else {
    return 'public';
  }
}
```

### Scope Propagation

Scopes are propagated through the filesystem hierarchy:

```typescript
function propagateScopes(node: Node): void {
  // Add public scope terms from parent to current node's public scope
  node.publicScope = { ...node.parent.publicScope, ...node.publicScope };
  
  // Add protected scope terms from parent to current node's protected scope
  // (but only for direct children)
  if (node.parent) {
    node.protectedScope = { ...node.parent.protectedScope, ...node.protectedScope };
  }
  
  // Propagate to children
  for (const child of node.children) {
    propagateScopes(child);
  }
}
```

### Scope Injection

Scopes are injected into module globals:

```typescript
function injectScope(module: Module, scope: Scope): void {
  // Implementation details...
}
```

## Numerable and Innumerable Terms

In addition to scope visibility, terms can be numerable or innumerable:

### Numerable Terms

- **Prefix**: None (e.g., `file.ts`)
- **Behavior**: Included in `Object.keys()` and `for...in` loops
- **Purpose**: Expose API for iteration and reflection

### Innumerable Terms

- **Prefix**: `_` (e.g., `_file.ts`)
- **Behavior**: Not included in `Object.keys()` and `for...in` loops
- **Purpose**: Internal implementation details

## Scope Namespaces

Scopes can contain specialized namespaces for specific purposes:

### Context Namespace

- **Purpose**: Share state between nodes
- **Access**: `scope.context`
- **Example**: `scope.context.user`

### Dependencies Namespace

- **Purpose**: Access external dependencies
- **Access**: `scope.deps`
- **Example**: `scope.deps.lodash`

### Types Namespace

- **Purpose**: Access type definitions
- **Access**: `scope.t`
- **Example**: `scope.t.User`

## Integration with forestry_cat_types

Scopes are implemented using the forestry_cat_types package:

```typescript
import { createScopedTree } from '@future/forestry_cat_types';

// Create a scoped tree for a node
const nodeTree = createScopedTree('node', category, {});

// Create scopes
const publicScope = nodeTree.createScope('public');
const privateScope = nodeTree.createScope('private');
const protectedScope = nodeTree.createScope('protected');

// Add terms to scopes
publicScope.define('api', apiTerm);
privateScope.define('internal', internalTerm);
protectedScope.define('limited', limitedTerm);
```

## Conclusion

Scope construction is a fundamental aspect of the stPragma system that enables hierarchical organization of terms with different visibility rules. By leveraging file naming conventions and filesystem structure, it provides a declarative way to define APIs and control access to implementation details.
