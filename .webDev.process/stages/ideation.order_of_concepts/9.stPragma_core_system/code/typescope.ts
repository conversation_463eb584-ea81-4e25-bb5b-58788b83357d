/**
 * TypeScope in stPragma
 *
 * This file demonstrates the TypeScope system that provides a structured way to access types
 * throughout the filesystem hierarchy.
 */

/**
 * TypeScope interface
 */
interface TypeScope {
  public: Record<string, any>;
  private: Record<string, any>;
}

/**
 * Node interface
 */
interface Node {
  id: string;
  name: string;
  parent?: Node;
  children: Node[];
  scope: Record<string, any>;
  typeScope: TypeScope;
  isPrivate: boolean;
  t: Record<string, any>;
}

/**
 * Module interface
 */
interface Module {
  id: string;
  exports: Record<string, any>;
  scope: Record<string, any>;
  t: Record<string, any>;
  isPrivate: boolean;
}

/**
 * Creates a TypeScope
 *
 * @returns A TypeScope
 */
function createTypeScope(): TypeScope {
  return {
    public: {},
    private: {}
  };
}

/**
 * Collects types from files
 *
 * @param rootDir - The root directory
 * @returns A TypeScope
 */
function collectTypes(rootDir: string): TypeScope {
  const typeScope = createTypeScope();

  // Simulate finding files
  const publicTypeFiles = [
    `${rootDir}/Button.type.ts`,
    `${rootDir}/User.type.ts`
  ];

  const privateTypeFiles = [
    `${rootDir}/_Button.type.ts`,
    `${rootDir}/_pragma.type.ts`
  ];

  // Simulate extracting types from public files
  for (const file of publicTypeFiles) {
    if (file.endsWith('Button.type.ts')) {
      typeScope.public.ButtonProps = {
        name: 'ButtonProps',
        properties: {
          onClick: { type: 'function', optional: true },
          disabled: { type: 'boolean', optional: true },
          variant: { type: 'string', optional: true }
        }
      };
    } else if (file.endsWith('User.type.ts')) {
      typeScope.public.User = {
        name: 'User',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          email: { type: 'string' }
        }
      };
    }
  }

  // Simulate extracting types from private files
  for (const file of privateTypeFiles) {
    if (file.endsWith('_Button.type.ts')) {
      typeScope.private.ButtonState = {
        name: 'ButtonState',
        properties: {
          isHovered: { type: 'boolean' },
          isPressed: { type: 'boolean' },
          isFocused: { type: 'boolean' }
        }
      };
    } else if (file.endsWith('_pragma.type.ts')) {
      typeScope.private.pragma = {
        name: 'PragmaType',
        properties: {
          name: { type: 'string' },
          transform: { type: 'function' },
          children: { type: 'object' }
        },
        children: {
          component: {
            name: 'ComponentPragma',
            properties: {
              name: { type: 'string' },
              transform: { type: 'function' },
              children: { type: 'object' }
            },
            children: {
              withState: {
                name: 'StatePragma',
                properties: {
                  name: { type: 'string' },
                  transform: { type: 'function' },
                  children: { type: 'object' }
                },
                children: {
                  styled: {
                    name: 'StyledPragma',
                    properties: {
                      name: { type: 'string' },
                      transform: { type: 'function' },
                      children: { type: 'object' }
                    }
                  }
                }
              }
            }
          }
        }
      };
    }
  }

  return typeScope;
}

/**
 * Propagates types through the filesystem hierarchy
 *
 * @param node - The node to propagate types to
 */
function propagateTypes(node: Node): void {
  if (node.parent) {
    // Add public types from parent to current node's public types
    node.typeScope.public = { ...node.parent.typeScope.public, ...node.typeScope.public };
  }

  // Propagate to children
  for (const child of node.children) {
    propagateTypes(child);
  }
}

/**
 * Injects types into a module
 *
 * @param module - The module to inject types into
 * @param typeScope - The TypeScope to inject
 */
function injectTypes(module: Module, typeScope: TypeScope): void {
  // Inject public types
  module.t = { ...typeScope.public };

  // Inject private types if this is a private module
  if (module.isPrivate) {
    module.t = { ...module.t, ...typeScope.private };
  }

  // Add runtime type methods to each type
  addRuntimeTypeMethods(module.t);
}

/**
 * Adds runtime type methods to types
 *
 * @param types - The types to add methods to
 */
function addRuntimeTypeMethods(types: Record<string, any>): void {
  for (const [name, type] of Object.entries(types)) {
    if (typeof type === 'object' && type !== null) {
      // Add is method
      type.is = function(value: any): boolean {
        // Basic type checking based on the type's properties
        if (typeof value !== 'object' || value === null) {
          return false;
        }

        // Check if all required properties exist
        for (const propName of Object.keys(type.properties || {})) {
          const prop = type.properties[propName];
          if (!prop.optional && !(propName in value)) {
            return false;
          }
        }

        return true;
      };

      // Add validate method
      type.validate = function(value: any): boolean {
        if (!type.is(value)) {
          return false;
        }

        // Deep validation of properties
        for (const [propName, propValue] of Object.entries(value)) {
          const propType = type.properties?.[propName];
          if (!propType) {
            continue;
          }

          // Validate property value based on its type
          if (propType.type === 'string' && typeof propValue !== 'string') {
            return false;
          } else if (propType.type === 'number' && typeof propValue !== 'number') {
            return false;
          } else if (propType.type === 'boolean' && typeof propValue !== 'boolean') {
            return false;
          } else if (propType.type === 'function' && typeof propValue !== 'function') {
            return false;
          } else if (propType.type === 'object' && (typeof propValue !== 'object' || propValue === null)) {
            return false;
          }
        }

        return true;
      };

      // Add create method
      type.create = function(props: Record<string, any> = {}): any {
        // Create default values based on properties
        const defaults: Record<string, any> = {};

        for (const [propName, propType] of Object.entries(type.properties || {})) {
          if (propType.type === 'string') {
            defaults[propName] = '';
          } else if (propType.type === 'number') {
            defaults[propName] = 0;
          } else if (propType.type === 'boolean') {
            defaults[propName] = false;
          } else if (propType.type === 'function') {
            defaults[propName] = () => {};
          } else if (propType.type === 'object') {
            defaults[propName] = {};
          } else {
            defaults[propName] = null;
          }
        }

        // Merge defaults with provided props
        const result = { ...defaults, ...props };

        // Validate the created object
        if (!type.validate(result)) {
          throw new Error(`Invalid ${name} created`);
        }

        return result;
      };

      // Recursively add methods to nested types
      if (type.children) {
        addRuntimeTypeMethods(type.children);
      }
    }
  }
}

/**
 * Simulates pragma type scoping
 *
 * @param parentNode - The parent node
 * @param childNode - The child node
 */
function simulatePragmaTypeScoping(parentNode: Node, childNode: Node): void {
  // Clone parent's t object
  childNode.t = { ...parentNode.t };

  // Add child's types
  Object.assign(childNode.t, childNode.typeScope.public);

  // Add private types if this is a private node
  if (childNode.isPrivate) {
    Object.assign(childNode.t, childNode.typeScope.private);
  }
}

/**
 * Example usage
 */
function exampleUsage() {
  // Create nodes
  const rootNode: Node = {
    id: 'root',
    name: 'Root',
    children: [],
    scope: {},
    typeScope: createTypeScope(),
    isPrivate: false,
    t: {}
  };

  const componentNode: Node = {
    id: 'component',
    name: 'Component',
    parent: rootNode,
    children: [],
    scope: {},
    typeScope: createTypeScope(),
    isPrivate: false,
    t: {}
  };

  const buttonNode: Node = {
    id: 'button',
    name: 'Button',
    parent: componentNode,
    children: [],
    scope: {},
    typeScope: createTypeScope(),
    isPrivate: false,
    t: {}
  };

  const privateButtonNode: Node = {
    id: 'privateButton',
    name: '_Button',
    parent: componentNode,
    children: [],
    scope: {},
    typeScope: createTypeScope(),
    isPrivate: true,
    t: {}
  };

  // Add children
  rootNode.children.push(componentNode);
  componentNode.children.push(buttonNode, privateButtonNode);

  // Collect types
  rootNode.typeScope = collectTypes('/root');
  componentNode.typeScope = collectTypes('/root/component');
  buttonNode.typeScope = collectTypes('/root/component/button');
  privateButtonNode.typeScope = collectTypes('/root/component/_button');

  // Propagate types
  propagateTypes(rootNode);

  // Simulate pragma type scoping
  simulatePragmaTypeScoping(rootNode, componentNode);
  simulatePragmaTypeScoping(componentNode, buttonNode);
  simulatePragmaTypeScoping(componentNode, privateButtonNode);

  // Create modules
  const rootModule: Module = {
    id: 'root',
    exports: {},
    scope: rootNode.scope,
    t: {},
    isPrivate: false
  };

  const componentModule: Module = {
    id: 'component',
    exports: {},
    scope: componentNode.scope,
    t: {},
    isPrivate: false
  };

  const buttonModule: Module = {
    id: 'button',
    exports: {},
    scope: buttonNode.scope,
    t: {},
    isPrivate: false
  };

  const privateButtonModule: Module = {
    id: 'privateButton',
    exports: {},
    scope: privateButtonNode.scope,
    t: {},
    isPrivate: true
  };

  // Inject types
  injectTypes(rootModule, rootNode.typeScope);
  injectTypes(componentModule, componentNode.typeScope);
  injectTypes(buttonModule, buttonNode.typeScope);
  injectTypes(privateButtonModule, privateButtonNode.typeScope);

  // Example usage in modules

  // Root module
  console.log('Root module types:');
  console.log(rootModule.t.User);
  console.log(rootModule.t.ButtonProps);

  // Component module
  console.log('Component module types:');
  console.log(componentModule.t.User);
  console.log(componentModule.t.ButtonProps);

  // Button module
  console.log('Button module types:');
  console.log(buttonModule.t.User);
  console.log(buttonModule.t.ButtonProps);

  // Private Button module
  console.log('Private Button module types:');
  console.log(privateButtonModule.t.User);
  console.log(privateButtonModule.t.ButtonProps);
  console.log(privateButtonModule.t.ButtonState);
  console.log(privateButtonModule.t.pragma.component.withState.styled);

  // Example of using runtime type methods

  // Using User type
  const user = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>'
  };

  // Check if user is a User
  const isUser = privateButtonModule.t.User.is(user);
  console.log('Is user a User?', isUser);

  // Validate user
  const isValidUser = privateButtonModule.t.User.validate(user);
  console.log('Is user a valid User?', isValidUser);

  // Create a new User
  const newUser = privateButtonModule.t.User.create({
    id: '2',
    name: 'Jane Doe',
    email: '<EMAIL>'
  });
  console.log('New User:', newUser);

  // Using ButtonProps type
  const buttonProps = {
    onClick: () => console.log('Clicked'),
    disabled: false,
    variant: 'primary'
  };

  // Check if buttonProps is a ButtonProps
  const isButtonProps = privateButtonModule.t.ButtonProps.is(buttonProps);
  console.log('Is buttonProps a ButtonProps?', isButtonProps);

  // Validate buttonProps
  const isValidButtonProps = privateButtonModule.t.ButtonProps.validate(buttonProps);
  console.log('Is buttonProps a valid ButtonProps?', isValidButtonProps);

  // Create new ButtonProps
  const newButtonProps = privateButtonModule.t.ButtonProps.create({
    onClick: () => console.log('New button clicked'),
    variant: 'secondary'
  });
  console.log('New ButtonProps:', newButtonProps);

  // Using ButtonState type (only in private module)
  const buttonState = {
    isHovered: false,
    isPressed: false,
    isFocused: false
  };

  // Check if buttonState is a ButtonState
  const isButtonState = privateButtonModule.t.ButtonState.is(buttonState);
  console.log('Is buttonState a ButtonState?', isButtonState);

  // Validate buttonState
  const isValidButtonState = privateButtonModule.t.ButtonState.validate(buttonState);
  console.log('Is buttonState a valid ButtonState?', isValidButtonState);

  // Create new ButtonState
  const newButtonState = privateButtonModule.t.ButtonState.create({
    isHovered: true
  });
  console.log('New ButtonState:', newButtonState);

  // Using pragma types (only in private module)
  const componentPragma = {
    name: 'component',
    transform: (node: any) => ({ ...node, component: true }),
    children: {}
  };

  // Check if componentPragma is a ComponentPragma
  const isComponentPragma = privateButtonModule.t.pragma.component.is(componentPragma);
  console.log('Is componentPragma a ComponentPragma?', isComponentPragma);

  // Validate componentPragma
  const isValidComponentPragma = privateButtonModule.t.pragma.component.validate(componentPragma);
  console.log('Is componentPragma a valid ComponentPragma?', isValidComponentPragma);

  // Create new ComponentPragma
  const newComponentPragma = privateButtonModule.t.pragma.component.create({
    name: 'newComponent',
    transform: (node: any) => ({ ...node, newComponent: true })
  });
  console.log('New ComponentPragma:', newComponentPragma);
}

// Run the example
exampleUsage();
