/**
 * Term Validation in stPragma
 * 
 * This file demonstrates the static `is` and `validate` functions for terms in stPragma,
 * as well as namespaced interfaces with dot notation access.
 */

/**
 * Term interface
 */
interface Term<T = any> {
  id: string;
  name: string;
  value: T;
  type: string;
  
  // Static validation functions
  static is(value: any): boolean;
  static validate(value: any): boolean;
}

/**
 * Component term
 */
class ComponentTerm implements Term<React.ComponentType<any>> {
  id: string;
  name: string;
  value: React.ComponentType<any>;
  type: 'component';
  
  constructor(id: string, name: string, value: React.ComponentType<any>) {
    this.id = id;
    this.name = name;
    this.value = value;
    this.type = 'component';
  }
  
  /**
   * Checks if a value is a component
   * 
   * @param value - The value to check
   * @returns Whether the value is a component
   */
  static is(value: any): boolean {
    return typeof value === 'function' || 
           (typeof value === 'object' && value !== null && typeof value.render === 'function');
  }
  
  /**
   * Validates a component
   * 
   * @param value - The value to validate
   * @returns Whether the value is a valid component
   */
  static validate(value: any): boolean {
    if (!ComponentTerm.is(value)) {
      return false;
    }
    
    // Additional validation logic
    // For example, check if the component has required props
    try {
      const propTypes = (value as any).propTypes || {};
      const requiredProps = Object.keys(propTypes).filter(prop => 
        propTypes[prop].isRequired
      );
      
      // A valid component should have documentation for required props
      return requiredProps.every(prop => 
        (value as any).__docgenInfo?.props?.[prop]?.description
      );
    } catch (error) {
      return false;
    }
  }
}

/**
 * Function term
 */
class FunctionTerm implements Term<Function> {
  id: string;
  name: string;
  value: Function;
  type: 'function';
  
  constructor(id: string, name: string, value: Function) {
    this.id = id;
    this.name = name;
    this.value = value;
    this.type = 'function';
  }
  
  /**
   * Checks if a value is a function
   * 
   * @param value - The value to check
   * @returns Whether the value is a function
   */
  static is(value: any): boolean {
    return typeof value === 'function';
  }
  
  /**
   * Validates a function
   * 
   * @param value - The value to validate
   * @returns Whether the value is a valid function
   */
  static validate(value: any): boolean {
    if (!FunctionTerm.is(value)) {
      return false;
    }
    
    // Additional validation logic
    // For example, check if the function has JSDoc comments
    try {
      const functionString = value.toString();
      return functionString.includes('/**') && functionString.includes('*/');
    } catch (error) {
      return false;
    }
  }
}

/**
 * Object term
 */
class ObjectTerm implements Term<object> {
  id: string;
  name: string;
  value: object;
  type: 'object';
  
  constructor(id: string, name: string, value: object) {
    this.id = id;
    this.name = name;
    this.value = value;
    this.type = 'object';
  }
  
  /**
   * Checks if a value is an object
   * 
   * @param value - The value to check
   * @returns Whether the value is an object
   */
  static is(value: any): boolean {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }
  
  /**
   * Validates an object
   * 
   * @param value - The value to validate
   * @returns Whether the value is a valid object
   */
  static validate(value: any): boolean {
    if (!ObjectTerm.is(value)) {
      return false;
    }
    
    // Additional validation logic
    // For example, check if the object has required properties
    try {
      const requiredProps = ['id', 'name'];
      return requiredProps.every(prop => prop in value);
    } catch (error) {
      return false;
    }
  }
}

/**
 * Namespaced interfaces with dot notation access
 */
namespace API {
  export interface User {
    id: string;
    name: string;
    email: string;
  }
  
  export namespace Auth {
    export interface Credentials {
      username: string;
      password: string;
    }
    
    export interface Token {
      value: string;
      expiresAt: string;
    }
    
    export interface Session {
      user: User;
      token: Token;
      isActive: boolean;
    }
  }
  
  export namespace Components {
    export interface Props {
      className?: string;
      style?: React.CSSProperties;
    }
    
    export interface ButtonProps extends Props {
      onClick?: () => void;
      disabled?: boolean;
      variant?: 'primary' | 'secondary' | 'tertiary';
    }
    
    export interface InputProps extends Props {
      value?: string;
      onChange?: (value: string) => void;
      placeholder?: string;
      type?: 'text' | 'password' | 'email';
    }
  }
}

/**
 * Example usage of namespaced interfaces with dot notation access
 */
function createUser(user: API.User): API.User {
  return user;
}

function login(credentials: API.Auth.Credentials): API.Auth.Session {
  return {
    user: {
      id: '1',
      name: 'John Doe',
      email: credentials.username
    },
    token: {
      value: 'token',
      expiresAt: new Date(Date.now() + 3600000).toISOString()
    },
    isActive: true
  };
}

function createButton(props: API.Components.ButtonProps): React.ReactElement {
  return {
    type: 'button',
    props: {
      className: props.className,
      style: props.style,
      onClick: props.onClick,
      disabled: props.disabled
    },
    key: null
  } as any;
}

/**
 * Example usage of term validation
 */
function validateTerm(term: Term): boolean {
  // Get the term class based on the term type
  const termClass = {
    'component': ComponentTerm,
    'function': FunctionTerm,
    'object': ObjectTerm
  }[term.type];
  
  if (!termClass) {
    return false;
  }
  
  // Check if the value is of the correct type
  if (!termClass.is(term.value)) {
    return false;
  }
  
  // Validate the value
  return termClass.validate(term.value);
}

/**
 * Example usage in stPragma
 */
function createScope() {
  const scope: Record<string, Term> = {};
  
  // Add terms to the scope
  scope.Button = new ComponentTerm('button', 'Button', createButton);
  scope.createUser = new FunctionTerm('createUser', 'createUser', createUser);
  scope.user = new ObjectTerm('user', 'user', { id: '1', name: 'John Doe' });
  
  return scope;
}

/**
 * Example usage of scope with validation
 */
function useScope() {
  const scope = createScope();
  
  // Validate terms in the scope
  for (const [name, term] of Object.entries(scope)) {
    console.log(`Term ${name} is valid: ${validateTerm(term)}`);
  }
  
  // Use terms from the scope
  const button = scope.Button.value({ onClick: () => console.log('Clicked') });
  const user = scope.createUser.value({ id: '2', name: 'Jane Doe', email: '<EMAIL>' });
  const existingUser = scope.user.value;
  
  console.log(button, user, existingUser);
}
