# Structure of Concept Folders - Summary

## Standard Structure

Each concept folder follows this standard structure:

```
N.concept_name/
  context.md           # Provides context and overview of the concept
  summary.md           # Summarizes key points and links to supporting docs
  code/                # Contains implementation code
    0.file_one.ts
    1.file_two.ts
    ...
  docs/                # Contains detailed documentation
    0.topic_one.md
    1.topic_two.md
    ...
```

## Naming Conventions

1. **Numerical Prefixes**:
   - All files and folders use numerical prefixes (e.g., `0.`, `1.`, etc.)
   - This indicates the temporal sequence within siblings
   - This is not a timestamp but an integer indicating order

2. **Concept Folders**:
   - Named with format `N.concept_name` where N is the sequence number
   - Names should be descriptive and use underscores for spaces

3. **Files**:
   - Follow the same numerical prefix convention
   - Names should clearly indicate content

## Documentation Structure

1. **context.md**:
   - Provides high-level context for the concept
   - Explains the purpose and key aspects
   - Does not go into implementation details

2. **summary.md**:
   - Summarizes key points of the concept
   - Contains links to supporting documentation
   - References code snippets in the code folder

3. **docs/ folder**:
   - Contains detailed documentation on specific aspects
   - Files are prefixed with numbers to indicate sequence
   - Should reference relevant code snippets

## Code Structure

1. **code/ folder**:
   - Contains implementation code
   - Files are prefixed with numbers to indicate sequence
   - Should be well-documented with comments

## Cross-Referencing

- Documentation should reference code snippets using relative paths
- Code should be documented to explain how it implements concepts
- Links between documents should use relative paths

## Related Documents

- [Documentation Standards](./docs/documentation_standards.md)
- [Code Organization](./docs/code_organization.md)
- [Cross-Referencing Guide](./docs/cross_referencing_guide.md)

## Code References

- [Folder Structure Validator](./code/folder_structure_validator.ts)
- [Documentation Generator](./code/documentation_generator.ts)
