/**
 * Pragma Composition
 * 
 * This file demonstrates how pragma enables composition at the schema level,
 * allowing APIs to be composed through their pragma definitions.
 * 
 * This extends concept 61 by showing how the structural composition
 * capabilities of pragma can be used for API composition.
 */

import { ApiSchema, PragmaApi, Result, Options } from './pragma_api_schema';

/**
 * User API Schema
 */
export interface UserApiSchema {
  /**
   * Creates a user
   * 
   * @param user - The user to create
   * @returns The created user
   */
  createUser: (user: User) => User;
  
  /**
   * Gets a user by ID
   * 
   * @param id - The user ID
   * @returns The user
   */
  getUser: (id: string) => User;
  
  /**
   * Updates a user
   * 
   * @param id - The user ID
   * @param user - The updated user
   * @returns The updated user
   */
  updateUser: (id: string, user: Partial<User>) => User;
  
  /**
   * Deletes a user
   * 
   * @param id - The user ID
   * @returns Whether the deletion was successful
   */
  deleteUser: (id: string) => boolean;
}

/**
 * User type
 */
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
}

/**
 * Product API Schema
 */
export interface ProductApiSchema {
  /**
   * Creates a product
   * 
   * @param product - The product to create
   * @returns The created product
   */
  createProduct: (product: Product) => Product;
  
  /**
   * Gets a product by ID
   * 
   * @param id - The product ID
   * @returns The product
   */
  getProduct: (id: string) => Product;
  
  /**
   * Updates a product
   * 
   * @param id - The product ID
   * @param product - The updated product
   * @returns The updated product
   */
  updateProduct: (id: string, product: Partial<Product>) => Product;
  
  /**
   * Deletes a product
   * 
   * @param id - The product ID
   * @returns Whether the deletion was successful
   */
  deleteProduct: (id: string) => boolean;
}

/**
 * Product type
 */
export interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
}

/**
 * User API implementation
 */
export const userApi: UserApiSchema = {
  createUser: (user: User): User => {
    // Implementation
    return { ...user, id: 'generated-id' };
  },
  
  getUser: (id: string): User => {
    // Implementation
    return { id, name: 'Test User', email: '<EMAIL>', role: 'user' };
  },
  
  updateUser: (id: string, user: Partial<User>): User => {
    // Implementation
    return { id, name: 'Test User', email: '<EMAIL>', role: 'user', ...user };
  },
  
  deleteUser: (id: string): boolean => {
    // Implementation
    return true;
  }
};

/**
 * User API pragma
 */
export const userPragma: PragmaApi = {
  schema: {
    createUser: (user: User): User => {
      throw new Error('Schema method should not be called directly');
    },
    
    getUser: (id: string): User => {
      throw new Error('Schema method should not be called directly');
    },
    
    updateUser: (id: string, user: Partial<User>): User => {
      throw new Error('Schema method should not be called directly');
    },
    
    deleteUser: (id: string): boolean => {
      throw new Error('Schema method should not be called directly');
    }
  } as unknown as ApiSchema,
  
  getSchema: () => userPragma.schema as unknown as ApiSchema,
  
  validateImplementation: (implementation: any): boolean => {
    // Check if all schema properties and methods exist in the implementation
    for (const key of Object.keys(userPragma.schema)) {
      if (!(key in implementation)) {
        console.error(`Missing ${key} in implementation`);
        return false;
      }
      
      // Check if the types match
      const schemaType = typeof userPragma.schema[key];
      const implType = typeof implementation[key];
      
      if (schemaType !== implType) {
        console.error(`Type mismatch for ${key}: expected ${schemaType}, got ${implType}`);
        return false;
      }
    }
    
    return true;
  },
  
  extendSchema: (extension: Partial<ApiSchema>): ApiSchema => {
    // Create a new schema with the extension
    const extendedSchema: ApiSchema = {
      ...userPragma.schema as unknown as ApiSchema,
      ...extension
    };
    
    return extendedSchema;
  },
  
  serializeSchema: (): string => {
    // Serialize the schema to JSON
    return JSON.stringify(userPragma.schema, (key, value) => {
      // Handle functions by converting them to strings
      if (typeof value === 'function') {
        return value.toString();
      }
      return value;
    });
  },
  
  deserializeSchema: (serialized: string): ApiSchema => {
    // Deserialize the schema from JSON
    return JSON.parse(serialized, (key, value) => {
      // Handle function strings by converting them back to functions
      if (typeof value === 'string' && value.startsWith('function')) {
        return new Function(`return ${value}`)();
      }
      return value;
    });
  }
};

/**
 * Product API implementation
 */
export const productApi: ProductApiSchema = {
  createProduct: (product: Product): Product => {
    // Implementation
    return { ...product, id: 'generated-id' };
  },
  
  getProduct: (id: string): Product => {
    // Implementation
    return { id, name: 'Test Product', price: 9.99, description: 'A test product' };
  },
  
  updateProduct: (id: string, product: Partial<Product>): Product => {
    // Implementation
    return { id, name: 'Test Product', price: 9.99, description: 'A test product', ...product };
  },
  
  deleteProduct: (id: string): boolean => {
    // Implementation
    return true;
  }
};

/**
 * Product API pragma
 */
export const productPragma: PragmaApi = {
  schema: {
    createProduct: (product: Product): Product => {
      throw new Error('Schema method should not be called directly');
    },
    
    getProduct: (id: string): Product => {
      throw new Error('Schema method should not be called directly');
    },
    
    updateProduct: (id: string, product: Partial<Product>): Product => {
      throw new Error('Schema method should not be called directly');
    },
    
    deleteProduct: (id: string): boolean => {
      throw new Error('Schema method should not be called directly');
    }
  } as unknown as ApiSchema,
  
  getSchema: () => productPragma.schema as unknown as ApiSchema,
  
  validateImplementation: (implementation: any): boolean => {
    // Check if all schema properties and methods exist in the implementation
    for (const key of Object.keys(productPragma.schema)) {
      if (!(key in implementation)) {
        console.error(`Missing ${key} in implementation`);
        return false;
      }
      
      // Check if the types match
      const schemaType = typeof productPragma.schema[key];
      const implType = typeof implementation[key];
      
      if (schemaType !== implType) {
        console.error(`Type mismatch for ${key}: expected ${schemaType}, got ${implType}`);
        return false;
      }
    }
    
    return true;
  },
  
  extendSchema: (extension: Partial<ApiSchema>): ApiSchema => {
    // Create a new schema with the extension
    const extendedSchema: ApiSchema = {
      ...productPragma.schema as unknown as ApiSchema,
      ...extension
    };
    
    return extendedSchema;
  },
  
  serializeSchema: (): string => {
    // Serialize the schema to JSON
    return JSON.stringify(productPragma.schema, (key, value) => {
      // Handle functions by converting them to strings
      if (typeof value === 'function') {
        return value.toString();
      }
      return value;
    });
  },
  
  deserializeSchema: (serialized: string): ApiSchema => {
    // Deserialize the schema from JSON
    return JSON.parse(serialized, (key, value) => {
      // Handle function strings by converting them back to functions
      if (typeof value === 'string' && value.startsWith('function')) {
        return new Function(`return ${value}`)();
      }
      return value;
    });
  }
};

/**
 * Composed API Schema
 */
export interface ComposedApiSchema extends UserApiSchema, ProductApiSchema {}

/**
 * Compose schemas at the pragma level
 */
export function composeSchemas<T, U>(schemaA: T, schemaB: U): T & U {
  return { ...schemaA, ...schemaB };
}

/**
 * Compose pragmas
 */
export function composePragmas(pragmaA: PragmaApi, pragmaB: PragmaApi): PragmaApi {
  // Compose the schemas
  const composedSchema = composeSchemas(
    pragmaA.getSchema(),
    pragmaB.getSchema()
  );
  
  // Create a new pragma with the composed schema
  return {
    schema: composedSchema,
    
    getSchema: () => composedSchema,
    
    validateImplementation: (implementation: any): boolean => {
      return pragmaA.validateImplementation(implementation) &&
             pragmaB.validateImplementation(implementation);
    },
    
    extendSchema: (extension: Partial<ApiSchema>): ApiSchema => {
      return {
        ...composedSchema,
        ...extension
      };
    },
    
    serializeSchema: (): string => {
      // Serialize the composed schema
      return JSON.stringify(composedSchema, (key, value) => {
        // Handle functions by converting them to strings
        if (typeof value === 'function') {
          return value.toString();
        }
        return value;
      });
    },
    
    deserializeSchema: (serialized: string): ApiSchema => {
      // Deserialize the schema from JSON
      return JSON.parse(serialized, (key, value) => {
        // Handle function strings by converting them back to functions
        if (typeof value === 'string' && value.startsWith('function')) {
          return new Function(`return ${value}`)();
        }
        return value;
      });
    }
  };
}

/**
 * Compose APIs
 */
export function composeApis<T, U>(apiA: T, apiB: U): T & U {
  return { ...apiA, ...apiB };
}

/**
 * Composed API
 */
export const composedApi: ComposedApiSchema = composeApis(userApi, productApi);

/**
 * Composed pragma
 */
export const composedPragma: PragmaApi = composePragmas(userPragma, productPragma);

/**
 * Validate the composed API against the composed pragma
 */
export function validateComposedApi(): boolean {
  return composedPragma.validateImplementation(composedApi);
}

/**
 * Export the composed API and pragma
 */
export default {
  api: composedApi,
  pragma: composedPragma
};
