/**
 * Proposal Template
 * 
 * This file defines the template for proposals generated from concept tests,
 * including structure, metadata, and content.
 */

/**
 * Proposal status
 */
export enum ProposalStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  REVISION = 'revision',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  IMPLEMENTED = 'implemented'
}

/**
 * Proposal type
 */
export enum ProposalType {
  CONCEPT = 'concept',
  DESIGN = 'design',
  IMPLEMENTATION = 'implementation'
}

/**
 * Test result reference
 */
export interface TestResultReference {
  // Test metadata
  testId: string;
  testName: string;
  
  // Test result summary
  success: boolean;
  overallRating: number;
  
  // Key findings
  keyFindings: string[];
}

/**
 * Proposal
 */
export interface Proposal {
  // Proposal metadata
  id: string;
  title: string;
  type: ProposalType;
  status: ProposalStatus;
  author: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Related items
  relatedTests: TestResultReference[];
  relatedConcepts: string[];
  
  // Proposal content
  summary: string;
  background: string;
  details: string;
  alternatives: string;
  impact: string;
  implementation: string;
  
  // Review information
  reviews: ProposalReview[];
  
  // Status history
  statusHistory: StatusChange[];
}

/**
 * Proposal review
 */
export interface ProposalReview {
  // Review metadata
  reviewerId: string;
  reviewerType: 'ai' | 'human';
  timestamp: Date;
  
  // Review content
  comments: string;
  rating: number; // 0-100
  recommendation: 'approve' | 'request_changes' | 'reject';
  
  // Specific feedback
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
}

/**
 * Status change
 */
export interface StatusChange {
  // Change metadata
  timestamp: Date;
  changedBy: string;
  
  // Status change
  fromStatus: ProposalStatus;
  toStatus: ProposalStatus;
  
  // Reason for change
  reason: string;
}

/**
 * Proposal template
 */
export class ProposalTemplate {
  /**
   * Create a proposal from test results
   * 
   * @param title - The proposal title
   * @param type - The proposal type
   * @param author - The proposal author
   * @param testResults - The test results
   * @param conceptIds - The concept IDs
   * @returns The proposal
   */
  createProposal(
    title: string,
    type: ProposalType,
    author: string,
    testResults: TestResultReference[],
    conceptIds: string[]
  ): Proposal {
    // Create the proposal
    const proposal: Proposal = {
      id: this.generateProposalId(),
      title,
      type,
      status: ProposalStatus.DRAFT,
      author,
      createdAt: new Date(),
      updatedAt: new Date(),
      relatedTests: testResults,
      relatedConcepts: conceptIds,
      summary: this.generateSummary(title, testResults),
      background: this.generateBackground(conceptIds),
      details: this.generateDetails(testResults),
      alternatives: this.generateAlternatives(),
      impact: this.generateImpact(conceptIds),
      implementation: this.generateImplementation(),
      reviews: [],
      statusHistory: [
        {
          timestamp: new Date(),
          changedBy: author,
          fromStatus: null as any,
          toStatus: ProposalStatus.DRAFT,
          reason: 'Initial creation'
        }
      ]
    };
    
    return proposal;
  }
  
  /**
   * Generate a proposal ID
   * 
   * @returns A unique proposal ID
   */
  private generateProposalId(): string {
    return `proposal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Generate a summary
   * 
   * @param title - The proposal title
   * @param testResults - The test results
   * @returns The summary
   */
  private generateSummary(title: string, testResults: TestResultReference[]): string {
    // This would generate a summary based on the title and test results
    // This is a simplified example
    return `This proposal suggests ${title.toLowerCase()} based on the results of ${testResults.length} concept tests. The tests demonstrated ${this.getSuccessRate(testResults)}% success rate with an average rating of ${this.getAverageRating(testResults)}/100.`;
  }
  
  /**
   * Generate background
   * 
   * @param conceptIds - The concept IDs
   * @returns The background
   */
  private generateBackground(conceptIds: string[]): string {
    // This would generate background based on the concepts
    // This is a simplified example
    return `This proposal is related to the following concepts: ${conceptIds.join(', ')}. These concepts provide the foundation for the proposal and establish the context for the suggested changes.`;
  }
  
  /**
   * Generate details
   * 
   * @param testResults - The test results
   * @returns The details
   */
  private generateDetails(testResults: TestResultReference[]): string {
    // This would generate details based on the test results
    // This is a simplified example
    return `The proposal is based on the following test results:

${testResults.map(result => `- ${result.testName}: ${result.success ? 'Success' : 'Failure'} (${result.overallRating}/100)
  Key findings: ${result.keyFindings.join(', ')}`).join('\n')}

These test results demonstrate the viability and value of the proposed changes.`;
  }
  
  /**
   * Generate alternatives
   * 
   * @returns The alternatives
   */
  private generateAlternatives(): string {
    // This would generate alternatives
    // This is a simplified example
    return `Several alternatives were considered during the concept testing phase. These alternatives were evaluated against the same criteria but did not perform as well as the proposed approach.`;
  }
  
  /**
   * Generate impact
   * 
   * @param conceptIds - The concept IDs
   * @returns The impact
   */
  private generateImpact(conceptIds: string[]): string {
    // This would generate impact based on the concepts
    // This is a simplified example
    return `The proposed changes will impact the following concepts: ${conceptIds.join(', ')}. The impact is expected to be positive, improving the clarity, consistency, and effectiveness of these concepts.`;
  }
  
  /**
   * Generate implementation
   * 
   * @returns The implementation
   */
  private generateImplementation(): string {
    // This would generate implementation details
    // This is a simplified example
    return `The implementation of this proposal will involve the following steps:

1. Update concept documentation
2. Refine concept definitions
3. Update related code and examples
4. Validate changes through additional testing

This implementation is expected to take approximately 2 weeks.`;
  }
  
  /**
   * Get the success rate
   * 
   * @param testResults - The test results
   * @returns The success rate
   */
  private getSuccessRate(testResults: TestResultReference[]): number {
    // Calculate the success rate
    const successCount = testResults.filter(result => result.success).length;
    return Math.round((successCount / testResults.length) * 100);
  }
  
  /**
   * Get the average rating
   * 
   * @param testResults - The test results
   * @returns The average rating
   */
  private getAverageRating(testResults: TestResultReference[]): number {
    // Calculate the average rating
    const totalRating = testResults.reduce((sum, result) => sum + result.overallRating, 0);
    return Math.round(totalRating / testResults.length);
  }
}

/**
 * Example usage:
 * 
 * // Create test result references
 * const testResults: TestResultReference[] = [
 *   {
 *     testId: 'test-001',
 *     testName: 'Pragma Structure Test',
 *     success: true,
 *     overallRating: 85,
 *     keyFindings: [
 *       'Highly expressive structure',
 *       'Good adaptability',
 *       'Some complexity in nested structures'
 *     ]
 *   },
 *   {
 *     testId: 'test-002',
 *     testName: 'Pragma Composition Test',
 *     success: true,
 *     overallRating: 90,
 *     keyFindings: [
 *       'Effective composition of pragmas',
 *       'Clear relationships between components',
 *       'Intuitive naming conventions'
 *     ]
 *   }
 * ];
 * 
 * // Create a proposal template
 * const template = new ProposalTemplate();
 * 
 * // Create a proposal
 * const proposal = template.createProposal(
 *   'Enhance Pragma Composition',
 *   ProposalType.CONCEPT,
 *   'John Doe',
 *   testResults,
 *   ['concept-001', 'concept-002']
 * );
 * 
 * // Use the proposal
 * console.log(`Proposal ID: ${proposal.id}`);
 * console.log(`Title: ${proposal.title}`);
 * console.log(`Status: ${proposal.status}`);
 * console.log(`Summary: ${proposal.summary}`);
 */
