/**
 * Review Framework
 * 
 * This file defines the framework for reviewing proposals generated from concept tests,
 * including review process, criteria, and evaluation.
 */

import { Proposal, ProposalReview, ProposalStatus } from './0.proposal_template';

/**
 * Review criteria
 */
export interface ReviewCriteria {
  // Criteria metadata
  id: string;
  name: string;
  description: string;
  
  // Criteria weight
  weight: number; // 0-1
  
  // Criteria evaluation function
  evaluate: (proposal: Proposal) => CriteriaEvaluation;
}

/**
 * Criteria evaluation
 */
export interface CriteriaEvaluation {
  // Criteria metadata
  criteriaId: string;
  
  // Evaluation
  rating: number; // 0-100
  comments: string;
  
  // Strengths and weaknesses
  strengths: string[];
  weaknesses: string[];
}

/**
 * Review result
 */
export interface ReviewResult {
  // Review metadata
  reviewerId: string;
  reviewerType: 'ai' | 'human';
  timestamp: Date;
  
  // Overall evaluation
  overallRating: number; // 0-100
  recommendation: 'approve' | 'request_changes' | 'reject';
  
  // Criteria evaluations
  criteriaEvaluations: CriteriaEvaluation[];
  
  // Comments
  generalComments: string;
  
  // Strengths and weaknesses
  strengths: string[];
  weaknesses: string[];
  
  // Suggestions
  suggestions: string[];
}

/**
 * Review framework
 */
export class ReviewFramework {
  // Review criteria
  private criteria: ReviewCriteria[];
  
  /**
   * Constructor
   * 
   * @param criteria - The review criteria
   */
  constructor(criteria: ReviewCriteria[]) {
    this.criteria = criteria;
  }
  
  /**
   * Review a proposal
   * 
   * @param proposal - The proposal to review
   * @param reviewerId - The reviewer ID
   * @param reviewerType - The reviewer type
   * @returns The review result
   */
  reviewProposal(
    proposal: Proposal,
    reviewerId: string,
    reviewerType: 'ai' | 'human'
  ): ReviewResult {
    // Evaluate each criterion
    const criteriaEvaluations = this.criteria.map(criterion => 
      criterion.evaluate(proposal)
    );
    
    // Calculate overall rating
    const overallRating = this.calculateOverallRating(criteriaEvaluations);
    
    // Determine recommendation
    const recommendation = this.determineRecommendation(overallRating);
    
    // Collect strengths and weaknesses
    const strengths = criteriaEvaluations.flatMap(eval => eval.strengths);
    const weaknesses = criteriaEvaluations.flatMap(eval => eval.weaknesses);
    
    // Generate suggestions
    const suggestions = this.generateSuggestions(weaknesses);
    
    // Generate general comments
    const generalComments = this.generateGeneralComments(
      overallRating,
      recommendation,
      strengths,
      weaknesses
    );
    
    // Return the review result
    return {
      reviewerId,
      reviewerType,
      timestamp: new Date(),
      overallRating,
      recommendation,
      criteriaEvaluations,
      generalComments,
      strengths,
      weaknesses,
      suggestions
    };
  }
  
  /**
   * Apply a review to a proposal
   * 
   * @param proposal - The proposal
   * @param reviewResult - The review result
   * @returns The updated proposal
   */
  applyReview(proposal: Proposal, reviewResult: ReviewResult): Proposal {
    // Create a proposal review
    const review: ProposalReview = {
      reviewerId: reviewResult.reviewerId,
      reviewerType: reviewResult.reviewerType,
      timestamp: reviewResult.timestamp,
      comments: reviewResult.generalComments,
      rating: reviewResult.overallRating,
      recommendation: reviewResult.recommendation,
      strengths: reviewResult.strengths,
      weaknesses: reviewResult.weaknesses,
      suggestions: reviewResult.suggestions
    };
    
    // Add the review to the proposal
    const updatedProposal = {
      ...proposal,
      reviews: [...proposal.reviews, review],
      updatedAt: new Date()
    };
    
    // Update the status if needed
    if (this.shouldUpdateStatus(updatedProposal)) {
      return this.updateStatus(updatedProposal);
    }
    
    return updatedProposal;
  }
  
  /**
   * Calculate the overall rating
   * 
   * @param criteriaEvaluations - The criteria evaluations
   * @returns The overall rating
   */
  private calculateOverallRating(criteriaEvaluations: CriteriaEvaluation[]): number {
    // Get the criteria
    const criteria = this.criteria;
    
    // Calculate the weighted sum
    let weightedSum = 0;
    let totalWeight = 0;
    
    for (let i = 0; i < criteria.length; i++) {
      const criterion = criteria[i];
      const evaluation = criteriaEvaluations[i];
      
      weightedSum += evaluation.rating * criterion.weight;
      totalWeight += criterion.weight;
    }
    
    // Calculate the weighted average
    return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
  }
  
  /**
   * Determine the recommendation
   * 
   * @param overallRating - The overall rating
   * @returns The recommendation
   */
  private determineRecommendation(overallRating: number): 'approve' | 'request_changes' | 'reject' {
    if (overallRating >= 80) {
      return 'approve';
    } else if (overallRating >= 50) {
      return 'request_changes';
    } else {
      return 'reject';
    }
  }
  
  /**
   * Generate suggestions
   * 
   * @param weaknesses - The weaknesses
   * @returns The suggestions
   */
  private generateSuggestions(weaknesses: string[]): string[] {
    // This would generate suggestions based on weaknesses
    // This is a simplified example
    return weaknesses.map(weakness => 
      `Address the weakness: "${weakness}"`
    );
  }
  
  /**
   * Generate general comments
   * 
   * @param overallRating - The overall rating
   * @param recommendation - The recommendation
   * @param strengths - The strengths
   * @param weaknesses - The weaknesses
   * @returns The general comments
   */
  private generateGeneralComments(
    overallRating: number,
    recommendation: 'approve' | 'request_changes' | 'reject',
    strengths: string[],
    weaknesses: string[]
  ): string {
    // This would generate general comments
    // This is a simplified example
    let comments = `This proposal received an overall rating of ${overallRating}/100. `;
    
    if (recommendation === 'approve') {
      comments += 'I recommend approving this proposal. ';
    } else if (recommendation === 'request_changes') {
      comments += 'I recommend requesting changes before approval. ';
    } else {
      comments += 'I recommend rejecting this proposal. ';
    }
    
    if (strengths.length > 0) {
      comments += `The proposal has several strengths, including ${strengths.slice(0, 3).join(', ')}. `;
    }
    
    if (weaknesses.length > 0) {
      comments += `However, there are some weaknesses that should be addressed, including ${weaknesses.slice(0, 3).join(', ')}. `;
    }
    
    return comments;
  }
  
  /**
   * Check if the proposal status should be updated
   * 
   * @param proposal - The proposal
   * @returns Whether the status should be updated
   */
  private shouldUpdateStatus(proposal: Proposal): boolean {
    // Only update status if the proposal is in review
    if (proposal.status !== ProposalStatus.REVIEW) {
      return false;
    }
    
    // Check if all reviewers have provided reviews
    // This is a simplified example - in a real implementation,
    // we would check against a list of assigned reviewers
    return proposal.reviews.length >= 2;
  }
  
  /**
   * Update the proposal status
   * 
   * @param proposal - The proposal
   * @returns The updated proposal
   */
  private updateStatus(proposal: Proposal): Proposal {
    // Calculate the average rating
    const totalRating = proposal.reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = Math.round(totalRating / proposal.reviews.length);
    
    // Determine the new status
    let newStatus: ProposalStatus;
    let reason: string;
    
    if (averageRating >= 80) {
      newStatus = ProposalStatus.ACCEPTED;
      reason = 'All reviews complete with high average rating';
    } else if (averageRating >= 50) {
      newStatus = ProposalStatus.REVISION;
      reason = 'All reviews complete with moderate average rating';
    } else {
      newStatus = ProposalStatus.REJECTED;
      reason = 'All reviews complete with low average rating';
    }
    
    // Add a status change
    const statusChange = {
      timestamp: new Date(),
      changedBy: 'Review Framework', // This would come from context
      fromStatus: proposal.status,
      toStatus: newStatus,
      reason
    };
    
    // Update the proposal
    return {
      ...proposal,
      status: newStatus,
      statusHistory: [...proposal.statusHistory, statusChange],
      updatedAt: new Date()
    };
  }
}

/**
 * Example usage:
 * 
 * // Define review criteria
 * const criteria: ReviewCriteria[] = [
 *   {
 *     id: 'goal-alignment',
 *     name: 'Goal Alignment',
 *     description: 'Alignment with project goals',
 *     weight: 0.3,
 *     evaluate: (proposal) => {
 *       // Implementation of criteria evaluation
 *       return {
 *         criteriaId: 'goal-alignment',
 *         rating: 85,
 *         comments: 'The proposal aligns well with project goals',
 *         strengths: ['Clear alignment with goal 1', 'Supports goal 2'],
 *         weaknesses: ['Limited support for goal 3']
 *       };
 *     }
 *   },
 *   // Other criteria would be defined here
 * ];
 * 
 * // Create a review framework
 * const framework = new ReviewFramework(criteria);
 * 
 * // Review a proposal
 * const reviewResult = framework.reviewProposal(
 *   proposal,
 *   'reviewer-001',
 *   'human'
 * );
 * 
 * // Apply the review to the proposal
 * const updatedProposal = framework.applyReview(proposal, reviewResult);
 * 
 * // Use the updated proposal
 * console.log(`Status: ${updatedProposal.status}`);
 * console.log(`Number of reviews: ${updatedProposal.reviews.length}`);
 * console.log(`Latest review rating: ${updatedProposal.reviews[updatedProposal.reviews.length - 1].rating}`);
 */
