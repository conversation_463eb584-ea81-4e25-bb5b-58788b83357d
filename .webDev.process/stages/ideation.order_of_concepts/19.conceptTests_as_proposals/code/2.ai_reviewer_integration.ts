/**
 * AI Reviewer Integration
 * 
 * This file defines the integration of AI reviewers into the proposal review process,
 * including AI reviewer types, training, and review generation.
 */

import { Proposal } from './0.proposal_template';
import { ReviewCriteria, CriteriaEvaluation, ReviewResult } from './1.review_framework';

/**
 * AI reviewer type
 */
export enum AIReviewerType {
  CONCEPT_REVIEWER = 'concept_reviewer',
  DESIGN_REVIEWER = 'design_reviewer',
  IMPLEMENTATION_REVIEWER = 'implementation_reviewer'
}

/**
 * AI reviewer training
 */
export interface AIReviewerTraining {
  // Training metadata
  id: string;
  name: string;
  description: string;
  
  // Training data
  trainingData: {
    proposals: Proposal[];
    reviews: ReviewResult[];
  };
  
  // Training parameters
  parameters: Record<string, any>;
}

/**
 * AI reviewer
 */
export interface AIReviewer {
  // Reviewer metadata
  id: string;
  name: string;
  type: AIReviewerType;
  
  // Reviewer training
  training: AIReviewerTraining;
  
  // Review criteria
  criteria: ReviewCriteria[];
  
  // Review function
  reviewProposal: (proposal: Proposal) => Promise<ReviewResult>;
  
  // Learning function
  learnFromReview: (proposal: Proposal, review: ReviewResult) => Promise<void>;
}

/**
 * AI reviewer factory
 */
export class AIReviewerFactory {
  /**
   * Create an AI reviewer
   * 
   * @param type - The reviewer type
   * @param training - The reviewer training
   * @param criteria - The review criteria
   * @returns The AI reviewer
   */
  createReviewer(
    type: AIReviewerType,
    training: AIReviewerTraining,
    criteria: ReviewCriteria[]
  ): AIReviewer {
    // Create the reviewer
    const reviewer: AIReviewer = {
      id: this.generateReviewerId(),
      name: `AI ${type}`,
      type,
      training,
      criteria,
      reviewProposal: async (proposal) => {
        return this.generateReview(proposal, reviewer);
      },
      learnFromReview: async (proposal, review) => {
        await this.learnFromReview(reviewer, proposal, review);
      }
    };
    
    return reviewer;
  }
  
  /**
   * Generate a reviewer ID
   * 
   * @returns A unique reviewer ID
   */
  private generateReviewerId(): string {
    return `ai-reviewer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Generate a review
   * 
   * @param proposal - The proposal
   * @param reviewer - The reviewer
   * @returns The review result
   */
  private async generateReview(proposal: Proposal, reviewer: AIReviewer): Promise<ReviewResult> {
    // Evaluate each criterion
    const criteriaEvaluations: CriteriaEvaluation[] = [];
    
    for (const criterion of reviewer.criteria) {
      const evaluation = criterion.evaluate(proposal);
      criteriaEvaluations.push(evaluation);
    }
    
    // Calculate overall rating
    const overallRating = this.calculateOverallRating(criteriaEvaluations, reviewer.criteria);
    
    // Determine recommendation
    const recommendation = this.determineRecommendation(overallRating);
    
    // Collect strengths and weaknesses
    const strengths = criteriaEvaluations.flatMap(eval => eval.strengths);
    const weaknesses = criteriaEvaluations.flatMap(eval => eval.weaknesses);
    
    // Generate suggestions
    const suggestions = this.generateSuggestions(weaknesses);
    
    // Generate general comments
    const generalComments = this.generateGeneralComments(
      overallRating,
      recommendation,
      strengths,
      weaknesses
    );
    
    // Return the review result
    return {
      reviewerId: reviewer.id,
      reviewerType: 'ai',
      timestamp: new Date(),
      overallRating,
      recommendation,
      criteriaEvaluations,
      generalComments,
      strengths,
      weaknesses,
      suggestions
    };
  }
  
  /**
   * Learn from a review
   * 
   * @param reviewer - The reviewer
   * @param proposal - The proposal
   * @param review - The review
   */
  private async learnFromReview(
    reviewer: AIReviewer,
    proposal: Proposal,
    review: ReviewResult
  ): Promise<void> {
    // This would update the reviewer's training based on the review
    // This is a simplified example - in a real implementation,
    // this would involve machine learning techniques
    
    // Add the proposal and review to the training data
    reviewer.training.trainingData.proposals.push(proposal);
    reviewer.training.trainingData.reviews.push(review);
    
    // Update the training parameters
    // This is a simplified example
    reviewer.training.parameters.lastTrainingTimestamp = new Date();
    reviewer.training.parameters.trainingCount = 
      (reviewer.training.parameters.trainingCount || 0) + 1;
  }
  
  /**
   * Calculate the overall rating
   * 
   * @param criteriaEvaluations - The criteria evaluations
   * @param criteria - The review criteria
   * @returns The overall rating
   */
  private calculateOverallRating(
    criteriaEvaluations: CriteriaEvaluation[],
    criteria: ReviewCriteria[]
  ): number {
    // Calculate the weighted sum
    let weightedSum = 0;
    let totalWeight = 0;
    
    for (let i = 0; i < criteria.length; i++) {
      const criterion = criteria[i];
      const evaluation = criteriaEvaluations[i];
      
      weightedSum += evaluation.rating * criterion.weight;
      totalWeight += criterion.weight;
    }
    
    // Calculate the weighted average
    return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
  }
  
  /**
   * Determine the recommendation
   * 
   * @param overallRating - The overall rating
   * @returns The recommendation
   */
  private determineRecommendation(overallRating: number): 'approve' | 'request_changes' | 'reject' {
    if (overallRating >= 80) {
      return 'approve';
    } else if (overallRating >= 50) {
      return 'request_changes';
    } else {
      return 'reject';
    }
  }
  
  /**
   * Generate suggestions
   * 
   * @param weaknesses - The weaknesses
   * @returns The suggestions
   */
  private generateSuggestions(weaknesses: string[]): string[] {
    // This would generate suggestions based on weaknesses
    // This is a simplified example
    return weaknesses.map(weakness => 
      `Address the weakness: "${weakness}"`
    );
  }
  
  /**
   * Generate general comments
   * 
   * @param overallRating - The overall rating
   * @param recommendation - The recommendation
   * @param strengths - The strengths
   * @param weaknesses - The weaknesses
   * @returns The general comments
   */
  private generateGeneralComments(
    overallRating: number,
    recommendation: 'approve' | 'request_changes' | 'reject',
    strengths: string[],
    weaknesses: string[]
  ): string {
    // This would generate general comments
    // This is a simplified example
    let comments = `This proposal received an overall rating of ${overallRating}/100. `;
    
    if (recommendation === 'approve') {
      comments += 'I recommend approving this proposal. ';
    } else if (recommendation === 'request_changes') {
      comments += 'I recommend requesting changes before approval. ';
    } else {
      comments += 'I recommend rejecting this proposal. ';
    }
    
    if (strengths.length > 0) {
      comments += `The proposal has several strengths, including ${strengths.slice(0, 3).join(', ')}. `;
    }
    
    if (weaknesses.length > 0) {
      comments += `However, there are some weaknesses that should be addressed, including ${weaknesses.slice(0, 3).join(', ')}. `;
    }
    
    return comments;
  }
}

/**
 * AI reviewer integration
 */
export class AIReviewerIntegration {
  // AI reviewers
  private reviewers: Map<AIReviewerType, AIReviewer> = new Map();
  
  /**
   * Constructor
   * 
   * @param factory - The AI reviewer factory
   */
  constructor(private factory: AIReviewerFactory) {}
  
  /**
   * Register an AI reviewer
   * 
   * @param reviewer - The reviewer to register
   */
  registerReviewer(reviewer: AIReviewer): void {
    this.reviewers.set(reviewer.type, reviewer);
  }
  
  /**
   * Get an AI reviewer
   * 
   * @param type - The reviewer type
   * @returns The reviewer
   */
  getReviewer(type: AIReviewerType): AIReviewer | undefined {
    return this.reviewers.get(type);
  }
  
  /**
   * Review a proposal
   * 
   * @param proposal - The proposal to review
   * @param type - The reviewer type
   * @returns The review result
   */
  async reviewProposal(
    proposal: Proposal,
    type: AIReviewerType
  ): Promise<ReviewResult> {
    // Get the reviewer
    const reviewer = this.getReviewer(type);
    
    if (!reviewer) {
      throw new Error(`No reviewer registered for type ${type}`);
    }
    
    // Review the proposal
    return reviewer.reviewProposal(proposal);
  }
  
  /**
   * Learn from a review
   * 
   * @param proposal - The proposal
   * @param review - The review
   */
  async learnFromReview(
    proposal: Proposal,
    review: ReviewResult
  ): Promise<void> {
    // Find the reviewer
    for (const [_, reviewer] of this.reviewers) {
      if (reviewer.id === review.reviewerId) {
        // Learn from the review
        await reviewer.learnFromReview(proposal, review);
        return;
      }
    }
    
    // If no reviewer found, ignore
  }
}

/**
 * Example usage:
 * 
 * // Define review criteria
 * const criteria: ReviewCriteria[] = [
 *   // Criteria would be defined here
 * ];
 * 
 * // Create an AI reviewer factory
 * const factory = new AIReviewerFactory();
 * 
 * // Create an AI reviewer
 * const reviewer = factory.createReviewer(
 *   AIReviewerType.CONCEPT_REVIEWER,
 *   {
 *     id: 'training-001',
 *     name: 'Concept Reviewer Training',
 *     description: 'Training for concept reviewers',
 *     trainingData: {
 *       proposals: [],
 *       reviews: []
 *     },
 *     parameters: {}
 *   },
 *   criteria
 * );
 * 
 * // Create an AI reviewer integration
 * const integration = new AIReviewerIntegration(factory);
 * 
 * // Register the reviewer
 * integration.registerReviewer(reviewer);
 * 
 * // Review a proposal
 * const reviewResult = await integration.reviewProposal(
 *   proposal,
 *   AIReviewerType.CONCEPT_REVIEWER
 * );
 * 
 * // Learn from a human review
 * await integration.learnFromReview(proposal, humanReviewResult);
 */
