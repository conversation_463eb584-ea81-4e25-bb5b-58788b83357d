/**
 * Pragma Filepath Linguistics
 * 
 * This module demonstrates how pragma composes functions based on filepath extensions
 * using linguistic literals, converting the linguistic pattern of extensions into
 * a linguistic pipeline.
 */

/**
 * Filepath extension processor
 */
interface ExtensionProcessor {
  name: string;
  process: <T>(input: T) => T;
}

/**
 * Registry of extension processors
 */
const extensionProcessors: Record<string, ExtensionProcessor> = {
  // Component type extensions
  'component': {
    name: 'component',
    process: <T>(input: T) => {
      console.log('Processing as component');
      return input;
    }
  },
  'page': {
    name: 'page',
    process: <T>(input: T) => {
      console.log('Processing as page');
      return input;
    }
  },
  'layout': {
    name: 'layout',
    process: <T>(input: T) => {
      console.log('Processing as layout');
      return input;
    }
  },
  
  // Behavior extensions
  'withState': {
    name: 'withState',
    process: <T>(input: T) => {
      console.log('Adding state management');
      return input;
    }
  },
  'withRouter': {
    name: 'withRouter',
    process: <T>(input: T) => {
      console.log('Adding router integration');
      return input;
    }
  },
  'withAuth': {
    name: 'withAuth',
    process: <T>(input: T) => {
      console.log('Adding authentication');
      return input;
    }
  },
  
  // Style extensions
  'styled': {
    name: 'styled',
    process: <T>(input: T) => {
      console.log('Adding styled components');
      return input;
    }
  },
  'themed': {
    name: 'themed',
    process: <T>(input: T) => {
      console.log('Adding theme support');
      return input;
    }
  },
  
  // Test extensions
  'test': {
    name: 'test',
    process: <T>(input: T) => {
      console.log('Adding test utilities');
      return input;
    }
  },
  'spec': {
    name: 'spec',
    process: <T>(input: T) => {
      console.log('Adding spec utilities');
      return input;
    }
  },
  'mock': {
    name: 'mock',
    process: <T>(input: T) => {
      console.log('Adding mock utilities');
      return input;
    }
  },
  
  // File type extensions
  'ts': {
    name: 'ts',
    process: <T>(input: T) => {
      console.log('Processing as TypeScript');
      return input;
    }
  },
  'tsx': {
    name: 'tsx',
    process: <T>(input: T) => {
      console.log('Processing as TypeScript with JSX');
      return input;
    }
  },
  'js': {
    name: 'js',
    process: <T>(input: T) => {
      console.log('Processing as JavaScript');
      return input;
    }
  },
  'jsx': {
    name: 'jsx',
    process: <T>(input: T) => {
      console.log('Processing as JavaScript with JSX');
      return input;
    }
  }
};

/**
 * Extracts extensions from a filepath
 * 
 * @param filepath - The filepath
 * @returns The extensions
 */
export function extractExtensions(filepath: string): string[] {
  // Get the filename without directory
  const filename = filepath.split('/').pop() || '';
  
  // Split by dots and remove the first part (base name)
  const parts = filename.split('.');
  const extensions = parts.slice(1);
  
  return extensions;
}

/**
 * Builds a linguistic pipeline from extensions
 * 
 * @param extensions - The extensions
 * @returns The pipeline
 */
export function buildPipeline(extensions: string[]): ExtensionProcessor[] {
  const pipeline: ExtensionProcessor[] = [];
  
  for (const ext of extensions) {
    const processor = extensionProcessors[ext];
    if (processor) {
      pipeline.push(processor);
    } else {
      console.warn(`Unknown extension: ${ext}`);
    }
  }
  
  return pipeline;
}

/**
 * Executes a pipeline on an input
 * 
 * @param pipeline - The pipeline
 * @param input - The input
 * @returns The processed output
 */
export function executePipeline<T>(pipeline: ExtensionProcessor[], input: T): T {
  return pipeline.reduce((result, processor) => processor.process(result), input);
}

/**
 * Linguistic literal for filepath processing
 * 
 * @param strings - Template strings
 * @param values - Template values
 * @returns The processed output
 */
export function fl<T>(strings: TemplateStringsArray, ...values: any[]): (input: T) => T {
  // Combine strings and values to get the filepath
  const filepath = String.raw(strings, ...values);
  
  // Extract extensions
  const extensions = extractExtensions(filepath);
  
  // Build pipeline
  const pipeline = buildPipeline(extensions);
  
  // Return a function that executes the pipeline
  return (input: T) => executePipeline(pipeline, input);
}

/**
 * Example usage of filepath linguistics
 */
export function exampleUsage(): void {
  // Process a component with state and styling
  const userProfileProcessor = fl`components/UserProfile.component.withState.styled.tsx`;
  const userProfileComponent = {}; // This would be the actual component
  const processedUserProfile = userProfileProcessor(userProfileComponent);
  
  console.log('\n---\n');
  
  // Process a page with routing and authentication
  const dashboardProcessor = fl`pages/Dashboard.page.withRouter.withAuth.tsx`;
  const dashboardPage = {}; // This would be the actual page
  const processedDashboard = dashboardProcessor(dashboardPage);
  
  console.log('\n---\n');
  
  // Process a test file
  const userServiceTestProcessor = fl`services/user.service.test.spec.ts`;
  const userServiceTest = {}; // This would be the actual test
  const processedUserServiceTest = userServiceTestProcessor(userServiceTest);
}

/**
 * Builds an index file using filepath linguistics
 * 
 * @param dirPath - The directory path
 * @returns The index file content
 */
export function buildIndexFile(dirPath: string): string {
  // Get all files in the directory
  const files = [
    'UserProfile.component.withState.styled.tsx',
    'Dashboard.page.withRouter.withAuth.tsx',
    'Navigation.component.withRouter.tsx',
    'AuthForm.component.withState.tsx'
  ];
  
  // Generate imports
  const imports = files.map(file => {
    const name = file.split('.')[0];
    return `import ${name} from './${file}';`;
  }).join('\n');
  
  // Generate exports
  const exports = files.map(file => {
    const name = file.split('.')[0];
    return `export { ${name} };`;
  }).join('\n');
  
  // Generate default export
  const defaultExport = `
export default {
${files.map(file => {
  const name = file.split('.')[0];
  return `  ${name}`;
}).join(',\n')}
};
`;
  
  // Combine everything
  return `${imports}\n\n${exports}\n\n${defaultExport}`;
}

/**
 * Builds a test file using filepath linguistics
 * 
 * @param filepath - The filepath
 * @returns The test file content
 */
export function buildTestFile(filepath: string): string {
  // Extract extensions
  const extensions = extractExtensions(filepath);
  
  // Get the base name
  const baseName = filepath.split('/').pop()?.split('.')[0] || '';
  
  // Generate test spec based on extensions
  let spec = `# ${baseName} Tests\n\n`;
  
  if (extensions.includes('component')) {
    spec += `## Component Tests\n`;
    spec += `- renders correctly\n`;
    spec += `  // Verifies that the component renders without errors\n\n`;
    spec += `- matches snapshot\n`;
    spec += `  // Verifies that the component's output matches the snapshot\n\n`;
  }
  
  if (extensions.includes('withState')) {
    spec += `## State Tests\n`;
    spec += `- initializes with correct state\n`;
    spec += `  // Verifies that the component initializes with the correct state\n\n`;
    spec += `- updates state correctly\n`;
    spec += `  // Verifies that the component updates state correctly\n\n`;
  }
  
  if (extensions.includes('withRouter')) {
    spec += `## Router Tests\n`;
    spec += `- navigates correctly\n`;
    spec += `  // Verifies that the component navigates correctly\n\n`;
    spec += `- handles route parameters\n`;
    spec += `  // Verifies that the component handles route parameters correctly\n\n`;
  }
  
  if (extensions.includes('withAuth')) {
    spec += `## Authentication Tests\n`;
    spec += `- handles authentication correctly\n`;
    spec += `  // Verifies that the component handles authentication correctly\n\n`;
    spec += `- redirects unauthenticated users\n`;
    spec += `  // Verifies that the component redirects unauthenticated users\n\n`;
  }
  
  // Process the spec through the linguistic function pipe
  // In a real implementation, this would use the l tag from test-pragma
  return spec;
}

/**
 * Example of building an index file and test file
 */
export function exampleBuildFiles(): void {
  // Build index file
  const indexContent = buildIndexFile('components');
  console.log('Index file content:');
  console.log(indexContent);
  
  console.log('\n---\n');
  
  // Build test file
  const testContent = buildTestFile('components/UserProfile.component.withState.styled.tsx');
  console.log('Test file content:');
  console.log(testContent);
}

// If this file is run directly, run the examples
if (require.main === module) {
  console.log('Example usage of filepath linguistics:');
  exampleUsage();
  
  console.log('\n==========\n');
  
  console.log('Example of building files:');
  exampleBuildFiles();
}
