/**
 * Linguistic Function Pipe
 * 
 * This module implements the linguistic function pipe that transforms
 * human-readable specifications into test specifications.
 */

/**
 * Section in a specification
 */
interface Section {
  title: string;
  content: string;
}

/**
 * Test case extracted from a specification
 */
interface TestCase {
  section: string;
  description: string;
  signature?: string;
  comment?: string;
}

/**
 * Test implementation inferred from a test case
 */
interface TestImplementation {
  section: string;
  description: string;
  implementation: string;
}

/**
 * Parameter in a function signature
 */
interface Parameter {
  name: string;
  type: string;
  optional: boolean;
}

/**
 * Function signature
 */
interface FunctionSignature {
  functionName: string;
  parameters: Parameter[];
  returnType: string;
}

/**
 * Parses markdown sections from a specification
 * 
 * @param spec - The specification
 * @returns The parsed sections
 */
export function parseMarkdownSections(spec: string): Section[] {
  const sections: Section[] = [];
  
  // Split by heading markers
  const parts = spec.split(/^#+\s+/m);
  
  // Process each part
  for (const part of parts) {
    if (!part.trim()) continue;
    
    // Extract section title and content
    const lines = part.split('\n');
    const title = lines[0].trim();
    const content = lines.slice(1).join('\n');
    
    sections.push({ title, content });
  }
  
  return sections;
}

/**
 * Extracts test cases from sections
 * 
 * @param sections - The sections
 * @returns The test cases
 */
export function extractTestCases(sections: Section[]): TestCase[] {
  const testCases: TestCase[] = [];
  
  for (const section of sections) {
    // Split by bullet points
    const cases = section.content.split(/^-\s+/m);
    
    for (const caseContent of cases) {
      if (!caseContent.trim()) continue;
      
      // Extract test case details
      const lines = caseContent.split('\n').map(line => line.trim());
      const description = lines[0];
      const signature = lines.find(line => line.includes('(') && line.includes(')'));
      const comment = lines.find(line => line.startsWith('//'));
      
      testCases.push({
        section: section.title,
        description,
        signature,
        comment
      });
    }
  }
  
  return testCases;
}

/**
 * Parses a function signature
 * 
 * @param signature - The function signature
 * @returns The parsed signature
 */
export function parseSignature(signature?: string): FunctionSignature {
  if (!signature) {
    return {
      functionName: 'unknown',
      parameters: [],
      returnType: 'any'
    };
  }
  
  // Extract function name
  const functionNameMatch = signature.match(/^(\w+)/);
  const functionName = functionNameMatch ? functionNameMatch[1] : 'unknown';
  
  // Extract parameters
  const parametersMatch = signature.match(/\((.*?)\)/);
  const parametersString = parametersMatch ? parametersMatch[1] : '';
  const parameters: Parameter[] = [];
  
  if (parametersString) {
    const parameterParts = parametersString.split(',');
    
    for (const part of parameterParts) {
      const [nameAndOptional, type] = part.split(':').map(p => p.trim());
      const optional = nameAndOptional.endsWith('?');
      const name = optional ? nameAndOptional.slice(0, -1) : nameAndOptional;
      
      parameters.push({
        name,
        type: type || 'any',
        optional
      });
    }
  }
  
  // Extract return type
  const returnTypeMatch = signature.match(/\):\s*(.*?)$/);
  const returnType = returnTypeMatch ? returnTypeMatch[1] : 'any';
  
  return {
    functionName,
    parameters,
    returnType
  };
}

/**
 * Analyzes a description to determine the test scenario
 * 
 * @param description - The description
 * @returns The scenario
 */
export function analyzeDescription(description: string): string {
  // In a real implementation, this would use NLP to analyze the description
  // For now, we'll just return the description
  return description;
}

/**
 * Generates parameter values based on parameter types and scenario
 * 
 * @param parameters - The parameters
 * @param scenario - The scenario
 * @returns The parameter values
 */
export function generateParameterValues(parameters: Parameter[], scenario: string): string[] {
  return parameters.map(param => {
    switch (param.type) {
      case 'string':
        return `'test${param.name}'`;
      case 'number':
        return '42';
      case 'boolean':
        return 'true';
      case 'Resource':
        return "{ name: 'Test Resource' }";
      default:
        if (param.type.includes('[]')) {
          return '[]';
        } else if (param.type.includes('Promise')) {
          return 'Promise.resolve()';
        } else {
          return '{}';
        }
    }
  });
}

/**
 * Generates assertions based on return type and scenario
 * 
 * @param returnType - The return type
 * @param scenario - The scenario
 * @returns The assertions
 */
export function generateAssertions(returnType: string, scenario: string): string[] {
  const assertions: string[] = [];
  
  if (returnType.includes('Promise')) {
    // Extract the type inside the Promise
    const innerTypeMatch = returnType.match(/Promise<(.*)>/);
    const innerType = innerTypeMatch ? innerTypeMatch[1] : 'any';
    
    return generateAssertions(innerType, scenario);
  }
  
  if (returnType === 'void') {
    return [];
  }
  
  if (returnType === 'boolean') {
    assertions.push('expect(result).toBe(true);');
  } else if (returnType === 'number') {
    assertions.push('expect(result).toBeGreaterThan(0);');
  } else if (returnType === 'string') {
    assertions.push('expect(result).toBeTruthy();');
  } else if (returnType === 'AuthResult') {
    assertions.push('expect(result.success).toBe(true);');
    assertions.push('expect(result.token).toBeDefined();');
  } else if (returnType === 'ResourceWithId' || returnType.includes('Resource')) {
    assertions.push('expect(result.id).toBeDefined();');
    assertions.push("expect(result.name).toBe('Test Resource');");
  } else if (returnType === 'DeleteResult') {
    assertions.push('expect(result.success).toBe(true);');
  } else if (returnType.includes('[]')) {
    assertions.push('expect(Array.isArray(result)).toBe(true);');
  } else {
    assertions.push('expect(result).toBeDefined();');
  }
  
  return assertions;
}

/**
 * Generates setup code based on scenario
 * 
 * @param scenario - The scenario
 * @returns The setup code
 */
export function generateSetup(scenario: string): string {
  if (scenario.includes('invalid')) {
    return "// Testing invalid case";
  } else if (scenario.includes('error')) {
    return `
    try {
      // Testing error case`;
  } else {
    return "// Testing valid case";
  }
}

/**
 * Infers test implementation from a test case
 * 
 * @param testCase - The test case
 * @returns The test implementation
 */
export function inferTestImplementation(testCase: TestCase): TestImplementation {
  const { functionName, parameters, returnType } = parseSignature(testCase.signature);
  
  // Analyze the description to determine the test scenario
  const scenario = analyzeDescription(testCase.description);
  
  // Generate parameter values based on parameter types
  const paramValues = generateParameterValues(parameters, scenario);
  
  // Generate assertions based on return type and scenario
  const assertions = generateAssertions(returnType, scenario);
  
  // Generate setup code
  const setup = generateSetup(scenario);
  
  // Determine if the function is async
  const isAsync = returnType.includes('Promise');
  
  // Generate the implementation
  let implementation = '';
  
  if (isAsync) {
    implementation = `async () => {
    ${setup}
    const result = await api.${functionName}(${paramValues.join(', ')});
    ${assertions.join('\n    ')}
  }`;
  } else {
    implementation = `() => {
    ${setup}
    const result = api.${functionName}(${paramValues.join(', ')});
    ${assertions.join('\n    ')}
  }`;
  }
  
  return {
    section: testCase.section,
    description: testCase.description,
    implementation
  };
}

/**
 * Groups test implementations by section
 * 
 * @param testImplementations - The test implementations
 * @returns The grouped implementations
 */
export function groupBySection(testImplementations: TestImplementation[]): Record<string, TestImplementation[]> {
  const grouped: Record<string, TestImplementation[]> = {};
  
  for (const impl of testImplementations) {
    if (!grouped[impl.section]) {
      grouped[impl.section] = [];
    }
    
    grouped[impl.section].push(impl);
  }
  
  return grouped;
}

/**
 * Generates a test module from test implementations
 * 
 * @param testImplementations - The test implementations
 * @returns The test module code
 */
export function generateTestModule(testImplementations: TestImplementation[]): string {
  let code = 'function testModule(scope) {\n';
  code += '  const { describe, it, expect, api, fail } = scope;\n\n';
  
  // Group by section
  const sections = groupBySection(testImplementations);
  
  for (const [section, implementations] of Object.entries(sections)) {
    code += `  describe('${section}', () => {\n`;
    
    for (const impl of implementations) {
      code += `    it('${impl.description}', ${impl.implementation});\n`;
    }
    
    code += '  });\n\n';
  }
  
  code += '}\n\nexport default testModule;';
  
  return code;
}

/**
 * Processes a specification through the linguistic function pipe
 * 
 * @param spec - The specification
 * @returns The test module function
 */
export function processSpec(spec: string): (scope: any) => void {
  // Process the spec through the linguistic function pipe
  const sections = parseMarkdownSections(spec);
  const testCases = extractTestCases(sections);
  const testImplementations = testCases.map(inferTestImplementation);
  const moduleCode = generateTestModule(testImplementations);
  
  // In a real implementation, this would use a safer evaluation method
  // For demonstration purposes, we're using eval
  // eslint-disable-next-line no-eval
  const moduleFunc = eval(moduleCode);
  
  return moduleFunc;
}

/**
 * Linguistic template literal tag
 * 
 * @param strings - Template strings
 * @param values - Template values
 * @returns The test module function
 */
export function l(strings: TemplateStringsArray, ...values: any[]): (scope: any) => void {
  const spec = String.raw(strings, ...values);
  return processSpec(spec);
}
