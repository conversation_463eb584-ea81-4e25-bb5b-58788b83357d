/**
 * Test Grammar
 * 
 * This module defines the grammar namespace for the test package.
 * The grammar maps linguistic patterns to sequences of vocabulary functions.
 */

import { vocabulary } from './test-vocabulary';

/**
 * Entity creation patterns
 */
export const creationPatterns = {
  // Generic creation patterns
  "creates a {entity}": (entity: string) => [
    vocabulary.create(entity, {}),
    vocabulary.getAll(entity),
    vocabulary.exists(entity, {})
  ],
  
  "creates a {entity} with {properties}": (entity: string, properties: any) => [
    vocabulary.create(entity, properties),
    vocabulary.getAll(entity),
    vocabulary.exists(entity, properties)
  ],
  
  "creates multiple {entity}": (entity: string) => [
    vocabulary.createMultiple(entity, [{}, {}, {}]),
    vocabulary.getAll(entity),
    vocabulary.count(3)
  ],
  
  // Domain-specific creation patterns
  "registers a user": [
    vocabulary.create('User', { username: 'testuser', email: '<EMAIL>', password: 'password123' }),
    vocabulary.getAll('User'),
    vocabulary.exists('User', { username: 'testuser' })
  ],
  
  "adds a product to the catalog": [
    vocabulary.create('Product', { name: 'Test Product', price: 9.99 }),
    vocabulary.getAll('Product'),
    vocabulary.exists('Product', { name: 'Test Product' })
  ],
  
  "creates a todo item": [
    vocabulary.create('Todo', { title: 'Test Todo', completed: false }),
    vocabulary.getAll('Todo'),
    vocabulary.exists('Todo', { title: 'Test Todo' })
  ]
};

/**
 * Entity retrieval patterns
 */
export const retrievalPatterns = {
  // Generic retrieval patterns
  "gets all {entity}": (entity: string) => [
    vocabulary.getAll(entity)
  ],
  
  "gets {entity} by id": (entity: string) => [
    vocabulary.getById(entity, 'test-id')
  ],
  
  "filters {entity} by {property}": (entity: string, property: string) => [
    vocabulary.getFiltered(entity, { [property]: 'test-value' })
  ],
  
  // Domain-specific retrieval patterns
  "lists all users": [
    vocabulary.getAll('User')
  ],
  
  "shows product details": [
    vocabulary.getById('Product', 'test-id')
  ],
  
  "displays completed todos": [
    vocabulary.getFiltered('Todo', { completed: true })
  ]
};

/**
 * Entity update patterns
 */
export const updatePatterns = {
  // Generic update patterns
  "updates a {entity}": (entity: string) => [
    vocabulary.updateById(entity, 'test-id', { updated: true }),
    vocabulary.getById(entity, 'test-id'),
    vocabulary.hasProperties({ updated: true })
  ],
  
  "updates {entity} {property}": (entity: string, property: string) => [
    vocabulary.updateById(entity, 'test-id', { [property]: 'updated-value' }),
    vocabulary.getById(entity, 'test-id'),
    vocabulary.hasProperties({ [property]: 'updated-value' })
  ],
  
  // Domain-specific update patterns
  "marks a todo as complete": [
    vocabulary.updateById('Todo', 'test-id', { completed: true }),
    vocabulary.getById('Todo', 'test-id'),
    vocabulary.hasProperties({ completed: true })
  ],
  
  "changes user password": [
    vocabulary.updateById('User', 'test-id', { password: 'newpassword123' }),
    vocabulary.getById('User', 'test-id'),
    vocabulary.hasProperties({ password: 'newpassword123' })
  ],
  
  "updates product price": [
    vocabulary.updateById('Product', 'test-id', { price: 19.99 }),
    vocabulary.getById('Product', 'test-id'),
    vocabulary.hasProperties({ price: 19.99 })
  ]
};

/**
 * Entity deletion patterns
 */
export const deletionPatterns = {
  // Generic deletion patterns
  "deletes a {entity}": (entity: string) => [
    vocabulary.deleteById(entity, 'test-id'),
    vocabulary.getAll(entity),
    vocabulary.notExists(entity, { id: 'test-id' })
  ],
  
  "deletes all {entity}": (entity: string) => [
    vocabulary.deleteAll(entity),
    vocabulary.getAll(entity),
    vocabulary.count(0)
  ],
  
  // Domain-specific deletion patterns
  "removes a user": [
    vocabulary.deleteById('User', 'test-id'),
    vocabulary.getAll('User'),
    vocabulary.notExists('User', { id: 'test-id' })
  ],
  
  "deletes a product from the catalog": [
    vocabulary.deleteById('Product', 'test-id'),
    vocabulary.getAll('Product'),
    vocabulary.notExists('Product', { id: 'test-id' })
  ],
  
  "clears completed todos": [
    vocabulary.deleteFiltered('Todo', { completed: true }),
    vocabulary.getAll('Todo'),
    vocabulary.all(todo => !todo.completed)
  ]
};

/**
 * Validation patterns
 */
export const validationPatterns = {
  // Generic validation patterns
  "{entity} requires {fields}": (entity: string, fields: string[]) => [
    vocabulary.requiresFields(entity, fields)
  ],
  
  "{entity} validates {field} format": (entity: string, field: string) => [
    vocabulary.validatesFormat(entity, field, 'invalid-value')
  ],
  
  "{entity} requires unique {field}": (entity: string, field: string) => [
    vocabulary.requiresUnique(entity, field, 'test-value')
  ],
  
  // Domain-specific validation patterns
  "user requires email and password": [
    vocabulary.requiresFields('User', ['email', 'password'])
  ],
  
  "validates email format": [
    vocabulary.validatesFormat('User', 'email', 'invalid-email')
  ],
  
  "requires unique username": [
    vocabulary.requiresUnique('User', 'username', 'testuser')
  ],
  
  "todo requires a title": [
    vocabulary.requiresFields('Todo', ['title'])
  ],
  
  "product requires name and price": [
    vocabulary.requiresFields('Product', ['name', 'price'])
  ]
};

/**
 * Business logic patterns
 */
export const businessLogicPatterns = {
  // Authentication patterns
  "authenticates with valid credentials": [
    (api: any) => api.authenticate('testuser', 'password123'),
    (result: any) => {
      expect(result.success).toBe(true);
      expect(result.token).toBeDefined();
      return result;
    }
  ],
  
  "rejects invalid credentials": [
    (api: any) => api.authenticate('testuser', 'wrongpassword'),
    (result: any) => {
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      return result;
    }
  ],
  
  // Order processing patterns
  "processes an order": [
    (api: any) => api.createOrder({ items: [{ productId: 'test-id', quantity: 1 }] }),
    (result: any) => {
      expect(result.id).toBeDefined();
      expect(result.status).toBe('processing');
      return result;
    }
  ],
  
  "calculates order total": [
    (api: any) => api.calculateTotal({ items: [{ productId: 'test-id', quantity: 2, price: 9.99 }] }),
    (result: any) => {
      expect(result).toBe(19.98);
      return result;
    }
  ],
  
  // Workflow patterns
  "completes a workflow": [
    (api: any) => api.startWorkflow('test-workflow'),
    (result: any) => api.completeWorkflowStep(result.id, 'step1'),
    (result: any) => api.completeWorkflowStep(result.id, 'step2'),
    (result: any) => api.completeWorkflowStep(result.id, 'step3'),
    (result: any) => {
      expect(result.status).toBe('completed');
      return result;
    }
  ]
};

/**
 * Combine all grammar patterns
 */
export const grammar = {
  ...creationPatterns,
  ...retrievalPatterns,
  ...updatePatterns,
  ...deletionPatterns,
  ...validationPatterns,
  ...businessLogicPatterns
};

/**
 * Builds a linguistic pipe from a pattern
 * 
 * @param pattern - The linguistic pattern
 * @param params - The parameters for the pattern
 * @returns The functional pipe
 */
export function buildLinguisticPipe(pattern: string, params: any[] = []): Function[] {
  // Check for parameterized patterns
  for (const [patternTemplate, pipeBuilder] of Object.entries(grammar)) {
    // Extract placeholders from pattern template
    const placeholders: string[] = [];
    const regexPattern = patternTemplate.replace(/{([^}]+)}/g, (_, placeholder) => {
      placeholders.push(placeholder);
      return '([^\\s]+)';
    });
    
    // Create regex from pattern template
    const regex = new RegExp(`^${regexPattern}$`);
    const match = pattern.match(regex);
    
    if (match) {
      // Extract values for placeholders
      const values = match.slice(1);
      
      // If the pipe builder is a function, call it with the extracted values
      if (typeof pipeBuilder === 'function') {
        return pipeBuilder(...values);
      }
      
      // Otherwise, return the pipe
      return pipeBuilder;
    }
  }
  
  // Check for exact pattern matches
  if (grammar[pattern]) {
    return grammar[pattern];
  }
  
  throw new Error(`Unknown linguistic pattern: ${pattern}`);
}

/**
 * Executes a linguistic pipe
 * 
 * @param pipe - The linguistic pipe
 * @param initialValue - The initial value
 * @returns The result of executing the pipe
 */
export function executePipe(pipe: Function[], initialValue: any): any {
  return pipe.reduce((value, func) => func(value), initialValue);
}
