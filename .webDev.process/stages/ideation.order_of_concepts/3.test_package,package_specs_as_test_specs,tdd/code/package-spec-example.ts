/**
 * Package Spec Example
 * 
 * This module demonstrates how to use the test package to create human-readable specs
 * that are converted to test specs through linguistic function pipes.
 */

import { l, runTests, createMock } from './test-package';

// Human-readable package specification
const packageSpec = l`
  # API Package Specification

  ## Authentication
  - authenticates with valid credentials
    authenticate(username: string, password: string): AuthResult
    // Validates user credentials and returns a token if valid

  - rejects invalid credentials
    authenticate(username: string, password: string): AuthResult
    // Returns an error when credentials are invalid

  - handles authentication errors
    authenticate(username: string, password: string): AuthResult
    // Properly handles and reports network or server errors

  ## Resource Management
  - creates resources
    createResource(resource: Resource): Promise<ResourceWithId>
    // Creates a new resource and assigns it an ID

  - retrieves resources
    getResource(id: string): Promise<Resource>
    // Fetches a resource by its ID

  - updates resources
    updateResource(id: string, resource: Resource): Promise<Resource>
    // Updates an existing resource with new data

  - deletes resources
    deleteResource(id: string): Promise<DeleteResult>
    // Removes a resource from the system
`;

// This would be converted by pragma to test specs:
function packageModule(scope: any) {
  const { describe, it, expect, api, fail } = scope;
  
  describe('API Package', () => {
    // Authentication Section
    describe('Authentication', () => {
      it('authenticates with valid credentials', () => {
        const result = api.authenticate('user', 'password');
        expect(result.success).toBe(true);
        expect(result.token).toBeDefined();
      });
      
      it('rejects invalid credentials', () => {
        const result = api.authenticate('user', 'wrong');
        expect(result.success).toBe(false);
        expect(result.error).toBe('Invalid credentials');
      });
      
      it('handles authentication errors', () => {
        const mockApi = {
          authenticate: () => { throw new Error('Network error'); }
        };
        
        try {
          mockApi.authenticate('user', 'password');
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.message).toBe('Network error');
        }
      });
    });
    
    // Resource Management Section
    describe('Resource Management', () => {
      it('creates resources', async () => {
        const resource = { name: 'Test Resource' };
        const result = await api.createResource(resource);
        expect(result.id).toBeDefined();
        expect(result.name).toBe('Test Resource');
      });
      
      it('retrieves resources', async () => {
        const resource = await api.getResource('123');
        expect(resource.id).toBe('123');
        expect(resource.name).toBeDefined();
      });
      
      it('updates resources', async () => {
        const resource = { id: '123', name: 'Updated Resource' };
        const result = await api.updateResource('123', resource);
        expect(result.name).toBe('Updated Resource');
      });
      
      it('deletes resources', async () => {
        const result = await api.deleteResource('123');
        expect(result.success).toBe(true);
      });
    });
  });
}

// The linguistic function pipe would transform the human-readable spec into test code
// This is a conceptual representation of how the transformation works:
const linguisticPipe = [
  parseMarkdownSections,       // Parse markdown sections into structured data
  extractTestCases,            // Extract test cases from each section
  inferTestImplementation,     // Infer test implementation from descriptions and types
  generateTestModule           // Generate the final test module
];

// Types that would be extracted from the spec
interface AuthResult {
  success: boolean;
  token?: string;
  error?: string;
}

interface Resource {
  id?: string;
  name: string;
  [key: string]: any;
}

interface ResourceWithId extends Resource {
  id: string;
}

interface DeleteResult {
  success: boolean;
  error?: string;
}

// API implementation that would be tested against the spec
const api = {
  authenticate(username: string, password: string): AuthResult {
    if (username === 'user' && password === 'password') {
      return { success: true, token: 'token123' };
    } else {
      return { success: false, error: 'Invalid credentials' };
    }
  },
  
  async createResource(resource: Resource): Promise<ResourceWithId> {
    return { id: '123', ...resource };
  },
  
  async getResource(id: string): Promise<Resource> {
    return { id, name: 'Test Resource' };
  },
  
  async updateResource(id: string, resource: Resource): Promise<Resource> {
    return { id, ...resource };
  },
  
  async deleteResource(id: string): Promise<DeleteResult> {
    return { success: true };
  }
};

// Run the test
runTests([packageModule], {
  api,
  fail: (message: string) => {
    throw new Error(message);
  }
});
