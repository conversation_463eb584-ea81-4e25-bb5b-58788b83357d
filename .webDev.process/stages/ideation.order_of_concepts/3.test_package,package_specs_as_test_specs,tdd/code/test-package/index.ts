/**
 * Test Package
 * 
 * This module exports the test package API.
 */

// Export categorical types
export * from './cat_types';

// Export linguistic structures
export * from './linguistic';

// Export test vocabulary
export * from './test_vocabulary';

// Export test grammar
export * from './test_grammar';

// Export linguistic pipe
export * from './linguistic_pipe';

// Export main API
import { l, createTestContext, runTest, runTestSpec } from './linguistic_pipe';
import { testVocabulary } from './test_vocabulary';
import { testGrammar } from './test_grammar';
import { LinguisticPipeImpl } from './linguistic';

/**
 * Main API
 */
export const api = {
  /**
   * Linguistic template literal tag for test specifications
   */
  l,
  
  /**
   * Creates a test context
   */
  createTestContext,
  
  /**
   * Runs a test module with the provided context
   */
  runTest,
  
  /**
   * Runs a test specification with the provided context
   */
  runTestSpec,
  
  /**
   * Test vocabulary
   */
  vocabulary: testVocabulary,
  
  /**
   * Test grammar
   */
  grammar: testGrammar,
  
  /**
   * Creates a linguistic pipe
   */
  createPipe: LinguisticPipeImpl.create
};

export default api;
