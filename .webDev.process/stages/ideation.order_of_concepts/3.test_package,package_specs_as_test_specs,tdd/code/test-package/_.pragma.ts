/**
 * Test Package Pragma
 * 
 * This module defines the pragma functions for the test package.
 * These functions are private and only available to index files.
 */

import * as fs from 'fs';
import * as path from 'path';
import { l, createTestContext, runTest } from './linguistic_pipe';

/**
 * Pragma function to build a test file from a spec file
 * 
 * @param specPath - The path to the spec file
 * @returns The path to the generated test file
 */
export function buildTestFile(specPath: string): string {
  // Read the spec file
  const spec = fs.readFileSync(specPath, 'utf-8');
  
  // Process the spec to get the test module
  const testModule = l(spec);
  
  // Generate the test file path
  const testPath = specPath.replace('.spec', '.test.ts');
  
  // Generate the test file content
  const content = `/**
 * Generated Test File
 * 
 * This file is generated by pragma. Do not edit directly.
 */

import { createTestContext } from '../test-package';
import * as api from './index';

${testModule.toString()}

// Create test context
const context = createTestContext({
  api,
  testSubject: api
});

// Run the test
testModule(context);
`;
  
  // Write the test file
  fs.writeFileSync(testPath, content);
  
  return testPath;
}

/**
 * Pragma function to build an index file for a node
 * 
 * @param nodePath - The path to the node
 * @returns The path to the generated index file
 */
export function buildIndexFile(nodePath: string): string {
  // Get all files in the node
  const files = fs.readdirSync(nodePath);
  
  // Filter out non-TypeScript files, test files, and index files
  const tsFiles = files.filter(file => 
    file.endsWith('.ts') && 
    !file.endsWith('.test.ts') && 
    !file.endsWith('.spec.ts') && 
    file !== 'index.ts' &&
    !file.startsWith('_.')
  );
  
  // Generate imports
  const imports = tsFiles.map(file => {
    const name = path.basename(file, '.ts');
    return `import * as ${name} from './${name}';`;
  }).join('\n');
  
  // Generate exports
  const exports = tsFiles.map(file => {
    const name = path.basename(file, '.ts');
    return `export * from './${name}';`;
  }).join('\n');
  
  // Generate named exports
  const namedExports = tsFiles.map(file => {
    const name = path.basename(file, '.ts');
    return `export { ${name} };`;
  }).join('\n');
  
  // Generate API object
  const apiObject = `export const api = {
${tsFiles.map(file => {
  const name = path.basename(file, '.ts');
  return `  ${name}`;
}).join(',\n')}
};`;
  
  // Generate default export
  const defaultExport = `export default api;`;
  
  // Combine everything
  const content = `/**
 * Generated Index File
 * 
 * This file is generated by pragma. Do not edit directly.
 */

${imports}

${exports}

${namedExports}

${apiObject}

${defaultExport}
`;
  
  // Write the index file
  const indexPath = path.join(nodePath, 'index.ts');
  fs.writeFileSync(indexPath, content);
  
  return indexPath;
}

/**
 * Pragma function to build all test files for a node
 * 
 * @param nodePath - The path to the node
 * @returns The paths to the generated test files
 */
export function buildAllTestFiles(nodePath: string): string[] {
  // Get all files in the node
  const files = fs.readdirSync(nodePath);
  
  // Filter out non-spec files
  const specFiles = files.filter(file => file.endsWith('.spec'));
  
  // Build test files for each spec file
  const testPaths = specFiles.map(file => {
    const specPath = path.join(nodePath, file);
    return buildTestFile(specPath);
  });
  
  return testPaths;
}

/**
 * Pragma function to run all tests for a node
 * 
 * @param nodePath - The path to the node
 * @returns The test results
 */
export function runAllTests(nodePath: string): any[] {
  // Get all files in the node
  const files = fs.readdirSync(nodePath);
  
  // Filter out non-test files
  const testFiles = files.filter(file => file.endsWith('.test.ts'));
  
  // Run each test file
  const results = testFiles.map(file => {
    const testPath = path.join(nodePath, file);
    
    // In a real implementation, this would use a proper test runner
    // For now, we'll just log that we're running the test
    console.log(`Running test: ${testPath}`);
    
    // Simulate running the test
    return {
      path: testPath,
      passed: true,
      results: []
    };
  });
  
  return results;
}

/**
 * Pragma function to build and run all tests for a node
 * 
 * @param nodePath - The path to the node
 * @returns The test results
 */
export function buildAndRunAllTests(nodePath: string): any[] {
  // Build all test files
  buildAllTestFiles(nodePath);
  
  // Run all tests
  return runAllTests(nodePath);
}
