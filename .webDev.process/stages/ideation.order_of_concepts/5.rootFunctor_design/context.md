````
this is the base categorical structure
designed to map on react
and to extend js
````
## root functors as implemented in core/cat.types package
- are rooted by the fact they inherit from another root functor
- they root a tree, each
- that tree is grown by invoking root functor with different args
- all functors can take args, as well as inherit from another functor
- root functors inherit from other root functors
- branch functors inherit from other branch functors
- a root functor can be a branch functor and be a branch functor progenitor
- branch functors of same tree are rooted by the same root functor
- branch functors of a tree are progenitors of other branch functors and leafs of the branch
- leafs and branch functors with lineage to same branch functor are a branch of a tree
- subjects of a tree branch is the branch categorie, constrained by morphisms of its branch functor
- what makes branches and leafs , all subjects of all branch cats, belong to same tree is shared context
- the do share the tree scope, but thats just an inevitable side effect of being in any tree
if they are placed out of context and into another tree, they assume scope of that tree, at
location of insertion, but they still carry their context from the tree of origin. this extends the notion of context in 
js. js object ARE the context and the scope of a tree formed by proto chains of an object. and therefore the confusion of
what and how and to what a method is bound to. we are resolving this conflict, categorically
- context of a tree forms a scope thru the tree, in same way scope does. But the context scope is not directly associated with structure
of each branch.hiowever, its proto chain is identical to the scope, at each node, but with different terms at each proto object
- thaT is to say - context is inherited by each branch functor, and passed to the subjects of the branch cat it constraints. 
by virtue of being their functor - they know whos the boss

this is mathematicaly consistent, rigorios
and it shines a light
why js is so confusing
to novices

```
it makes no distinction between scope and context
react tries to fix that
but stops short of fixing js - does not mod its base structure
it does not have a way to do it - lives outside of any structure common to js
by design, but pays the price

i bridge the disconnect, 
by introducing a layer of categorical structure, 

on top of js and react, 
that maps equally well on both
```