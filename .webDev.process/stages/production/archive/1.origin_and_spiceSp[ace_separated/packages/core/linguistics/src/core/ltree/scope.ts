import { v4 as uuidv4 } from 'uuid';
import {
  Scope,
  Term,
  ScopeAnalysis
} from './types';

/**
 * Implementation of a Scope
 */
export class BaseScope implements Scope {
  id: string;
  name: string;
  parent: <PERSON>ope | null;
  children: <PERSON>ope[];
  terms: Map<string, Term>;

  constructor(name: string, parent: <PERSON>ope | null = null) {
    this.id = uuidv4();
    this.name = name;
    this.parent = parent;
    this.children = [];
    this.terms = new Map<string, Term>();

    // Add this scope as a child of the parent
    if (parent) {
      parent.children.push(this);
    }
  }

  lookup(name: string): Term | null {
    // Check if the term exists in this scope
    if (this.terms.has(name)) {
      return this.terms.get(name)!;
    }

    // If not found and there's a parent scope, look there
    if (this.parent) {
      return this.parent.lookup(name);
    }

    // Not found
    return null;
  }

  define(name: string, term: Term): void {
    // Check if the term already exists in this scope
    if (this.terms.has(name)) {
      throw new Error(`Term ${name} already exists in scope ${this.name}`);
    }

    // Add the term to this scope
    this.terms.set(name, term);
  }

  createChild(name: string): Scope {
    // Create a new child scope
    return new BaseScope(name, this);
  }

  analyze(): ScopeAnalysis {
    // Calculate scope statistics
    const termCount = this.terms.size;
    const depth = this.calculateDepth();
    const breadth = this.children.length;
    const complexity = this.calculateComplexity();

    return {
      termCount,
      depth,
      breadth,
      complexity
    };
  }

  private calculateDepth(): number {
    // The depth is the maximum depth of any child scope, plus 1
    if (this.children.length === 0) {
      return 0;
    }

    return 1 + Math.max(...this.children.map(child => child.analyze().depth));
  }

  private calculateComplexity(): number {
    // The complexity is the sum of:
    // 1. The number of terms in this scope
    // 2. The complexity of each term
    // 3. The complexity of each child scope
    
    // Start with the number of terms
    let complexity = this.terms.size;
    
    // Add the complexity of each term
    for (const term of this.terms.values()) {
      complexity += term.analyze().complexity;
    }
    
    // Add the complexity of each child scope
    for (const child of this.children) {
      complexity += child.analyze().complexity;
    }
    
    return complexity;
  }
}
