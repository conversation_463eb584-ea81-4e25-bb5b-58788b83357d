{"name": "@spicetime/linguistics", "version": "0.1.0", "description": "Adaptive linguistic package for SpiceTime architecture", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:minimal": "tsc -p tsconfig.minimal.json", "build:standard": "tsc -p tsconfig.standard.json", "build:advanced": "tsc -p tsconfig.advanced.json", "test": "jest", "test:minimal": "jest --config jest.minimal.config.js", "test:standard": "jest --config jest.standard.config.js", "test:advanced": "jest --config jest.advanced.config.js", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run clean && npm run build"}, "keywords": ["spicetime", "linguistics", "functional", "programming", "adaptive"], "author": "SpiceTime Team", "license": "MIT", "dependencies": {"uuid": "^9.0.0", "tiny-parse": "^0.3.5", "idb": "^7.1.1"}, "optionalDependencies": {"acorn": "^8.8.2", "fp-ts": "^2.16.0", "immutable": "^4.3.0", "axios": "^1.4.0", "ramda": "^0.29.0", "lodash": "^4.17.21", "nextcloud-client": "^0.2.0", "couchdb-client": "^0.3.0", "pg": "^8.10.0", "zod": "^3.21.4"}, "devDependencies": {"@types/acorn": "^4.0.6", "@types/jest": "^29.5.1", "@types/node": "^18.16.3", "@types/uuid": "^9.0.1", "@types/ramda": "^0.29.0", "@types/lodash": "^4.14.194", "@types/pg": "^8.6.6", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "eslint": "^8.39.0", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}, "peerDependencies": {"@spicetime/core": "^0.1.0"}, "engines": {"node": ">=14.0.0"}, "browser": {"./dist/storage/nextcloud.js": "./dist/storage/browser-nextcloud.js", "./dist/storage/couchdb.js": "./dist/storage/browser-couchdb.js", "./dist/storage/postgres.js": "./dist/storage/browser-postgres.js"}}