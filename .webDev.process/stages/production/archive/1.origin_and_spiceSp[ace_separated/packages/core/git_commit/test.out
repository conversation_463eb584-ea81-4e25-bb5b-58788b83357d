
> git_commit@ test /Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/git_commit
> vitest run


 RUN  v2.1.8 /Users/<USER>/WebstormProjects/spicetime-architecture/packages/utils/git_commit

 ❯ src/tests/get_operations.test.ts (21 tests | 2 failed) 13ms
   × Git Operations > getCommitHistory > should return commit history for the repository 3ms
     → expected "spy" to be called with arguments: [ Array(1) ]

Received: 

  1st spy call:

  Array [
-   "git log --pretty=format:\"%h %s\" -n 100",
+   "git log --pretty=format:\"%h %s\" -n 10 ",
  ]


Number of calls: 1

   × Git Operations > getCommitHistory > should limit the number of commits when specified 1ms
     → expected "spy" to be called with arguments: [ Array(1) ]

Received: 

  1st spy call:

  Array [
-   "git log --pretty=format:\"%h %s\" -n 1",
+   "git log --pretty=format:\"%h %s\" -n 1 ",
  ]


Number of calls: 1

 ✓ src/tests/cli.test.ts (1 test) 3ms
 ❯ src/tests/augment_integration.test.ts (11 tests | 1 failed) 9ms
   × augment_integration > generateCommitSuggestions > should generate commit suggestions from staged changes 5ms
     → Failed to generate commit suggestions: Failed to analyze file changes
 ❯ src/tests/augment_launcher.test.ts (9 tests | 9 failed) 5ms
   × augment_launcher > main > should check if current directory is a git repository 3ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should check if there are staged changes 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should generate commit suggestions 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should handle no suggestions 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should print suggestions in dry-run mode 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should launch Augment with context 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should use custom template if provided 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should handle errors when launching Augment 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
   × augment_launcher > main > should output debug information when debug option is true 0ms
     → generateCommitSuggestions.mockReturnValue is not a function
 ❯ src/tests/commit_analyzer.test.ts (6 tests | 4 failed) 11ms
   × Commit Analyzer > analyzeFileChanges > should analyze staged changes and return file metadata 5ms
     → expected +0 to be 2 // Object.is equality
   × Commit Analyzer > analyzeFileChanges > should handle errors and return empty array 2ms
     → Failed to analyze file changes
   × Commit Analyzer > organizeCommits > should group files by directory structure 1ms
     → expected 1 to be 2 // Object.is equality
   × Commit Analyzer > suggestCommitType > should generate commit messages based on file paths 1ms
     → expected undefined to be 'components' // Object.is equality

 Test Files  4 failed | 1 passed (5)
      Tests  16 failed | 32 passed (48)
   Start at  22:17:18
   Duration  343ms (transform 237ms, setup 0ms, collect 464ms, tests 41ms, environment 1ms, prepare 313ms)

 ELIFECYCLE  Test failed. See above for more details.
