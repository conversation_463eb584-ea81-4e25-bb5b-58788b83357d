/**
 * @module git_commit/types
 * @category Types
 * @packageDocumentation
 *
 * Type definitions for the Augment launcher
 */

import type { CommitSuggestion } from './augment_integration.type';

/**
 * Context data structure for Augment chat
 */
export interface AugmentContext {
  /** Type of context */
  type: string;
  /** Serialized commit suggestions */
  suggestions: string;
  /** Timestamp of context creation */
  timestamp: string;
}

/**
 * Options for the launcher
 */
export interface LauncherOptions {
  /** Show planned commits without launching Augment */
  dryRun?: boolean;
  /** Use a specific template (default: git-commit) */
  template?: string;
  /** Enable verbose logging */
  debug?: boolean;
}

/**
 * Result of the launcher operation
 */
export interface LauncherResult {
  /** Whether the operation was successful */
  success: boolean;
  /** Any error message if unsuccessful */
  error?: string;
  /** Number of suggestions generated */
  suggestionCount?: number;
}