#!/usr/bin/env node

/**
 * @module git_commit/cli
 * @category CLI
 * @packageDocumentation
 *
 * CLI entry point for git commit utility
 */

import { program } from 'commander';
import { main } from './augment_launcher';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

/**
 * Configures the CLI program
 * @returns Configured commander program
 */
export function configureCli() {
  return program
    .name('git_commit')
    .description('Git commit utility with Augment integration')
    .version('0.1.0')
    .option('--dry-run', 'Show planned commits without launching Augment')
    .option('--debug', 'Enable verbose logging')
    .option('--template <name>', 'Use a specific template (default: git-commit)')
    .action((options) => {
      try {
        main(options);
      } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : String(error));
        process.exit(1);
      }
    });
}

// Check if this file is being executed directly as the main module
const isMainModule = () => {
  // For ESM
  if (typeof import.meta.url === 'string') {
    const currentFilePath = fileURLToPath(import.meta.url);
    const currentFileDir = dirname(currentFilePath);
    return process.argv[1] && process.argv[1].startsWith(currentFileDir);
  }
  // For CommonJS
  return require.main === module;
};

// Only run CLI when this file is executed directly
if (isMainModule()) {
  configureCli().parse(process.argv);
}