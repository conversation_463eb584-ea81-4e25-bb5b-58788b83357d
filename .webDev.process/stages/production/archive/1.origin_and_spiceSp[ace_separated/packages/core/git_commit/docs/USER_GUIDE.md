# Git Commit User Guide

## Introduction

The `git_commit` package helps you create meaningful, consistent Git commit messages with the assistance of AI. It analyzes your staged changes, suggests appropriate commit messages, and helps you organize multiple changes into logical commits.

## Installation

```bash
# Install globally
npm install -g @local/git_commit

# Or install locally in your project
npm install --save-dev @local/git_commit
```

## Quick Start

1. Stage your changes using Git:
   ```bash
   git add <files>
   ```

2. Run the git-commit command:
   ```bash
   git-commit
   ```

3. Review the AI-suggested commit messages and select the ones you want to use.

4. The selected commits will be created automatically.

## Command Line Options

```
Usage: git-commit [options]

Options:
  -d, --dry-run       Show suggestions without creating commits
  -m, --message TEXT  Provide a custom commit message
  -t, --type TEXT     Specify commit type (feat, fix, docs, etc.)
  -s, --scope TEXT    Specify commit scope
  -a, --amend         Amend the previous commit
  -S, --sign          Sign the commit
  --temporal TEXT     Add temporal reference
  --reference TEXT    Add external reference
  -h, --help          Show help information
```

## Features

### AI-Assisted Commit Messages

The package uses Augment AI to analyze your changes and suggest appropriate commit messages. The AI considers:

- File types and locations
- The nature of the changes (additions, deletions, modifications)
- Patterns in the code
- Conventional commit format

### Conventional Commits

All suggested commits follow the [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Common types include:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or correcting tests
- `chore`: Changes to the build process or auxiliary tools

### Multiple Commits

If you have changes across different components or features, the tool will suggest organizing them into multiple commits. This helps keep your commit history clean and focused.

### Dry Run Mode

Use the `--dry-run` option to see what commits would be created without actually creating them:

```bash
git-commit --dry-run
```

### Custom Commit Messages

If you prefer to provide your own commit message:

```bash
git-commit --message "Your custom message" --type feat --scope auth
```

### Amending Commits

To amend the previous commit:

```bash
git-commit --amend
```

### Temporal References

Add temporal references to indicate when a change should be applied:

```bash
git-commit --temporal "after-release-2.0"
```

### External References

Link commits to external systems:

```bash
git-commit --reference "JIRA-123"
```

## Examples

### Basic Usage

```bash
# Stage some changes
git add src/components/Button.tsx

# Generate commit suggestions
git-commit
```

### Multiple Components

```bash
# Stage changes in different components
git add src/components/Button.tsx src/utils/format.ts

# Generate commit suggestions
git-commit
```

The tool might suggest two commits:
1. `feat(components): Update Button component`
2. `fix(utils): Fix formatting utility`

### Custom Commit with Scope

```bash
git-commit --message "Add authentication flow" --type feat --scope auth
```

Creates: `feat(auth): Add authentication flow`

### Amending with Sign

```bash
git-commit --amend --sign
```

Amends the previous commit and adds a signature.

## Best Practices

1. **Stage Related Changes Together**: Group related changes to get better commit suggestions.

2. **Review Suggestions Carefully**: While the AI is helpful, always review the suggestions to ensure they accurately describe your changes.

3. **Use Multiple Commits**: Don't hesitate to create multiple focused commits instead of one large commit.

4. **Provide Context When Needed**: For complex changes, consider adding a commit body with more details.

5. **Use Conventional Types**: Stick to standard conventional commit types for consistency.

## Troubleshooting

### No Suggestions Generated

- Ensure you have staged changes with `git add`
- Check that you're running the command in a Git repository
- Verify that the Augment AI service is accessible

### Error: "Failed to launch Augment"

- Check your internet connection
- Ensure you have the necessary permissions to access the Augment service
- Try again later if the service might be temporarily unavailable

### Error: "Failed to analyze file changes"

- Ensure you're in a valid Git repository
- Check that Git is installed and accessible in your PATH
- Verify that you have permission to read the repository files

## Integration with Other Tools

### Git Hooks

You can integrate `git-commit` with Git hooks to automatically suggest commits:

```bash
# In .git/hooks/prepare-commit-msg
#!/bin/sh
git-commit --dry-run > .git/COMMIT_EDITMSG
```

### CI/CD Pipelines

Use `git-commit` in CI/CD pipelines to validate commit messages:

```yaml
# Example GitHub Action
validate-commits:
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v2
    - name: Install git-commit
      run: npm install -g @local/git_commit
    - name: Validate commit messages
      run: git-commit --validate
```

## Advanced Configuration

Create a `.gitcommitrc` file in your project root to customize behavior:

```json
{
  "defaultType": "feat",
  "scopes": ["auth", "ui", "api", "docs"],
  "maxCommitLength": 72,
  "aiSuggestions": true,
  "conventionalCommit": true
}
```

## Support and Feedback

For issues, feature requests, or feedback, please create an issue in the repository or contact the maintainers directly.
