import { describe, it, expect, vi, beforeEach } from 'vitest';
import { configureCli } from '../cli';

describe('CLI', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  it('should configure CLI with correct options', () => {
    const program = configureCli();

    // Check program configuration
    expect(program.name()).toBe('git_commit');

    // Check options
    const options = program.opts();
    expect(options).toBeDefined();

    // Check command structure
    const commands = program.commands;
    expect(commands).toBeDefined();
  });
});