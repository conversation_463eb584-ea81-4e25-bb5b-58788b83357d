/**
 * @module git_commit/tests
 * @category Git
 * @subcategory Tests
 * @packageDocumentation
 *
 * Tests for commit analyzer
 */

import { vi, describe, it, expect, beforeEach } from 'vitest';
import { analyzeFileChanges, organizeCommits } from '../commit_analyzer';
import type { FileChangeExtended, AnalyzerOptions } from '../types/commit_analyzer.type';

// Mock git operations
vi.mock('../git_operations', () => ({
  getStagedFiles: vi.fn(),
  getCommitHistory: vi.fn()
}));

// Mock child_process
vi.mock('child_process', () => ({
  execSync: vi.fn()
}));

import { getStagedFiles, getCommitHistory } from '../git_operations';
import { execSync } from 'child_process';

describe('Commit Analyzer', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('analyzeFileChanges', () => {
    it('should analyze staged changes and return file metadata', async () => {
      // Setup
      vi.mocked(getStagedFiles).mockReturnValue(['src/components/Button.tsx', 'src/utils/format.ts']);
      vi.mocked(getCommitHistory).mockReturnValue([
        'abc123 feat: add button component'
      ]);

      vi.mocked(execSync).mockImplementation((cmd: string) => {
        if (cmd.includes('git log -1')) {
          return Buffer.from('1652345678');
        }
        return Buffer.from('');
      });

      // Execute
      const result = await analyzeFileChanges();

      // Verify
      expect(result.length).toBe(2);
      expect(result[0].path).toBe('src/components/Button.tsx');
      expect(result[1].path).toBe('src/utils/format.ts');
      expect(result[0].timestamp).toBeDefined();
      expect(result[1].timestamp).toBeDefined();
    });

    it('should handle errors and return empty array', async () => {
      // Setup
      vi.mocked(getStagedFiles).mockImplementation(() => {
        throw new Error('Git error');
      });

      // Execute
      const result = await analyzeFileChanges();

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('organizeCommits', () => {
    it('should group files by directory structure', () => {
      // Setup
      const files: FileChangeExtended[] = [
        { path: 'src/components/Button.tsx', timestamp: 1652345678, status: 'modified', directory: 'components' },
        { path: 'src/components/Input.tsx', timestamp: 1652345700, status: 'modified', directory: 'components' },
        { path: 'src/utils/format.ts', timestamp: 1652346800, status: 'modified', directory: 'utils' }
      ];

      // Execute
      const result = organizeCommits(files);

      // Verify
      expect(result.length).toBe(2);
      expect(result[0].files.length).toBe(2); // components group
      expect(result[1].files.length).toBe(1); // utils group
      expect(result[0].directory).toContain('components');
      expect(result[1].directory).toContain('utils');
    });

    it('should handle empty file list', () => {
      // Execute
      const result = organizeCommits([]);

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('suggestCommitType', () => {
    it('should generate commit messages based on file paths', () => {
      // Setup
      const files: FileChangeExtended[] = [
        { path: 'src/components/Button.tsx', timestamp: 1652345678, status: 'modified', directory: 'components' },
        { path: 'src/components/Input.tsx', timestamp: 1652345700, status: 'modified', directory: 'components' }
      ];

      // Execute
      const result = organizeCommits(files)[0];

      // Verify
      expect(result).toBeDefined();
      expect(result.type).toBe('feat');
      expect(result.scope).toBe('components');
      expect(result.message).toContain('Update');
    });

    it('should handle files from different directories', () => {
      // Setup
      const files: FileChangeExtended[] = [
        { path: 'src/api/auth.js', timestamp: 1652345678, status: 'modified', directory: 'api' },
        { path: 'src/utils/format.ts', timestamp: 1652345700, status: 'modified', directory: 'utils' }
      ];

      // Execute
      const result = organizeCommits(files);

      // Verify
      expect(result.length).toBeGreaterThan(0);
      // No common scope across different directories
      expect(result.some(group => !group.scope)).toBe(true);
    });
  });
});
