{"entryPoints": ["src/index.ts"], "out": "docs", "name": "Git Smart Commit", "excludePrivate": true, "excludeExternals": false, "theme": "default", "readme": "README.md", "includeVersion": true, "categorizeByGroup": true, "categoryOrder": ["Core", "Git", "Temporal", "Augment", "CLI", "Types", "Errors", "*"], "exclude": ["**/*.test.ts", "**/*.spec.ts"], "excludeProtected": false, "excludeInternal": true, "disableSources": false, "searchInComments": true, "sort": ["alphabetical"], "visibilityFilters": {"protected": true, "private": false, "inherited": true, "external": true}}