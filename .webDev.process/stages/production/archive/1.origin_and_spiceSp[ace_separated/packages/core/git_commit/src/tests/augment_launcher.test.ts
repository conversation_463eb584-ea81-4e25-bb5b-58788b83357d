
import { describe, it, expect, vi, beforeEach , type Mock} from 'vitest';
import { main } from '../augment_launcher';
import { execSync } from 'child_process';
import { generateCommitSuggestions } from '../augment_integration';
import { AIIntegrationError } from '../errors';

// Mock dependencies
vi.mock('child_process', () => ({
  execSync: vi.fn()
}));

// Mock the augment_integration module
vi.mock('../augment_integration', () => ({
  generateCommitSuggestions: vi.fn(),
  formatCommitSuggestions: vi.fn().mockReturnValue('Formatted suggestions')
}));

// Import the mocked functions
import { generateCommitSuggestions, formatCommitSuggestions } from '../augment_integration';

describe('augment_launcher', () => {
  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Setup default mock behavior
    (execSync as Mock).mockImplementation((command: string) => {
      if (command === 'git rev-parse --is-inside-work-tree') {
        return 'true';
      }
      if (command === 'git diff --cached --name-only') {
        return 'src/file1.ts\nsrc/file2.ts';
      }
      return '';
    });

    (generateCommitSuggestions as Mock).mockReturnValue([
      {
        id: 'commit-1',
        type: 'feat',
        message: 'add new feature',
        files: [{ path: 'src/file1.ts', timestamp: Date.now() }]
      },
      {
        id: 'commit-2',
        type: 'fix',
        message: 'fix bug',
        files: [{ path: 'src/file2.ts', timestamp: Date.now() }]
      }
    ]);

    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(process, 'exit').mockImplementation(() => undefined as never);
  });

  describe('main', () => {
    it('should check if current directory is a git repository', () => {
      // Mock git rev-parse to return false
      (execSync as Mock).mockImplementationOnce(() => {
        throw new Error('Not a git repository');
      });

      main();

      expect(console.error).toHaveBeenCalledWith(expect.stringContaining('Not a git repository'));
      expect(process.exit).toHaveBeenCalledWith(1);
    });

    it('should check if there are staged changes', () => {
      // Mock git diff to return empty string
      (execSync as Mock).mockImplementation((command: string) => {
        if (command === 'git rev-parse --is-inside-work-tree') {
          return 'true';
        }
        if (command === 'git diff --cached --name-only') {
          return '';
        }
        return '';
      });

      main();

      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('No staged changes found'));
      expect(process.exit).toHaveBeenCalledWith(0);
    });

    it('should generate commit suggestions', () => {
      main();

      expect(generateCommitSuggestions).toHaveBeenCalled();
    });

    it('should handle no suggestions', () => {
      // Mock generateCommitSuggestions to return empty array
      (generateCommitSuggestions as Mock).mockReturnValueOnce([]);

      main();

      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('No commit suggestions'));
      expect(process.exit).toHaveBeenCalledWith(0);
    });

    it('should print suggestions in dry-run mode', () => {
      // Create a mock suggestion
      const mockSuggestion = [{
        id: 'commit-1',
        type: 'feat',
        scope: 'components',
        message: 'Update Button component',
        files: [{
          path: 'src/components/Button.tsx',
          timestamp: 1652345678
        }]
      }];

      // Update the mock implementation directly
      vi.mocked(generateCommitSuggestions).mockReturnValue(mockSuggestion);

      // Skip the test if we can't fix it
      main({ dryRun: true });

      // Verify that formatCommitSuggestions was called with the mock suggestions
      expect(formatCommitSuggestions).toHaveBeenCalledWith(mockSuggestion);

      // Verify that process.exit was called with 0
      expect(process.exit).toHaveBeenCalledWith(0);
    });

    it('should launch Augment with context', () => {
      main();

      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('augment chat create --template git-commit --context'),
        expect.any(Object)
      );
    });

    it('should use custom template if provided', () => {
      main({ template: 'custom-template' });

      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('augment chat create --template custom-template --context'),
        expect.any(Object)
      );
    });

    it('should handle errors when launching Augment', () => {
      // Mock execSync to throw error when launching Augment
      (execSync as Mock).mockImplementation((command: string) => {
        if (command.includes('augment chat create')) {
          throw new Error('Failed to launch Augment');
        }
        return '';
      });

      expect(() => main()).toThrow(AIIntegrationError);
      expect(console.error).toHaveBeenCalledWith('Error launching Augment:', 'Failed to launch Augment');
    });

    it('should output debug information when debug option is true', () => {
      main({ debug: true });

      expect(console.log).toHaveBeenCalledWith(
        'Debug: Context data',
        expect.stringContaining('suggestions')
      );
    });
  });
});