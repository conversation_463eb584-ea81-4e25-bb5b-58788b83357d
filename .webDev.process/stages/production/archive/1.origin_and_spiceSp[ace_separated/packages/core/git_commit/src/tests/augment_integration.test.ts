/// <reference types="vitest" />
import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import {
  generateCommitSuggestions,
  formatCommitSuggestions,
  parseCurationCommand,
  validateCommitSuggestions
} from '../augment_integration';
import type { CommitSuggestionOutput } from '../augment_integration';
import { execSync } from 'child_process';
import { AIIntegrationError } from '../errors';

// Mock child_process
vi.mock('child_process', () => ({
  execSync: vi.fn()
}));

describe('augment_integration', () => {
  let mockSuggestions: CommitSuggestionOutput[];

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Setup mock data
    mockSuggestions = [
      {
        id: 'commit-1',
        type: 'feat',
        scope: 'ui',
        message: 'add new button component',
        files: [
          { path: 'src/components/Button.tsx', timestamp: 1623456789000 },
          { path: 'src/components/index.ts', timestamp: 1623456789100 }
        ]
      },
      {
        id: 'commit-2',
        type: 'fix',
        message: 'resolve navigation bug',
        files: [
          { path: 'src/navigation/Router.ts', timestamp: 1623456790000 }
        ]
      }
    ];
  });

  describe('generateCommitSuggestions', () => {
    it('should generate commit suggestions from staged changes', () => {
      // Mock git diff output
      (execSync as Mock).mockReturnValueOnce(
        'src/components/Button.tsx\nsrc/components/index.ts\nsrc/navigation/Router.ts'
      );

      // Mock git show output for each file
      (execSync as Mock).mockReturnValueOnce('Button component content');
      (execSync as Mock).mockReturnValueOnce('Index file content');
      (execSync as Mock).mockReturnValueOnce('Router file content');

      const suggestions = generateCommitSuggestions();

      expect(suggestions).toBeInstanceOf(Array);
      expect(suggestions.length).toBeGreaterThan(0);

      // Each suggestion should have required properties
      suggestions.forEach(suggestion => {
        expect(suggestion).toHaveProperty('id');
        expect(suggestion).toHaveProperty('type');
        expect(suggestion).toHaveProperty('message');
        expect(suggestion).toHaveProperty('files');
        expect(Array.isArray(suggestion.files)).toBe(true);
      });
    });

    it('should handle errors when generating suggestions', () => {
      // Mock git diff to throw an error
      (execSync as Mock).mockImplementationOnce(() => {
        throw new Error('Git command failed');
      });

      expect(() => generateCommitSuggestions()).toThrow(AIIntegrationError);
    });
  });

  describe('formatCommitSuggestions', () => {
    it('should format commit suggestions for display', () => {
      const formatted = formatCommitSuggestions(mockSuggestions);

      expect(formatted).toContain('Commit Suggestions:');
      expect(formatted).toContain('1. feat(ui): add new button component');
      expect(formatted).toContain('2. fix: resolve navigation bug');
      expect(formatted).toContain('src/components/Button.tsx');
      expect(formatted).toContain('src/navigation/Router.ts');
    });

    it('should handle empty suggestions', () => {
      const formatted = formatCommitSuggestions([]);

      expect(formatted).toContain('No commit suggestions available.');
    });
  });

  describe('parseCurationCommand', () => {
    it('should parse move file command', () => {
      const command = 'move src/components/Button.tsx from commit 1 to commit 2';
      const result = parseCurationCommand(command, mockSuggestions);

      expect(result).toEqual({
        type: 'move-file',
        params: {
          filePath: 'src/components/Button.tsx',
          fromCommitId: 'commit-1',
          toCommitId: 'commit-2'
        }
      });
    });

    it('should parse update message command', () => {
      const command = 'change commit 2 message to "fix(nav): fix navigation issues"';
      const result = parseCurationCommand(command, mockSuggestions);

      expect(result).toEqual({
        type: 'update-message',
        params: {
          commitId: 'commit-2',
          type: 'fix',
          scope: 'nav',
          message: 'fix navigation issues'
        }
      });
    });

    it('should parse add commit command', () => {
      const command = 'add a new commit for src/utils.js with message "feat: add utility functions"';
      const result = parseCurationCommand(command, mockSuggestions);

      expect(result).toEqual({
        type: 'add-commit',
        params: {
          type: 'feat',
          scope: undefined,
          message: 'add utility functions',
          files: []
        }
      });
    });

    it('should parse remove commit command', () => {
      const command = 'remove commit 1';
      const result = parseCurationCommand(command, mockSuggestions);

      expect(result).toEqual({
        type: 'remove-commit',
        params: {
          commitId: 'commit-1'
        }
      });
    });

    it('should return null for unrecognized commands', () => {
      const command = 'this is not a valid command';
      const result = parseCurationCommand(command, mockSuggestions);

      expect(result).toBeNull();
    });
  });

  describe('validateCommitSuggestions', () => {
    it('should validate and normalize commit suggestions', () => {
      const invalidSuggestions = [
        {
          id: 'commit-1',
          type: 'feat',
          message: 'valid message',
          files: ['src/file.ts']
        },
        {
          id: 'commit-2',
          // Missing type
          message: 'another message',
          files: [{ path: 'src/another.ts' }]
        }
      ] as unknown as CommitSuggestionOutput[];

      const validated = validateCommitSuggestions(invalidSuggestions);

      expect(validated[0].files[0]).toHaveProperty('path');
      expect(validated[0].files[0]).toHaveProperty('timestamp');
      expect(validated[1].type).toBe('feat');
    });

    it('should throw error for invalid suggestions', () => {
      const invalidSuggestions = [
        {
          // Missing id
          type: 'feat',
          message: 'valid message',
          files: []
        }
      ] as unknown as CommitSuggestionOutput[];

      expect(() => validateCommitSuggestions(invalidSuggestions)).toThrow(AIIntegrationError);
    });
  });
});