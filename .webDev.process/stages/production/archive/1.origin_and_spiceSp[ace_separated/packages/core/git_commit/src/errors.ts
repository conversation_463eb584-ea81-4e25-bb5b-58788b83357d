/**
 * @module git_commit/errors
 * @category Git
 * @packageDocumentation
 *
 * Custom error types for git_commit operations
 */

import { STError } from '@local/utils_error';

/**
 * Base error class for git_commit operations
 */
export class GitCommitError extends ST<PERSON>rror {
  constructor(message: string, details?: Record<string, any>) {
    super(message, { domain: 'git_commit', ...details });
  }
}

/**
 * Error thrown when git repository operations fail
 */
export class GitRepoError extends GitCommitError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, { type: 'repository', ...details });
  }
}

/**
 * Error thrown when commit operations fail
 */
export class CommitError extends GitCommitError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, { type: 'commit', ...details });
  }
}

/**
 * Error thrown when temporal analysis fails
 */
export class TemporalAnalysisError extends GitCommitError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, { type: 'temporal_analysis', ...details });
  }
}

/**
 * Error thrown when AI integration fails
 */
export class AIIntegrationError extends GitCommitError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, { type: 'ai_integration', ...details });
  }
}