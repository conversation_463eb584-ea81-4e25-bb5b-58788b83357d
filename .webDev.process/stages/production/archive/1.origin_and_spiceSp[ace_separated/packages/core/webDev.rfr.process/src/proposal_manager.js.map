{"version": 3, "file": "proposal_manager.js", "sourceRoot": "", "sources": ["proposal_manager.ts"], "names": [], "mappings": "AAAA,MAAM,OAAO,eAAe;IACxB;;OAEG;IACH,cAAc,CAAC,OAKd;QACG,MAAM,UAAU,GAAG,YAAY,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC5C,MAAM,YAAY,GAAG,gBAAgB,UAAU,EAAE,CAAC;QAElD,4BAA4B;QAC5B,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAEhD,0BAA0B;QAC1B,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,EACxC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CACnC,CAAC;QAEF,qBAAqB;QACrB,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,OAAO,CAAC,WAAW,KAAK,CAAC,EACpD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CACtC,CAAC;QAEF,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,UAAkB;QAC5B,MAAM,YAAY,GAAG,gBAAgB,UAAU,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CACvB,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,EAAE,MAAM,CAAC,CACpE,CAAC;QAEF,gCAAgC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,WAAW,KAAK,CAAC,EACrD,MAAM,CACT,CAAC;QAEF,0BAA0B;QAC1B,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAE5D,+BAA+B;QAC/B,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAEtC,2BAA2B;QAC3B,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,EACtC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAC/D,CAAC;IACN,CAAC;IAEO,aAAa,CAAC,QAAgB,EAAE,KAAa;QACjD,IAAI,KAAK,KAAK,YAAY,EAAE;YACxB,OAAO,QAAQ,CAAC;SACnB;QAED,OAAO,QAAQ,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;IACnE,CAAC;IAEO,aAAa,CAAC,OAAY;QAC9B,6BAA6B;QAC7B,yCAAyC;QACzC,OAAO,yBAAyB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;CACJ"}