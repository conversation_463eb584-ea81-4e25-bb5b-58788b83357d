# Path Pragmas Convention: Beyond Branch Types

## Understanding Path Pragmas

In our system, the `@` symbol doesn't merely denote branch types but serves as a **path pragma** - a semantic modifier that can be applied to any path component. This provides a powerful and flexible way to express organizational structure, process stages, and semantic relationships.

## Core Concepts

### 1. Path Pragmas as Semantic Modifiers

A path pragma modifies the meaning of a path component:
```
component@pragma
```

For example:
- `utils@meta` - Meta information about utils (represented as `.utils`)
- `docs@id` - A specific instance/ID of documentation

### 2. Multiple Pragmas

Path components can have multiple pragmas:
```
component@pragma1@pragma2
```

For example:
- `utils@meta@id` - An identified instance of meta-information about utils

### 3. Hierarchical Application

Pragmas can be applied at any level of the path hierarchy:
```
root/component@pragma/subcomponent
```

## Common Pragma Types

### @meta
Indicates meta-information about a component rather than the component itself.
- `packages/utils@meta` → `packages/.utils`

### @id
Denotes a specific instance or stage of a process.
- `packages/utils@id` → A specific instance of the utils package
- `release@id` → A specific release stage

### Combined Examples

```
packages/utils@meta@id
```
**Meaning**: A specific instance of meta-information about the utils package
- This could represent documentation about a particular version of utils
- Or configuration specific to a development stage

## Organizational Reflection

This convention allows the repository structure to reflect organizational structure:

### Team Responsibility
```
dev@@auth/login
```
**Meaning**: The dev team is responsible for the auth/login component
- The double `@@` indicates a team assignment pragma

### Process Stages
```
<path@id>/termOfPath@meta>
```
**Meaning**:
- `path@id` - A specific instance/stage of a path
- `termOfPath@meta` - Meta-information about termOfPath

## Real-World Applications

### Development Process Representation
```
packages/utils@alpha/docs
packages/utils@beta/docs
packages/utils@release/docs
```
**Meaning**: Documentation for different stages of the utils package

### Organizational Structure Mapping
```
frontend@@team1/components
backend@@team2/api
```
**Meaning**: Team assignments for different parts of the codebase

### Decision Authority
```
core@@main-manager/config
```
**Meaning**: The main-manager has decision authority over core configuration
- Changes here are considered source of truth
- Conflicts are resolved in favor of this branch

## Benefits for Conflict Resolution

This convention supports a hierarchical decision-making process:

1. **Clear Authority**: Each path component can have a designated manager
2. **Source of Truth**: Changes by the responsible manager become the source of truth
3. **Simplified Merging**: Other contributors' changes are merged without conflict resolution misery
4. **Organizational Alignment**: Repository structure mirrors team structure and responsibilities

## Conclusion

By using path pragmas, we create a flexible yet structured naming convention that:
1. Reflects organizational structure
2. Provides clarity on decision authority
3. Supports various stages of development processes
4. Allows for semantic richness beyond simple categorization

This approach gives us the flexibility needed for real-world development scenarios while maintaining a coherent structure that can be understood by both humans and our tooling.