/**
 * Tic management implementation
 *
 * This file contains the implementation of tic-related helper functions.
 */

import { Process, TicInfo } from './types/index.type';

/**
 * Get the current process from the generator context
 *
 * @returns Current process
 */
export function _getCurrentProcess(): Process {
  // In a real implementation, this would get the current process from the generator context
  // For now, we'll just throw an error
  throw new Error('Not implemented: _getCurrentProcess');
}

/**
 * Request a tic
 *
 * @param priority - Priority of the tic request
 * @returns Whether a tic was allocated
 */
export function* requestTic(priority: number = 0): Generator<any, boolean, any> {
  const process = _getCurrentProcess();
  return yield process.requestTic(priority);
}

/**
 * Get remaining time in the current tic
 *
 * @returns Remaining time in ms
 */
export function getTicRemainingTime(): number {
  const process = _getCurrentProcess();

  if (!process.currentTic) {
    return 0;
  }

  const elapsed = Date.now() - process.currentTic.startTime;
  return Math.max(0, process.currentTic.duration - elapsed);
}

/**
 * Check if currently executing in a tic
 *
 * @returns Whether currently executing in a tic
 */
export function isInTic(): boolean {
  const process = _getCurrentProcess();
  return !!process.currentTic;
}

/**
 * Get information about the current tic
 *
 * @returns Current tic info or null if not in a tic
 */
export function getCurrentTic(): TicInfo | null {
  const process = _getCurrentProcess();
  return process.currentTic;
}

export default {
  requestTic,
  getTicRemainingTime,
  isInTic,
  getCurrentTic
};
