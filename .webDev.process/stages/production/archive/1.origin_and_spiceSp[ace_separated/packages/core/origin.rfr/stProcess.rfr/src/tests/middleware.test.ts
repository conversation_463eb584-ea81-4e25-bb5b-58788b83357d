import { describe, it, expect, vi } from 'vitest';
import { createMiddleware } from '../middleware';

describe('Middleware', () => {
  describe('createMiddleware', () => {
    it('should create a middleware manager', () => {
      const middleware = createMiddleware();
      
      expect(middleware).toBeDefined();
      expect(middleware.use).toBeDefined();
      expect(middleware.remove).toBeDefined();
      expect(middleware.execute).toBeDefined();
    });
  });
  
  describe('use', () => {
    it('should add middleware to the stack', async () => {
      const middleware = createMiddleware();
      const mockMiddleware = vi.fn((context, next) => next());
      
      middleware.use(mockMiddleware);
      
      // Execute to verify the middleware was added
      await middleware.execute({}, () => Promise.resolve('result'));
      
      expect(mockMiddleware).toHaveBeenCalled();
    });
  });
  
  describe('remove', () => {
    it('should remove middleware from the stack', async () => {
      const middleware = createMiddleware();
      const mockMiddleware = vi.fn((context, next) => next());
      
      middleware.use(mockMiddleware);
      middleware.remove(mockMiddleware);
      
      // Execute to verify the middleware was removed
      await middleware.execute({}, () => Promise.resolve('result'));
      
      expect(mockMiddleware).not.toHaveBeenCalled();
    });
  });
  
  describe('execute', () => {
    it('should execute middleware in order', async () => {
      const middleware = createMiddleware();
      const order: number[] = [];
      
      const middleware1 = vi.fn(async (context, next) => {
        order.push(1);
        const result = await next();
        order.push(4);
        return result;
      });
      
      const middleware2 = vi.fn(async (context, next) => {
        order.push(2);
        const result = await next();
        order.push(3);
        return result;
      });
      
      middleware.use(middleware1);
      middleware.use(middleware2);
      
      const result = await middleware.execute({}, () => {
        order.push(2.5);
        return Promise.resolve('result');
      });
      
      expect(result).toBe('result');
      expect(order).toEqual([1, 2, 2.5, 3, 4]);
    });
    
    it('should pass context to middleware', async () => {
      const middleware = createMiddleware();
      const context = { foo: 'bar' };
      const mockMiddleware = vi.fn((ctx, next) => next());
      
      middleware.use(mockMiddleware);
      
      await middleware.execute(context, () => Promise.resolve('result'));
      
      expect(mockMiddleware).toHaveBeenCalledWith(context, expect.any(Function));
    });
    
    it('should allow middleware to modify context', async () => {
      const middleware = createMiddleware();
      const context = { foo: 'bar' };
      
      const mockMiddleware = vi.fn((ctx, next) => {
        ctx.foo = 'baz';
        return next();
      });
      
      middleware.use(mockMiddleware);
      
      await middleware.execute(context, () => Promise.resolve('result'));
      
      expect(context.foo).toBe('baz');
    });
    
    it('should allow middleware to modify the result', async () => {
      const middleware = createMiddleware();
      
      const mockMiddleware = vi.fn(async (ctx, next) => {
        const result = await next();
        return `modified: ${result}`;
      });
      
      middleware.use(mockMiddleware);
      
      const result = await middleware.execute({}, () => Promise.resolve('result'));
      
      expect(result).toBe('modified: result');
    });
    
    it('should handle errors in middleware', async () => {
      const middleware = createMiddleware();
      const error = new Error('middleware error');
      
      const mockMiddleware = vi.fn(() => {
        throw error;
      });
      
      middleware.use(mockMiddleware);
      
      await expect(middleware.execute({}, () => Promise.resolve('result'))).rejects.toThrow(error);
    });
  });
});
