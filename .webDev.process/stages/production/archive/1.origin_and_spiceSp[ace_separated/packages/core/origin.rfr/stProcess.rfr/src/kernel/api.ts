/**
 * API request helpers
 */

import { v4 as uuidv4 } from 'uuid';
import { ApiRequest, ApiResponse } from './types';

/**
 * API request priority levels
 */
export enum ApiPriority {
  /** Critical system requests (highest priority) */
  CRITICAL = 0,
  /** High priority requests */
  HIGH = 10,
  /** Normal priority requests */
  NORMAL = 50,
  /** Low priority requests */
  LOW = 100,
  /** Background tasks (lowest priority) */
  BACKGROUND = 200
}

/**
 * Create an API request
 * 
 * @param type - Request type
 * @param payload - Request payload
 * @param sourceId - Source node ID
 * @param targetId - Target node ID
 * @param priority - Request priority
 * @returns API request
 */
export function createApiRequest(
  type: string,
  payload: any,
  sourceId: string,
  targetId: string,
  priority: ApiPriority = ApiPriority.NORMAL
): ApiRequest {
  return {
    id: uuidv4(),
    type,
    payload,
    priority,
    sourceId,
    targetId
  };
}

/**
 * Create a success response
 * 
 * @param requestId - Request ID
 * @param data - Response data
 * @returns API response
 */
export function createSuccessResponse(requestId: string, data: any): ApiResponse {
  return {
    requestId,
    status: 'success',
    data
  };
}

/**
 * Create an error response
 * 
 * @param requestId - Request ID
 * @param error - Error message
 * @returns API response
 */
export function createErrorResponse(requestId: string, error: string): ApiResponse {
  return {
    requestId,
    status: 'error',
    error
  };
}
