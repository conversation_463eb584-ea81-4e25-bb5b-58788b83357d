import { vi } from 'vitest';

// Mock the fs module
vi.mock('fs', () => ({
  exists: vi.fn((path, callback) => callback(true)),
  readFile: vi.fn((path, encoding, callback) => {
    if (typeof encoding === 'function') {
      encoding(null, 'mocked file content');
    } else {
      callback(null, 'mocked file content');
    }
  }),
  writeFile: vi.fn((path, content, encoding, callback) => {
    if (typeof encoding === 'function') {
      encoding(null);
    } else {
      callback(null);
    }
  }),
  mkdir: vi.fn((path, options, callback) => {
    if (typeof options === 'function') {
      options(null);
    } else {
      callback(null);
    }
  }),
  readdir: vi.fn((path, callback) => callback(null, ['file1', 'file2'])),
  rm: vi.fn((path, options, callback) => {
    if (typeof options === 'function') {
      options(null);
    } else {
      callback(null);
    }
  })
}));

// Mock the uuid module
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'test-uuid')
}));
