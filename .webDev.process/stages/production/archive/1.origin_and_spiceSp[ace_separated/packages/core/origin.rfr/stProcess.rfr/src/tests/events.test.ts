import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as events from '../events';
import { emitEvent, onEvent, broadcast, _getCurrentProcess } from '../events';

describe('Events', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('_getCurrentProcess', () => {
    it('should throw an error if not implemented', () => {
      // The _getCurrentProcess function is private, but we can test it indirectly
      // by calling functions that use it

      const generator = emitEvent('test');

      expect(() => generator.next()).toThrow('Not implemented: _getCurrentProcess');
    });
  });

  describe('emitEvent', () => {
    it('should emit an event on the current process', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockProcess = {
        emit: vi.fn().mockReturnValue(true)
      };

      // Mock the _getCurrentProcess function
      vi.spyOn(events, '_getCurrentProcess').mockReturnValue(mockProcess);

      const generator = emitEvent('test', 'arg1', 'arg2');
      const result = generator.next();

      expect(result.value).toBe(true);
      expect(mockProcess.emit).toHaveBeenCalledWith('test', 'arg1', 'arg2');
    });
  });

  describe('onEvent', () => {
    it('should listen for an event on the current process', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockProcess = {
        once: vi.fn((event, callback) => {
          // Simulate the event being triggered
          callback('event data');
        })
      };

      // Mock the _getCurrentProcess function
      vi.spyOn(events, '_getCurrentProcess').mockReturnValue(mockProcess);

      const generator = onEvent('test');
      const result = generator.next();

      // The result should be a Promise that resolves to 'event data'
      expect(result.value).toBeInstanceOf(Promise);
      expect(mockProcess.once).toHaveBeenCalledWith('test', expect.any(Function));

      return result.value.then(data => {
        expect(data).toBe('event data');
      });
    });

    it('should handle multiple arguments', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockProcess = {
        once: vi.fn((event, callback) => {
          // Simulate the event being triggered with multiple arguments
          callback('arg1', 'arg2', 'arg3');
        })
      };

      // Replace the private _getCurrentProcess function
      vi.spyOn(Object.getPrototypeOf(onEvent), '_getCurrentProcess').mockReturnValue(mockProcess);

      const generator = onEvent('test');
      const result = generator.next();

      return result.value.then(data => {
        expect(data).toEqual(['arg1', 'arg2', 'arg3']);
      });
    });
  });

  describe('broadcast', () => {
    it('should broadcast an event to all processes', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockChild1 = {
        emit: vi.fn()
      };

      const mockChild2 = {
        emit: vi.fn(),
        children: []
      };

      const mockRoot = {
        emit: vi.fn(),
        children: [mockChild1, mockChild2]
      };

      const mockProcess = {
        emit: vi.fn(),
        getRoot: vi.fn().mockReturnValue(mockRoot)
      };

      // Mock the _getCurrentProcess function
      vi.spyOn(events, '_getCurrentProcess').mockReturnValue(mockProcess);

      const generator = broadcast('test', 'arg1', 'arg2');
      generator.next();

      expect(mockProcess.getRoot).toHaveBeenCalled();
      expect(mockRoot.emit).toHaveBeenCalledWith('test', 'arg1', 'arg2');
      expect(mockChild1.emit).toHaveBeenCalledWith('test', 'arg1', 'arg2');
      expect(mockChild2.emit).toHaveBeenCalledWith('test', 'arg1', 'arg2');
    });
  });
});
