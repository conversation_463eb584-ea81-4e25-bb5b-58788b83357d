/**
 * Events implementation
 *
 * This file contains the implementation of event-related helper functions.
 */

import { Process, ProcessEvent } from './types/index.type';

/**
 * Get the current process from the generator context
 *
 * @returns Current process
 */
export function _getCurrentProcess(): Process {
  // In a real implementation, this would get the current process from the generator context
  // For now, we'll just throw an error
  throw new Error('Not implemented: _getCurrentProcess');
}

/**
 * Emit an event
 *
 * @param event - Event to emit
 * @param args - Event arguments
 * @returns Whether the event had listeners
 */
export function* emitEvent(event: ProcessEvent, ...args: any[]): Generator<any, boolean, any> {
  const process = _getCurrentProcess();
  return process.emit(event, ...args);
}

/**
 * Listen for an event
 *
 * @param event - Event to listen for
 * @returns Event data
 */
export function* onEvent(event: ProcessEvent): Generator<any, any, any> {
  const process = _getCurrentProcess();

  return new Promise(resolve => {
    process.once(event, (...args: any[]) => {
      resolve(args.length === 1 ? args[0] : args);
    });
  });
}

/**
 * Broadcast an event to all processes
 *
 * @param event - Event to broadcast
 * @param args - Event arguments
 */
export function* broadcast(event: ProcessEvent, ...args: any[]): Generator<any, void, any> {
  const process = _getCurrentProcess();
  const root = process.getRoot();

  // Recursively emit event on all processes
  function emitOnAll(proc: Process): void {
    proc.emit(event, ...args);

    for (const child of proc.children) {
      emitOnAll(child);
    }
  }

  emitOnAll(root);
}

export default {
  emitEvent,
  onEvent,
  broadcast
};
