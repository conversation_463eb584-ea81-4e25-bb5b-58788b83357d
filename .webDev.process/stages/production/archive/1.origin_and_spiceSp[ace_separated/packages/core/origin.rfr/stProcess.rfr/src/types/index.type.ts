/**
 * Type definitions for stProcess module
 *
 * This file contains TypeScript type definitions for the stProcess module.
 */

/**
 * Process state enum
 */
export enum ProcessState {
  /** Process has been created but not started */
  CREATED = 'created',
  /** Process is starting */
  STARTING = 'starting',
  /** Process is running */
  RUNNING = 'running',
  /** Process is paused */
  PAUSED = 'paused',
  /** Process is stopping */
  STOPPING = 'stopping',
  /** Process has been stopped */
  STOPPED = 'stopped',
  /** Process has completed successfully */
  COMPLETED = 'completed',
  /** Process encountered an error */
  ERROR = 'error'
}

/**
 * Information about a tic allocation
 */
export interface TicInfo {
  /** Unique tic identifier */
  id: string;
  /** Tic start timestamp */
  startTime: number;
  /** Expected tic duration (ms) */
  duration: number;
  /** Tic priority */
  priority: number;
  /** ID of the process that received the tic */
  processId: string;
}

/**
 * Events that can be emitted by a process
 */
export type ProcessEvent =
  | 'start'           // Process has started
  | 'stop'            // Process has stopped
  | 'pause'           // Process has been paused
  | 'resume'          // Process has been resumed
  | 'complete'        // Process has completed
  | 'error'           // Process encountered an error
  | 'ticRequest'      // Process is requesting a tic
  | 'ticAllocated'    // Tic has been allocated to the process
  | 'ticStart'        // Tic execution has started
  | 'ticEnd'          // Tic execution has ended
  | 'childAdded'      // Child process has been added
  | 'childRemoved'    // Child process has been removed
  | 'parentChanged'   // Parent process has changed
  | 'message'         // Process received a message
  | 'broadcast';      // Broadcast message received

/**
 * Process interface
 */
export interface Process<Props = any, Result = any> {
  /** Unique process identifier */
  id: string;
  /** Path in the filesystem */
  path: string;
  /** Parent process ID (if any) */
  parentId: string | null;
  /** Process properties */
  props: Props;
  /** Child processes */
  children: Process[];
  /** Current lifecycle state */
  state: ProcessState;

  /** Start the process */
  start(): Promise<void>;
  /** Stop the process */
  stop(): Promise<void>;
  /** Pause the process */
  pause(): Promise<void>;
  /** Resume the process */
  resume(): Promise<void>;

  /** Request a tic from kernel */
  requestTic(priority?: number): Promise<boolean>;
  /** Release current tic back to kernel */
  releaseTic(): void;

  /** Add a child process */
  appendChild(child: Process): void;
  /** Remove a child process */
  removeChild(child: Process): void;
  /** Get parent process */
  getParent(): Process | null;
  /** Get root process */
  getRoot(): Process;

  /** Register event listener */
  on(event: ProcessEvent, listener: Function): Process;
  /** Register one-time event listener */
  once(event: ProcessEvent, listener: Function): Process;
  /** Remove event listener */
  off(event: ProcessEvent, listener: Function): Process;
  /** Emit an event */
  emit(event: ProcessEvent, ...args: any[]): boolean;

  /** Get the final result of the process */
  getResult(): Promise<Result>;
}

/**
 * API request interface
 */
export interface ApiRequest {
  /** Unique request identifier */
  id: string;

  /** Request type */
  type: string;

  /** Request payload */
  payload: any;

  /** Request priority (lower number = higher priority) */
  priority: number;

  /** Source node ID */
  sourceId: string;

  /** Target node ID */
  targetId: string;
}

/**
 * API response interface
 */
export interface ApiResponse {
  /** Request ID this response is for */
  requestId: string;

  /** Response status */
  status: 'success' | 'error';

  /** Response data */
  data?: any;

  /** Error message (if status is error) */
  error?: string;
}

/**
 * Interface for interacting with the SpiceTime kernel
 */
export interface KernelInterface {
  /** Request a tic for a process */
  requestTic(processId: string, priority?: number): Promise<TicInfo | null>;
  /** Release a tic */
  releaseTic(ticId: string): void;

  /** Register a process with the kernel */
  registerProcess(process: Process): void;
  /** Unregister a process from the kernel */
  unregisterProcess(processId: string): void;
  /** Get a process by ID */
  getProcessById(processId: string): Process | null;

  /** Register a sergeant process */
  registerSergeant(process: Process, domain: string): void;
  /** Unregister a sergeant process */
  unregisterSergeant(processId: string): void;

  /** Get the current system load */
  getSystemLoad(): number;
  /** Get the system uptime */
  getSystemUptime(): number;

  /** Handle API request */
  handleApiRequest(request: ApiRequest): Promise<ApiResponse>;
  /** Send API request */
  sendApiRequest(request: ApiRequest): Promise<ApiResponse>;
}

/**
 * Middleware function type
 */
export type MiddlewareFunction = (context: any, next: () => Promise<any>) => Promise<any>;

/**
 * Interface for managing middleware in the process hierarchy
 */
export interface MiddlewareInterface {
  /** Add middleware to the stack */
  use(middleware: MiddlewareFunction): void;
  /** Remove middleware from the stack */
  remove(middleware: MiddlewareFunction): void;
  /** Execute middleware stack */
  execute(context: any, next: () => Promise<any>): Promise<any>;
}

/**
 * Interface for the CSS-like selector engine
 */
export interface SelectorInterface {
  /** Select processes matching the selector */
  select(selector: string): Process[];
  /** Apply properties to matching processes */
  apply(selector: string, properties: Record<string, any>): void;
  /** Check if a process matches a selector */
  matches(process: Process, selector: string): boolean;
  /** Register a dynamic selector rule */
  registerRule(condition: () => boolean, selector: string, properties: Record<string, any>): void;
}

/**
 * Interface for filesystem operations
 */
export interface FileSystemInterface {
  /** Check if a file exists */
  exists(path: string): Promise<boolean>;
  /** Read a file */
  readFile(path: string): Promise<string>;
  /** Write a file */
  writeFile(path: string, content: string): Promise<void>;
  /** Create a directory */
  mkdir(path: string): Promise<void>;
  /** Read a directory */
  readdir(path: string): Promise<string[]>;
  /** Remove a file or directory */
  remove(path: string): Promise<void>;
}

/**
 * Interface for logging
 */
export interface LoggerInterface {
  /** Log a debug message */
  debug(...args: any[]): void;
  /** Log an info message */
  info(...args: any[]): void;
  /** Log a warning message */
  warn(...args: any[]): void;
  /** Log an error message */
  error(...args: any[]): void;
}

/**
 * Context object passed to processes
 */
export interface ProcessContext {
  /** Reference to the SpiceTime kernel */
  kernel: KernelInterface;
  /** Filesystem access */
  fs: FileSystemInterface;
  /** Environment variables */
  env: Record<string, any>;
  /** Parent process */
  parent: Process | null;
  /** Root process */
  root: Process;
  /** Logging utility */
  logger: LoggerInterface;
  /** CSS-like selector engine */
  selector: SelectorInterface;
  /** Middleware manager */
  middleware: MiddlewareInterface;
}
