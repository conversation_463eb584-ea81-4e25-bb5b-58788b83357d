{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "lib": ["ES2020"], "types": ["node", "vitest/globals"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "src/tests", "**/*.test.ts", "src/example.ts"]}