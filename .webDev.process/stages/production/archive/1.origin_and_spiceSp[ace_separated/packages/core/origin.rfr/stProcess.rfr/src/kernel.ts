/**
 * Kernel implementation
 *
 * This file contains the implementation of the kernel interface.
 * It re-exports the implementation from the kernel module.
 */

import { KernelInterface } from './types/index.type';
import { createKernel as createKernelImpl } from './kernel/index';

/**
 * Create a kernel instance
 *
 * @returns Kernel interface
 */
export function createKernel(): KernelInterface {
  return createKernelImpl();
}

export default {
  createKernel
};
