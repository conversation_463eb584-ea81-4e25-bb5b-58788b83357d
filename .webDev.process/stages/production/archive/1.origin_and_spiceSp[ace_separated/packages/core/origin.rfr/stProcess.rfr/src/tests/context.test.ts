import { describe, it, expect, vi } from 'vitest';
import { createDefaultContext } from '../context';

// Mock dependencies
vi.mock('../kernel', () => ({
  createKernel: vi.fn(() => ({ mockKernel: true }))
}));

vi.mock('../filesystem', () => ({
  createFileSystem: vi.fn(() => ({ mockFileSystem: true }))
}));

vi.mock('../middleware', () => ({
  createMiddleware: vi.fn(() => ({ mockMiddleware: true }))
}));

vi.mock('../selector', () => ({
  createSelector: vi.fn(() => ({ mockSelector: true }))
}));

vi.mock('../logger', () => ({
  createLogger: vi.fn(() => ({ mockLogger: true }))
}));

describe('Context', () => {
  describe('createDefaultContext', () => {
    it('should create a default context with all required properties', () => {
      const mockProcess = { id: 'test-process' };
      
      const context = createDefaultContext(mockProcess as any);
      
      expect(context).toBeDefined();
      expect(context.kernel).toEqual({ mockKernel: true });
      expect(context.fs).toEqual({ mockFileSystem: true });
      expect(context.env).toEqual({});
      expect(context.parent).toBeNull();
      expect(context.root).toBe(mockProcess);
      expect(context.logger).toEqual({ mockLogger: true });
      expect(context.selector).toEqual({ mockSelector: true });
      expect(context.middleware).toEqual({ mockMiddleware: true });
    });
  });
});
