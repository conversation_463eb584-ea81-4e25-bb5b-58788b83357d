/**
 * Process implementation
 * 
 * This file contains the implementation of the Process class and the createProcess function.
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { ProcessState, Process as ProcessInterface, TicInfo } from './types/index.type';
import { createDefaultContext } from './context';

/**
 * Process class that wraps a generator function
 * @template Props - Type of process properties
 * @template Result - Type of process result
 */
export class Process<Props = any, Result = any> extends EventEmitter implements ProcessInterface<Props, Result> {
  /** Unique process identifier */
  public readonly id: string;
  
  /** Path in the filesystem */
  public path: string;
  
  /** Parent process ID (if any) */
  public parentId: string | null;
  
  /** Process properties */
  public readonly props: Props;
  
  /** Child processes */
  public readonly children: ProcessInterface[];
  
  /** Current lifecycle state */
  public state: ProcessState;
  
  /** Generator function that defines the process */
  private readonly generatorFn: (props: Props) => Generator<any, Result, any>;
  
  /** Generator instance */
  private generator: Generator<any, Result, any> | null;
  
  /** Process context */
  public readonly context: any;
  
  /** Current tic */
  public currentTic: TicInfo | null;
  
  /** Process result */
  private result: Result | null;
  
  /** Process error */
  private error: Error | null;
  
  /**
   * Create a new Process
   * 
   * @param generatorFn - Generator function that defines the process
   * @param props - Properties to pass to the generator function
   * @param context - Context to pass to the process
   */
  constructor(generatorFn: (props: Props) => Generator<any, Result, any>, props: Props = {} as Props, context: any = null) {
    super();
    
    this.id = uuidv4();
    this.path = '';
    this.parentId = null;
    this.props = props;
    this.children = [];
    this.state = ProcessState.CREATED;
    this.generatorFn = generatorFn;
    this.generator = null;
    this.context = context || createDefaultContext(this);
    this.currentTic = null;
    this.result = null;
    this.error = null;
    
    // Register with kernel
    if (this.context.kernel) {
      this.context.kernel.registerProcess(this);
    }
  }
  
  /**
   * Start the process
   */
  public async start(): Promise<void> {
    if (this.state !== ProcessState.CREATED && this.state !== ProcessState.STOPPED) {
      throw new Error(`Cannot start process in state: ${this.state}`);
    }
    
    this._setState(ProcessState.STARTING);
    
    try {
      // Initialize the generator
      this.generator = this.generatorFn(this.props);
      
      // Emit start event
      this.emit('start');
      
      // Set state to running
      this._setState(ProcessState.RUNNING);
      
      // Request first tic
      await this.requestTic();
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Stop the process
   */
  public async stop(): Promise<void> {
    if (this.state === ProcessState.STOPPED || this.state === ProcessState.COMPLETED) {
      return;
    }
    
    this._setState(ProcessState.STOPPING);
    
    try {
      // Release current tic if any
      if (this.currentTic) {
        this.releaseTic();
      }
      
      // Stop all children
      for (const child of this.children) {
        await child.stop();
      }
      
      // Emit stop event
      this.emit('stop');
      
      // Set state to stopped
      this._setState(ProcessState.STOPPED);
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Pause the process
   */
  public async pause(): Promise<void> {
    if (this.state !== ProcessState.RUNNING) {
      throw new Error(`Cannot pause process in state: ${this.state}`);
    }
    
    try {
      // Release current tic if any
      if (this.currentTic) {
        this.releaseTic();
      }
      
      // Emit pause event
      this.emit('pause');
      
      // Set state to paused
      this._setState(ProcessState.PAUSED);
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Resume the process
   */
  public async resume(): Promise<void> {
    if (this.state !== ProcessState.PAUSED) {
      throw new Error(`Cannot resume process in state: ${this.state}`);
    }
    
    try {
      // Emit resume event
      this.emit('resume');
      
      // Set state to running
      this._setState(ProcessState.RUNNING);
      
      // Request next tic
      await this.requestTic();
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      throw error;
    }
  }
  
  /**
   * Request a tic from the kernel
   * 
   * @param priority - Priority of the tic request
   * @returns Whether a tic was allocated
   */
  public async requestTic(priority: number = 0): Promise<boolean> {
    if (this.state !== ProcessState.RUNNING) {
      return false;
    }
    
    this.emit('ticRequest', priority);
    
    try {
      // Request tic from kernel
      const tic = await this.context.kernel.requestTic(this.id, priority);
      
      if (!tic) {
        return false;
      }
      
      this.currentTic = tic;
      this.emit('ticAllocated', tic);
      
      // Execute the process for this tic
      await this._executeTic(tic);
      
      return true;
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      return false;
    }
  }
  
  /**
   * Release the current tic back to the kernel
   */
  public releaseTic(): void {
    if (!this.currentTic) {
      return;
    }
    
    this.context.kernel.releaseTic(this.currentTic.id);
    this.currentTic = null;
  }
  
  /**
   * Add a child process
   * 
   * @param child - Child process to add
   */
  public appendChild(child: ProcessInterface): void {
    if (this.children.includes(child)) {
      return;
    }
    
    this.children.push(child);
    child.parentId = this.id;
    
    this.emit('childAdded', child);
  }
  
  /**
   * Remove a child process
   * 
   * @param child - Child process to remove
   */
  public removeChild(child: ProcessInterface): void {
    const index = this.children.indexOf(child);
    if (index === -1) {
      return;
    }
    
    this.children.splice(index, 1);
    child.parentId = null;
    
    this.emit('childRemoved', child);
  }
  
  /**
   * Get the parent process
   * 
   * @returns Parent process or null if no parent
   */
  public getParent(): ProcessInterface | null {
    if (!this.parentId) {
      return null;
    }
    
    return this.context.kernel.getProcessById(this.parentId);
  }
  
  /**
   * Get the root process
   * 
   * @returns Root process
   */
  public getRoot(): ProcessInterface {
    let current: ProcessInterface = this;
    let parent = current.getParent();
    
    while (parent) {
      current = parent;
      parent = current.getParent();
    }
    
    return current;
  }
  
  /**
   * Get the final result of the process
   * 
   * @returns Process result
   */
  public async getResult(): Promise<Result> {
    if (this.state === ProcessState.COMPLETED) {
      return this.result as Result;
    }
    
    return new Promise<Result>((resolve, reject) => {
      this.once('complete', () => resolve(this.result as Result));
      this.once('error', (error) => reject(error));
    });
  }
  
  /**
   * Execute the process for a tic
   * 
   * @param tic - Tic information
   */
  private async _executeTic(tic: TicInfo): Promise<void> {
    if (!this.generator) {
      throw new Error('Generator not initialized');
    }
    
    this.emit('ticStart', tic);
    
    try {
      const startTime = Date.now();
      const endTime = startTime + tic.duration;
      
      // Execute until we run out of time or the generator completes
      while (Date.now() < endTime) {
        const result = this.generator.next();
        
        if (result.done) {
          // Generator has completed
          this.result = result.value;
          this._setState(ProcessState.COMPLETED);
          this.emit('complete', this.result);
          this.releaseTic();
          return;
        }
        
        // If the yield value is a Promise, wait for it
        if (result.value instanceof Promise) {
          await result.value;
        }
        
        // Check if we've run out of time
        if (Date.now() >= endTime) {
          break;
        }
      }
      
      // We've run out of time, request another tic
      this.emit('ticEnd', tic);
      this.releaseTic();
      
      // Request next tic if still running
      if (this.state === ProcessState.RUNNING) {
        await this.requestTic();
      }
    } catch (error) {
      this.error = error as Error;
      this._setState(ProcessState.ERROR);
      this.emit('error', error);
      this.releaseTic();
    }
  }
  
  /**
   * Set the process state
   * 
   * @param state - New state
   */
  private _setState(state: ProcessState): void {
    const oldState = this.state;
    this.state = state;
    
    if (oldState !== state) {
      this.emit('stateChange', { oldState, newState: state });
    }
  }
}

/**
 * Create a process from a generator function
 * 
 * @template Props - Type of process properties
 * @template Result - Type of process result
 * @param generatorFn - Generator function that defines the process
 * @param props - Properties to pass to the generator function
 * @param context - Context to pass to the process
 * @returns Created process
 */
export function createProcess<Props = any, Result = any>(
  generatorFn: (props: Props) => Generator<any, Result, any>,
  props?: Props,
  context?: any
): Process<Props, Result> {
  return new Process<Props, Result>(generatorFn, props as Props, context);
}

export default {
  Process,
  createProcess
};
