import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as fs from 'fs';

// Mock the fs module
vi.mock('fs', () => ({
  exists: vi.fn((path, callback) => callback(true)),
  readFile: vi.fn((path, encoding, callback) => {
    if (typeof encoding === 'function') {
      encoding(null, 'file content');
    } else {
      callback(null, 'file content');
    }
  }),
  writeFile: vi.fn((path, content, encoding, callback) => {
    if (typeof encoding === 'function') {
      encoding(null);
    } else {
      callback(null);
    }
  }),
  mkdir: vi.fn((path, options, callback) => {
    if (typeof options === 'function') {
      options(null);
    } else {
      callback(null);
    }
  }),
  readdir: vi.fn((path, callback) => callback(null, ['file1.txt', 'file2.txt'])),
  rm: vi.fn((path, options, callback) => {
    if (typeof options === 'function') {
      options(null);
    } else {
      callback(null);
    }
  })
}));

// Mock the util.promisify function
vi.mock('util', () => ({
  promisify: vi.fn((fn) => {
    return (...args) => {
      return new Promise((resolve, reject) => {
        fn(...args, (err, result) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    };
  })
}));
import { Process, createProcess } from '../process';
import { ProcessState } from '../types/index.type';

describe('Process', () => {
  // Mock generator function
  function* testGenerator(props: any): Generator<any, any, any> {
    const { value = 'default' } = props;
    yield;
    return { result: value };
  }

  // Mock context
  const mockContext = {
    kernel: {
      registerProcess: vi.fn(),
      requestTic: vi.fn().mockResolvedValue({
        id: 'test-tic',
        startTime: Date.now(),
        duration: 100,
        priority: 0,
        processId: 'test-process'
      }),
      releaseTic: vi.fn()
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create a process with default options', () => {
      const process = new Process(testGenerator, {}, mockContext);

      expect(process.id).toBeDefined();
      expect(process.state).toBe(ProcessState.CREATED);
      expect(process.props).toEqual({});
      expect(process.children).toEqual([]);
      expect(mockContext.kernel.registerProcess).toHaveBeenCalledWith(process);
    });

    it('should use provided props', () => {
      const props = { value: 'test' };
      const process = new Process(testGenerator, props, mockContext);

      expect(process.props).toBe(props);
    });
  });

  describe('lifecycle methods', () => {
    it('should start a process', async () => {
      const process = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(process, 'emit');

      await process.start();

      expect(process.state).toBe(ProcessState.RUNNING);
      expect(emitSpy).toHaveBeenCalledWith('start');
      expect(mockContext.kernel.requestTic).toHaveBeenCalledWith(process.id, 0);
    });

    it('should stop a process', async () => {
      const process = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(process, 'emit');

      await process.start();
      await process.stop();

      expect(process.state).toBe(ProcessState.STOPPED);
      expect(emitSpy).toHaveBeenCalledWith('stop');
    });

    it('should pause a process', async () => {
      const process = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(process, 'emit');

      await process.start();
      await process.pause();

      expect(process.state).toBe(ProcessState.PAUSED);
      expect(emitSpy).toHaveBeenCalledWith('pause');
    });

    it('should resume a process', async () => {
      const process = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(process, 'emit');

      await process.start();
      await process.pause();
      await process.resume();

      expect(process.state).toBe(ProcessState.RUNNING);
      expect(emitSpy).toHaveBeenCalledWith('resume');
      expect(mockContext.kernel.requestTic).toHaveBeenCalledTimes(2);
    });
  });

  describe('hierarchy methods', () => {
    it('should add a child process', () => {
      const parent = new Process(testGenerator, {}, mockContext);
      const child = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(parent, 'emit');

      parent.appendChild(child);

      expect(parent.children).toContain(child);
      expect(child.parentId).toBe(parent.id);
      expect(emitSpy).toHaveBeenCalledWith('childAdded', child);
    });

    it('should remove a child process', () => {
      const parent = new Process(testGenerator, {}, mockContext);
      const child = new Process(testGenerator, {}, mockContext);
      const emitSpy = vi.spyOn(parent, 'emit');

      parent.appendChild(child);
      parent.removeChild(child);

      expect(parent.children).not.toContain(child);
      expect(child.parentId).toBeNull();
      expect(emitSpy).toHaveBeenCalledWith('childRemoved', child);
    });
  });

  describe('createProcess', () => {
    it('should create a process instance', () => {
      const props = { value: 'test' };
      const process = createProcess(testGenerator, props, mockContext);

      expect(process).toBeInstanceOf(Process);
      expect(process.props).toBe(props);
    });
  });
});
