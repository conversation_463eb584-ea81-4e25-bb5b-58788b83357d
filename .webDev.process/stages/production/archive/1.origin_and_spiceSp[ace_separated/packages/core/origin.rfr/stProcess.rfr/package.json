{"name": "@spicetime/core/origin/stProcess", "version": "0.1.0", "description": "SpiceTime Process - A filesystem-based process model using generator functions", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublish": "npm run clean && npm run build"}, "keywords": ["spicetime", "process", "generator", "filesystem"], "author": "SpiceTime Team", "license": "MIT", "dependencies": {"uuid": "^9.0.0"}, "devDependencies": {}}