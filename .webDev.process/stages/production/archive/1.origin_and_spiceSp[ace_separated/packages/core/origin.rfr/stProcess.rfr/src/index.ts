/**
 * SpiceTime Process - A filesystem-based process model using generator functions
 *
 * This module provides a process model for SpiceTime, allowing processes to be
 * defined as generator functions that can yield control and resume later.
 */

// Import core classes and functions
import { Process, createProcess } from './process';
import { createKernel } from './kernel';
import { createMiddleware } from './middleware';
import { createSelector } from './selector';
import { createFileSystem } from './filesystem';
import { createLogger } from './logger';
import { createDefaultContext } from './context';

// Import event helpers
import { emitEvent, onEvent, broadcast } from './events';

// Import tic helpers
import { requestTic, getTicRemainingTime, isInTic, getCurrentTic } from './tic';

// Import process helpers
import { spawn, waitFor, waitForAll, loadProcess, saveProcess } from './helpers';

// Import types
import { ProcessState, ProcessEvent, TicInfo } from './types/index.type';

/**
 * stProcess module
 */
export default {
  // Core functionality
  createProcess,
  Process,
  ProcessState,
  createKernel,
  createMiddleware,
  createSelector,
  createFileSystem,
  createLogger,
  createDefaultContext,

  // Process management
  spawn,
  waitFor,
  waitForAll,
  loadProcess,
  saveProcess,

  // Event handling
  emitEvent,
  onEvent,
  broadcast,

  // Tic management
  requestTic,
  getTicRemainingTime,
  isInTic,
  getCurrentTic
};

// Export core classes and functions
export { Process, createProcess };
export { createKernel };
export { createMiddleware };
export { createSelector };
export { createFileSystem };
export { createLogger };
export { createDefaultContext };

// Export event helpers
export { emitEvent, onEvent, broadcast };

// Export tic helpers
export { requestTic, getTicRemainingTime, isInTic, getCurrentTic };

// Export process helpers
export { spawn, waitFor, waitForAll, loadProcess, saveProcess };

// Export types
export {
  ProcessState,
  ProcessEvent,
  TicInfo
};

// Export all types from index.type.ts
export * from './types/index.type';
