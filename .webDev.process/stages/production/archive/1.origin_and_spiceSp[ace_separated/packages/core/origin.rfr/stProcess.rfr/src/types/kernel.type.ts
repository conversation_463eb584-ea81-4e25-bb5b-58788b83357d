let me finish anothe/**
 * Kernel type definitions
 */

import { ProcessType } from './process.type';
import { TicInfo } from './index.type';

/**
 * Kernel interface type
 */
export interface KernelType {
  /**
   * Request a tic for a process
   * 
   * @param processId - ID of the process requesting a tic
   * @param priority - Priority of the request
   * @returns Allocated tic or null if no tic could be allocated
   */
  requestTic(processId: string, priority?: number): Promise<TicInfo | null>;
  
  /**
   * Release a tic
   * 
   * @param ticId - ID of the tic to release
   */
  releaseTic(ticId: string): void;
  
  /**
   * Register a process with the kernel
   * 
   * @param process - Process to register
   */
  registerProcess(process: ProcessType): void;
  
  /**
   * Unregister a process from the kernel
   * 
   * @param processId - ID of the process to unregister
   */
  unregisterProcess(processId: string): void;
  
  /**
   * Get a process by ID
   * 
   * @param processId - ID of the process to get
   * @returns Process or null if not found
   */
  getProcessById(processId: string): ProcessType | null;
  
  /**
   * Register a sergeant process
   * 
   * @param process - Sergeant process
   * @param domain - Domain of the sergeant
   */
  registerSergeant(process: ProcessType, domain: string): void;
  
  /**
   * Unregister a sergeant process
   * 
   * @param processId - ID of the sergeant process to unregister
   */
  unregisterSergeant(processId: string): void;
  
  /**
   * Get the current system load
   * 
   * @returns System load (0-1)
   */
  getSystemLoad(): number;
  
  /**
   * Get the system uptime
   * 
   * @returns System uptime in ms
   */
  getSystemUptime(): number;
}

/**
 * Create kernel function type
 */
export type CreateKernelFn = () => KernelType;
