export interface TripodStage<T, R> {
    process: (scope: T) => R;
    meta?: {
        stage: 'ideation' | 'design' | 'production';
        path?: string;
    };
}
export interface TripodProcess<I, D, P, R> {
    ideation: TripodStage<I, D>;
    design: TripodStage<D, P>;
    production: TripodStage<P, R>;
    rotate?: (currentStage: string, context: any) => string;
}
//# sourceMappingURL=core.type.d.ts.map