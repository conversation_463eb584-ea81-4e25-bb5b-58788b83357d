export interface TripodStage<T, R> {
    process: (scope: T) => R;
    meta?: {
        stage: 'ideation' | 'design' | 'production';
        path?: string; // Path to meta file if detached
    };
}

export interface TripodProcess<I, D, P, R> {
    ideation: TripodStage<I, D>;
    design: TripodStage<D, P>;
    production: TripodStage<P, R>;

    // Optional rotation control
    rotate?: (currentStage: string, context: any) => string;
}