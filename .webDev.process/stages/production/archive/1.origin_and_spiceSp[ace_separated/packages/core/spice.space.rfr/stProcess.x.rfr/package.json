{"name": "@spicetime/core/origin/stProcess", "version": "0.1.0", "description": "SpiceTime Process - extends Node.js process functionality", "main": "dist/stProcess.js", "types": "dist/stProcess.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint *.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublish": "npm run clean && npm run build"}, "keywords": ["spicetime", "process", "node"], "author": "SpiceTime Team", "license": "MIT", "dependencies": {"uuid": "^9.0.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.0", "@types/uuid": "^9.0.0", "eslint": "^8.36.0", "jest": "^29.5.0", "rimraf": "^4.4.0", "ts-jest": "^29.1.0", "typescript": "^5.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov"]}}