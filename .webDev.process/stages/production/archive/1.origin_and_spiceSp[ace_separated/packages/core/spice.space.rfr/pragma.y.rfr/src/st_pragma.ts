/**
 * SpiceTime Pragma decorator for tripodic process structure
 */
export function stPragma(options: {
    stage?: 'ideation' | 'design' | 'production';
    metaSync?: boolean;
}) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = function(...args: any[]) {
            // Create scope from args
            const scope = args[0] || {};

            // Two-phase invocation still exists under the hood
            const spin1Context = { id: this?.id || 'anonymous' };
            const spin2Context = { ...this, id: undefined };

            // Inject scope into module global
            if (options.stage === 'production') {
                global.__stScope = scope;
            }

            // Apply original method with enhanced context
            return originalMethod.apply(this, [
                { ...scope, __spin1: spin1Context, __spin2: spin2Context },
                ...args.slice(1)
            ]);
        };

        return descriptor;
    };
}