# Origin Process Specification

## Overview

The `stProcess` module extends the Node.js process functionality to provide a foundation for SpiceTime processes. This specification defines the basic structure and functionality of the `stProcess` module, which serves as a building block for more complex process types in the SpiceTime architecture.

## Core Functionality

The `stProcess` module provides the following core functionality:

1. **Process Lifecycle Management**: Extensions to the standard Node.js process lifecycle events and controls.
2. **Environment Management**: Enhanced environment variable handling with SpiceTime-specific features.
3. **Signal Handling**: Improved signal handling with additional SpiceTime-specific signals.
4. **Inter-Process Communication**: Basic mechanisms for communication between processes.
5. **Resource Management**: Utilities for monitoring and controlling resource usage.

## API Specification

### Class: `StProcess`

The main class that extends Node.js process functionality.

```typescript
class StProcess {
  // Constructor
  constructor(options?: StProcessOptions);
  
  // Properties
  readonly id: string;                 // Unique process identifier
  readonly parentId: string | null;    // Parent process ID (if any)
  readonly startTime: number;          // Process start timestamp
  readonly state: ProcessState;        // Current process state
  
  // Environment Management
  env: StEnvironment;                  // Enhanced environment object
  
  // Lifecycle Methods
  start(): Promise<void>;              // Start the process
  stop(options?: StopOptions): Promise<void>;  // Stop the process
  restart(options?: RestartOptions): Promise<void>;  // Restart the process
  
  // Event Handling
  on(event: ProcessEvent, listener: Function): this;  // Register event listener
  once(event: ProcessEvent, listener: Function): this;  // Register one-time event listener
  off(event: ProcessEvent, listener: Function): this;  // Remove event listener
  
  // Resource Management
  getResourceUsage(): ResourceUsage;   // Get current resource usage
  setResourceLimits(limits: ResourceLimits): void;  // Set resource limits
  
  // Signal Handling
  sendSignal(signal: ProcessSignal): void;  // Send a signal to the process
  
  // Static Methods
  static current(): StProcess;         // Get current process instance
  static list(): StProcess[];          // List all known processes
  static getById(id: string): StProcess | null;  // Get process by ID
}
```

### Interface: `StProcessOptions`

Options for creating a new StProcess instance.

```typescript
interface StProcessOptions {
  id?: string;                         // Custom process ID (generated if not provided)
  env?: Record<string, string>;        // Initial environment variables
  cwd?: string;                        // Current working directory
  resourceLimits?: ResourceLimits;     // Initial resource limits
  autoStart?: boolean;                 // Whether to start automatically (default: true)
}
```

### Interface: `StEnvironment`

Enhanced environment object with additional functionality.

```typescript
interface StEnvironment extends Record<string, string> {
  // Methods
  get(key: string, defaultValue?: string): string | undefined;
  set(key: string, value: string): void;
  unset(key: string): void;
  has(key: string): boolean;
  
  // Namespaced environment variables
  getNamespace(namespace: string): Record<string, string>;
  setNamespace(namespace: string, values: Record<string, string>): void;
}
```

### Enum: `ProcessState`

Possible states of a process.

```typescript
enum ProcessState {
  CREATED = 'created',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  CRASHED = 'crashed'
}
```

### Interface: `ResourceUsage`

Information about process resource usage.

```typescript
interface ResourceUsage {
  cpu: {
    user: number;      // User CPU time (ms)
    system: number;    // System CPU time (ms)
    percent: number;   // CPU usage percentage
  };
  memory: {
    rss: number;       // Resident Set Size (bytes)
    heapTotal: number; // Total heap size (bytes)
    heapUsed: number;  // Used heap size (bytes)
    external: number;  // External memory (bytes)
  };
  time: {
    uptime: number;    // Process uptime (ms)
    elapsed: number;   // Time since start (ms)
  };
}
```

### Interface: `ResourceLimits`

Resource limits for a process.

```typescript
interface ResourceLimits {
  cpu?: {
    maxPercent?: number;  // Maximum CPU percentage
  };
  memory?: {
    maxRss?: number;      // Maximum RSS (bytes)
    maxHeap?: number;     // Maximum heap size (bytes)
  };
  time?: {
    maxUptime?: number;   // Maximum uptime (ms)
  };
}
```

### Type: `ProcessEvent`

Events that can be emitted by a process.

```typescript
type ProcessEvent = 
  | 'start'        // Process has started
  | 'stop'         // Process has stopped
  | 'restart'      // Process has restarted
  | 'error'        // Process encountered an error
  | 'warning'      // Process encountered a warning
  | 'stateChange'  // Process state has changed
  | 'resourceWarning'  // Resource usage warning
  | 'signal'       // Process received a signal
  | 'message'      // Process received a message
  | 'exit'         // Process is exiting
  | 'beforeExit';  // Process is about to exit
```

### Type: `ProcessSignal`

Signals that can be sent to a process.

```typescript
type ProcessSignal =
  // Standard Node.js signals
  | 'SIGINT'    // Interrupt from keyboard
  | 'SIGTERM'   // Termination signal
  | 'SIGHUP'    // Terminal closed
  | 'SIGBREAK'  // Break from keyboard (Windows)
  | 'SIGUSR1'   // User-defined signal 1
  | 'SIGUSR2'   // User-defined signal 2
  
  // SpiceTime-specific signals
  | 'ST_PAUSE'  // Pause process execution
  | 'ST_RESUME' // Resume process execution
  | 'ST_RELOAD' // Reload process configuration
  | 'ST_STATUS' // Request status information
  | 'ST_DEBUG'  // Toggle debug mode
  | 'ST_TRACE'  // Toggle trace mode
  | 'ST_PROF'   // Toggle profiling
  | 'ST_DUMP'   // Dump process state
  | 'ST_GC'     // Force garbage collection
  | 'ST_ROTATE' // Rotate logs
  | 'ST_SYNC';  // Synchronize with parent
```

## Usage Examples

### Basic Usage

```typescript
import { StProcess } from '@spicetime/core/origin/stProcess';

// Create a new process
const process = new StProcess({
  env: { NODE_ENV: 'development' },
  cwd: '/path/to/working/dir'
});

// Register event handlers
process.on('start', () => {
  console.log('Process started');
});

process.on('error', (error) => {
  console.error('Process error:', error);
});

// Start the process
await process.start();

// Get resource usage
const usage = process.getResourceUsage();
console.log('Memory usage:', usage.memory.heapUsed);

// Send a signal
process.sendSignal('ST_RELOAD');

// Stop the process
await process.stop();
```

### Environment Management

```typescript
import { StProcess } from '@spicetime/core/origin/stProcess';

const process = new StProcess();

// Set environment variables
process.env.set('DEBUG', 'true');
process.env.setNamespace('APP', {
  PORT: '3000',
  HOST: 'localhost'
});

// Get environment variables
const debug = process.env.get('DEBUG');
const appEnv = process.env.getNamespace('APP');
console.log(`Port: ${appEnv.PORT}, Host: ${appEnv.HOST}`);
```

### Resource Management

```typescript
import { StProcess } from '@spicetime/core/origin/stProcess';

const process = new StProcess();

// Set resource limits
process.setResourceLimits({
  memory: {
    maxHeap: 1024 * 1024 * 100 // 100MB
  },
  cpu: {
    maxPercent: 50 // 50% CPU
  }
});

// Monitor resource usage
setInterval(() => {
  const usage = process.getResourceUsage();
  console.log(`CPU: ${usage.cpu.percent}%, Memory: ${usage.memory.heapUsed}`);
}, 1000);
```

## Implementation Notes

1. The `StProcess` class should be implemented as a singleton for the current process, with the ability to create proxy instances for other processes.

2. Resource monitoring should use Node.js built-in `process.memoryUsage()` and `process.cpuUsage()` functions, with additional calculations for percentages.

3. Signal handling should extend Node.js signal handling with custom signals implemented using IPC mechanisms.

4. Environment variable management should provide namespacing to avoid conflicts between different modules.

5. The implementation should be compatible with both CommonJS and ESM module systems.

## Future Extensions

1. **Process Hierarchy**: Enhanced parent-child relationship management.
2. **Process Groups**: Ability to manage groups of related processes.
3. **Process Migration**: Support for migrating processes between hosts.
4. **Process Snapshots**: Ability to create and restore process snapshots.
5. **Process Metrics**: Advanced metrics collection and reporting.
6. **Process Debugging**: Enhanced debugging capabilities.
7. **Process Security**: Security features for process isolation and protection.

## Compatibility

The `stProcess` module should maintain compatibility with:

1. All Node.js APIs related to the `process` global object.
2. Standard Node.js event emitter patterns.
3. Existing Node.js process management tools and libraries.

## Dependencies

1. Node.js core modules only (no external dependencies).
2. TypeScript for type definitions.

## Testing

The module should include comprehensive tests for:

1. Process lifecycle management
2. Environment variable handling
3. Signal handling
4. Resource monitoring and limits
5. Error handling and recovery
6. Performance under load
