{"name": "@local/core_error", "version": "0.1.0", "description": "Core error handling system for SpiceTime architecture", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "bin"], "scripts": {"dev": "tsup --watch", "build": "tsup index.ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "docs": "typedoc", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "prepublishOnly": "npm run build"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}}