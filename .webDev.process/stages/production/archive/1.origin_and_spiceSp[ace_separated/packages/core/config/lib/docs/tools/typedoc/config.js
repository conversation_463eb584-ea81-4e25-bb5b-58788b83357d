/**
 * TypeDoc configuration factory
 * Creates composable TypeDoc configurations
 */

const createTypedocConfig = ({ base, extensions = [], environment }) => {
  // Default base configuration
  const defaultBase = {
    entryPoints: ["src/index.ts"],
    out: "docs/api",
    plugin: ["typedoc-plugin-markdown"],
    theme: "default",
    excludePrivate: true,
    excludeProtected: true,
    excludeExternals: true,
    readme: "README.md",
    includeVersion: true
  };

  // Merge base with default
  const mergedBase = { ...defaultBase, ...base };

  // Apply extensions
  let config = { ...mergedBase };
  extensions.forEach(extension => {
    config = deepMerge(config, extension);
  });

  // Apply environment-specific overrides if provided
  if (environment && mergedBase.environments && mergedBase.environments[environment]) {
    config = deepMerge(config, mergedBase.environments[environment]);
  }

  return config;
};

// Helper function for deep merging objects
function deepMerge(target, source) {
  const output = { ...target };

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }

  return output;
}

function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

module.exports = { createTypedocConfig };