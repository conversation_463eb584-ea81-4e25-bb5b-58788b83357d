---
title: "Simple Category Example"
description: "A simple example of creating and using a category"
---

# Simple Category Example

This example demonstrates how to create a category with objects and morphisms, and how to compose morphisms.

## Code Example

```typescript
import { ConcreteCategory } from '../src/atomic/cat';
import { createCatObject } from '../src/atomic/catObject';
import { BaseMorphism } from '../src/atomic/morphism';

// Create a category for numbers
const numberCategory = new ConcreteCategory();

// Create some objects (numbers)
const zero = createCatObject(0, 'zero');
const one = createCatObject(1, 'one');
const two = createCatObject(2, 'two');
const three = createCatObject(3, 'three');

// Add objects to the category
numberCategory.addObject(zero);
numberCategory.addObject(one);
numberCategory.addObject(two);
numberCategory.addObject(three);

// Create morphisms (functions between numbers)
const addOne = new BaseMorphism(
  zero,
  one,
  (x: number) => x + 1
);

const double = new BaseMorphism(
  one,
  two,
  (x: number) => x * 2
);

const addOneToDouble = new BaseMorphism(
  two,
  three,
  (x: number) => x + 1
);

// Add morphisms to the category
numberCategory.addMorphism(addOne);
numberCategory.addMorphism(double);
numberCategory.addMorphism(addOneToDouble);

// Compose morphisms
const composed = numberCategory.compose(addOne, double);
const composed2 = numberCategory.compose(composed, addOneToDouble);

// Use the composed morphism
console.log('0 -> addOne -> double -> addOneToDouble =', composed2.apply(0)); // 5
```

## Explanation

1. We create a category for numbers.
2. We create objects representing the numbers 0, 1, 2, and 3.
3. We create morphisms representing functions between these numbers:
   - `addOne`: Adds 1 to a number
   - `double`: Doubles a number
   - `addOneToDouble`: Adds 1 to a doubled number
4. We compose these morphisms to create a new morphism that applies all three operations in sequence.
5. We apply the composed morphism to the number 0, resulting in 5.

This demonstrates the key concept of composition in category theory, where morphisms can be combined to create new morphisms while preserving the structure of the category.
