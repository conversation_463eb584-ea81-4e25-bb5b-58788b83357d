{"version": 3, "file": "zipper.fr.d.ts", "sourceRoot": "", "sources": ["zipper.fr.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAG5C;;;;;;;;;;;GAWG;AACH,MAAM,WAAW,MAAM,CAAC,CAAC;IACrB;;OAEG;IACH,KAAK,EAAE,CAAC,CAAC;IAET;;OAEG;IACH,OAAO,EAAE,GAAG,CAAC;IAEb;;;;OAIG;IACH,EAAE,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAE3B;;;;;OAKG;IACH,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAE1C;;;;OAIG;IACH,IAAI,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAE7B;;;;OAIG;IACH,KAAK,EAAE,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAE9B;;;;;OAKG;IACH,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtC;;;;OAIG;IACH,MAAM,EAAE,MAAM,GAAG,CAAC;CACrB;AAED;;;;;;;;GAQG;AACH,qBAAa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAE,YAAW,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAQjE,OAAO,CAAC,QAAQ,CAAC,SAAS;IAC1B,OAAO,CAAC,QAAQ,CAAC,UAAU;IAR/B;;;;;OAKG;gBAEkB,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EACtB,UAAU,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG;IAGtD;;;;;;OAMG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAyDvC;;;;;OAKG;IACH,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAM/D;;;;;OAKG;IACH,YAAY,IAAI,OAAO;IAKvB;;;;;;;OAOG;IACH,MAAM,CAAC,gBAAgB,CAAC,CAAC,EACrB,IAAI,EAAE;QAAE,KAAK,EAAE,CAAC,CAAC;QAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;KAAE,EACzC,IAAI,GAAE,KAAK,CAAC;QACR,MAAM,EAAE,GAAG,CAAC;QACZ,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QAClB,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAA;KACrB,CAAM,GACR,MAAM,CAAC,CAAC,CAAC;CAkFf"}