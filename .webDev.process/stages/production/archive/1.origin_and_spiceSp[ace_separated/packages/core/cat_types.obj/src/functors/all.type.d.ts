/**
 * @module utils/core/cat_types/functors
 *
 * Type definitions for various functor types in category theory.
 */
import { Category, CatObject, Morphism, Functor } from '../atomic';
/**
 * Endofunctor - A functor from a category to itself
 * F: C → C
 */
export interface Endofunctor<O extends CatObject, M extends Morphism<O>> extends Functor<O, M, O, M> {
    /**
     * Iterate the endofunctor n times
     * @param times Number of iterations
     * @returns A new endofunctor representing n applications
     */
    iterate(times: number): Endofunctor<O, M>;
    /**
     * Fixed point of the endofunctor, if it exists
     * @returns The fixed point object
     */
    fixedPoint?(): O;
}
/**
 * Contravariant Functor - Reverses the direction of morphisms
 * F: C^op → D
 */
export interface ContravariantFunctor<S extends CatObject, SM extends Morphism<S>, T extends CatObject, TM extends Morphism<T>> extends Functor<S, SM, T, TM> {
    /**
     * Maps a morphism from target to source (in reverse)
     * @param morphism Morphism in the source category
     * @returns Corresponding morphism in the target category, with direction reversed
     */
    contraMapMorphism(morphism: SM): TM;
}
/**
 * Bifunctor - A functor of two arguments
 * F: C × D → E
 */
export interface Bifunctor<S1 extends CatObject, SM1 extends Morphism<S1>, S2 extends CatObject, SM2 extends Morphism<S2>, T extends CatObject, TM extends Morphism<T>> {
    /**
     * Source categories
     */
    readonly source1: Category<S1, SM1>;
    readonly source2: Category<S2, SM2>;
    /**
     * Target category
     */
    readonly target: Category<T, TM>;
    /**
     * Maps a pair of objects to a target object
     * @param obj1 Object from first source category
     * @param obj2 Object from second source category
     * @returns Corresponding object in target category
     */
    mapObjects(obj1: S1, obj2: S2): T;
    /**
     * Maps a pair of morphisms to a target morphism
     * @param morphism1 Morphism from first source category
     * @param morphism2 Morphism from second source category
     * @returns Corresponding morphism in target category
     */
    mapMorphisms(morphism1: SM1, morphism2: SM2): TM;
}
/**
 * Profunctor - Contravariant in first argument, covariant in second
 * F: C^op × D → Set
 */
export interface Profunctor<S1 extends CatObject, SM1 extends Morphism<S1>, S2 extends CatObject, SM2 extends Morphism<S2>, T extends CatObject, TM extends Morphism<T>> {
    /**
     * Source categories
     */
    readonly source1: Category<S1, SM1>;
    readonly source2: Category<S2, SM2>;
    /**
     * Target category
     */
    readonly target: Category<T, TM>;
    /**
     * Maps objects with contravariance in first argument
     * @param obj1 Object from first source category
     * @param obj2 Object from second source category
     * @returns Corresponding object in target category
     */
    dimap(obj1: S1, obj2: S2): T;
    /**
     * Maps morphisms with contravariance in first argument
     * @param morphism1 Morphism from first source category (used contravariantly)
     * @param morphism2 Morphism from second source category (used covariantly)
     * @returns Corresponding morphism in target category
     */
    dimapMorphisms(morphism1: SM1, morphism2: SM2): TM;
}
/**
 * Monad - An endofunctor with additional structure
 * (T, η, μ) where T: C → C
 */
export interface Monad<O extends CatObject, M extends Morphism<O>> extends Endofunctor<O, M> {
    /**
     * Unit/return operation (η)
     * @param obj Object to lift into the monad
     * @returns Monadic object
     */
    unit(obj: O): O;
    /**
     * Join/flatten operation (μ)
     * @param obj Nested monadic object
     * @returns Flattened monadic object
     */
    join(obj: O): O;
    /**
     * Bind/flatMap operation (>>=)
     * @param obj Monadic object
     * @param f Function from object to monadic object
     * @returns Resulting monadic object
     */
    bind<A, B>(obj: O, f: (a: A) => O): O;
}
/**
 * Comonad - Dual of a monad
 * (T, ε, δ) where T: C → C
 */
export interface Comonad<O extends CatObject, M extends Morphism<O>> extends Endofunctor<O, M> {
    /**
     * Counit/extract operation (ε)
     * @param obj Comonadic object
     * @returns Extracted value
     */
    extract(obj: O): O;
    /**
     * Duplicate operation (δ)
     * @param obj Comonadic object
     * @returns Nested comonadic object
     */
    duplicate(obj: O): O;
    /**
     * Extend operation
     * @param obj Comonadic object
     * @param f Function from comonadic object to value
     * @returns New comonadic object
     */
    extend<A, B>(obj: O, f: (a: O) => A): O;
}
/**
 * AdjointPair - A pair of functors that are adjoint to each other
 * F: C → D (left adjoint)
 * G: D → C (right adjoint)
 * with natural transformations η: 1_C → G∘F and ε: F∘G → 1_D
 */
export interface AdjointPair<S extends CatObject, SM extends Morphism<S>, T extends CatObject, TM extends Morphism<T>> {
    /**
     * Left adjoint functor (forward direction)
     */
    readonly leftAdjoint: Functor<S, SM, T, TM>;
    /**
     * Right adjoint functor (backward direction)
     */
    readonly rightAdjoint: Functor<T, TM, S, SM>;
    /**
     * Unit natural transformation
     * η: 1_C → G∘F
     */
    unit(obj: S): SM;
    /**
     * Counit natural transformation
     * ε: F∘G → 1_D
     */
    counit(obj: T): TM;
    /**
     * Verify adjunction property
     * Checks if the adjunction laws hold
     */
    verifyAdjunction(): boolean;
}
//# sourceMappingURL=all.type.d.ts.map