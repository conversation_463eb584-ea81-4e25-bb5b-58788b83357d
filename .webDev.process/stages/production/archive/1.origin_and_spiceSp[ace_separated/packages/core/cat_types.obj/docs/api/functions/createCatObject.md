[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / createCatObject

# Function: createCatObject()

> **createCatObject**\<`T`\>(`value`, `name`?): [`CatObject`](../interfaces/CatObject.md)\<`T`\>

Defined in: [atomic/catObject.ts:33](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L33)

Creates a new category object

## Type Parameters

• **T**

## Parameters

### value

`T`

Value to be contained in the object

### name?

`string`

Optional name for debugging

## Returns

[`CatObject`](../interfaces/CatObject.md)\<`T`\>

A new CatObject instance
