# Cat Types Examples

This directory contains examples of using the `cat_types` package.

## Running Examples

To run all examples:

```bash
npm run example
```

To run specific examples:

```bash
npm run example:category
npm run example:functor
npm run example:monad
```

## Example Descriptions

### Simple Category

[Simple Category Example](../../examples/simple-category.ts)

Demonstrates how to create a category with objects and morphisms, and how to compose morphisms.

### Simple Functor

[Simple Functor Example](../../examples/simple-functor.ts)

Demonstrates how to create a functor between two categories, mapping objects and morphisms while preserving structure.

### Simple Monad

[Simple Monad Example](../../examples/simple-monad.ts)

Demonstrates how to create a monad (a "Maybe" monad in this case) and use it for safe computations.

## Integration with Documentation

These examples are referenced from the usage documentation and are designed to be integrated with a Gatsby documentation site.
