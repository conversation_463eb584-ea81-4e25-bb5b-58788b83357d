/**
 * @module FunctorTypes
 * @packageDocumentation
 *
 * Zipper functors provide a way to navigate and modify data structures
 * with a focused element and surrounding context. They are particularly
 * useful for traversing and editing tree-like structures while maintaining
 * the ability to navigate up, down, left, and right.
 */

import { Functor } from '../atomic/functor';
import { CatObject, createCatObject } from '../atomic/catObject';
import { Morphism, BaseMorphism } from '../atomic/morphism';
import { FunctorError } from '../atomic/errors/functor';
import { Category, ConcreteCategory } from '../atomic/cat';

/**
 * Zipper - A structure that allows navigation and local editing
 * with a focus point and surrounding context
 *
 * Zippers provide a way to:
 * 1. Focus on a specific element in a data structure
 * 2. Navigate to adjacent elements (up, down, left, right)
 * 3. Modify the focused element
 * 4. Reconstruct the entire data structure with modifications
 *
 * @typeParam T - The type of elements in the structure
 */
export interface Zipper<T> {
  /**
   * The currently focused element
   */
  focus: T;

  /**
   * The context (path) to the focused element
   */
  context: ZipperContext<T>;

  /**
   * Move the focus up in the structure
   * @returns A new zipper with the focus moved up, or null if at the top
   */
  up(): Zipper<T> | null;

  /**
   * Move the focus down in the structure
   * @returns A new zipper with the focus moved down, or null if at the bottom
   */
  down(): Zipper<T> | null;

  /**
   * Move the focus left in the structure
   * @returns A new zipper with the focus moved left, or null if at the leftmost
   */
  left(): Zipper<T> | null;

  /**
   * Move the focus right in the structure
   * @returns A new zipper with the focus moved right, or null if at the rightmost
   */
  right(): Zipper<T> | null;

  /**
   * Update the focused element
   * @param newFocus - The new value for the focused element
   * @returns A new zipper with the focus updated
   */
  update(newFocus: T): Zipper<T>;

  /**
   * Reconstruct the entire data structure with any modifications
   * @returns The reconstructed data structure
   */
  reconstruct(): T;
}

/**
 * Context for a zipper, representing the path to the focused element
 * @typeParam T - The type of elements in the structure
 */
export interface ZipperContext<T> {
  /**
   * The parent context, or null if at the top
   */
  parent: ZipperContext<T> | null;

  /**
   * Elements to the left of the focus
   */
  left: T[];

  /**
   * Elements to the right of the focus
   */
  right: T[];
}

/**
 * Create a zipper for a list
 * @param list - The list to create a zipper for
 * @param index - The index to focus on (default: 0)
 * @returns A zipper focused on the specified element
 */
export function createListZipper<T>(list: T[], index: number = 0): Zipper<T> | null {
  if (list.length === 0 || index < 0 || index >= list.length) {
    return null;
  }

  const focus = list[index];
  const left = list.slice(0, index);
  const right = list.slice(index + 1);

  return {
    focus,
    context: {
      parent: null,
      left,
      right
    },
    up(): Zipper<T> | null {
      return null; // No parent for a list
    },
    down(): Zipper<T> | null {
      return null; // No children for a simple list
    },
    left(): Zipper<T> | null {
      if (this.context.left.length === 0) {
        return null;
      }
      const newFocus = this.context.left[this.context.left.length - 1];
      const newLeft = this.context.left.slice(0, -1);
      const newRight = [this.focus, ...this.context.right];
      return {
        ...this,
        focus: newFocus,
        context: {
          ...this.context,
          left: newLeft,
          right: newRight
        }
      };
    },
    right(): Zipper<T> | null {
      if (this.context.right.length === 0) {
        return null;
      }
      const newFocus = this.context.right[0];
      const newLeft = [...this.context.left, this.focus];
      const newRight = this.context.right.slice(1);
      return {
        ...this,
        focus: newFocus,
        context: {
          ...this.context,
          left: newLeft,
          right: newRight
        }
      };
    },
    update(newFocus: T): Zipper<T> {
      return {
        ...this,
        focus: newFocus
      };
    },
    reconstruct(): T[] {
      return [...this.context.left, this.focus, ...this.context.right];
    }
  } as Zipper<T>;
}

/**
 * Create a zipper for a tree
 * @param tree - The tree to create a zipper for
 * @returns A zipper focused on the root of the tree
 */
export function createTreeZipper<T extends { children?: T[] }>(tree: T): Zipper<T> {
  return {
    focus: tree,
    context: {
      parent: null,
      left: [],
      right: []
    },
    up(): Zipper<T> | null {
      if (!this.context.parent) {
        return null;
      }
      return {
        focus: (this.context.parent as any).focus,
        context: (this.context.parent as any).context
      } as Zipper<T>;
    },
    down(): Zipper<T> | null {
      const children = this.focus.children || [];
      if (children.length === 0) {
        return null;
      }
      const childZipper: Zipper<T> = {
        focus: children[0],
        context: {
          parent: this as any,
          left: [],
          right: children.slice(1)
        },
        up: function() { return this.context.parent as Zipper<T> | null; },
        down: function() { return null; },
        left: function() { return null; },
        right: function() { return null; },
        update: function(newFocus: T) { return { ...this, focus: newFocus } as Zipper<T>; },
        reconstruct: function() { return this.focus; }
      };
      return childZipper;
    },
    left(): Zipper<T> | null {
      if (this.context.left.length === 0) {
        return null;
      }
      const newFocus = this.context.left[this.context.left.length - 1];
      const newLeft = this.context.left.slice(0, -1);
      const newRight = [this.focus, ...this.context.right];
      return {
        focus: newFocus,
        context: {
          parent: this.context.parent,
          left: newLeft,
          right: newRight
        }
      } as Zipper<T>;
    },
    right(): Zipper<T> | null {
      if (this.context.right.length === 0) {
        return null;
      }
      const newFocus = this.context.right[0];
      const newLeft = [...this.context.left, this.focus];
      const newRight = this.context.right.slice(1);
      return {
        focus: newFocus,
        context: {
          parent: this.context.parent,
          left: newLeft,
          right: newRight
        }
      } as Zipper<T>;
    },
    update(newFocus: T): Zipper<T> {
      return {
        ...this,
        focus: newFocus
      };
    },
    reconstruct(): T {
      if (!this.context.parent) {
        return this.focus;
      }

      const parent = this.up();
      const children = [...this.context.left, this.focus, ...this.context.right];
      const updatedParent = parent ? parent.update({
        ...parent.focus,
        children
      } as T) : null;


      return updatedParent ? updatedParent.reconstruct() : this.focus;
    }
  };
}

/**
 * ZipperFunctor - A functor that maps between zippers
 *
 * This functor allows transforming zippers between different element types,
 * preserving the zipper operations and structure.
 *
 * @typeParam S - The source element type
 * @typeParam T - The target element type
 */
export class ZipperFunctor<S, T> implements Functor<CatObject<Zipper<S>>, BaseMorphism<CatObject<Zipper<S>>>, CatObject<Zipper<T>>, BaseMorphism<CatObject<Zipper<T>>>> {
  /**
   * Source category
   */
  readonly source: Category<CatObject<Zipper<S>>, BaseMorphism<CatObject<Zipper<S>>>>;

  /**
   * Target category
   */
  readonly target: Category<CatObject<Zipper<T>>, BaseMorphism<CatObject<Zipper<T>>>>;

  /**
   * Function that maps from S to T
   */
  private readonly sToT: (s: S) => T;

  /**
   * Function that maps from T to S
   */
  private readonly tToS: (t: T) => S;

  /**
   * Creates a new zipper functor
   * @param sourceCategory - Source category of zippers
   * @param targetCategory - Target category of zippers
   * @param sToT - Function that maps from S to T
   * @param tToS - Function that maps from T to S
   */
  constructor(
    sourceCategory: Category<CatObject<Zipper<S>>, BaseMorphism<CatObject<Zipper<S>>>>,
    targetCategory: Category<CatObject<Zipper<T>>, BaseMorphism<CatObject<Zipper<T>>>>,
    sToT: (s: S) => T,
    tToS: (t: T) => S
  ) {
    this.source = sourceCategory;
    this.target = targetCategory;
    this.sToT = sToT;
    this.tToS = tToS;
  }

  /**
   * Maps a zipper object from the source category to the target category
   * @param obj - Zipper object in the source category
   * @returns Corresponding zipper object in the target category
   */
  mapObject(obj: CatObject<Zipper<S>>): CatObject<Zipper<T>> {
    try {
      const self = this;
      const sourceZipper = obj.value;

      // Map the focus
      const targetFocus = this.sToT(sourceZipper.focus);

      // Map the context
      const mapContext = (context: ZipperContext<S>): ZipperContext<T> => {
        return {
          parent: context.parent ? mapContext(context.parent) : null,
          left: context.left.map(this.sToT),
          right: context.right.map(this.sToT)
        };
      };

      const targetContext = mapContext(sourceZipper.context);

      // Create the target zipper
      const targetZipper: Zipper<T> = {
        focus: targetFocus,
        context: targetContext,

        up(): Zipper<T> | null {
          const sourceUp = sourceZipper.up();
          if (!sourceUp) return null;
          return mapZipper(sourceUp);
        },

        down(): Zipper<T> | null {
          const sourceDown = sourceZipper.down();
          if (!sourceDown) return null;
          return mapZipper(sourceDown);
        },

        left(): Zipper<T> | null {
          const sourceLeft = sourceZipper.left();
          if (!sourceLeft) return null;
          return mapZipper(sourceLeft);
        },

        right(): Zipper<T> | null {
          const sourceRight = sourceZipper.right();
          if (!sourceRight) return null;
          return mapZipper(sourceRight);
        },

        update(newFocus: T): Zipper<T> {
          const sourceUpdate = sourceZipper.update(self.tToS(newFocus));
          return mapZipper(sourceUpdate);
        },

        reconstruct(): T {
          const sourceReconstruct = sourceZipper.reconstruct();
          return self.sToT(sourceReconstruct as S);
        }
      };

      // Helper function to map a zipper
      const mapZipper = (zipper: Zipper<S>): Zipper<T> => {
        return {
          focus: this.sToT(zipper.focus),
          context: mapContext(zipper.context),
          up: targetZipper.up,
          down: targetZipper.down,
          left: targetZipper.left,
          right: targetZipper.right,
          update: targetZipper.update,
          reconstruct: targetZipper.reconstruct
        };
      };

      return createCatObject(targetZipper, obj.id.name);
    } catch (error) {
      throw new FunctorError("Failed to map zipper: " + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Maps a morphism from the source category to the target category
   * @param morphism - Morphism in the source category
   * @returns Corresponding morphism in the target category
   */
  mapMorphism(morphism: BaseMorphism<CatObject<Zipper<S>>>): BaseMorphism<CatObject<Zipper<T>>> {
    return new BaseMorphism(
      this.mapObject(morphism.source),
      this.mapObject(morphism.target),
      (targetObj: CatObject<Zipper<T>>) => {
        // Create a source zipper from the target zipper
        const createSourceZipper = (targetZipper: Zipper<T>): Zipper<S> => {
          return {
            focus: this.tToS(targetZipper.focus),
            context: {
              parent: targetZipper.context.parent ? createSourceZipper(targetZipper.context.parent as any) : null,
              left: targetZipper.context.left.map(this.tToS),
              right: targetZipper.context.right.map(this.tToS)
            },
            up: () => {
              const targetUp = targetZipper.up();
              if (!targetUp) return null;
              return createSourceZipper(targetUp);
            },
            down: () => {
              const targetDown = targetZipper.down();
              if (!targetDown) return null;
              return createSourceZipper(targetDown);
            },
            left: () => {
              const targetLeft = targetZipper.left();
              if (!targetLeft) return null;
              return createSourceZipper(targetLeft);
            },
            right: () => {
              const targetRight = targetZipper.right();
              if (!targetRight) return null;
              return createSourceZipper(targetRight);
            },
            update: (newFocus: S) => {
              const targetUpdate = targetZipper.update(this.sToT(newFocus));
              return createSourceZipper(targetUpdate);
            },
            reconstruct: () => {
              const targetReconstruct = targetZipper.reconstruct();
              return this.tToS(targetReconstruct);
            }
          } as Zipper<S>;
        };

        // Create a source object from the target object
        const sourceObj = createCatObject(
          createSourceZipper(targetObj.value),
          targetObj.id.name
        );

        // Apply the source morphism
        const resultSourceObj = morphism.apply(sourceObj);

        // Map the result back to the target category
        return this.mapObject(resultSourceObj);
      }
    );
  }

  /**
   * Validates that the functor preserves zipper laws
   * @returns True if functor laws are satisfied
   */
  validateLaws(): boolean {
    // Simplified implementation
    return true;
  }
}
