/**
 * Simple example using the compiled output of cat_types
 */

// First, we need to build the package
// Run: npm run build

// Then we can import from the dist directory
import * as catTypes from '../dist/index.js';

// Create a category
const category = new catTypes.ConcreteCategory();

// Create objects
const objA = catTypes.createCatObject(5, 'A');
const objB = catTypes.createCatObject(10, 'B');

// Add objects to the category
category.addObject(objA);
category.addObject(objB);

// Create a morphism
const double = new catTypes.BaseMorphism(
  objA,
  objB,
  (x) => x * 2
);

// Add the morphism to the category
category.addMorphism(double);

// Use the morphism
const result = double.apply(5);
console.log(`Result of applying double to 5: ${result}`);

// Create another morphism
const addOne = new catTypes.BaseMorphism(
  objB,
  catTypes.createCatObject(11, 'C'),
  (x) => x + 1
);

// Add the morphism to the category
category.addMorphism(addOne);

// Compose morphisms
const composed = category.compose(double, addOne);

// Use the composed morphism
const composedResult = composed.apply(5);
console.log(`Result of applying double then addOne to 5: ${composedResult}`);
