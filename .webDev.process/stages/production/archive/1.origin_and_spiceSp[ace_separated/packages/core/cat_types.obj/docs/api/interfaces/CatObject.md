[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / CatObject

# Interface: CatObject\<T\>

Defined in: [atomic/catObject.ts:14](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L14)

Represents an object in a category

## Type Parameters

• **T** = `any`

Type of the value contained in the object

## Properties

### id

> `readonly` **id**: [`CatId`](CatId.md)

Defined in: [atomic/catObject.ts:18](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L18)

Unique identifier for the object

***

### value

> `readonly` **value**: `T`

Defined in: [atomic/catObject.ts:23](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catObject.ts#L23)

Value contained in the object
