**Cat Types API Reference v0.1.0**

***

# Cat Types API Reference v0.1.0

## Classes

- [BaseFunctor](classes/BaseFunctor.md)
- [BaseMonad](classes/BaseMonad.md)
- [BaseMorphism](classes/BaseMorphism.md)
- [CategoryError](classes/CategoryError.md)
- [ConcreteCategory](classes/ConcreteCategory.md)
- [FunctorError](classes/FunctorError.md)
- [LensFunctor](classes/LensFunctor.md)
- [MonadError](classes/MonadError.md)
- [MorphismError](classes/MorphismError.md)
- [ZipperFunctor](classes/ZipperFunctor.md)

## Interfaces

- [Category](interfaces/Category.md)
- [CatId](interfaces/CatId.md)
- [CatObject](interfaces/CatObject.md)
- [Functor](interfaces/Functor.md)
- [Lens](interfaces/Lens.md)
- [Monad](interfaces/Monad.md)
- [Morphism](interfaces/Morphism.md)
- [Zipper](interfaces/Zipper.md)
- [ZipperContext](interfaces/ZipperContext.md)

## Functions

- [composeLens](functions/composeLens.md)
- [createCatId](functions/createCatId.md)
- [createCatObject](functions/createCatObject.md)
- [createLens](functions/createLens.md)
- [createListZipper](functions/createListZipper.md)
- [createTreeZipper](functions/createTreeZipper.md)
- [index](functions/index.md)
- [prop](functions/prop.md)
