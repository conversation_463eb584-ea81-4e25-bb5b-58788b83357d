import React from 'react';

/**
 * Component to embed TypeDoc documentation in a Gatsby page
 */
const TypeDocFrame = ({ path }) => {
  const typedocPath = path || 'index.html';
  const fullPath = `/api/${typedocPath}`;
  
  return (
    <div className="typedoc-container">
      <iframe
        src={fullPath}
        title="TypeDoc API Reference"
        style={{
          width: '100%',
          height: '800px',
          border: 'none',
        }}
      />
    </div>
  );
};

export default TypeDocFrame;
