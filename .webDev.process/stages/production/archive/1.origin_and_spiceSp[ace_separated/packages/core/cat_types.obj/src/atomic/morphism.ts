/**
 * Morphism implementation
 *
 * This file implements the Morphism type, which represents a morphism (arrow)
 * between objects in a category.
 */

import { CatObject } from './catObject';

// @Morphism
/**
 * Represents a morphism (arrow) between objects in a category
 * @typeParam O - Type of objects in the category
 */
interface Morphism<O extends CatObject> {
  /**
   * Source object of the morphism
   */
  source: O;

  /**
   * Target object of the morphism
   */
  target: O;

  /**
   * Applies the morphism to an input
   * @param input - Input to apply the morphism to
   * @returns Result of applying the morphism
   */
  apply(input: any): any;
}

// @BaseMorphism
/**
 * Base implementation of a morphism
 * @typeParam O - Type of objects in the category
 */
class BaseMorphism<O extends CatObject> implements Morphism<O> {
  /**
   * Source object of the morphism
   */
  source: O;

  /**
   * Target object of the morphism
   */
  target: O;

  /**
   * Transformation function
   */
  private transform: (input: any) => any;

  /**
   * Creates a new morphism
   * @param source - Source object
   * @param target - Target object
   * @param transform - Transformation function
   */
  constructor(source: O, target: O, transform: (input: any) => any) {
    this.source = source;
    this.target = target;
    this.transform = transform;
  }

  /**
   * Applies the morphism to an input
   * @param input - Input to apply the morphism to
   * @returns Result of applying the morphism
   */
  apply(input: any): any {
    return this.transform(input);
  }
}

export { Morphism, BaseMorphism };
