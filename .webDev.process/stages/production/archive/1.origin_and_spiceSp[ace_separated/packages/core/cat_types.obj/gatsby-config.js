/**
 * Documentation metadata for cat_types package
 *
 * This file provides metadata for the cat_types package documentation
 * to be integrated with a central Gatsby documentation site.
 */

module.exports = {
  // Package information
  packageName: 'cat_types',
  packagePath: 'utils/origin.rfr/cat_types.obj',

  // Documentation metadata
  documentation: {
    title: 'Cat Types',
    description: 'TypeScript implementation of category theory concepts',
    sections: [
      {
        id: 'usage',
        title: 'Usage Guides',
        path: './docs/usage',
        order: 1
      },
      {
        id: 'api',
        title: 'API Reference',
        path: './docs/api',
        order: 2
      },
      {
        id: 'examples',
        title: 'Examples',
        path: './docs/examples',
        order: 3
      }
    ]
  },

  // TypeDoc configuration
  typedoc: {
    entryPoints: ['./src/index.ts'],
    tsconfig: './tsconfig.json',
    out: './docs/api'
  }
};
