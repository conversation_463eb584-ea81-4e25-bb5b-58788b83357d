[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / Category

# Interface: Category\<O, M\>

Defined in: [atomic/cat.ts:18](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L18)

Represents a category in category theory

## Type Parameters

• **O** *extends* [`CatObject`](CatObject.md)

Type of objects in the category

• **M** *extends* [`Morphism`](Morphism.md)\<`O`\>

Type of morphisms in the category

## Methods

### addMorphism()

> **addMorphism**(`morphism`): `void`

Defined in: [atomic/cat.ts:50](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L50)

Adds a morphism to the category

#### Parameters

##### morphism

`M`

Morphism to add

#### Returns

`void`

***

### addObject()

> **addObject**(`obj`): `void`

Defined in: [atomic/cat.ts:44](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L44)

Adds an object to the category

#### Parameters

##### obj

`O`

Object to add

#### Returns

`void`

***

### compose()

> **compose**(`f`, `g`): `M`

Defined in: [atomic/cat.ts:32](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L32)

Composes two morphisms in the category

#### Parameters

##### f

`M`

First morphism

##### g

`M`

Second morphism

#### Returns

`M`

The composed morphism

***

### getMorphisms()

> **getMorphisms**(): `Map`\<`string`, `M`\>

Defined in: [atomic/cat.ts:62](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L62)

Gets all morphisms in the category

#### Returns

`Map`\<`string`, `M`\>

Map of all morphisms

***

### getObjects()

> **getObjects**(): `Set`\<`O`\>

Defined in: [atomic/cat.ts:56](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L56)

Gets all objects in the category

#### Returns

`Set`\<`O`\>

Set of all objects

***

### id()

> **id**(`obj`): `M`

Defined in: [atomic/cat.ts:24](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L24)

Returns the identity morphism for a given object

#### Parameters

##### obj

`O`

The object to get identity for

#### Returns

`M`

The identity morphism

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/cat.ts:38](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L38)

Checks if the category laws hold for this instance

#### Returns

`boolean`

True if category laws are satisfied
