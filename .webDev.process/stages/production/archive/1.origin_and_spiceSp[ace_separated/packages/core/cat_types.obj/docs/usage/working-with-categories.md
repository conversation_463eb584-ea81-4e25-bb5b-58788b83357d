---
title: "Working with Categories"
description: "Learn how to create and work with categories in the cat_types package"
---

# Working with Categories

Categories are the fundamental structure in category theory. In the `cat_types` package, categories are represented by the `Category` interface and implemented by the `ConcreteCategory` class.

## Creating a Category

To create a category, you instantiate a `ConcreteCategory`:

```typescript
import { ConcreteCategory } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();
```

## Adding Objects to a Category

Objects in a category are represented by the `CatObject` interface. You can create objects using the `createCatObject` function:

```typescript
import { createCatObject } from '@future/cat_types';

// Create objects
const objA = createCatObject(5, 'A');
const objB = createCatObject(10, 'B');
const objC = createCatObject(15, 'C');

// Add objects to the category
category.addObject(objA);
category.addObject(objB);
category.addObject(objC);
```

The `createCatObject` function takes two parameters:
- The value of the object
- An optional name for the object (useful for debugging)

## Adding Morphisms to a Category

Morphisms (arrows) in a category are represented by the `Morphism` interface. You can create morphisms using the `BaseMorphism` class:

```typescript
import { BaseMorphism } from '@future/cat_types';

// Create morphisms
const double = new BaseMorphism(
  objA,
  objB,
  (x: number) => x * 2
);

const addFive = new BaseMorphism(
  objB,
  objC,
  (x: number) => x + 5
);

// Add morphisms to the category
category.addMorphism(double);
category.addMorphism(addFive);
```

The `BaseMorphism` constructor takes three parameters:
- The source object
- The target object
- The transformation function

## Composing Morphisms

One of the key operations in category theory is composition of morphisms. You can compose morphisms using the `compose` method of a category:

```typescript
// Compose double and addFive
const doubleAndAddFive = category.compose(double, addFive);

// Apply the composed morphism
const result = doubleAndAddFive.apply(5); // (5 * 2) + 5 = 15
```

Note that composition is only possible when the target of the first morphism matches the source of the second morphism.

## Identity Morphisms

Every object in a category has an identity morphism, which is a morphism from the object to itself that leaves the object unchanged. You can get the identity morphism for an object using the `id` method of a category:

```typescript
// Get the identity morphism for objA
const idA = category.id(objA);

// Apply the identity morphism
const result = idA.apply(5); // 5
```

## Validating Category Laws

Categories must satisfy certain laws:
1. Composition is associative: `(f ∘ g) ∘ h = f ∘ (g ∘ h)`
2. Identity morphisms act as units for composition: `f ∘ id_A = f` and `id_B ∘ f = f` for any morphism `f: A → B`

You can validate that a category satisfies these laws using the `validateLaws` method:

```typescript
// Check if the category satisfies the category laws
const isValid = category.validateLaws();
```

## Working with Multiple Categories

You can create multiple categories and work with them independently:

```typescript
// Create a category of strings
const stringCategory = new ConcreteCategory();

// Create objects
const strA = createCatObject("hello", "strA");
const strB = createCatObject("world", "strB");

// Add objects to the category
stringCategory.addObject(strA);
stringCategory.addObject(strB);

// Create a morphism
const appendExclamation = new BaseMorphism(
  strA,
  strB,
  (s: string) => s + "!"
);

// Add the morphism to the category
stringCategory.addMorphism(appendExclamation);
```

## Next Steps

Now that you understand how to work with categories, you can explore more advanced concepts like [functors](./working-with-functors.md) and [monads](./working-with-monads.md).
