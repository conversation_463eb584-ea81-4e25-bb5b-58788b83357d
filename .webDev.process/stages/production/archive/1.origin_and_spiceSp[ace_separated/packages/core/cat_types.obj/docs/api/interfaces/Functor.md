[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / Functor

# Interface: Functor\<S, SM, T, TM\>

Defined in: [atomic/functor.ts:16](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L16)

Represents a functor between categories

## Type Parameters

• **S** *extends* `CatObject`

Source category object type

• **SM** *extends* `Morphism`\<`S`\>

Source category morphism type

• **T** *extends* `CatObject`

Target category object type

• **TM** *extends* `Morphism`\<`T`\>

Target category morphism type

## Properties

### source

> `readonly` **source**: `Category`\<`S`, `SM`\>

Defined in: [atomic/functor.ts:25](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L25)

Source category

***

### target

> `readonly` **target**: `Category`\<`T`, `TM`\>

Defined in: [atomic/functor.ts:30](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L30)

Target category

## Methods

### mapMorphism()

> **mapMorphism**(`morphism`): `TM`

Defined in: [atomic/functor.ts:44](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L44)

Maps a morphism from source category to target category

#### Parameters

##### morphism

`SM`

Morphism in source category

#### Returns

`TM`

Corresponding morphism in target category

***

### mapObject()

> **mapObject**(`obj`): `T`

Defined in: [atomic/functor.ts:37](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L37)

Maps an object from source category to target category

#### Parameters

##### obj

`S`

Object in source category

#### Returns

`T`

Corresponding object in target category

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/functor.ts:50](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L50)

Validates that the functor preserves composition and identity

#### Returns

`boolean`

True if functor laws are satisfied
