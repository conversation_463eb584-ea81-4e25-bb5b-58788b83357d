# Cat Types Usage Guide

This directory contains usage guides for the `cat_types` package.

## Getting Started

The `cat_types` package provides TypeScript implementations of category theory concepts. These can be used to model complex relationships in your domain while maintaining mathematical guarantees.

## Basic Concepts

### Categories

A category consists of:
- Objects
- Morphisms (arrows) between objects
- Composition of morphisms
- Identity morphisms

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects
const objA = createCatObject(5, 'A');
const objB = createCatObject(10, 'B');

// Add objects to the category
category.addObject(objA);
category.addObject(objB);

// Create a morphism
const double = new BaseMorphism(
  objA,
  objB,
  (x: number) => x * 2
);

// Add the morphism to the category
category.addMorphism(double);

// Use the morphism
const result = double.apply(5); // 10
```

### Functors

Functors map between categories, preserving structure:

```typescript
import { BaseFunctor } from '@future/cat_types';

// Create a functor between two categories
const functor = new BaseFunctor(
  sourceCategory,
  targetCategory,
  objectMapper,
  morphismMapper
);

// Map an object from source to target
const targetObj = functor.mapObject(sourceObj);

// Map a morphism from source to target
const targetMorphism = functor.mapMorphism(sourceMorphism);
```

### Monads

Monads are a special kind of functor with additional structure:

```typescript
import { BaseMonad } from '@future/cat_types';

// Create a monad
const monad = new BaseMonad(
  functor,
  category,
  unitFn,
  joinFn
);

// Lift a value into the monad
const monadicValue = monad.unit(value);

// Flatten a nested monadic value
const flattenedValue = monad.join(nestedValue);

// Bind a function to a monadic value
const result = monad.bind(monadicValue, fn);
```

## Advanced Usage

See the [examples](../examples) directory for more advanced usage patterns.
