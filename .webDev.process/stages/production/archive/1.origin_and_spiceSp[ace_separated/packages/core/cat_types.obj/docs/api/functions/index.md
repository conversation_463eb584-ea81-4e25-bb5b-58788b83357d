[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / index

# Function: index()

> **index**\<`A`\>(`index`): [`Lens`](../interfaces/Lens.md)\<`A`[], `A`\>

Defined in: [functors/lens.fr.ts:93](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L93)

Create a lens that focuses on an index of an array

## Type Parameters

• **A**

## Parameters

### index

`number`

The index to focus on

## Returns

[`Lens`](../interfaces/Lens.md)\<`A`[], `A`\>

A lens that focuses on the specified index
