/**
 * Example of using the cat_types system to model a simple category
 */

import { ConcreteCategory } from '../src/atomic/cat';
import { createCatObject } from '../src/atomic/catObject';
import { BaseMorphism } from '../src/atomic/morphism';

// Create a category of numbers with addition and multiplication
const numberCategory = new ConcreteCategory();

// Create some objects (numbers)
const zero = createCatObject(0, 'zero');
const one = createCatObject(1, 'one');
const two = createCatObject(2, 'two');
const three = createCatObject(3, 'three');

// Add objects to the category
numberCategory.addObject(zero);
numberCategory.addObject(one);
numberCategory.addObject(two);
numberCategory.addObject(three);

// Create morphisms (operations)
const addOne = new BaseMorphism(
  zero,
  one,
  (x: number) => x + 1
);

const addTwo = new BaseMorphism(
  zero,
  two,
  (x: number) => x + 2
);

const double = new BaseMorphism(
  one,
  two,
  (x: number) => x * 2
);

const triple = new BaseMorphism(
  one,
  three,
  (x: number) => x * 3
);

// Add morphisms to the category
numberCategory.addMorphism(addOne);
numberCategory.addMorphism(addTwo);
numberCategory.addMorphism(double);
numberCategory.addMorphism(triple);

// Usage example
function demonstrateCategory() {
  console.log('Number Category Example:');

  // Apply morphisms
  console.log(`addOne(0) = ${addOne.apply(0)}`);
  console.log(`double(1) = ${double.apply(1)}`);

  // Composition (manual, since the category's compose method is not fully implemented)
  const addOneThenDouble = (x: number) => double.apply(addOne.apply(x));
  console.log(`addOneThenDouble(0) = ${addOneThenDouble(0)}`);

  // Get all objects
  const objects = numberCategory.getObjects();
  console.log('Objects in category:', Array.from(objects).map(obj => obj.value));

  // Get all morphisms
  const morphisms = numberCategory.getMorphisms();
  console.log('Number of morphisms:', morphisms.size);
}

// Export for use in other examples
export {
  numberCategory,
  zero, one, two, three,
  addOne, addTwo, double, triple,
  demonstrateCategory
};
