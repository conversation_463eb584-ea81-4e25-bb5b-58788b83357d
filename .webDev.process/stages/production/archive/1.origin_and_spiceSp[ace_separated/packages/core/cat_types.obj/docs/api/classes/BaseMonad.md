[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / BaseMonad

# Class: BaseMonad\<C, M\>

Defined in: [atomic/monad.ts:60](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L60)

Base implementation of a monad

## Type Parameters

• **C** *extends* `CatObject`

Category object type

• **M** *extends* `Morphism`\<`C`\>

Category morphism type

## Implements

- [`Monad`](../interfaces/Monad.md)\<`C`, `M`\>

## Constructors

### new BaseMonad()

> **new BaseMonad**\<`C`, `M`\>(`functor`, `category`, `unitFn`, `joinFn`): [`BaseMonad`](BaseMonad.md)\<`C`, `M`\>

Defined in: [atomic/monad.ts:88](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L88)

Creates a new monad

#### Parameters

##### functor

`Functor`\<`C`, `M`, `C`, `M`\>

The underlying endofunctor

##### category

`Category`\<`C`, `M`\>

The category on which this monad operates

##### unitFn

(`obj`) => `C`

Function implementing the unit natural transformation

##### joinFn

(`obj`) => `C`

Function implementing the join natural transformation

#### Returns

[`BaseMonad`](BaseMonad.md)\<`C`, `M`\>

## Properties

### category

> `readonly` **category**: `Category`\<`C`, `M`\>

Defined in: [atomic/monad.ts:69](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L69)

The category on which this monad operates

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`category`](../interfaces/Monad.md#category)

***

### functor

> `readonly` **functor**: `Functor`\<`C`, `M`, `C`, `M`\>

Defined in: [atomic/monad.ts:64](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L64)

The underlying endofunctor

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`functor`](../interfaces/Monad.md#functor)

## Methods

### bind()

> **bind**(`obj`, `f`): `C`

Defined in: [atomic/monad.ts:124](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L124)

Bind (flatMap) operation

#### Parameters

##### obj

`C`

Monadic object

##### f

(`x`) => `C`

Function that returns a monadic object

#### Returns

`C`

Result of applying f to the value inside obj, then flattening

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`bind`](../interfaces/Monad.md#bind)

***

### join()

> **join**(`obj`): `C`

Defined in: [atomic/monad.ts:114](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L114)

Join (flatten) natural transformation

#### Parameters

##### obj

`C`

Nested monadic object

#### Returns

`C`

Flattened monadic object

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`join`](../interfaces/Monad.md#join)

***

### unit()

> **unit**(`obj`): `C`

Defined in: [atomic/monad.ts:105](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L105)

Unit (return) natural transformation

#### Parameters

##### obj

`C`

Object to lift into the monad

#### Returns

`C`

Monadic object

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`unit`](../interfaces/Monad.md#unit)

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/monad.ts:135](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L135)

Validates that the monad laws are satisfied

#### Returns

`boolean`

True if monad laws are satisfied

#### Implementation of

[`Monad`](../interfaces/Monad.md).[`validateLaws`](../interfaces/Monad.md#validatelaws)
