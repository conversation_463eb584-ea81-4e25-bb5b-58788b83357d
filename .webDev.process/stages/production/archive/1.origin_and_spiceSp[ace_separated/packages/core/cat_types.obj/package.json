{"name": "@future/cat_types", "version": "0.1.0", "description": "Category theory types and utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"test": "vitest run", "build": "tsc", "example": "ts-node -P examples/tsconfig.json examples/index.ts", "example:category": "ts-node -P examples/tsconfig.json examples/simple-category.ts", "example:functor": "ts-node -P examples/tsconfig.json examples/simple-functor.ts", "example:monad": "ts-node -P examples/tsconfig.json examples/simple-monad.ts", "example:simple": "node examples/simple-example.js", "example:direct": "ts-node -P examples/tsconfig.json examples/direct-example.ts", "example:cjs": "node examples/cjs-example.cjs", "example:lens": "node examples/lens-example.cjs", "example:zipper": "node examples/zipper-example.cjs", "docs": "./scripts/build-docs.sh"}, "dependencies": {"@local/core_error": "workspace:*"}, "devDependencies": {"@types/node": "^18.11.9", "ts-node": "^10.9.1", "typescript": "^4.9.3", "vitest": "^0.25.3", "typedoc": "^0.25.0", "typedoc-plugin-markdown": "^3.15.0"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}}