---
title: "State Management Guide"
description: "Implementing state management with lenses and monads"
---

# State Management Guide

This guide shows how to implement state management in web applications using lenses and monads from the `cat_types` package.

## The Problem with Traditional State Management

Traditional state management often involves:

1. Mutable state that's hard to track
2. Deeply nested updates that are error-prone
3. Side effects mixed with state updates
4. Difficulty in testing state changes

## Solution: Immutable State with Lenses

Lenses provide a clean way to work with immutable state:

1. Focus on specific parts of your state
2. Make immutable updates
3. Compose lenses to work with deeply nested state
4. Keep your state updates pure and testable

## Basic Implementation

### Setting Up Your State

```typescript
import { createLens, prop, composeLens } from '@future/cat_types';

// Define your application state
interface AppState {
  user: {
    profile: {
      name: string;
      email: string;
    };
    preferences: {
      theme: 'light' | 'dark';
      notifications: boolean;
    };
  };
  ui: {
    sidebar: {
      isOpen: boolean;
    };
    modal: {
      isOpen: boolean;
      content: string;
    };
  };
  data: {
    posts: Post[];
    comments: Comment[];
    isLoading: boolean;
    error: string | null;
  };
}

// Initial state
const initialState: AppState = {
  user: {
    profile: {
      name: '',
      email: ''
    },
    preferences: {
      theme: 'light',
      notifications: true
    }
  },
  ui: {
    sidebar: {
      isOpen: false
    },
    modal: {
      isOpen: false,
      content: ''
    }
  },
  data: {
    posts: [],
    comments: [],
    isLoading: false,
    error: null
  }
};
```

### Creating Lenses

```typescript
// User lenses
const userLens = prop<AppState, 'user'>('user');
const profileLens = prop<AppState['user'], 'profile'>('profile');
const userProfileLens = composeLens(userLens, profileLens);
const nameLens = prop<AppState['user']['profile'], 'name'>('name');
const emailLens = prop<AppState['user']['profile'], 'email'>('email');
const userNameLens = composeLens(userProfileLens, nameLens);
const userEmailLens = composeLens(userProfileLens, emailLens);

const preferencesLens = prop<AppState['user'], 'preferences'>('preferences');
const userPreferencesLens = composeLens(userLens, preferencesLens);
const themeLens = prop<AppState['user']['preferences'], 'theme'>('theme');
const notificationsLens = prop<AppState['user']['preferences'], 'notifications'>('notifications');
const userThemeLens = composeLens(userPreferencesLens, themeLens);
const userNotificationsLens = composeLens(userPreferencesLens, notificationsLens);

// UI lenses
const uiLens = prop<AppState, 'ui'>('ui');
const sidebarLens = prop<AppState['ui'], 'sidebar'>('sidebar');
const uiSidebarLens = composeLens(uiLens, sidebarLens);
const sidebarIsOpenLens = prop<AppState['ui']['sidebar'], 'isOpen'>('isOpen');
const sidebarIsOpenStateLens = composeLens(uiSidebarLens, sidebarIsOpenLens);

const modalLens = prop<AppState['ui'], 'modal'>('modal');
const uiModalLens = composeLens(uiLens, modalLens);
const modalIsOpenLens = prop<AppState['ui']['modal'], 'isOpen'>('isOpen');
const modalContentLens = prop<AppState['ui']['modal'], 'content'>('content');
const modalIsOpenStateLens = composeLens(uiModalLens, modalIsOpenLens);
const modalContentStateLens = composeLens(uiModalLens, modalContentLens);

// Data lenses
const dataLens = prop<AppState, 'data'>('data');
const postsLens = prop<AppState['data'], 'posts'>('posts');
const commentsLens = prop<AppState['data'], 'comments'>('comments');
const isLoadingLens = prop<AppState['data'], 'isLoading'>('isLoading');
const errorLens = prop<AppState['data'], 'error'>('error');
const dataPostsLens = composeLens(dataLens, postsLens);
const dataCommentsLens = composeLens(dataLens, commentsLens);
const dataIsLoadingLens = composeLens(dataLens, isLoadingLens);
const dataErrorLens = composeLens(dataLens, errorLens);
```

### State Update Functions

```typescript
// User update functions
function updateUserName(state: AppState, name: string): AppState {
  return userNameLens.set(name, state);
}

function updateUserEmail(state: AppState, email: string): AppState {
  return userEmailLens.set(email, state);
}

function toggleTheme(state: AppState): AppState {
  return userThemeLens.modify(state, theme => theme === 'light' ? 'dark' : 'light');
}

function toggleNotifications(state: AppState): AppState {
  return userNotificationsLens.modify(state, notifications => !notifications);
}

// UI update functions
function toggleSidebar(state: AppState): AppState {
  return sidebarIsOpenStateLens.modify(state, isOpen => !isOpen);
}

function openModal(state: AppState, content: string): AppState {
  let newState = modalIsOpenStateLens.set(true, state);
  return modalContentStateLens.set(content, newState);
}

function closeModal(state: AppState): AppState {
  return modalIsOpenStateLens.set(false, state);
}

// Data update functions
function setLoading(state: AppState, isLoading: boolean): AppState {
  return dataIsLoadingLens.set(isLoading, state);
}

function setError(state: AppState, error: string | null): AppState {
  return dataErrorLens.set(error, state);
}

function setPosts(state: AppState, posts: Post[]): AppState {
  return dataPostsLens.set(posts, state);
}

function addPost(state: AppState, post: Post): AppState {
  return dataPostsLens.modify(state, posts => [...posts, post]);
}

function updatePost(state: AppState, updatedPost: Post): AppState {
  return dataPostsLens.modify(state, posts => 
    posts.map(post => post.id === updatedPost.id ? updatedPost : post)
  );
}

function deletePost(state: AppState, postId: string): AppState {
  return dataPostsLens.modify(state, posts => 
    posts.filter(post => post.id !== postId)
  );
}
```

## Integration with React

### Using with useState

```typescript
import React, { useState } from 'react';

function App() {
  const [state, setState] = useState<AppState>(initialState);
  
  const handleUpdateName = (name: string) => {
    setState(state => updateUserName(state, name));
  };
  
  const handleToggleTheme = () => {
    setState(state => toggleTheme(state));
  };
  
  const handleOpenModal = (content: string) => {
    setState(state => openModal(state, content));
  };
  
  const handleCloseModal = () => {
    setState(state => closeModal(state));
  };
  
  // Get values from state using lenses
  const userName = userNameLens.get(state);
  const theme = userThemeLens.get(state);
  const isModalOpen = modalIsOpenStateLens.get(state);
  const modalContent = modalContentStateLens.get(state);
  
  return (
    <div className={`app ${theme}`}>
      <header>
        <h1>Welcome, {userName || 'Guest'}</h1>
        <button onClick={handleToggleTheme}>
          Switch to {theme === 'light' ? 'Dark' : 'Light'} Mode
        </button>
      </header>
      
      <main>
        <input
          type="text"
          value={userName}
          onChange={e => handleUpdateName(e.target.value)}
          placeholder="Enter your name"
        />
        
        <button onClick={() => handleOpenModal('This is a modal')}>
          Open Modal
        </button>
      </main>
      
      {isModalOpen && (
        <div className="modal">
          <div className="modal-content">
            <h2>Modal</h2>
            <p>{modalContent}</p>
            <button onClick={handleCloseModal}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
}
```

### Using with useReducer

```typescript
import React, { useReducer } from 'react';

// Define action types
type Action =
  | { type: 'UPDATE_USER_NAME'; payload: string }
  | { type: 'UPDATE_USER_EMAIL'; payload: string }
  | { type: 'TOGGLE_THEME' }
  | { type: 'TOGGLE_NOTIFICATIONS' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'OPEN_MODAL'; payload: string }
  | { type: 'CLOSE_MODAL' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_POSTS'; payload: Post[] }
  | { type: 'ADD_POST'; payload: Post }
  | { type: 'UPDATE_POST'; payload: Post }
  | { type: 'DELETE_POST'; payload: string };

// Reducer function
function reducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'UPDATE_USER_NAME':
      return updateUserName(state, action.payload);
    case 'UPDATE_USER_EMAIL':
      return updateUserEmail(state, action.payload);
    case 'TOGGLE_THEME':
      return toggleTheme(state);
    case 'TOGGLE_NOTIFICATIONS':
      return toggleNotifications(state);
    case 'TOGGLE_SIDEBAR':
      return toggleSidebar(state);
    case 'OPEN_MODAL':
      return openModal(state, action.payload);
    case 'CLOSE_MODAL':
      return closeModal(state);
    case 'SET_LOADING':
      return setLoading(state, action.payload);
    case 'SET_ERROR':
      return setError(state, action.payload);
    case 'SET_POSTS':
      return setPosts(state, action.payload);
    case 'ADD_POST':
      return addPost(state, action.payload);
    case 'UPDATE_POST':
      return updatePost(state, action.payload);
    case 'DELETE_POST':
      return deletePost(state, action.payload);
    default:
      return state;
  }
}

function App() {
  const [state, dispatch] = useReducer(reducer, initialState);
  
  const handleUpdateName = (name: string) => {
    dispatch({ type: 'UPDATE_USER_NAME', payload: name });
  };
  
  const handleToggleTheme = () => {
    dispatch({ type: 'TOGGLE_THEME' });
  };
  
  const handleOpenModal = (content: string) => {
    dispatch({ type: 'OPEN_MODAL', payload: content });
  };
  
  const handleCloseModal = () => {
    dispatch({ type: 'CLOSE_MODAL' });
  };
  
  // Get values from state using lenses
  const userName = userNameLens.get(state);
  const theme = userThemeLens.get(state);
  const isModalOpen = modalIsOpenStateLens.get(state);
  const modalContent = modalContentStateLens.get(state);
  
  return (
    <div className={`app ${theme}`}>
      {/* Same UI as before */}
    </div>
  );
}
```

## Integration with Redux

```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Create a slice with lens-based reducers
const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    updateUserName: (state, action: PayloadAction<string>) => {
      return updateUserName(state, action.payload);
    },
    updateUserEmail: (state, action: PayloadAction<string>) => {
      return updateUserEmail(state, action.payload);
    },
    toggleTheme: (state) => {
      return toggleTheme(state);
    },
    toggleNotifications: (state) => {
      return toggleNotifications(state);
    },
    toggleSidebar: (state) => {
      return toggleSidebar(state);
    },
    openModal: (state, action: PayloadAction<string>) => {
      return openModal(state, action.payload);
    },
    closeModal: (state) => {
      return closeModal(state);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      return setLoading(state, action.payload);
    },
    setError: (state, action: PayloadAction<string | null>) => {
      return setError(state, action.payload);
    },
    setPosts: (state, action: PayloadAction<Post[]>) => {
      return setPosts(state, action.payload);
    },
    addPost: (state, action: PayloadAction<Post>) => {
      return addPost(state, action.payload);
    },
    updatePost: (state, action: PayloadAction<Post>) => {
      return updatePost(state, action.payload);
    },
    deletePost: (state, action: PayloadAction<string>) => {
      return deletePost(state, action.payload);
    }
  }
});

export const {
  updateUserName,
  updateUserEmail,
  toggleTheme,
  toggleNotifications,
  toggleSidebar,
  openModal,
  closeModal,
  setLoading,
  setError,
  setPosts,
  addPost,
  updatePost,
  deletePost
} = appSlice.actions;

export default appSlice.reducer;
```

## Advanced Patterns

### Combining Multiple Updates

```typescript
function updateUserProfile(state: AppState, name: string, email: string): AppState {
  // Method 1: Chain lens operations
  return userEmailLens.set(email, userNameLens.set(name, state));
  
  // Method 2: Create a composite update function
  const updateProfile = (s: AppState) => {
    const s1 = userNameLens.set(name, s);
    const s2 = userEmailLens.set(email, s1);
    return s2;
  };
  
  return updateProfile(state);
}
```

### Creating a State Monad

```typescript
import { createCatObject, BaseMonad, BaseFunctor, ConcreteCategory } from '@future/cat_types';

// Define the State type
type State<S, A> = (s: S) => [A, S];

// Helper functions
const get = <S>(): State<S, S> => s => [s, s];
const put = <S>(s: S): State<S, void> => _ => [undefined, s];
const modify = <S>(f: (s: S) => S): State<S, void> => s => [undefined, f(s)];
const returns = <S, A>(a: A): State<S, A> => s => [a, s];

// Run a state computation
const runState = <S, A>(state: State<S, A>, initialState: S): [A, S] => {
  return state(initialState);
};

// Example usage
const updateUserProfileState = (name: string, email: string): State<AppState, void> => {
  return s => {
    const s1 = userNameLens.set(name, s);
    const s2 = userEmailLens.set(email, s1);
    return [undefined, s2];
  };
};

const toggleThemeState = (): State<AppState, void> => {
  return s => {
    const newState = userThemeLens.modify(s, theme => theme === 'light' ? 'dark' : 'light');
    return [undefined, newState];
  };
};

// Compose state operations
const updateProfileAndToggleTheme = (name: string, email: string): State<AppState, void> => {
  return s => {
    const [_, s1] = updateUserProfileState(name, email)(s);
    return toggleThemeState()(s1);
  };
};

// Usage
const [_, newState] = runState(updateProfileAndToggleTheme('John Doe', '<EMAIL>'), initialState);
```

## Performance Considerations

1. **Memoize lenses**: If you create lenses dynamically, consider memoizing them.

```typescript
import { memoize } from 'lodash';

const memoizedProp = memoize(prop);
```

2. **Batch updates**: When making multiple updates, batch them together.

```typescript
// Instead of this:
let state = initialState;
state = updateUserName(state, 'John');
state = updateUserEmail(state, '<EMAIL>');
state = toggleTheme(state);

// Do this:
const updateAll = (state: AppState) => {
  const s1 = updateUserName(state, 'John');
  const s2 = updateUserEmail(s1, '<EMAIL>');
  return toggleTheme(s2);
};

const newState = updateAll(initialState);
```

3. **Use selectors**: For derived state, use memoized selectors.

```typescript
import { createSelector } from 'reselect';

const selectUserProfile = createSelector(
  (state: AppState) => userProfileLens.get(state),
  profile => profile
);

const selectFullName = createSelector(
  selectUserProfile,
  profile => `${profile.firstName} ${profile.lastName}`
);
```

## Testing

Lens-based state management is highly testable:

```typescript
import { updateUserName, toggleTheme } from './state';

describe('State Management', () => {
  test('updateUserName should update the user name', () => {
    const state = { ...initialState };
    const newState = updateUserName(state, 'John');
    
    expect(userNameLens.get(newState)).toBe('John');
    // Original state is unchanged
    expect(userNameLens.get(state)).toBe('');
  });
  
  test('toggleTheme should switch between light and dark', () => {
    const state = { ...initialState };
    const newState = toggleTheme(state);
    
    expect(userThemeLens.get(newState)).toBe('dark');
    expect(userThemeLens.get(state)).toBe('light');
    
    const thirdState = toggleTheme(newState);
    expect(userThemeLens.get(thirdState)).toBe('light');
  });
});
```

## Conclusion

Using lenses for state management provides several benefits:

1. **Immutability**: All state updates are immutable, preventing bugs from mutation.
2. **Composability**: Lenses can be composed to work with deeply nested state.
3. **Type Safety**: Lenses provide strong type safety for state updates.
4. **Testability**: Pure functions are easy to test.
5. **Readability**: State update logic is clear and declarative.

By adopting this approach, you can create more maintainable and robust state management for your web applications.
