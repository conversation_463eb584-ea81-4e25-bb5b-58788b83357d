/**
 * @module FunctorTypes
 * @packageDocumentation
 *
 * Zipper functors provide a way to navigate and modify data structures
 * with a focused element and surrounding context. They are particularly
 * useful for traversing and editing tree-like structures while maintaining
 * the ability to navigate up, down, left, and right.
 */
import { Functor } from '../functor.type';
import { Morphism } from '../morphism.type';
/**
 * Zipper - A structure that allows navigation and local editing
 * with a focus point and surrounding context
 *
 * Zippers provide a way to:
 * 1. Focus on a specific element in a data structure
 * 2. Navigate to adjacent elements (up, down, left, right)
 * 3. Modify the focused element
 * 4. Reconstruct the entire data structure with modifications
 *
 * @typeParam T - The type of elements in the structure
 */
export interface Zipper<T> {
    /**
     * The currently focused element
     */
    focus: T;
    /**
     * The context/path information needed to reconstruct the whole structure
     */
    context: any;
    /**
     * Move up in the structure (toward parent/root)
     *
     * @returns A new zipper focused on the parent, or null if at the root
     */
    up: () => <PERSON><PERSON>per<T> | null;
    /**
     * Move down in the structure (toward children)
     *
     * @param index - The index of the child to focus on
     * @returns A new zipper focused on the child, or null if no child exists
     */
    down: (index: number) => Zipper<T> | null;
    /**
     * Move left in the structure (to previous sibling)
     *
     * @returns A new zipper focused on the previous sibling, or null if leftmost
     */
    left: () => Zipper<T> | null;
    /**
     * Move right in the structure (to next sibling)
     *
     * @returns A new zipper focused on the next sibling, or null if rightmost
     */
    right: () => Zipper<T> | null;
    /**
     * Modify the focused element
     *
     * @param f - Function to transform the focused element
     * @returns A new zipper with the modified focus
     */
    modify: (f: (t: T) => T) => Zipper<T>;
    /**
     * Reconstruct the complete structure with modifications
     *
     * @returns The complete data structure with all modifications applied
     */
    commit: () => any;
}
/**
 * ZipperFunctor - A functor that maps between zippers
 *
 * This functor allows transforming zippers between different element types,
 * preserving the navigation and modification capabilities.
 *
 * @typeParam S - The element type of the source zipper
 * @typeParam T - The element type of the target zipper
 */
export declare class ZipperFunctor<S, T> implements Functor<Zipper<S>, Zipper<T>> {
    private readonly objectMap;
    private readonly contextMap;
    /**
     * Creates a new zipper functor
     *
     * @param objectMap - Function to map from source elements to target elements
     * @param contextMap - Function to map from source context to target context
     */
    constructor(objectMap: (s: S) => T, contextMap: (context: any) => any);
    /**
     * Maps a source zipper to a target zipper
     *
     * @param zipper - The source zipper to map
     * @returns A new zipper with mapped elements and context
     * @throws {FunctorError} If mapping fails
     */
    mapObject(zipper: Zipper<S>): Zipper<T>;
    /**
     * Maps a morphism between zippers
     *
     * @param morphism - The source morphism to map
     * @returns A new morphism between target zippers
     */
    mapMorphism(morphism: Morphism<Zipper<S>>): Morphism<Zipper<T>>;
    /**
     * Validates that the functor preserves zipper laws
     *
     * @returns True if the functor preserves zipper laws
     * @throws {Error} Method not implemented
     */
    validateLaws(): boolean;
    /**
     * Create a tree zipper from a tree node
     *
     * @param root - The root node of the tree
     * @param path - The path from the root to the current focus
     * @returns A zipper focused on the specified node
     * @static
     */
    static createTreeZipper<T>(root: {
        value: T;
        children?: Array<any>;
    }, path?: Array<{
        parent: any;
        lefts: Array<any>;
        rights: Array<any>;
    }>): Zipper<T>;
}
//# sourceMappingURL=zipper.fr.d.ts.map