/**
 * Main example file demonstrating the cat_types system
 */

import { demonstrateCategory } from './simple-category.ts';
import { demonstrateFunctor } from './simple-functor.ts';
import { demonstrateMonad } from './simple-monad.ts';

// Run all demonstrations
console.log('=== CAT_TYPES SYSTEM DEMONSTRATION ===');
console.log('\n=== CATEGORY EXAMPLE ===');
demonstrateCategory();

console.log('\n=== FUNCTOR EXAMPLE ===');
demonstrateFunctor();

console.log('\n=== MONAD EXAMPLE ===');
demonstrateMonad();

console.log('\n=== END OF DEMONSTRATION ===');
