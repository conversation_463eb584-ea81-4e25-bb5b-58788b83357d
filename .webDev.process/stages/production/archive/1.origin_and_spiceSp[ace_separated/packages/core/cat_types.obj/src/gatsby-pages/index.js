import React from 'react';
import { Link, graphql } from 'gatsby';

/**
 * Main documentation page for the cat_types package
 */
const CatTypesDocIndex = ({ data }) => {
  const { site, allMarkdownRemark } = data;
  
  // Group documentation by category
  const usageDocs = allMarkdownRemark.edges.filter(
    ({ node }) => node.fileAbsolutePath.includes('/docs/usage/')
  );
  
  const exampleDocs = allMarkdownRemark.edges.filter(
    ({ node }) => node.fileAbsolutePath.includes('/docs/examples/')
  );
  
  const apiDocs = allMarkdownRemark.edges.filter(
    ({ node }) => node.fileAbsolutePath.includes('/docs/api/')
  );
  
  return (
    <div>
      <h1>Cat Types Documentation</h1>
      
      <p>
        A TypeScript implementation of category theory concepts for building 
        type-safe functional abstractions.
      </p>
      
      <h2>Usage Guides</h2>
      <ul>
        {usageDocs.map(({ node }) => (
          <li key={node.id}>
            <Link to={node.fields.slug}>{node.frontmatter.title}</Link>
            <p>{node.excerpt}</p>
          </li>
        ))}
      </ul>
      
      <h2>API Reference</h2>
      <ul>
        {apiDocs.map(({ node }) => (
          <li key={node.id}>
            <Link to={node.fields.slug}>{node.frontmatter.title}</Link>
            <p>{node.excerpt}</p>
          </li>
        ))}
      </ul>
      
      <h2>Examples</h2>
      <ul>
        {exampleDocs.map(({ node }) => (
          <li key={node.id}>
            <Link to={node.fields.slug}>{node.frontmatter.title}</Link>
            <p>{node.excerpt}</p>
          </li>
        ))}
      </ul>
    </div>
  );
};

export const query = graphql`
  query {
    site {
      siteMetadata {
        title
      }
    }
    allMarkdownRemark(sort: { fields: [frontmatter___title], order: ASC }) {
      edges {
        node {
          id
          excerpt(pruneLength: 160)
          fields {
            slug
          }
          frontmatter {
            title
            description
          }
          fileAbsolutePath
        }
      }
    }
  }
`;

export default CatTypesDocIndex;
