---
title: "Error Handling Guide"
description: "Robust error handling with monads"
---

# Error Handling Guide

This guide shows how to implement robust error handling in web applications using monads from the `cat_types` package.

## The Problem with Traditional Error Handling

Traditional error handling often involves:

1. Try/catch blocks that are easy to forget
2. Null checks scattered throughout the code
3. Inconsistent error propagation
4. Difficulty handling multiple error types
5. Side effects mixed with error handling

## Solution: Monadic Error Handling

Monads provide a structured way to handle errors:

1. Explicit representation of success and failure
2. Consistent error propagation
3. Composition of error-prone operations
4. Type-safe error handling
5. Separation of error handling from business logic

## Basic Implementation

### The Maybe Monad

The Maybe monad handles potentially missing values:

```typescript
import { createCatObject, BaseMonad, BaseFunctor, ConcreteCategory } from '@future/cat_types';

// Define our Maybe type
type Maybe<T> = { tag: 'Nothing' } | { tag: 'Just', value: T };

// Helper functions
const nothing = <T>(): Maybe<T> => ({ tag: 'Nothing' });
const just = <T>(value: T): Maybe<T> => ({ tag: 'Just', value });
const isNothing = <T>(maybe: Maybe<T>): boolean => maybe.tag === 'Nothing';
const isJust = <T>(maybe: Maybe<T>): boolean => maybe.tag === 'Just';
const fromMaybe = <T>(defaultValue: T, maybe: Maybe<T>): T => 
  isJust(maybe) ? (maybe as { tag: 'Just', value: T }).value : defaultValue;

// Set up the Maybe monad
const category = new ConcreteCategory();
const nothingObj = createCatObject(nothing(), 'Nothing');
const justObj = createCatObject(just(null), 'Just');
category.addObject(nothingObj);
category.addObject(justObj);

const maybeFunctor = new BaseFunctor(
  category,
  category,
  obj => obj.value.tag === 'Nothing' ? nothingObj : justObj,
  morphism => morphism
);

const maybeMonad = new BaseMonad(
  maybeFunctor,
  category,
  obj => createCatObject(just(obj.value), 'Just'),
  obj => {
    const maybeValue = obj.value as Maybe<any>;
    if (isNothing(maybeValue)) return nothingObj;
    const innerValue = (maybeValue as { tag: 'Just', value: Maybe<any> }).value;
    if (isNothing(innerValue)) return nothingObj;
    return createCatObject(just((innerValue as { tag: 'Just', value: any }).value), 'Just');
  }
);

// Example usage
function findUser(id: string): Maybe<User> {
  const user = users.find(u => u.id === id);
  return user ? just(user) : nothing();
}

function getUserEmail(user: User): Maybe<string> {
  return user.email ? just(user.email) : nothing();
}

function findUserEmail(id: string): Maybe<string> {
  const userMaybe = findUser(id);
  if (isNothing(userMaybe)) return nothing();
  const user = (userMaybe as { tag: 'Just', value: User }).value;
  return getUserEmail(user);
}

// Usage
const emailMaybe = findUserEmail('123');
const email = fromMaybe('No email found', emailMaybe);
```

### The Either Monad

The Either monad handles errors with additional context:

```typescript
// Define our Either type
type Either<E, A> = { tag: 'Left', value: E } | { tag: 'Right', value: A };

// Helper functions
const left = <E, A>(value: E): Either<E, A> => ({ tag: 'Left', value });
const right = <E, A>(value: A): Either<E, A> => ({ tag: 'Right', value });
const isLeft = <E, A>(either: Either<E, A>): boolean => either.tag === 'Left';
const isRight = <E, A>(either: Either<E, A>): boolean => either.tag === 'Right';
const fromEither = <E, A>(defaultValue: A, either: Either<E, A>): A => 
  isRight(either) ? (either as { tag: 'Right', value: A }).value : defaultValue;

// Error types
class NotFoundError extends Error {
  constructor(public resourceType: string, public id: string) {
    super(`${resourceType} with id ${id} not found`);
    this.name = 'NotFoundError';
  }
}

class ValidationError extends Error {
  constructor(public field: string, message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class NetworkError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'NetworkError';
  }
}

// Example usage
function findUser(id: string): Either<NotFoundError, User> {
  const user = users.find(u => u.id === id);
  return user ? right(user) : left(new NotFoundError('User', id));
}

function validateEmail(email: string): Either<ValidationError, string> {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) 
    ? right(email) 
    : left(new ValidationError('email', 'Invalid email format'));
}

function updateUserEmail(id: string, email: string): Either<Error, User> {
  // Find the user
  const userResult = findUser(id);
  if (isLeft(userResult)) {
    return userResult;
  }
  
  // Validate the email
  const emailResult = validateEmail(email);
  if (isLeft(emailResult)) {
    return emailResult;
  }
  
  // Update the user
  const user = (userResult as { tag: 'Right', value: User }).value;
  const validEmail = (emailResult as { tag: 'Right', value: string }).value;
  
  try {
    const updatedUser = { ...user, email: validEmail };
    // Save to database...
    return right(updatedUser);
  } catch (error) {
    return left(new Error(`Failed to update user: ${error.message}`));
  }
}

// Usage
const result = updateUserEmail('123', '<EMAIL>');

if (isLeft(result)) {
  const error = (result as { tag: 'Left', value: Error }).value;
  console.error('Error:', error.message);
  
  if (error instanceof NotFoundError) {
    console.error(`Could not find ${error.resourceType} with id ${error.id}`);
  } else if (error instanceof ValidationError) {
    console.error(`Validation error in field ${error.field}`);
  }
} else {
  const user = (result as { tag: 'Right', value: User }).value;
  console.log('User updated:', user);
}
```

## Practical Applications

### Form Validation

```typescript
interface FormValues {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
}

function validateName(name: string): Either<ValidationError, string> {
  if (!name) {
    return left(new ValidationError('name', 'Name is required'));
  }
  if (name.length < 2) {
    return left(new ValidationError('name', 'Name must be at least 2 characters'));
  }
  return right(name);
}

function validateEmail(email: string): Either<ValidationError, string> {
  if (!email) {
    return left(new ValidationError('email', 'Email is required'));
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return left(new ValidationError('email', 'Invalid email format'));
  }
  return right(email);
}

function validatePassword(password: string): Either<ValidationError, string> {
  if (!password) {
    return left(new ValidationError('password', 'Password is required'));
  }
  if (password.length < 8) {
    return left(new ValidationError('password', 'Password must be at least 8 characters'));
  }
  return right(password);
}

function validateConfirmPassword(password: string, confirmPassword: string): Either<ValidationError, string> {
  if (!confirmPassword) {
    return left(new ValidationError('confirmPassword', 'Please confirm your password'));
  }
  if (password !== confirmPassword) {
    return left(new ValidationError('confirmPassword', 'Passwords do not match'));
  }
  return right(confirmPassword);
}

function validateForm(values: FormValues): { isValid: boolean, errors: FormErrors, validatedValues?: FormValues } {
  const nameResult = validateName(values.name);
  const emailResult = validateEmail(values.email);
  const passwordResult = validatePassword(values.password);
  const confirmPasswordResult = validateConfirmPassword(values.password, values.confirmPassword);
  
  const errors: FormErrors = {};
  let isValid = true;
  
  if (isLeft(nameResult)) {
    isValid = false;
    errors.name = (nameResult as { tag: 'Left', value: ValidationError }).value.message;
  }
  
  if (isLeft(emailResult)) {
    isValid = false;
    errors.email = (emailResult as { tag: 'Left', value: ValidationError }).value.message;
  }
  
  if (isLeft(passwordResult)) {
    isValid = false;
    errors.password = (passwordResult as { tag: 'Left', value: ValidationError }).value.message;
  }
  
  if (isLeft(confirmPasswordResult)) {
    isValid = false;
    errors.confirmPassword = (confirmPasswordResult as { tag: 'Left', value: ValidationError }).value.message;
  }
  
  if (!isValid) {
    return { isValid, errors };
  }
  
  return {
    isValid,
    errors,
    validatedValues: {
      name: (nameResult as { tag: 'Right', value: string }).value,
      email: (emailResult as { tag: 'Right', value: string }).value,
      password: (passwordResult as { tag: 'Right', value: string }).value,
      confirmPassword: (confirmPasswordResult as { tag: 'Right', value: string }).value
    }
  };
}

// Usage in a React component
function RegistrationForm() {
  const [values, setValues] = useState<FormValues>({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setValues(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const result = validateForm(values);
    
    if (!result.isValid) {
      setErrors(result.errors);
      return;
    }
    
    // Form is valid, proceed with submission
    console.log('Form submitted:', result.validatedValues);
    // Reset errors
    setErrors({});
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="name">Name</label>
        <input
          type="text"
          id="name"
          name="name"
          value={values.name}
          onChange={handleChange}
        />
        {errors.name && <div className="error">{errors.name}</div>}
      </div>
      
      <div>
        <label htmlFor="email">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          value={values.email}
          onChange={handleChange}
        />
        {errors.email && <div className="error">{errors.email}</div>}
      </div>
      
      <div>
        <label htmlFor="password">Password</label>
        <input
          type="password"
          id="password"
          name="password"
          value={values.password}
          onChange={handleChange}
        />
        {errors.password && <div className="error">{errors.password}</div>}
      </div>
      
      <div>
        <label htmlFor="confirmPassword">Confirm Password</label>
        <input
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          value={values.confirmPassword}
          onChange={handleChange}
        />
        {errors.confirmPassword && <div className="error">{errors.confirmPassword}</div>}
      </div>
      
      <button type="submit">Register</button>
    </form>
  );
}
```

### API Error Handling

```typescript
// Define API error types
class ApiError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

class AuthError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(401, message);
    this.name = 'AuthError';
  }
}

class ForbiddenError extends ApiError {
  constructor(message: string = 'Access forbidden') {
    super(403, message);
    this.name = 'ForbiddenError';
  }
}

class NotFoundError extends ApiError {
  constructor(resource: string) {
    super(404, `${resource} not found`);
    this.name = 'NotFoundError';
  }
}

class ServerError extends ApiError {
  constructor(message: string = 'Internal server error') {
    super(500, message);
    this.name = 'ServerError';
  }
}

// API client with Either for error handling
async function fetchApi<T>(url: string, options?: RequestInit): Promise<Either<ApiError, T>> {
  try {
    const response = await fetch(url, options);
    
    if (response.status === 401) {
      return left(new AuthError());
    }
    
    if (response.status === 403) {
      return left(new ForbiddenError());
    }
    
    if (response.status === 404) {
      return left(new NotFoundError(url));
    }
    
    if (response.status >= 500) {
      return left(new ServerError());
    }
    
    if (!response.ok) {
      return left(new ApiError(response.status, `API error: ${response.statusText}`));
    }
    
    const data = await response.json();
    return right(data as T);
  } catch (error) {
    return left(new ApiError(0, `Network error: ${error.message}`));
  }
}

// Usage in a React component with React Query
import { useQuery, useMutation, QueryClient } from 'react-query';

// Create a query client
const queryClient = new QueryClient();

// User API functions
async function getUser(id: string): Promise<Either<ApiError, User>> {
  return fetchApi<User>(`/api/users/${id}`);
}

async function updateUser(user: User): Promise<Either<ApiError, User>> {
  return fetchApi<User>(`/api/users/${user.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(user)
  });
}

// React component
function UserProfile({ userId }) {
  // Query for user data
  const { data, error, isLoading } = useQuery(['user', userId], () => getUser(userId), {
    retry: (failureCount, error) => {
      // Don't retry for certain errors
      if (error instanceof AuthError || error instanceof NotFoundError) {
        return false;
      }
      // Retry server errors up to 3 times
      return failureCount < 3;
    }
  });
  
  // Mutation for updating user
  const mutation = useMutation(updateUser, {
    onSuccess: (data) => {
      if (isRight(data)) {
        // Update the cache with the new user data
        queryClient.setQueryData(['user', userId], data);
        toast.success('User updated successfully');
      }
    }
  });
  
  // Handle loading state
  if (isLoading) {
    return <div>Loading...</div>;
  }
  
  // Handle error state
  if (error) {
    return <div>Error: {error.message}</div>;
  }
  
  // Handle API error
  if (!data || isLeft(data)) {
    const apiError = data ? (data as { tag: 'Left', value: ApiError }).value : new Error('No data');
    
    if (apiError instanceof AuthError) {
      return <div>Please log in to view this profile</div>;
    }
    
    if (apiError instanceof NotFoundError) {
      return <div>User not found</div>;
    }
    
    return <div>Error: {apiError.message}</div>;
  }
  
  // Extract user data
  const user = (data as { tag: 'Right', value: User }).value;
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form...
    
    // Update user
    mutation.mutate(user);
  };
  
  return (
    <div>
      <h1>User Profile</h1>
      
      {mutation.isLoading && <div>Saving...</div>}
      
      {mutation.isError && (
        <div className="error">
          Error: {mutation.error.message}
        </div>
      )}
      
      {mutation.data && isLeft(mutation.data) && (
        <div className="error">
          Error: {(mutation.data as { tag: 'Left', value: ApiError }).value.message}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {/* Form fields */}
        <button type="submit" disabled={mutation.isLoading}>
          Save
        </button>
      </form>
    </div>
  );
}
```

## Advanced Patterns

### Combining Maybe and Either

```typescript
// Define a Result type that combines Maybe and Either
type Result<T> = 
  | { tag: 'Success', value: T }
  | { tag: 'Empty' }
  | { tag: 'Error', error: Error };

// Helper functions
const success = <T>(value: T): Result<T> => ({ tag: 'Success', value });
const empty = <T>(): Result<T> => ({ tag: 'Empty' });
const error = <T>(err: Error): Result<T> => ({ tag: 'Error', error: err });

const isSuccess = <T>(result: Result<T>): boolean => result.tag === 'Success';
const isEmpty = <T>(result: Result<T>): boolean => result.tag === 'Empty';
const isError = <T>(result: Result<T>): boolean => result.tag === 'Error';

// Example usage
function findUser(id: string): Result<User> {
  if (!id) {
    return empty();
  }
  
  try {
    const user = users.find(u => u.id === id);
    return user ? success(user) : empty();
  } catch (err) {
    return error(err);
  }
}

// Usage
const result = findUser('123');

switch (result.tag) {
  case 'Success':
    console.log('User found:', result.value);
    break;
  case 'Empty':
    console.log('User not found');
    break;
  case 'Error':
    console.error('Error:', result.error.message);
    break;
}
```

### Async Either

```typescript
// Helper for async operations that might fail
async function tryAsync<T>(
  promise: Promise<T>,
  errorMapper: (error: unknown) => Error = (e) => e instanceof Error ? e : new Error(String(e))
): Promise<Either<Error, T>> {
  try {
    const result = await promise;
    return right(result);
  } catch (e) {
    return left(errorMapper(e));
  }
}

// Example usage
async function fetchUser(id: string): Promise<Either<Error, User>> {
  return tryAsync(
    fetch(`/api/users/${id}`).then(res => {
      if (!res.ok) {
        throw new ApiError(res.status, `Failed to fetch user: ${res.statusText}`);
      }
      return res.json();
    }),
    (error) => {
      if (error instanceof ApiError) {
        return error;
      }
      return new Error(`Network error: ${String(error)}`);
    }
  );
}

// Usage
const result = await fetchUser('123');

if (isLeft(result)) {
  const error = (result as { tag: 'Left', value: Error }).value;
  console.error('Error:', error.message);
} else {
  const user = (result as { tag: 'Right', value: User }).value;
  console.log('User:', user);
}
```

### Chaining Operations with Either

```typescript
// Chain multiple Either operations
function chain<E, A, B>(either: Either<E, A>, fn: (a: A) => Either<E, B>): Either<E, B> {
  if (isLeft(either)) {
    return either as Either<E, B>;
  }
  return fn((either as { tag: 'Right', value: A }).value);
}

// Example usage
async function getUserData(userId: string): Promise<Either<Error, UserData>> {
  // Get the user
  const userResult = await fetchUser(userId);
  
  // Chain operations
  return chain(userResult, user => {
    // Get the user's posts
    return chain(fetchPosts(user.id), posts => {
      // Get the user's comments
      return chain(fetchComments(user.id), comments => {
        // Combine the data
        return right({
          user,
          posts,
          comments
        });
      });
    });
  });
}

// Usage
const result = await getUserData('123');

if (isLeft(result)) {
  const error = (result as { tag: 'Left', value: Error }).value;
  console.error('Error:', error.message);
} else {
  const userData = (result as { tag: 'Right', value: UserData }).value;
  console.log('User data:', userData);
}
```

## Performance Considerations

1. **Avoid excessive wrapping**: Don't wrap every value in a monad unless it's necessary.

2. **Use appropriate monads**: Choose the right monad for the job - Maybe for optional values, Either for errors.

3. **Optimize chains**: When chaining multiple operations, try to minimize intermediate allocations.

4. **Consider alternatives**: For simple cases, traditional error handling might be more efficient.

## Testing

Monadic error handling is highly testable:

```typescript
import { validateEmail, validatePassword } from './validation';

describe('Validation', () => {
  test('validateEmail should return Right for valid emails', () => {
    const result = validateEmail('<EMAIL>');
    expect(isRight(result)).toBe(true);
    expect((result as any).value).toBe('<EMAIL>');
  });
  
  test('validateEmail should return Left for invalid emails', () => {
    const result = validateEmail('invalid-email');
    expect(isLeft(result)).toBe(true);
    expect((result as any).value).toBeInstanceOf(ValidationError);
    expect((result as any).value.field).toBe('email');
  });
  
  test('validatePassword should return Right for valid passwords', () => {
    const result = validatePassword('password123');
    expect(isRight(result)).toBe(true);
    expect((result as any).value).toBe('password123');
  });
  
  test('validatePassword should return Left for short passwords', () => {
    const result = validatePassword('pass');
    expect(isLeft(result)).toBe(true);
    expect((result as any).value).toBeInstanceOf(ValidationError);
    expect((result as any).value.field).toBe('password');
  });
});
```

## Conclusion

Using monads for error handling provides several benefits:

1. **Explicit Errors**: Errors are represented explicitly in the type system.
2. **Composition**: Error-prone operations can be composed safely.
3. **Type Safety**: The compiler helps ensure you handle all error cases.
4. **Readability**: Error handling logic is clear and consistent.
5. **Testability**: Error handling is easy to test.

By adopting this approach, you can create more robust and maintainable error handling in your web applications.
