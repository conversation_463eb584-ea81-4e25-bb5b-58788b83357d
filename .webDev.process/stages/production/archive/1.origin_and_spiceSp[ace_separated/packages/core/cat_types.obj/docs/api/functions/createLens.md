[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / createLens

# Function: createLens()

> **createLens**\<`S`, `A`\>(`getter`, `setter`): [`Lens`](../interfaces/Lens.md)\<`S`, `A`\>

Defined in: [functors/lens.fr.ts:65](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L65)

Create a lens from getter and setter functions

## Type Parameters

• **S**

• **A**

## Parameters

### getter

(`s`) => `A`

Function to extract the focus from the structure

### setter

(`s`, `a`) => `S`

Function to update the focus in the structure

## Returns

[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>

A new lens with the specified getter and setter

## Static
