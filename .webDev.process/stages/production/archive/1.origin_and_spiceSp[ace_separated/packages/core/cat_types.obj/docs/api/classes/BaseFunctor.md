[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / BaseFunctor

# Class: BaseFunctor\<S, SM, T, TM\>

Defined in: [atomic/functor.ts:61](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L61)

Base implementation of a functor

## Type Parameters

• **S** *extends* `CatObject`

Source category object type

• **SM** *extends* `Morphism`\<`S`\>

Source category morphism type

• **T** *extends* `CatObject`

Target category object type

• **TM** *extends* `Morphism`\<`T`\>

Target category morphism type

## Implements

- [`Functor`](../interfaces/Functor.md)\<`S`, `SM`, `T`, `TM`\>

## Constructors

### new BaseFunctor()

> **new BaseFunctor**\<`S`, `SM`, `T`, `TM`\>(`source`, `target`, `objectMap`, `morphismMap`): [`BaseFunctor`](BaseFunctor.md)\<`S`, `SM`, `T`, `TM`\>

Defined in: [atomic/functor.ts:94](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L94)

Creates a new functor

#### Parameters

##### source

`Category`\<`S`, `SM`\>

Source category

##### target

`Category`\<`T`, `TM`\>

Target category

##### objectMap

(`obj`) => `T`

Function mapping objects

##### morphismMap

(`morphism`) => `TM`

Function mapping morphisms

#### Returns

[`BaseFunctor`](BaseFunctor.md)\<`S`, `SM`, `T`, `TM`\>

## Properties

### source

> `readonly` **source**: `Category`\<`S`, `SM`\>

Defined in: [atomic/functor.ts:70](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L70)

Source category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`source`](../interfaces/Functor.md#source)

***

### target

> `readonly` **target**: `Category`\<`T`, `TM`\>

Defined in: [atomic/functor.ts:75](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L75)

Target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`target`](../interfaces/Functor.md#target)

## Methods

### mapMorphism()

> **mapMorphism**(`morphism`): `TM`

Defined in: [atomic/functor.ts:120](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L120)

Maps a morphism from source category to target category

#### Parameters

##### morphism

`SM`

Morphism in source category

#### Returns

`TM`

Corresponding morphism in target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapMorphism`](../interfaces/Functor.md#mapmorphism)

***

### mapObject()

> **mapObject**(`obj`): `T`

Defined in: [atomic/functor.ts:111](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L111)

Maps an object from source category to target category

#### Parameters

##### obj

`S`

Object in source category

#### Returns

`T`

Corresponding object in target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapObject`](../interfaces/Functor.md#mapobject)

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/functor.ts:128](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/functor.ts#L128)

Validates that the functor preserves composition and identity

#### Returns

`boolean`

True if functor laws are satisfied

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`validateLaws`](../interfaces/Functor.md#validatelaws)
