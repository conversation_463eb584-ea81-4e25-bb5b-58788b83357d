[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / createCatId

# Function: createCatId()

> **createCatId**(`name`?): [`CatId`](../interfaces/CatId.md)

Defined in: [atomic/catId.ts:30](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/catId.ts#L30)

Creates a new category ID

## Parameters

### name?

`string`

Optional name for debugging

## Returns

[`CatId`](../interfaces/CatId.md)

A new CatId instance
