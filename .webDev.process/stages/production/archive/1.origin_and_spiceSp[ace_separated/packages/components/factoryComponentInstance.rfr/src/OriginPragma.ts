/**
 * OriginPragma implementation
 * 
 * The factory layer of the Factory → Component → Instance pattern
 */

import {
  OriginPragmaInterface,
  ComponentConstructor,
  ComponentConfig,
  PragmaComponentInterface
} from './types';

/**
 * OriginPragma class
 * 
 * Factory for creating and managing components
 */
export class OriginPragma implements OriginPragmaInterface {
  /**
   * Version of this Origin Pragma
   */
  public readonly version: string;
  
  /**
   * Registry of component constructors
   */
  private componentRegistry: Map<string, ComponentConstructor>;
  
  /**
   * Create a new Origin Pragma
   * @param version Version string in semver format
   */
  constructor(version: string) {
    this.version = version;
    this.componentRegistry = new Map<string, ComponentConstructor>();
  }
  
  /**
   * Register a component constructor
   * @param type Component type identifier
   * @param constructor Component constructor function
   */
  public registerComponent(type: string, constructor: ComponentConstructor): void {
    if (this.componentRegistry.has(type)) {
      console.warn(`Component type '${type}' is already registered. Overwriting.`);
    }
    
    this.componentRegistry.set(type, constructor);
  }
  
  /**
   * Create a component instance
   * @param type Component type identifier
   * @param config Configuration for the component
   * @returns Created component or null if type not found
   */
  public createComponent(type: string, config: ComponentConfig): PragmaComponentInterface | null {
    const Constructor = this.componentRegistry.get(type);
    if (!Constructor) {
      console.warn(`Component type '${type}' is not registered.`);
      return null;
    }
    
    try {
      return new Constructor(config, this);
    } catch (error) {
      console.error(`Error creating component of type '${type}':`, error);
      return null;
    }
  }
  
  /**
   * Check if this Origin is compatible with another version
   * @param otherVersion Version to check compatibility with
   * @returns Whether the versions are compatible
   */
  public isCompatibleWith(otherVersion: string): boolean {
    const [major, minor] = this.version.split('.');
    const [otherMajor, otherMinor] = otherVersion.split('.');
    
    // Major versions must match
    if (major !== otherMajor) return false;
    
    // This minor version must be greater than or equal to the other
    return parseInt(minor) >= parseInt(otherMinor);
  }
  
  /**
   * Get all registered component types
   * @returns Array of registered component types
   */
  public getRegisteredTypes(): string[] {
    return Array.from(this.componentRegistry.keys());
  }
  
  /**
   * Check if a component type is registered
   * @param type Component type to check
   * @returns Whether the type is registered
   */
  public hasComponentType(type: string): boolean {
    return this.componentRegistry.has(type);
  }
  
  /**
   * Deserialize a component
   * @param serialized Serialized component
   * @returns Deserialized component or null if type not found
   */
  public deserialize(serialized: { type: string; config: ComponentConfig }): PragmaComponentInterface | null {
    return this.createComponent(serialized.type, serialized.config);
  }
}

export default OriginPragma;
