{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "jsx": "react", "lib": ["ES2020", "DOM"], "types": ["node", "jest", "react", "react-dom"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.test.tsx"]}