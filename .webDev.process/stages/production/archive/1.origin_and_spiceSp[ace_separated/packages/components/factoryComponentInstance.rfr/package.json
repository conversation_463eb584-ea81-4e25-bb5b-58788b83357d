{"name": "@spicetime/components/factoryComponentInstance", "version": "0.1.0", "description": "Factory → Component → Instance Pattern for SpiceTime", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint src/**/*.{ts,tsx}", "clean": "<PERSON><PERSON><PERSON> dist", "prepublish": "npm run clean && npm run build"}, "keywords": ["spicetime", "factory", "component", "instance", "pattern"], "author": "SpiceTime Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "eslint": "^8.36.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.5.0", "rimraf": "^4.4.0", "ts-jest": "^29.1.0", "typescript": "^5.0.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov"]}}