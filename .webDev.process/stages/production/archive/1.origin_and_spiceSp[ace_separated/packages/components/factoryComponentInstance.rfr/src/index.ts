/**
 * Factory → Component → Instance Pattern
 * 
 * Main entry point for the pattern
 */

// Export core classes
export { OriginPragma } from './OriginPragma';
export { PragmaComponent } from './PragmaComponent';

// Export component implementations
export { DirectoryPragma } from './DirectoryPragma';
export { FilePragma } from './FilePragma';
export { LinkPragma } from './LinkPragma';

// Export instance components
export { DirectoryInstance } from './instances/DirectoryInstance';
export { FileInstance } from './instances/FileInstance';
export { LinkInstance } from './instances/LinkInstance';

// Export types
export * from './types';

// Default export
import OriginPragma from './OriginPragma';
import DirectoryPragma from './DirectoryPragma';
import FilePragma from './FilePragma';
import LinkPragma from './LinkPragma';

export default {
  OriginPragma,
  DirectoryPragma,
  FilePragma,
  LinkPragma
};
