# Factory → Component → Instance Pattern

A concrete implementation of the Factory → Component → Instance pattern for the SpiceTime architecture. This pattern provides a powerful framework for creating dynamic, version-compatible, and extensible systems while maintaining a clear separation of concerns.

## Installation

```bash
npm install @spicetime/components/factoryComponentInstance
```

## Usage

### Creating an Origin Pragma (Factory)

```typescript
import { OriginPragma, DirectoryPragma, FilePragma, LinkPragma } from '@spicetime/components/factoryComponentInstance';

// Create the Origin
const fsBuilderOrigin = new OriginPragma('1.0.0');

// Register component types
fsBuilderOrigin.registerComponent('directory', DirectoryPragma);
fsBuilderOrigin.registerComponent('file', FilePragma);
fsBuilderOrigin.registerComponent('link', LinkPragma);
```

### Creating Components

```typescript
// Create components
const projectDir = fsBuilderOrigin.createComponent('directory', {
  name: 'project',
  permissions: '755'
});

const sourceFile = fsBuilderOrigin.createComponent('file', {
  name: 'index.ts',
  content: 'export default {}',
  syntax: 'typescript'
});

const configLink = fsBuilderOrigin.createComponent('link', {
  name: 'config.json',
  target: '/etc/config.json',
  type: 'symbolic'
});
```

### Creating Instances

```tsx
import React from 'react';

function FilesystemEditor() {
  return (
    <div className="fs-editor">
      {projectDir.createInstance({
        expanded: true,
        onRename: (newName) => { /* Implementation */ },
        onCreateChild: (type) => { /* Implementation */ }
      })}
      
      {sourceFile.createInstance({
        editable: true,
        highlightSyntax: true,
        onChange: (content) => { /* Implementation */ }
      })}
      
      {configLink.createInstance({
        onRetarget: (newTarget) => { /* Implementation */ }
      })}
    </div>
  );
}
```

## Core Concepts

### Origin Pragma (Factory)

The Origin Pragma serves as a factory that creates and configures components. It has these specific responsibilities:

- Maintaining a registry of component constructors
- Creating component instances with specific configurations
- Performing version compatibility checks
- Providing global context to all components it creates

### Pragma Component (Component)

The Pragma Component represents a specific type of component created by the Origin Pragma. Each component:

- Implements specific business logic
- Manages its own internal state and data structures
- Creates JSX instances with specific props and behaviors
- Maintains a connection to its origin

### Component Instance (Instance)

The Component Instance is the actual React component rendered in the application. Each instance:

- Receives specific props from its component
- Uses React hooks for state management and side effects
- Renders UI elements based on its current state and props
- Interacts with the user and other instances

## Built-in Components

### DirectoryPragma

A component for representing directories in the filesystem.

```typescript
interface DirectoryConfig {
  name: string;
  permissions?: string;
  metadata?: Record<string, any>;
}
```

### FilePragma

A component for representing files in the filesystem.

```typescript
interface FileConfig {
  name: string;
  content?: string;
  syntax?: string;
  permissions?: string;
  metadata?: Record<string, any>;
}
```

### LinkPragma

A component for representing links in the filesystem.

```typescript
interface LinkConfig {
  name: string;
  target: string;
  type: 'symbolic' | 'hard';
  metadata?: Record<string, any>;
}
```

## Serialization and Deserialization

The pattern supports serialization and deserialization of components:

```typescript
// Serialize a component
const serialized = projectDir.serialize();

// Deserialize a component
const deserialized = fsBuilderOrigin.deserialize(serialized);
```

## Version Compatibility

The pattern includes explicit version compatibility checks:

```typescript
// Check if two origins are compatible
const isCompatible = origin1.isCompatibleWith(origin2.version);

// Check if two components are compatible
const areCompatible = component1.isCompatibleWith(component2);
```

## License

MIT
