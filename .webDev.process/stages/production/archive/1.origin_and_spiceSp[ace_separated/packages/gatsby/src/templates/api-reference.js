import React from 'react';
import { graphql } from 'gatsby';
import Layout from '../components/layout';

/**
 * Template for rendering API reference pages
 */
const ApiReferenceTemplate = ({ data }) => {
  const { markdownRemark } = data;
  const { frontmatter, html, fields } = markdownRemark;
  
  return (
    <Layout pageTitle={frontmatter.title}>
      <div className="api-meta">
        <p className="package-info">
          Package: <a href={`/packages/${fields.packageName}`}>{fields.packageName}</a>
        </p>
        {frontmatter.description && (
          <p className="description">{frontmatter.description}</p>
        )}
      </div>
      <div className="api-content" dangerouslySetInnerHTML={{ __html: html }} />
    </Layout>
  );
};

export const pageQuery = graphql`
  query($id: String!) {
    markdownRemark(id: { eq: $id }) {
      html
      frontmatter {
        title
        description
      }
      fields {
        packageName
        packagePath
      }
    }
  }
`;

export default ApiReferenceTemplate;
