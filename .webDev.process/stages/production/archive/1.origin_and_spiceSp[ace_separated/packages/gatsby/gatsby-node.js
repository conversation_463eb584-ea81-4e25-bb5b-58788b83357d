/**
 * Implement Gatsby's Node APIs in this file.
 * See: https://www.gatsbyjs.com/docs/reference/config-files/gatsby-node/
 */

const path = require('path');
const fs = require('fs');
const { createFilePath } = require('gatsby-source-filesystem');

// Create pages from markdown files
exports.createPages = async ({ graphql, actions, reporter }) => {
  const { createPage } = actions;

  // Define templates for different types of pages
  const markdownPageTemplate = path.resolve('./src/templates/markdown-page.js');
  const apiReferenceTemplate = path.resolve('./src/templates/api-reference.js');
  const packageIndexTemplate = path.resolve('./src/templates/package-index.js');

  // Get all markdown files
  const result = await graphql(`
    {
      allMarkdownRemark {
        nodes {
          id
          fields {
            slug
            packageName
            packagePath
            docType
          }
          frontmatter {
            title
            description
          }
        }
      }
    }
  `);

  if (result.errors) {
    reporter.panicOnBuild('Error loading markdown files', result.errors);
    return;
  }

  const posts = result.data.allMarkdownRemark.nodes;

  // Create pages for each markdown file
  if (posts.length > 0) {
    posts.forEach((post) => {
      const template = post.fields.docType === 'api' 
        ? apiReferenceTemplate 
        : markdownPageTemplate;
      
      createPage({
        path: post.fields.slug,
        component: template,
        context: {
          id: post.id,
          packageName: post.fields.packageName,
          packagePath: post.fields.packagePath,
        },
      });
    });
  }

  // Create package index pages
  const packages = new Set();
  posts.forEach((post) => {
    if (post.fields.packageName) {
      packages.add(post.fields.packageName);
    }
  });

  packages.forEach((packageName) => {
    createPage({
      path: `/packages/${packageName}`,
      component: packageIndexTemplate,
      context: {
        packageName,
      },
    });
  });
};

// Add custom fields to markdown nodes
exports.onCreateNode = ({ node, actions, getNode }) => {
  const { createNodeField } = actions;

  if (node.internal.type === 'MarkdownRemark') {
    // Create slug field
    const slug = createFilePath({ node, getNode });
    createNodeField({
      name: 'slug',
      node,
      value: slug,
    });

    // Extract package information from file path
    const filePath = node.fileAbsolutePath;
    if (filePath.includes('/packages/')) {
      const packagePath = filePath.split('/packages/')[1].split('/docs/')[0];
      const packageName = packagePath.split('/').pop();
      
      createNodeField({
        name: 'packagePath',
        node,
        value: packagePath,
      });
      
      createNodeField({
        name: 'packageName',
        node,
        value: packageName,
      });
      
      // Determine doc type (usage, api, examples)
      let docType = 'usage';
      if (filePath.includes('/docs/api/')) {
        docType = 'api';
      } else if (filePath.includes('/docs/examples/')) {
        docType = 'examples';
      }
      
      createNodeField({
        name: 'docType',
        node,
        value: docType,
      });
    }
  }
};

// Process gatsby-config.js files from packages
exports.onPreInit = ({ reporter }) => {
  reporter.info('Processing package documentation configurations');
  
  // Find all gatsby-config.js files in packages
  const packagesDir = path.resolve(__dirname, '../../packages');
  const packageConfigs = [];
  
  function findGatsbyConfigs(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        findGatsbyConfigs(filePath);
      } else if (file === 'gatsby-config.js') {
        packageConfigs.push(filePath);
      }
    });
  }
  
  try {
    findGatsbyConfigs(packagesDir);
    reporter.info(`Found ${packageConfigs.length} package documentation configurations`);
  } catch (error) {
    reporter.error('Error finding package documentation configurations', error);
  }
};
