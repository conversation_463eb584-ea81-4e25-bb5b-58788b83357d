[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / prop

# Function: prop()

> **prop**\<`S`, `K`\>(`prop`): [`Lens`](../interfaces/Lens.md)\<`S`, `S`\[`K`\]\>

Defined in: [functors/lens.fr.ts:81](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L81)

Create a lens that focuses on a property of an object

## Type Parameters

• **S** *extends* `Record`\<`string`, `any`\>

• **K** *extends* `string` \| `number` \| `symbol`

## Parameters

### prop

`K`

The property to focus on

## Returns

[`Lens`](../interfaces/Lens.md)\<`S`, `S`\[`K`\]\>

A lens that focuses on the specified property
