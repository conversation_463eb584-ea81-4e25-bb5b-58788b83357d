[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / MonadError

# Class: MonadError

Defined in: [atomic/errors/monad.ts:12](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/monad.ts#L12)

Represents an error that occurs in a monad

## Extends

- `Error`

## Constructors

### new MonadError()

> **new MonadError**(`message`): [`MonadError`](MonadError.md)

Defined in: [atomic/errors/monad.ts:17](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/monad.ts#L17)

Creates a new monad error

#### Parameters

##### message

`string`

Error message

#### Returns

[`MonadError`](MonadError.md)

#### Overrides

`Error.constructor`
