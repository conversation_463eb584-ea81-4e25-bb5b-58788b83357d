[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / LensFunctor

# Class: LensFunctor\<S, T, A, B\>

Defined in: [functors/lens.fr.ts:132](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L132)

LensFunctor - A functor that maps between lenses

This functor allows transforming lenses between different source and target types,
preserving the lens laws and operations.

## Type Parameters

• **S**

The source structure type of the source lens

• **T**

The source structure type of the target lens

• **A**

The focus type of the source lens

• **B**

The focus type of the target lens

## Implements

- [`Functor`](../interfaces/Functor.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>\>, [`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>\>\>

## Constructors

### new LensFunctor()

> **new LensFunctor**\<`S`, `T`, `A`, `B`\>(`sourceCategory`, `targetCategory`, `sToT`, `tToS`, `aToB`, `bToA`): [`LensFunctor`](LensFunctor.md)\<`S`, `T`, `A`, `B`\>

Defined in: [functors/lens.fr.ts:172](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L172)

Creates a new lens functor

#### Parameters

##### sourceCategory

[`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>\>\>

Source category of lenses

##### targetCategory

[`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>\>\>

Target category of lenses

##### sToT

(`s`) => `T`

Function that maps from S to T

##### tToS

(`t`) => `S`

Function that maps from T to S

##### aToB

(`a`) => `B`

Function that maps from A to B

##### bToA

(`b`) => `A`

Function that maps from B to A

#### Returns

[`LensFunctor`](LensFunctor.md)\<`S`, `T`, `A`, `B`\>

## Properties

### source

> `readonly` **source**: [`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>\>\>

Defined in: [functors/lens.fr.ts:136](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L136)

Source category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`source`](../interfaces/Functor.md#source)

***

### target

> `readonly` **target**: [`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>\>\>

Defined in: [functors/lens.fr.ts:141](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L141)

Target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`target`](../interfaces/Functor.md#target)

## Methods

### mapMorphism()

> **mapMorphism**(`morphism`): [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>\>

Defined in: [functors/lens.fr.ts:211](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L211)

Maps a morphism from the source category to the target category

#### Parameters

##### morphism

[`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>\>

Morphism in the source category

#### Returns

[`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>\>

Corresponding morphism in the target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapMorphism`](../interfaces/Functor.md#mapmorphism)

***

### mapObject()

> **mapObject**(`obj`): [`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>

Defined in: [functors/lens.fr.ts:193](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L193)

Maps a lens object from the source category to the target category

#### Parameters

##### obj

[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>\>

Lens object in the source category

#### Returns

[`CatObject`](../interfaces/CatObject.md)\<[`Lens`](../interfaces/Lens.md)\<`T`, `B`\>\>

Corresponding lens object in the target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapObject`](../interfaces/Functor.md#mapobject)

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [functors/lens.fr.ts:235](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L235)

Validates that the functor preserves lens laws

#### Returns

`boolean`

True if functor laws are satisfied

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`validateLaws`](../interfaces/Functor.md#validatelaws)
