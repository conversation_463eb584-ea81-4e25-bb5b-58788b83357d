[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / ZipperFunctor

# Class: ZipperFunctor\<S, T\>

Defined in: [functors/zipper.fr.ts:281](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L281)

ZipperFunctor - A functor that maps between zippers

This functor allows transforming zippers between different element types,
preserving the zipper operations and structure.

## Type Parameters

• **S**

The source element type

• **T**

The target element type

## Implements

- [`Functor`](../interfaces/Functor.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>\>, [`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>\>\>

## Constructors

### new ZipperFunctor()

> **new ZipperFunctor**\<`S`, `T`\>(`sourceCategory`, `targetCategory`, `sToT`, `tToS`): [`ZipperFunctor`](ZipperFunctor.md)\<`S`, `T`\>

Defined in: [functors/zipper.fr.ts:309](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L309)

Creates a new zipper functor

#### Parameters

##### sourceCategory

[`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>\>\>

Source category of zippers

##### targetCategory

[`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>\>\>

Target category of zippers

##### sToT

(`s`) => `T`

Function that maps from S to T

##### tToS

(`t`) => `S`

Function that maps from T to S

#### Returns

[`ZipperFunctor`](ZipperFunctor.md)\<`S`, `T`\>

## Properties

### source

> `readonly` **source**: [`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>\>\>

Defined in: [functors/zipper.fr.ts:285](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L285)

Source category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`source`](../interfaces/Functor.md#source)

***

### target

> `readonly` **target**: [`Category`](../interfaces/Category.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>, [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>\>\>

Defined in: [functors/zipper.fr.ts:290](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L290)

Target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`target`](../interfaces/Functor.md#target)

## Methods

### mapMorphism()

> **mapMorphism**(`morphism`): [`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>\>

Defined in: [functors/zipper.fr.ts:410](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L410)

Maps a morphism from the source category to the target category

#### Parameters

##### morphism

[`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>\>

Morphism in the source category

#### Returns

[`BaseMorphism`](BaseMorphism.md)\<[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>\>

Corresponding morphism in the target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapMorphism`](../interfaces/Functor.md#mapmorphism)

***

### mapObject()

> **mapObject**(`obj`): [`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>

Defined in: [functors/zipper.fr.ts:326](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L326)

Maps a zipper object from the source category to the target category

#### Parameters

##### obj

[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`S`\>\>

Zipper object in the source category

#### Returns

[`CatObject`](../interfaces/CatObject.md)\<[`Zipper`](../interfaces/Zipper.md)\<`T`\>\>

Corresponding zipper object in the target category

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`mapObject`](../interfaces/Functor.md#mapobject)

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [functors/zipper.fr.ts:474](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L474)

Validates that the functor preserves zipper laws

#### Returns

`boolean`

True if functor laws are satisfied

#### Implementation of

[`Functor`](../interfaces/Functor.md).[`validateLaws`](../interfaces/Functor.md#validatelaws)
