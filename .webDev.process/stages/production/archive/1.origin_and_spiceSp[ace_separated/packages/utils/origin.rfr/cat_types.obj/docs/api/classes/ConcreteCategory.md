[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / ConcreteCategory

# Class: ConcreteCategory\<O, M\>

Defined in: [atomic/cat.ts:71](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L71)

Concrete implementation of a category

## Type Parameters

• **O** *extends* [`CatObject`](../interfaces/CatObject.md) = [`CatObject`](../interfaces/CatObject.md)

Type of objects in the category

• **M** *extends* [`Morphism`](../interfaces/Morphism.md)\<`O`\> = [`Morphism`](../interfaces/Morphism.md)\<`O`\>

Type of morphisms in the category

## Implements

- [`Category`](../interfaces/Category.md)\<`O`, `M`\>

## Constructors

### new ConcreteCategory()

> **new ConcreteCategory**\<`O`, `M`\>(): [`ConcreteCategory`](ConcreteCategory.md)\<`O`, `M`\>

Defined in: [atomic/cat.ts:85](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L85)

Creates a new category

#### Returns

[`ConcreteCategory`](ConcreteCategory.md)\<`O`, `M`\>

## Methods

### addMorphism()

> **addMorphism**(`morphism`): `void`

Defined in: [atomic/cat.ts:142](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L142)

Adds a morphism to the category

#### Parameters

##### morphism

`M`

Morphism to add

#### Returns

`void`

#### Implementation of

[`Category`](../interfaces/Category.md).[`addMorphism`](../interfaces/Category.md#addmorphism)

***

### addObject()

> **addObject**(`obj`): `void`

Defined in: [atomic/cat.ts:134](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L134)

Adds an object to the category

#### Parameters

##### obj

`O`

Object to add

#### Returns

`void`

#### Implementation of

[`Category`](../interfaces/Category.md).[`addObject`](../interfaces/Category.md#addobject)

***

### compose()

> **compose**(`f`, `g`): `M`

Defined in: [atomic/cat.ts:109](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L109)

Composes two morphisms in the category

#### Parameters

##### f

`M`

First morphism

##### g

`M`

Second morphism

#### Returns

`M`

The composed morphism

#### Implementation of

[`Category`](../interfaces/Category.md).[`compose`](../interfaces/Category.md#compose)

***

### getMorphisms()

> **getMorphisms**(): `Map`\<`string`, `M`\>

Defined in: [atomic/cat.ts:159](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L159)

Gets all morphisms in the category

#### Returns

`Map`\<`string`, `M`\>

Map of all morphisms

#### Implementation of

[`Category`](../interfaces/Category.md).[`getMorphisms`](../interfaces/Category.md#getmorphisms)

***

### getObjects()

> **getObjects**(): `Set`\<`O`\>

Defined in: [atomic/cat.ts:151](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L151)

Gets all objects in the category

#### Returns

`Set`\<`O`\>

Set of all objects

#### Implementation of

[`Category`](../interfaces/Category.md).[`getObjects`](../interfaces/Category.md#getobjects)

***

### id()

> **id**(`obj`): `M`

Defined in: [atomic/cat.ts:95](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L95)

Returns the identity morphism for a given object

#### Parameters

##### obj

`O`

The object to get identity for

#### Returns

`M`

The identity morphism

#### Implementation of

[`Category`](../interfaces/Category.md).[`id`](../interfaces/Category.md#id)

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/cat.ts:125](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/cat.ts#L125)

Checks if the category laws hold for this instance

#### Returns

`boolean`

True if category laws are satisfied

#### Implementation of

[`Category`](../interfaces/Category.md).[`validateLaws`](../interfaces/Category.md#validatelaws)
