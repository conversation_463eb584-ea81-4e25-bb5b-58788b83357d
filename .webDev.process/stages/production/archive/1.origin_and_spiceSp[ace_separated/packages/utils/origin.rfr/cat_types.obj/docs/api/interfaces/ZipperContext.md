[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / ZipperContext

# Interface: ZipperContext\<T\>

Defined in: [functors/zipper.fr.ts:82](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L82)

Context for a zipper, representing the path to the focused element

## Type Parameters

• **T**

The type of elements in the structure

## Properties

### left

> **left**: `T`[]

Defined in: [functors/zipper.fr.ts:91](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L91)

Elements to the left of the focus

***

### parent

> **parent**: `null` \| [`ZipperContext`](ZipperContext.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:86](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L86)

The parent context, or null if at the top

***

### right

> **right**: `T`[]

Defined in: [functors/zipper.fr.ts:96](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L96)

Elements to the right of the focus
