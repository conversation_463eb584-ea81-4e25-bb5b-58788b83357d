[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / FunctorError

# Class: FunctorError

Defined in: [atomic/errors/functor.ts:12](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/functor.ts#L12)

Represents an error that occurs in a functor

## Extends

- `Error`

## Constructors

### new FunctorError()

> **new FunctorError**(`message`): [`FunctorError`](FunctorError.md)

Defined in: [atomic/errors/functor.ts:17](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/functor.ts#L17)

Creates a new functor error

#### Parameters

##### message

`string`

Error message

#### Returns

[`FunctorError`](FunctorError.md)

#### Overrides

`Error.constructor`
