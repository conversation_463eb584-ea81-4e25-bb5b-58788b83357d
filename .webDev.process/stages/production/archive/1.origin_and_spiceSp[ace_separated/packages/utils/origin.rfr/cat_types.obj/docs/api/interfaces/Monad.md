[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / Monad

# Interface: Monad\<C, M\>

Defined in: [atomic/monad.ts:14](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L14)

Represents a monad in category theory

## Type Parameters

• **C** *extends* `CatObject`

Category object type

• **M** *extends* `Morphism`\<`C`\>

Category morphism type

## Properties

### category

> `readonly` **category**: `Category`\<`C`, `M`\>

Defined in: [atomic/monad.ts:23](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L23)

The category on which this monad operates

***

### functor

> `readonly` **functor**: `Functor`\<`C`, `M`, `C`, `M`\>

Defined in: [atomic/monad.ts:18](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L18)

The underlying endofunctor

## Methods

### bind()

> **bind**(`obj`, `f`): `C`

Defined in: [atomic/monad.ts:45](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L45)

Bind (flatMap) operation

#### Parameters

##### obj

`C`

Monadic object

##### f

(`x`) => `C`

Function that returns a monadic object

#### Returns

`C`

Result of applying f to the value inside obj, then flattening

***

### join()

> **join**(`obj`): `C`

Defined in: [atomic/monad.ts:37](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L37)

Join (flatten) natural transformation

#### Parameters

##### obj

`C`

Nested monadic object

#### Returns

`C`

Flattened monadic object

***

### unit()

> **unit**(`obj`): `C`

Defined in: [atomic/monad.ts:30](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L30)

Unit (return) natural transformation

#### Parameters

##### obj

`C`

Object to lift into the monad

#### Returns

`C`

Monadic object

***

### validateLaws()

> **validateLaws**(): `boolean`

Defined in: [atomic/monad.ts:51](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/monad.ts#L51)

Validates that the monad laws are satisfied

#### Returns

`boolean`

True if monad laws are satisfied
