/**
 * CLI Integration for StProcess
 * 
 * This module provides command-line interface integration for the StProcess module,
 * allowing processes to be managed via CLI commands.
 */

import { StProcess, ProcessState, ProcessSignal, ResourceLimits } from './stProcess';

/**
 * CLI command options for process management
 */
export interface ProcessCliOptions {
  /** Process ID to target (defaults to current process) */
  id?: string;
  /** Whether to format output as JSON */
  json?: boolean;
  /** Whether to include detailed information */
  verbose?: boolean;
  /** Custom output format template */
  format?: string;
  /** Maximum wait time for operations (ms) */
  timeout?: number;
}

/**
 * Process CLI command handler
 */
export class ProcessCliHandler {
  /**
   * Create a new ProcessCliHandler
   */
  constructor() {}

  /**
   * List all processes
   */
  async list(options: ProcessCliOptions = {}): Promise<void> {
    const processes = StProcess.list();
    
    if (options.json) {
      console.log(JSON.stringify(processes.map(p => this.formatProcessInfo(p, options.verbose))));
      return;
    }
    
    if (processes.length === 0) {
      console.log('No processes found');
      return;
    }
    
    console.log('Processes:');
    for (const proc of processes) {
      const info = this.formatProcessInfo(proc, options.verbose);
      console.log(`- ${info.id} (${info.state}): ${info.uptime}ms`);
      
      if (options.verbose) {
        console.log(`  Parent: ${info.parentId || 'none'}`);
        console.log(`  Memory: ${this.formatBytes(info.memory.heapUsed)} / ${this.formatBytes(info.memory.heapTotal)}`);
        console.log(`  CPU: ${info.cpu.percent.toFixed(1)}%`);
      }
    }
  }

  /**
   * Show information about a specific process
   */
  async info(options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    const info = this.formatProcessInfo(proc, true);
    
    if (options.json) {
      console.log(JSON.stringify(info));
      return;
    }
    
    console.log(`Process ${info.id}:`);
    console.log(`- State: ${info.state}`);
    console.log(`- Parent: ${info.parentId || 'none'}`);
    console.log(`- Started: ${new Date(info.startTime).toISOString()}`);
    console.log(`- Uptime: ${info.uptime}ms`);
    console.log(`- Memory:`);
    console.log(`  - RSS: ${this.formatBytes(info.memory.rss)}`);
    console.log(`  - Heap: ${this.formatBytes(info.memory.heapUsed)} / ${this.formatBytes(info.memory.heapTotal)}`);
    console.log(`  - External: ${this.formatBytes(info.memory.external)}`);
    console.log(`- CPU:`);
    console.log(`  - User: ${info.cpu.user}ms`);
    console.log(`  - System: ${info.cpu.system}ms`);
    console.log(`  - Percent: ${info.cpu.percent.toFixed(1)}%`);
    
    // Show environment variables if verbose
    if (options.verbose) {
      console.log(`- Environment:`);
      const envVars = Object.entries(proc.env);
      for (const [key, value] of envVars) {
        console.log(`  - ${key}: ${value}`);
      }
    }
  }

  /**
   * Start a process
   */
  async start(options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    
    if (proc.state !== ProcessState.CREATED && proc.state !== ProcessState.STOPPED) {
      console.error(`Process ${proc.id} is already running (state: ${proc.state})`);
      return;
    }
    
    try {
      await proc.start();
      console.log(`Process ${proc.id} started`);
    } catch (error) {
      console.error(`Failed to start process ${proc.id}:`, error);
    }
  }

  /**
   * Stop a process
   */
  async stop(options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    
    if (proc.state !== ProcessState.RUNNING) {
      console.error(`Process ${proc.id} is not running (state: ${proc.state})`);
      return;
    }
    
    try {
      await proc.stop({ timeout: options.timeout });
      console.log(`Process ${proc.id} stopped`);
    } catch (error) {
      console.error(`Failed to stop process ${proc.id}:`, error);
    }
  }

  /**
   * Restart a process
   */
  async restart(options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    
    try {
      await proc.restart({ timeout: options.timeout });
      console.log(`Process ${proc.id} restarted`);
    } catch (error) {
      console.error(`Failed to restart process ${proc.id}:`, error);
    }
  }

  /**
   * Send a signal to a process
   */
  async signal(signal: ProcessSignal, options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    
    try {
      proc.sendSignal(signal);
      console.log(`Signal ${signal} sent to process ${proc.id}`);
    } catch (error) {
      console.error(`Failed to send signal ${signal} to process ${proc.id}:`, error);
    }
  }

  /**
   * Set resource limits for a process
   */
  async setLimits(limits: ResourceLimits, options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    
    try {
      proc.setResourceLimits(limits);
      console.log(`Resource limits set for process ${proc.id}`);
    } catch (error) {
      console.error(`Failed to set resource limits for process ${proc.id}:`, error);
    }
  }

  /**
   * Monitor a process in real-time
   */
  async monitor(options: ProcessCliOptions = {}): Promise<void> {
    const proc = this.getTargetProcess(options.id);
    let running = true;
    
    // Handle Ctrl+C to stop monitoring
    process.on('SIGINT', () => {
      running = false;
      console.log('\nStopped monitoring');
    });
    
    console.log(`Monitoring process ${proc.id} (Ctrl+C to stop):`);
    
    while (running) {
      const usage = proc.getResourceUsage();
      
      // Clear screen
      process.stdout.write('\x1Bc');
      
      console.log(`Process ${proc.id} (${proc.state}):`);
      console.log(`- Uptime: ${usage.time.uptime}ms`);
      console.log(`- Memory: ${this.formatBytes(usage.memory.heapUsed)} / ${this.formatBytes(usage.memory.heapTotal)}`);
      console.log(`- CPU: ${usage.cpu.percent.toFixed(1)}%`);
      
      // Wait for next update
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * Parse CLI arguments and execute the appropriate command
   */
  async parseAndExecute(args: string[]): Promise<void> {
    if (args.length === 0) {
      this.printHelp();
      return;
    }
    
    const command = args[0];
    const options: ProcessCliOptions = {};
    
    // Parse options
    for (let i = 1; i < args.length; i++) {
      const arg = args[i];
      
      if (arg === '--json') {
        options.json = true;
      } else if (arg === '--verbose' || arg === '-v') {
        options.verbose = true;
      } else if (arg === '--id' || arg === '-i') {
        options.id = args[++i];
      } else if (arg === '--timeout' || arg === '-t') {
        options.timeout = parseInt(args[++i], 10);
      } else if (arg === '--format' || arg === '-f') {
        options.format = args[++i];
      }
    }
    
    // Execute command
    switch (command) {
      case 'list':
        await this.list(options);
        break;
      case 'info':
        await this.info(options);
        break;
      case 'start':
        await this.start(options);
        break;
      case 'stop':
        await this.stop(options);
        break;
      case 'restart':
        await this.restart(options);
        break;
      case 'signal':
        const signal = args[1] as ProcessSignal;
        await this.signal(signal, options);
        break;
      case 'monitor':
        await this.monitor(options);
        break;
      case 'help':
        this.printHelp();
        break;
      default:
        console.error(`Unknown command: ${command}`);
        this.printHelp();
        break;
    }
  }

  /**
   * Print help information
   */
  printHelp(): void {
    console.log('StProcess CLI Usage:');
    console.log('  process list [options]           List all processes');
    console.log('  process info [options]           Show information about a process');
    console.log('  process start [options]          Start a process');
    console.log('  process stop [options]           Stop a process');
    console.log('  process restart [options]        Restart a process');
    console.log('  process signal <signal> [options] Send a signal to a process');
    console.log('  process monitor [options]        Monitor a process in real-time');
    console.log('  process help                     Show this help information');
    console.log('');
    console.log('Options:');
    console.log('  --id, -i <id>                   Process ID (defaults to current process)');
    console.log('  --json                          Format output as JSON');
    console.log('  --verbose, -v                   Include detailed information');
    console.log('  --timeout, -t <ms>              Maximum wait time for operations (ms)');
    console.log('  --format, -f <format>           Custom output format template');
  }

  // Helper methods

  /**
   * Get the target process based on ID or default to current
   */
  private getTargetProcess(id?: string): StProcess {
    if (id) {
      const proc = StProcess.getById(id);
      if (!proc) {
        throw new Error(`Process with ID ${id} not found`);
      }
      return proc;
    }
    
    return StProcess.current();
  }

  /**
   * Format process information for display
   */
  private formatProcessInfo(proc: StProcess, verbose: boolean = false): any {
    const usage = proc.getResourceUsage();
    
    const info: any = {
      id: proc.id,
      state: proc.state,
      parentId: proc.parentId,
      startTime: proc.startTime,
      uptime: usage.time.uptime
    };
    
    if (verbose) {
      info.memory = usage.memory;
      info.cpu = usage.cpu;
    }
    
    return info;
  }

  /**
   * Format bytes to human-readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
  }
}

/**
 * Main CLI entry point
 */
export async function main(args: string[] = process.argv.slice(2)): Promise<void> {
  const handler = new ProcessCliHandler();
  await handler.parseAndExecute(args);
}

// Execute if this file is run directly
if (require.main === module) {
  main().catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
}
