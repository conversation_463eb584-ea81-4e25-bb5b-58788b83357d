import { describe, it, expect, vi, beforeEach } from 'vitest';
import { STError } from './STError';
import { resolveLocation } from './location';

// Mock resolveLocation utility
vi.mock('./location', () => ({
  resolveLocation: vi.fn().mockResolvedValue({
    packagePath: '/test/package/path',
    staRootPath: '/test/sta/root'
  })
}));

// Custom error class for testing inheritance
class CustomTestError extends STError {
  constructor(message: string, extInfo?: unknown) {
    super(message, extInfo);
    Object.defineProperty(this, 'name', { value: 'CustomTestError' });
  }
}

describe('STError', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('creates error with basic message', () => {
      const error = new STError('Test error');

      expect(error.message).toBe('Test error');
      expect(error.name).toBe('STError');
      expect(error.info.errorType).toBe('STError');
      expect(error.info.message).toBe('Test error');
    });

    it('initializes with default location', () => {
      const error = new STError('Test error');

      expect(error.info.location).toEqual({
        packagePath: process.cwd(),
        staRootPath: undefined
      });
    });

    it('handles Error as extInfo', () => {
      const cause = new Error('Original error');
      const error = new STError('Wrapped error', cause);

      expect(error.info.extInfo).toEqual({
        name: 'Error',
        message: 'Original error',
        stack: cause.stack
      });
    });

    it('updates location asynchronously', async () => {
      const error = new STError('Test error');
      
      // Wait for async location update to complete
      await new Promise(resolve => setTimeout(resolve, 10));
      
      expect(error.info.location).toEqual({
        packagePath: '/test/package/path',
        staRootPath: '/test/sta/root'
      });
      expect(resolveLocation).toHaveBeenCalled();
    });
  });

  describe('inheritance', () => {
    it('supports custom error types through inheritance', () => {
      const error = new CustomTestError('Custom error');
      
      expect(error instanceof STError).toBe(true);
      expect(error instanceof CustomTestError).toBe(true);
      expect(error.name).toBe('CustomTestError');
      expect(error.info.errorType).toBe('CustomTestError');
    });
    
    it('preserves extInfo in custom errors', () => {
      const extInfo = { code: 'INVALID_INPUT', field: 'email' };
      const error = new CustomTestError('Custom error with info', extInfo);
      
      expect(error.info.extInfo).toEqual(extInfo);
    });
  });
});