import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as helpers from '../helpers';
import { spawn, waitFor, waitForAll, loadProcess, saveProcess, _getCurrentProcess } from '../helpers';
import { createProcess } from '../process';

// Mock the createProcess function
vi.mock('../process', () => ({
  createProcess: vi.fn()
}));

describe('Helpers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('_getCurrentProcess', () => {
    it('should throw an error if not implemented', () => {
      // The _getCurrentProcess function is private, but we can test it indirectly
      // by calling functions that use it

      const generator = spawn(() => {});

      expect(() => generator.next()).toThrow('Not implemented: _getCurrentProcess');
    });
  });

  describe('spawn', () => {
    it('should spawn a child process', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockChild = {
        start: vi.fn().mockResolvedValue(undefined)
      };

      const mockParent = {
        context: { parent: null },
        appendChild: vi.fn()
      };

      // Mock the createProcess function to return the mock child
      (createProcess as any).mockReturnValue(mockChild);

      // Mock the _getCurrentProcess function
      vi.spyOn(helpers, '_getCurrentProcess').mockReturnValue(mockParent);

      const generatorFn = () => {};
      const props = { foo: 'bar' };
      const options = { baz: 'qux' };

      const generator = spawn(generatorFn, props, options);
      const result = generator.next();

      expect(result.value).toBeInstanceOf(Promise);
      expect(createProcess).toHaveBeenCalledWith(generatorFn, props, { ...mockParent.context, parent: mockParent });
      expect(mockParent.appendChild).toHaveBeenCalledWith(mockChild);

      return result.value.then(() => {
        expect(mockChild.start).toHaveBeenCalled();
      });
    });
  });

  describe('waitFor', () => {
    it('should wait for a process to complete', () => {
      // Mock the process
      const mockProcess = {
        getResult: vi.fn().mockResolvedValue('process result')
      };

      const generator = waitFor(mockProcess);
      const result = generator.next();

      expect(result.value).toBeInstanceOf(Promise);
      expect(mockProcess.getResult).toHaveBeenCalled();

      return result.value.then(data => {
        expect(data).toBe('process result');
      });
    });
  });

  describe('waitForAll', () => {
    it('should wait for all processes to complete', () => {
      // Mock the processes
      const mockProcess1 = {
        getResult: vi.fn().mockResolvedValue('result 1')
      };

      const mockProcess2 = {
        getResult: vi.fn().mockResolvedValue('result 2')
      };

      // Create a mock implementation of waitFor
      const mockWaitFor = function* (process) {
        return yield Promise.resolve(process.getResult());
      };

      // Mock the waitFor function
      vi.spyOn(helpers, 'waitFor').mockImplementation(mockWaitFor);

      const generator = waitForAll([mockProcess1, mockProcess2]);

      // First yield should be the result of waitFor(mockProcess1)
      const result1 = generator.next();
      expect(result1.value).toEqual(Promise.resolve(mockProcess1.getResult()));

      // Second yield should be the result of waitFor(mockProcess2)
      const result2 = generator.next('result 1');
      expect(result2.value).toEqual(Promise.resolve(mockProcess2.getResult()));

      // Final result should be an array of all results
      const finalResult = generator.next('result 2');
      expect(finalResult.value).toEqual(['result 1', 'result 2']);
      expect(finalResult.done).toBe(true);
    });
  });

  describe('loadProcess', () => {
    it('should load a process from the filesystem', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockProcess = {
        context: {
          fs: {
            readFile: vi.fn().mockResolvedValue('function* testProcess() { yield; return "loaded"; }')
          }
        }
      };

      // Mock the createProcess function to return a mock loaded process
      const mockLoadedProcess = { id: 'loaded-process' };
      (createProcess as any).mockReturnValue(mockLoadedProcess);

      // Mock the _getCurrentProcess function
      vi.spyOn(helpers, '_getCurrentProcess').mockReturnValue(mockProcess);

      const generator = loadProcess('test.js');

      // First yield should be the result of fs.readFile
      const result1 = generator.next();
      expect(result1.value).toBeInstanceOf(Promise);
      expect(mockProcess.context.fs.readFile).toHaveBeenCalledWith('test.js');

      // Final result should be the created process
      const finalResult = generator.next('function* testProcess() { yield; return "loaded"; }');
      expect(finalResult.value).toBe(mockLoadedProcess);
      expect(finalResult.done).toBe(true);
      expect(createProcess).toHaveBeenCalledWith(expect.any(Function), {}, mockProcess.context);
    });
  });

  describe('saveProcess', () => {
    it('should save a process to the filesystem', () => {
      // Mock the _getCurrentProcess function to return a mock process
      const mockCurrentProcess = {
        context: {
          fs: {
            writeFile: vi.fn().mockResolvedValue(undefined)
          }
        }
      };

      // Mock the process to save
      const mockProcessToSave = {
        generatorFn: function* testProcess() { yield; return 'saved'; },
        path: ''
      };

      // Mock the _getCurrentProcess function
      vi.spyOn(helpers, '_getCurrentProcess').mockReturnValue(mockCurrentProcess);

      const generator = saveProcess(mockProcessToSave, 'test.js');

      // First yield should be the result of fs.writeFile
      const result1 = generator.next();
      expect(result1.value).toBeInstanceOf(Promise);
      expect(mockCurrentProcess.context.fs.writeFile).toHaveBeenCalledWith('test.js', mockProcessToSave.generatorFn.toString());

      // Final result should update the process path
      generator.next();
      expect(mockProcessToSave.path).toBe('test.js');
    });
  });
});
