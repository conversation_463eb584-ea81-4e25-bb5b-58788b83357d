/**
 * Middleware implementation
 * 
 * This file contains the implementation of the middleware interface.
 */

import { MiddlewareInterface, MiddlewareFunction } from './types/index.type';

/**
 * Create a middleware manager
 * 
 * @returns Middleware interface
 */
export function createMiddleware(): MiddlewareInterface {
  /** Array of middleware functions */
  const middlewares: MiddlewareFunction[] = [];
  
  return {
    /**
     * Add middleware to the stack
     * 
     * @param middleware - Middleware function to add
     */
    use(middleware: MiddlewareFunction): void {
      middlewares.push(middleware);
    },
    
    /**
     * Remove middleware from the stack
     * 
     * @param middleware - Middleware function to remove
     */
    remove(middleware: MiddlewareFunction): void {
      const index = middlewares.indexOf(middleware);
      if (index !== -1) {
        middlewares.splice(index, 1);
      }
    },
    
    /**
     * Execute middleware stack
     * 
     * @param context - Context to pass to middleware
     * @param next - Function to call after middleware
     * @returns Result of middleware execution
     */
    async execute(context: any, next: () => Promise<any>): Promise<any> {
      // Create a function that executes each middleware in the stack
      const dispatch = async (index: number): Promise<any> => {
        if (index === middlewares.length) {
          return next();
        }
        
        const middleware = middlewares[index];
        return middleware(context, () => dispatch(index + 1));
      };
      
      // Start execution with the first middleware
      return dispatch(0);
    }
  };
}

export default {
  createMiddleware
};
