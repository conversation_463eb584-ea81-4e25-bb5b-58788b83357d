/**
 * Kernel module
 */

// Export types
export * from './types';

// Export core implementations
export { createKernelCore } from './core';
export { createNode } from './node';
export { createPrioritySystem } from './priority';
export { createGeodesicCalculator, createDefaultMetricTensor } from './geodesic';
export { createHoloG } from './holog';
export { createReactAdapter } from './react';
export { createRequest, createRequestQueue, RequestPriority, RequestStatus } from './request';
export { createApiRequest, createSuccessResponse, createErrorResponse, ApiPriority } from './api';

// Import implementations
import { v4 as uuidv4 } from 'uuid';
import { createKernelCore } from './core';
import { createNode } from './node';
import { createPrioritySystem } from './priority';
import { createGeodesicCalculator } from './geodesic';
import { createHoloG } from './holog';
import { createReactAdapter } from './react';
import { createApiRequest, ApiPriority } from './api';
import { KernelInterface, Process, TicInfo } from '../types/index.type';
import { ResourceType, ApiRequest, ApiResponse } from './types';

/**
 * Create a kernel instance
 *
 * @returns Kernel interface
 */
export function createKernel(): KernelInterface {
  // Create core components
  const core = createKernelCore();
  const rootNode = createNode(null);
  const prioritySystem = createPrioritySystem();
  const geodesicCalculator = createGeodesicCalculator();
  const holoG = createHoloG();
  const reactAdapter = createReactAdapter();

  // Map of process ID to process
  const processes = new Map<string, Process>();

  // Map of tic ID to tic info
  const activeTics = new Map<string, TicInfo>();

  // Map of domain to sergeant process ID
  const sergeants = new Map<string, string>();

  // Kernel start time
  const startTime = Date.now();

  return {
    /**
     * Request a tic for a process
     *
     * @param processId - ID of the process requesting a tic
     * @param priority - Priority of the request
     * @returns Allocated tic or null if no tic could be allocated
     */
    async requestTic(processId: string, priority: number = 0): Promise<TicInfo | null> {
      const process = processes.get(processId);
      if (!process) {
        return null;
      }

      // Queue process for tic allocation
      core.queueProcess(processId);

      // Request CPU resource from root node
      const resource = await rootNode.requestResource(
        ResourceType.CPU,
        1, // Amount (1 tic)
        priority
      );

      if (!resource) {
        return null;
      }

      // Create a new tic
      const tic: TicInfo = {
        id: uuidv4(),
        startTime: Date.now(),
        duration: 100, // Default tic duration in ms
        priority,
        processId
      };

      activeTics.set(tic.id, tic);

      return tic;
    },

    /**
     * Release a tic
     *
     * @param ticId - ID of the tic to release
     */
    releaseTic(ticId: string): void {
      activeTics.delete(ticId);
    },

    /**
     * Register a process with the kernel
     *
     * @param process - Process to register
     */
    registerProcess(process: Process): void {
      processes.set(process.id, process);
    },

    /**
     * Unregister a process from the kernel
     *
     * @param processId - ID of the process to unregister
     */
    unregisterProcess(processId: string): void {
      processes.delete(processId);
    },

    /**
     * Get a process by ID
     *
     * @param processId - ID of the process to get
     * @returns Process or null if not found
     */
    getProcessById(processId: string): Process | null {
      return processes.get(processId) || null;
    },

    /**
     * Register a sergeant process
     *
     * @param process - Sergeant process
     * @param domain - Domain to register for
     */
    registerSergeant(process: Process, domain: string): void {
      sergeants.set(domain, process.id);
      processes.set(process.id, process);
    },

    /**
     * Unregister a sergeant process
     *
     * @param domain - Domain to unregister
     */
    unregisterSergeant(domain: string): void {
      sergeants.delete(domain);
    },

    /**
     * Get system load
     *
     * @returns System load (0-1)
     */
    getSystemLoad(): number {
      return core.getMetrics().systemLoad;
    },

    /**
     * Get system uptime
     *
     * @returns System uptime in ms
     */
    getSystemUptime(): number {
      return Date.now() - startTime;
    },

    /**
     * Handle API request
     *
     * @param request - API request
     * @returns API response
     */
    async handleApiRequest(request: ApiRequest): Promise<ApiResponse> {
      // Forward request to root node
      return rootNode.handleApiRequest(request);
    },

    /**
     * Send API request
     *
     * @param request - API request
     * @returns API response
     */
    async sendApiRequest(request: ApiRequest): Promise<ApiResponse> {
      // Forward request to root node
      return rootNode.sendApiRequest(request);
    }
  };
}
