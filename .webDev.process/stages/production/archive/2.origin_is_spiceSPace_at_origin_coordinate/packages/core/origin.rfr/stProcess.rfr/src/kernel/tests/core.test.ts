/**
 * Tests for kernel core
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createKernelCore } from '../core';

describe('Kernel Core', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now to return a consistent value
    vi.spyOn(Date, 'now').mockReturnValue(1000);
  });

  describe('createKernelCore', () => {
    it('should create a kernel core instance', () => {
      const core = createKernelCore();

      expect(core).toBeDefined();
      expect(core.queueProcess).toBeDefined();
      expect(core.distributeTics).toBeDefined();
      expect(core.getMetrics).toBeDefined();
    });

    it('should queue a process', () => {
      const core = createKernelCore();
      const processId = 'test-process';

      core.queueProcess(processId);

      // Verify process is queued by checking metrics
      const metrics = core.getMetrics();
      expect(metrics.systemLoad).toBeGreaterThan(0);
    });

    it('should distribute tics', () => {
      const core = createKernelCore();
      const processId = 'test-process';

      core.queueProcess(processId);
      core.distributeTics();

      // Verify process is no longer queued
      const metrics = core.getMetrics();
      expect(metrics.systemLoad).toBe(0);
    });

    it('should return metrics', () => {
      const core = createKernelCore();

      const metrics = core.getMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.systemLoad).toBeDefined();
      expect(metrics.uptime).toBeDefined();
      expect(metrics.activeProcesses).toBeDefined();
      expect(metrics.activeTics).toBeDefined();
      expect(metrics.pendingRequests).toBeDefined();
    });
  });
});
