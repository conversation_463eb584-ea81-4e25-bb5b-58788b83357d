/**
 * Helpers type definitions
 */

import { ProcessType } from './process.type';

/**
 * Spawn function type
 */
export type SpawnFn = <Props = any, Result = any>(
  generatorFn: (props: Props) => Generator<any, Result, any>,
  props?: Props,
  options?: Record<string, any>
) => Generator<any, ProcessType<Props, Result>, any>;

/**
 * Wait for function type
 */
export type WaitForFn = <Result = any>(
  process: ProcessType<any, Result>
) => Generator<any, Result, any>;

/**
 * Wait for all function type
 */
export type WaitForAllFn = (
  processes: ProcessType[]
) => Generator<any, any[], any>;

/**
 * Load process function type
 */
export type LoadProcessFn = (
  path: string
) => Generator<any, ProcessType, any>;

/**
 * Save process function type
 */
export type SaveProcessFn = (
  process: ProcessType,
  path: string
) => Generator<any, void, any>;
