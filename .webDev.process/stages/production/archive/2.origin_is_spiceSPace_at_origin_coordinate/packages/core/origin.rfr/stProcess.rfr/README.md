# StProcess

A filesystem-based process model using generator functions inspired by React components. This module enables processes to pause and resume execution across multiple tics, maintain state between executions, and participate in a hierarchical structure similar to the DOM.

## Features

- **Generator-Based Processes**: Processes are implemented as generator functions that can yield control and resume later.
- **Hierarchical Structure**: Processes form a DOM-like hierarchy with parent-child relationships.
- **Tic Allocation**: Processes can request and receive execution time slices (tics) from the kernel.
- **Event Propagation**: Events bubble up the process hierarchy through middleware filters.
- **CSS-like Targeting**: Selectors for targeting and configuring processes based on their properties.
- **Filesystem Integration**: Processes can be loaded from and saved to the filesystem.

## Installation

```bash
npm install @spicetime/core/origin/stProcess
```

## Usage

### Basic Process

```typescript
import stProcess from '@spicetime/core/origin/stProcess';

// Define a process as a generator function
function* DataProcessor(props) {
  // Initialize
  const { data, batchSize = 10 } = props;
  let processedItems = 0;
  
  // Process data in batches
  while (processedItems < data.length) {
    const batch = data.slice(processedItems, processedItems + batchSize);
    
    // Process each item in the batch
    for (const item of batch) {
      yield* processItem(item);
      processedItems++;
      
      // Check if we're running out of time in this tic
      if (stProcess.getTicRemainingTime() < 5) {
        // Yield control back to the kernel, will resume in next tic
        yield;
      }
    }
    
    // Report progress
    yield* stProcess.emitEvent('progress', { processed: processedItems, total: data.length });
  }
  
  // Return result
  return { processedItems };
}

// Create and start the process
const processor = stProcess.createProcess(DataProcessor, { 
  data: Array(100).fill(0).map((_, i) => ({ id: i, value: Math.random() }))
});

processor.on('progress', (progress) => {
  console.log(`Progress: ${progress.processed}/${progress.total}`);
});

processor.start();
```

### Process Composition

```typescript
import stProcess from '@spicetime/core/origin/stProcess';

// Define a compound process
function* AppProcess() {
  // Start child processes in parallel
  const dataProcessor = yield* stProcess.spawn(DataProcessor, { data });
  const logger = yield* stProcess.spawn(LoggerProcess, { level: 'info' });
  
  // Wait for both to complete
  const [processorResult, loggerResult] = yield* stProcess.waitForAll([dataProcessor, logger]);
  
  // Do final processing
  yield* stProcess.spawn(FinalizeProcess, { results: processorResult });
  
  // Return combined results
  return { 
    processor: processorResult,
    logger: loggerResult
  };
}
```

## API Reference

### Core

- `createProcess(generatorFn, props?, context?)`: Create a process from a generator function
- `Process`: Process class

### Process Management

- `spawn(generatorFn, props?, options?)`: Spawn a child process
- `waitFor(process)`: Wait for a process to complete
- `waitForAll(processes)`: Wait for multiple processes to complete
- `loadProcess(path)`: Load a process from the filesystem
- `saveProcess(process, path)`: Save a process to the filesystem

### Event Handling

- `emitEvent(event, ...args)`: Emit an event
- `onEvent(event)`: Listen for an event
- `broadcast(event, ...args)`: Broadcast an event to all processes

### Tic Management

- `requestTic(priority?)`: Request a tic
- `getTicRemainingTime()`: Get remaining time in the current tic
- `isInTic()`: Check if currently executing in a tic
- `getCurrentTic()`: Get information about the current tic

## License

MIT
