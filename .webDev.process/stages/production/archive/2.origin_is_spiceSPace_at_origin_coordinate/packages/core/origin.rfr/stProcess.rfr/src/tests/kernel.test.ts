import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createKernel } from '../kernel';

describe('Kernel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now to return a consistent value
    vi.spyOn(Date, 'now').mockReturnValue(1000);
  });

  describe('createKernel', () => {
    it('should create a kernel instance', () => {
      const kernel = createKernel();

      expect(kernel).toBeDefined();
      expect(kernel.requestTic).toBeDefined();
      expect(kernel.releaseTic).toBeDefined();
      expect(kernel.registerProcess).toBeDefined();
      expect(kernel.unregisterProcess).toBeDefined();
      expect(kernel.getProcessById).toBeDefined();
      expect(kernel.registerSergeant).toBeDefined();
      expect(kernel.unregisterSergeant).toBeDefined();
      expect(kernel.getSystemLoad).toBeDefined();
      expect(kernel.getSystemUptime).toBeDefined();
    });
  });

  describe('requestTic', () => {
    it('should return null if process is not registered', async () => {
      const kernel = createKernel();

      const tic = await kernel.requestTic('non-existent-process');

      expect(tic).toBeNull();
    });

    it('should create a tic for a registered process', async () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-process' };

      kernel.registerProcess(mockProcess as any);
      const tic = await kernel.requestTic('test-process');

      expect(tic).not.toBeNull();
      expect(tic?.id).toBeDefined();
      expect(tic?.startTime).toBe(1000);
      expect(tic?.duration).toBe(100);
      expect(tic?.priority).toBe(0);
      expect(tic?.processId).toBe('test-process');
    });

    it('should respect the priority parameter', async () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-process' };

      kernel.registerProcess(mockProcess as any);
      const tic = await kernel.requestTic('test-process', 5);

      expect(tic?.priority).toBe(5);
    });
  });

  describe('releaseTic', () => {
    it('should remove the tic from active tics', async () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-process' };

      kernel.registerProcess(mockProcess as any);
      const tic = await kernel.requestTic('test-process');

      // Mock the activeTics Map
      const mockActiveTics = new Map();
      mockActiveTics.set(tic?.id, tic);

      // Set the mock activeTics on the kernel
      (kernel as any).activeTics = mockActiveTics;

      expect(mockActiveTics.has(tic?.id)).toBe(true);

      kernel.releaseTic(tic?.id as string);

      expect(mockActiveTics.has(tic?.id)).toBe(false);
    });
  });

  describe('process management', () => {
    it('should register and retrieve a process', () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-process' };

      kernel.registerProcess(mockProcess as any);

      const retrievedProcess = kernel.getProcessById('test-process');
      expect(retrievedProcess).toBe(mockProcess);
    });

    it('should unregister a process', () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-process' };

      kernel.registerProcess(mockProcess as any);
      kernel.unregisterProcess('test-process');

      const retrievedProcess = kernel.getProcessById('test-process');
      expect(retrievedProcess).toBeNull();
    });
  });

  describe('sergeant management', () => {
    it('should register and unregister a sergeant', () => {
      const kernel = createKernel();
      const mockProcess = { id: 'test-sergeant' };

      kernel.registerSergeant(mockProcess as any, 'test-domain');

      // We don't have a direct way to check if a sergeant is registered,
      // so we'll unregister it and check that it works
      kernel.unregisterSergeant('test-sergeant');

      // No assertion needed, just checking that it doesn't throw
    });
  });

  describe('system metrics', () => {
    it('should return system load', () => {
      const kernel = createKernel();

      const load = kernel.getSystemLoad();

      expect(load).toBe(0); // No active tics, so load should be 0
    });

    it('should return system uptime', () => {
      const kernel = createKernel();

      const uptime = kernel.getSystemUptime();

      expect(uptime).toBe(0); // Date.now() is mocked to return 1000, and startTime is also 1000
    });
  });
});
