/**
 * Process type definitions
 */

import { EventEmitter } from 'events';
import { ProcessState, ProcessEvent, TicInfo } from './index.type';

/**
 * Process class type
 */
export interface ProcessType<Props = any, Result = any> {
  /** Unique process identifier */
  readonly id: string;
  
  /** Path in the filesystem */
  path: string;
  
  /** Parent process ID (if any) */
  parentId: string | null;
  
  /** Process properties */
  readonly props: Props;
  
  /** Child processes */
  readonly children: ProcessType[];
  
  /** Current lifecycle state */
  state: ProcessState;
  
  /** Process context */
  readonly context: any;
  
  /** Current tic */
  currentTic: TicInfo | null;
  
  /**
   * Start the process
   */
  start(): Promise<void>;
  
  /**
   * Stop the process
   */
  stop(): Promise<void>;
  
  /**
   * Pause the process
   */
  pause(): Promise<void>;
  
  /**
   * Resume the process
   */
  resume(): Promise<void>;
  
  /**
   * Request a tic from kernel
   * 
   * @param priority - Priority of the tic request
   * @returns Whether a tic was allocated
   */
  requestTic(priority?: number): Promise<boolean>;
  
  /**
   * Release the current tic back to the kernel
   */
  releaseTic(): void;
  
  /**
   * Add a child process
   * 
   * @param child - Child process to add
   */
  appendChild(child: ProcessType): void;
  
  /**
   * Remove a child process
   * 
   * @param child - Child process to remove
   */
  removeChild(child: ProcessType): void;
  
  /**
   * Get the parent process
   * 
   * @returns Parent process or null if no parent
   */
  getParent(): ProcessType | null;
  
  /**
   * Get the root process
   * 
   * @returns Root process
   */
  getRoot(): ProcessType;
  
  /**
   * Get the final result of the process
   * 
   * @returns Process result
   */
  getResult(): Promise<Result>;
  
  /**
   * Register event listener
   */
  on(event: ProcessEvent, listener: Function): ProcessType;
  
  /**
   * Register one-time event listener
   */
  once(event: ProcessEvent, listener: Function): ProcessType;
  
  /**
   * Remove event listener
   */
  off(event: ProcessEvent, listener: Function): ProcessType;
  
  /**
   * Emit an event
   */
  emit(event: ProcessEvent, ...args: any[]): boolean;
}

/**
 * Create process function type
 */
export type CreateProcessFn = <Props = any, Result = any>(
  generatorFn: (props: Props) => Generator<any, Result, any>,
  props?: Props,
  context?: any
) => ProcessType<Props, Result>;
