/**
 * Selector implementation
 * 
 * This file contains the implementation of the CSS-like selector engine.
 */

import { SelectorInterface, Process } from './types/index.type';

/**
 * Dynamic rule interface
 */
interface DynamicRule {
  /** Condition function */
  condition: () => boolean;
  /** CSS-like selector */
  selector: string;
  /** Properties to apply */
  properties: Record<string, any>;
}

/**
 * Create a selector engine
 * 
 * @returns Selector interface
 */
export function createSelector(): SelectorInterface {
  /** Array of dynamic rules */
  const dynamicRules: DynamicRule[] = [];
  
  /** Map of selector to matching processes */
  const processCache = new Map<string, Process[]>();
  
  return {
    /**
     * Select processes matching the selector
     * 
     * @param selector - CSS-like selector
     * @returns Matching processes
     */
    select(selector: string): Process[] {
      // Simple implementation for now - just parse the selector and return matching processes
      // In a real implementation, this would use a proper CSS selector engine
      
      // Check cache first
      if (processCache.has(selector)) {
        return processCache.get(selector) || [];
      }
      
      // Parse the selector
      const match = selector.match(/process\(([^)]*)\)/);
      if (!match) {
        return [];
      }
      
      const processType = match[1];
      
      // Get all processes from the kernel
      // This is a simplified implementation - in reality, we would need access to the kernel
      const allProcesses: Process[] = [];
      
      // Filter processes by type
      const matchingProcesses = allProcesses.filter(process => {
        if (processType === '*') {
          return true;
        }
        
        // Check if the process generator function name matches the type
        return (process as any).generatorFn.name === processType;
      });
      
      // Cache the result
      processCache.set(selector, matchingProcesses);
      
      return matchingProcesses;
    },
    
    /**
     * Apply properties to matching processes
     * 
     * @param selector - CSS-like selector
     * @param properties - Properties to apply
     */
    apply(selector: string, properties: Record<string, any>): void {
      const processes = this.select(selector);
      
      for (const process of processes) {
        // Apply properties to the process
        Object.assign(process.props, properties);
      }
    },
    
    /**
     * Check if a process matches a selector
     * 
     * @param process - Process to check
     * @param selector - CSS-like selector
     * @returns Whether the process matches the selector
     */
    matches(process: Process, selector: string): boolean {
      // Simple implementation for now
      const match = selector.match(/process\(([^)]*)\)/);
      if (!match) {
        return false;
      }
      
      const processType = match[1];
      
      if (processType === '*') {
        return true;
      }
      
      // Check if the process generator function name matches the type
      return (process as any).generatorFn.name === processType;
    },
    
    /**
     * Register a dynamic selector rule
     * 
     * @param condition - Condition function
     * @param selector - CSS-like selector
     * @param properties - Properties to apply
     */
    registerRule(condition: () => boolean, selector: string, properties: Record<string, any>): void {
      dynamicRules.push({ condition, selector, properties });
    }
  };
}

export default {
  createSelector
};
