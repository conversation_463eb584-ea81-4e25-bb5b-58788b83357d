/**
 * Tests for API request functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createApiRequest, ApiPriority } from '../api';
import { createNode } from '../node';

describe('API Request', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now to return a consistent value
    vi.spyOn(Date, 'now').mockReturnValue(1000);
  });

  describe('createApiRequest', () => {
    it('should create an API request', () => {
      const request = createApiRequest(
        'test',
        { foo: 'bar' },
        'source-node',
        'target-node',
        ApiPriority.NORMAL
      );

      expect(request).toBeDefined();
      expect(request.id).toBeDefined();
      expect(request.type).toBe('test');
      expect(request.payload).toEqual({ foo: 'bar' });
      expect(request.sourceId).toBe('source-node');
      expect(request.targetId).toBe('target-node');
      expect(request.priority).toBe(ApiPriority.NORMAL);
    });

    it('should use default priority if not specified', () => {
      const request = createApiRequest(
        'test',
        { foo: 'bar' },
        'source-node',
        'target-node'
      );

      expect(request.priority).toBe(ApiPriority.NORMAL);
    });
  });

  describe('Node API handling', () => {
    it('should handle API requests', async () => {
      const node = createNode();

      // Create a request
      const request = createApiRequest(
        'test',
        { foo: 'bar' },
        'source-node',
        node.id,
        ApiPriority.HIGH
      );

      // Handle request
      const response = await node.handleApiRequest(request);

      expect(response).toBeDefined();
      expect(response.requestId).toBe(request.id);
      // Since we don't have a handler for 'test', it should return an error
      expect(response.status).toBe('error');
    });

    it('should send API requests', async () => {
      const node = createNode();

      // Create a request
      const request = createApiRequest(
        'test',
        { foo: 'bar' },
        node.id,
        'target-node',
        ApiPriority.HIGH
      );

      // Send request
      const response = await node.sendApiRequest(request);

      expect(response).toBeDefined();
      expect(response.requestId).toBe(request.id);
      expect(response.status).toBe('success');
      expect(response.data).toBeDefined();
    });
  });
});
