/**
 * Context type definitions
 */

import { KernelType } from './kernel.type';
import { FileSystemType } from './filesystem.type';
import { LoggerType } from './logger.type';
import { SelectorType } from './selector.type';
import { MiddlewareType } from './middleware.type';
import { ProcessType } from './process.type';

/**
 * Process context type
 */
export interface ProcessContextType {
  /** Reference to the SpiceTime kernel */
  kernel: KernelType;
  
  /** Filesystem access */
  fs: FileSystemType;
  
  /** Environment variables */
  env: Record<string, any>;
  
  /** Parent process */
  parent: ProcessType | null;
  
  /** Root process */
  root: ProcessType;
  
  /** Logging utility */
  logger: LoggerType;
  
  /** CSS-like selector engine */
  selector: SelectorType;
  
  /** Middleware manager */
  middleware: MiddlewareType;
}

/**
 * Create default context function type
 */
export type CreateDefaultContextFn = (process: ProcessType) => ProcessContextType;
