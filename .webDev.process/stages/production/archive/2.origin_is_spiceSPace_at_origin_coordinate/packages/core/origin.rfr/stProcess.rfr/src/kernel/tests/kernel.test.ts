/**
 * Tests for kernel
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createKernel } from '../index';

describe('Kernel', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now to return a consistent value
    vi.spyOn(Date, 'now').mockReturnValue(1000);
  });

  describe('createKernel', () => {
    it('should create a kernel instance', () => {
      const kernel = createKernel();

      expect(kernel).toBeDefined();
      expect(kernel.requestTic).toBeDefined();
      expect(kernel.releaseTic).toBeDefined();
      expect(kernel.registerProcess).toBeDefined();
      expect(kernel.unregisterProcess).toBeDefined();
      expect(kernel.getProcessById).toBeDefined();
      expect(kernel.registerSergeant).toBeDefined();
      expect(kernel.unregisterSergeant).toBeDefined();
      expect(kernel.getSystemLoad).toBeDefined();
      expect(kernel.getSystemUptime).toBeDefined();
    });

    it('should register and retrieve a process', () => {
      const kernel = createKernel();
      const process = {
        id: 'test-process',
        path: '/test',
        parentId: null,
        props: {},
        children: [],
        state: 'created',
        start: vi.fn(),
        stop: vi.fn(),
        pause: vi.fn(),
        resume: vi.fn(),
        requestTic: vi.fn(),
        releaseTic: vi.fn()
      };

      kernel.registerProcess(process);
      const retrievedProcess = kernel.getProcessById('test-process');

      expect(retrievedProcess).toBe(process);
    });

    it('should unregister a process', () => {
      const kernel = createKernel();
      const process = {
        id: 'test-process',
        path: '/test',
        parentId: null,
        props: {},
        children: [],
        state: 'created',
        start: vi.fn(),
        stop: vi.fn(),
        pause: vi.fn(),
        resume: vi.fn(),
        requestTic: vi.fn(),
        releaseTic: vi.fn()
      };

      kernel.registerProcess(process);
      kernel.unregisterProcess('test-process');
      const retrievedProcess = kernel.getProcessById('test-process');

      expect(retrievedProcess).toBeNull();
    });

    it('should register and unregister a sergeant', () => {
      const kernel = createKernel();
      const process = {
        id: 'test-sergeant',
        path: '/test',
        parentId: null,
        props: {},
        children: [],
        state: 'created',
        start: vi.fn(),
        stop: vi.fn(),
        pause: vi.fn(),
        resume: vi.fn(),
        requestTic: vi.fn(),
        releaseTic: vi.fn()
      };

      kernel.registerSergeant(process, 'test-domain');
      kernel.unregisterSergeant('test-domain');
    });

    it('should return system load', () => {
      const kernel = createKernel();
      const load = kernel.getSystemLoad();

      expect(load).toBeGreaterThanOrEqual(0);
      expect(load).toBeLessThanOrEqual(1);
    });

    it('should return system uptime', () => {
      const kernel = createKernel();
      const uptime = kernel.getSystemUptime();

      expect(uptime).toBe(0); // Since Date.now() is mocked to return 1000
    });
  });
});
