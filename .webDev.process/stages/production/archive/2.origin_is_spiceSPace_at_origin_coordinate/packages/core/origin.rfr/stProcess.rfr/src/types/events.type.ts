/**
 * Events type definitions
 */

import { ProcessEvent } from './index.type';

/**
 * Emit event function type
 */
export type EmitEventFn = (event: ProcessEvent, ...args: any[]) => Generator<any, boolean, any>;

/**
 * On event function type
 */
export type OnEventFn = (event: ProcessEvent) => Generator<any, any, any>;

/**
 * Broadcast function type
 */
export type BroadcastFn = (event: ProcessEvent, ...args: any[]) => Generator<any, void, any>;
