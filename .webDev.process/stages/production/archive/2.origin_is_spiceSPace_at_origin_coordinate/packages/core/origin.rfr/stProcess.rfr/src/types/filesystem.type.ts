/**
 * Filesystem type definitions
 */

/**
 * Filesystem interface type
 */
export interface FileSystemType {
  /**
   * Check if a file exists
   * 
   * @param filePath - Path to check
   * @returns Whether the file exists
   */
  exists(filePath: string): Promise<boolean>;
  
  /**
   * Read a file
   * 
   * @param filePath - Path to read
   * @returns File contents
   */
  readFile(filePath: string): Promise<string>;
  
  /**
   * Write a file
   * 
   * @param filePath - Path to write
   * @param content - Content to write
   */
  writeFile(filePath: string, content: string): Promise<void>;
  
  /**
   * Create a directory
   * 
   * @param dirPath - Path to create
   */
  mkdir(dirPath: string): Promise<void>;
  
  /**
   * Read a directory
   * 
   * @param dirPath - Path to read
   * @returns Directory contents
   */
  readdir(dirPath: string): Promise<string[]>;
  
  /**
   * Remove a file or directory
   * 
   * @param path - Path to remove
   */
  remove(path: string): Promise<void>;
}

/**
 * Create filesystem function type
 */
export type CreateFileSystemFn = () => FileSystemType;
