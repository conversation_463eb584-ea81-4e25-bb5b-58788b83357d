/**
 * Kernel core implementation
 */

import { v4 as uuidv4 } from 'uuid';
import { KernelCore, KernelMetrics, Resource, ResourceType } from './types';
import { Process, TicInfo } from '../types/index.type';

/**
 * Create a kernel core
 * 
 * @returns Kernel core interface
 */
export function createKernelCore(): KernelCore {
  /** Process queue */
  const processQueue: string[] = [];
  
  /** Map of process ID to process */
  const processes = new Map<string, Process>();
  
  /** Map of tic ID to tic info */
  const activeTics = new Map<string, TicInfo>();
  
  /** Kernel start time */
  const startTime = Date.now();
  
  /** System load (0-1) */
  let systemLoad = 0;
  
  /** Pending resource requests */
  const pendingRequests = new Map<string, {
    processId: string;
    type: ResourceType;
    amount: number;
    priority: number;
  }>();
  
  /**
   * Update system load
   */
  function updateSystemLoad(): void {
    // Simple load calculation based on queue length
    // In a real implementation, this would be more sophisticated
    systemLoad = Math.min(processQueue.length / 10, 1);
  }
  
  /**
   * Allocate a tic to a process
   * 
   * @param processId - Process ID
   * @param priority - Priority
   * @returns Allocated tic or null
   */
  async function allocateTic(processId: string, priority: number = 0): Promise<TicInfo | null> {
    const process = processes.get(processId);
    if (!process) {
      return null;
    }
    
    // Create a new tic
    const tic: TicInfo = {
      id: uuidv4(),
      startTime: Date.now(),
      duration: 100, // Default tic duration in ms
      priority,
      processId
    };
    
    activeTics.set(tic.id, tic);
    
    return tic;
  }
  
  return {
    /**
     * Queue a process for tic allocation
     * 
     * @param processId - Process ID
     */
    queueProcess(processId: string): void {
      // Add process to queue if not already queued
      if (!processQueue.includes(processId)) {
        processQueue.push(processId);
        updateSystemLoad();
      }
    },
    
    /**
     * Distribute tics to queued processes
     */
    distributeTics(): void {
      // Simple round-robin distribution
      // In a real implementation, this would be more sophisticated
      if (processQueue.length === 0) {
        return;
      }
      
      // Get next process in queue
      const processId = processQueue.shift()!;
      
      // Allocate tic to process
      allocateTic(processId).then(tic => {
        if (tic) {
          // Process will handle the tic
          // When done, it will release the tic and can queue again
        }
      });
      
      updateSystemLoad();
    },
    
    /**
     * Get metrics about kernel
     * 
     * @returns Kernel metrics
     */
    getMetrics(): KernelMetrics {
      return {
        systemLoad,
        uptime: Date.now() - startTime,
        activeProcesses: processes.size,
        activeTics: activeTics.size,
        pendingRequests: pendingRequests.size
      };
    }
  };
}
