/**
 * HoloG (Holographic Gravity) implementation
 */

import { v4 as uuidv4 } from 'uuid';
import { Holo<PERSON>, Goal, Vector } from './types';

/**
 * Create a HoloG instance
 * 
 * @returns HoloG interface
 */
export function createHoloG(): HoloG {
  /** Goals (goalId -> goal) */
  const goals = new Map<string, Goal>();
  
  /** Node positions (nodeId -> position) */
  const nodePositions = new Map<string, Vector>();
  
  /**
   * Calculate gravitational force between points
   * 
   * @param pos1 - First position
   * @param pos2 - Second position
   * @param mass - Mass at second position
   * @returns Force vector
   */
  function calculateGravitationalForce(pos1: Vector, pos2: Vector, mass: number): Vector {
    // Calculate distance
    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;
    const dz = pos2.z - pos1.z;
    
    const distanceSquared = dx * dx + dy * dy + dz * dz;
    
    // Avoid division by zero
    if (distanceSquared < 0.0001) {
      return { x: 0, y: 0, z: 0 };
    }
    
    const distance = Math.sqrt(distanceSquared);
    
    // Calculate force magnitude (F = G * m1 * m2 / r^2)
    // We use a simplified model where G = 1 and m1 = 1
    const forceMagnitude = mass / distanceSquared;
    
    // Calculate force components
    return {
      x: (dx / distance) * forceMagnitude,
      y: (dy / distance) * forceMagnitude,
      z: (dz / distance) * forceMagnitude
    };
  }
  
  /**
   * Get node position
   * 
   * @param nodeId - Node ID
   * @returns Node position
   */
  function getNodePosition(nodeId: string): Vector {
    return nodePositions.get(nodeId) || { x: 0, y: 0, z: 0 };
  }
  
  return {
    /**
     * Register a goal
     * 
     * @param goal - Goal to register
     */
    registerGoal(goal: Goal): void {
      // If goal has no ID, generate one
      if (!goal.id) {
        goal = {
          ...goal,
          id: uuidv4()
        };
      }
      
      goals.set(goal.id, goal);
    },
    
    /**
     * Update goal priority
     * 
     * @param goalId - Goal ID
     * @param priority - New priority
     */
    updateGoalPriority(goalId: string, priority: number): void {
      const goal = goals.get(goalId);
      if (goal) {
        goals.set(goalId, {
          ...goal,
          priority
        });
      }
    },
    
    /**
     * Calculate curvature for node
     * 
     * @param nodeId - Node ID
     * @returns Curvature vector
     */
    calculateCurvature(nodeId: string): Vector {
      const nodePosition = getNodePosition(nodeId);
      
      // Initialize curvature vector
      const curvature: Vector = { x: 0, y: 0, z: 0 };
      
      // Sum forces from all goals
      for (const goal of goals.values()) {
        // Calculate force from this goal
        const force = calculateGravitationalForce(
          nodePosition,
          goal.position,
          goal.strength * goal.priority
        );
        
        // Add to curvature
        curvature.x += force.x;
        curvature.y += force.y;
        curvature.z += force.z;
      }
      
      return curvature;
    }
  };
}
