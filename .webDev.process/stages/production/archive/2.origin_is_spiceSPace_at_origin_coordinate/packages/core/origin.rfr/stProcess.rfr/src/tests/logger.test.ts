import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createLogger } from '../logger';

describe('Logger', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock console methods
    vi.spyOn(console, 'debug').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  describe('createLogger', () => {
    it('should create a logger interface', () => {
      const logger = createLogger();
      
      expect(logger).toBeDefined();
      expect(logger.debug).toBeDefined();
      expect(logger.info).toBeDefined();
      expect(logger.warn).toBeDefined();
      expect(logger.error).toBeDefined();
    });
  });
  
  describe('debug', () => {
    it('should log a debug message', () => {
      const logger = createLogger();
      
      logger.debug('test message');
      
      expect(console.debug).toHaveBeenCalledWith('[DEBUG]', 'test message');
    });
    
    it('should log multiple arguments', () => {
      const logger = createLogger();
      
      logger.debug('test message', 123, { foo: 'bar' });
      
      expect(console.debug).toHaveBeenCalledWith('[DEBUG]', 'test message', 123, { foo: 'bar' });
    });
  });
  
  describe('info', () => {
    it('should log an info message', () => {
      const logger = createLogger();
      
      logger.info('test message');
      
      expect(console.info).toHaveBeenCalledWith('[INFO]', 'test message');
    });
    
    it('should log multiple arguments', () => {
      const logger = createLogger();
      
      logger.info('test message', 123, { foo: 'bar' });
      
      expect(console.info).toHaveBeenCalledWith('[INFO]', 'test message', 123, { foo: 'bar' });
    });
  });
  
  describe('warn', () => {
    it('should log a warning message', () => {
      const logger = createLogger();
      
      logger.warn('test message');
      
      expect(console.warn).toHaveBeenCalledWith('[WARN]', 'test message');
    });
    
    it('should log multiple arguments', () => {
      const logger = createLogger();
      
      logger.warn('test message', 123, { foo: 'bar' });
      
      expect(console.warn).toHaveBeenCalledWith('[WARN]', 'test message', 123, { foo: 'bar' });
    });
  });
  
  describe('error', () => {
    it('should log an error message', () => {
      const logger = createLogger();
      
      logger.error('test message');
      
      expect(console.error).toHaveBeenCalledWith('[ERROR]', 'test message');
    });
    
    it('should log multiple arguments', () => {
      const logger = createLogger();
      
      logger.error('test message', 123, { foo: 'bar' });
      
      expect(console.error).toHaveBeenCalledWith('[ERROR]', 'test message', 123, { foo: 'bar' });
    });
  });
});
