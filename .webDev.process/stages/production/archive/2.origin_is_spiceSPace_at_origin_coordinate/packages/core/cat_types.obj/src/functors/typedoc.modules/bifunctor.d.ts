/**
 * @module Functors/Bifunctor
 */
/**
 * ## Bifunctors
 *
 * A bifunctor is covariant in two arguments.
 *
 * ### Web Development Applications:
 * - **Pair Types**: Transform both elements of a pair
 * - **Either/Result Types**: Transform both success and error cases
 * - **Form Fields**: Transform both field value and validation state
 *
 * ### Example:
 * ```typescript
 * // Either type as a bifunctor
 * interface Either<L, R> {
 *   isLeft: () => boolean;
 *   isRight: () => boolean;
 *   mapLeft: <L2>(f: (l: L) => L2) => Either<L2, R>;
 *   mapRight: <R2>(f: (r: R) => R2) => Either<L, R2>;
 *   bimap: <L2, R2>(f: (l: L) => L2, g: (r: R) => R2) => Either<L2, R2>;
 * }
 *
 * // Left constructor
 * const left = <L, R>(value: L): Either<L, R> => ({
 *   isLeft: () => true,
 *   isRight: () => false,
 *   mapLeft: <L2>(f: (l: L) => L2) => left<L2, R>(f(value)),
 *   mapRight: () => left<L, R>(value),
 *   bimap: <L2, R2>(f: (l: L) => L2, _: (r: R) => R2) => left<L2, R2>(f(value))
 * });
 *
 * // Right constructor
 * const right = <L, R>(value: R): Either<L, R> => ({
 *   isLeft: () => false,
 *   isRight: () => true,
 *   mapLeft: () => right<L, R>(value),
 *   mapRight: <R2>(g: (r: R) => R2) => right<L, R2>(g(value)),
 *   bimap: <L2, R2>(_: (l: L) => L2, g: (r: R) => R2) => right<L2, R2>(g(value))
 * });
 * ```
 *
 * ### Bifunctor Laws:
 * 1. Identity: `F.bimap(id, id) = id`
 * 2. Composition: `F.bimap(f, g).bimap(h, i) = F.bimap(x => h(f(x)), y => i(g(y)))`
 */
/**
 * @module Functors/Bifunctor/Tests
 */
/**
 * ## Bifunctor Tests
 *
 * Test specification for the Bifunctor implementation.
 *
 * ### Interface Tests:
 * - Verify `mapLeft` correctly transforms left values
 * - Verify `mapRight` correctly transforms right values
 * - Verify `bimap` correctly transforms both values
 *
 * ### Laws Validation:
 * - Test Identity Law: `F.bimap(id, id) = id`
 * - Test Composition Law: `F.bimap(f, g).bimap(h, i) = F.bimap(x => h(f(x)), y => i(g(y)))`
 *
 * ### Functor Tests:
 * - Test mapping between bifunctor types
 * - Test preservation of bifunctor behavior after mapping
 * - Test error handling for invalid mappings
 *
 * ### Utility Tests:
 * - Test bifunctor creation
 * - Test composition with other functors
 * - Test with complex transformation functions
 *
 * ### Specific Bifunctor Tests:
 * - Test Either bifunctor with left and right values
 * - Test Pair bifunctor with different value types
 * - Test Result bifunctor with success and error cases
 */ 
//# sourceMappingURL=bifunctor.d.ts.map