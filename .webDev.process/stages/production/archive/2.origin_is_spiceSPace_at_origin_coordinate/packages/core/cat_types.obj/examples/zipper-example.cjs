/**
 * Example demonstrating the use of zippers for navigating and editing data structures
 */

// Import from the compiled output
const {
  createCatObject,
  ConcreteCategory,
  BaseMorphism,
  createListZipper,
  createTreeZ<PERSON>per,
  ZipperFunctor
} = require('../dist/index.js');

// Create a simple list
const numbers = [1, 2, 3, 4, 5];
console.log('Original list:', numbers);

// Create a zipper for the list
const zipper = createListZipper(numbers, 2);
console.log('Zipper focused on:', zipper.focus);

// Navigate left
const leftZipper = zipper.left();
console.log('After moving left, focus is on:', leftZipper.focus);

// Navigate right twice
const rightZipper = zipper.right();
console.log('After moving right, focus is on:', rightZipper.focus);
const rightAgainZipper = rightZipper.right();
console.log('After moving right again, focus is on:', rightAgainZipper.focus);

// Update the focused element
const updatedZipper = zipper.update(10);
console.log('After updating focus to 10:', updatedZipper.focus);

// Reconstruct the list
const updatedList = updatedZipper.reconstruct();
console.log('Reconstructed list:', updatedList);
console.log('Original list (unchanged):', numbers);

// Create a tree structure
const tree = {
  value: 'root',
  children: [
    {
      value: 'child1',
      children: [
        { value: 'grandchild1', children: [] },
        { value: 'grandchild2', children: [] }
      ]
    },
    {
      value: 'child2',
      children: [
        { value: 'grandchild3', children: [] }
      ]
    }
  ]
};

console.log('\nTree structure:');
console.log(JSON.stringify(tree, null, 2));

// Create a zipper for the tree
const treeZipper = createTreeZipper(tree);
console.log('Tree zipper focused on:', treeZipper.focus.value);

// Navigate down to the first child
const childZipper = treeZipper.down();
console.log('After moving down, focus is on:', childZipper.focus.value);

// Navigate to the first grandchild
const grandchildZipper = childZipper.down();
if (grandchildZipper) {
  console.log('After moving to grandchild, focus is on:', grandchildZipper.focus.value);

  // Update the grandchild
  const updatedGrandchildZipper = grandchildZipper.update({
    ...grandchildZipper.focus,
    value: 'updated grandchild1'
  });
  console.log('After updating grandchild:', updatedGrandchildZipper.focus.value);

  // Navigate back up to the root
  const backToParentZipper = updatedGrandchildZipper.up();
  if (backToParentZipper) {
    console.log('After moving back to parent, focus is on:', backToParentZipper.focus.value);
  }
} else {
  console.log('Could not navigate to grandchild');
}

// Reconstruct the tree
const updatedTree = childZipper.reconstruct();
console.log('\nUpdated tree structure:');
console.log(JSON.stringify(updatedTree, null, 2));

// Create categories for zippers
const numberZipperCategory = new ConcreteCategory();
const stringZipperCategory = new ConcreteCategory();

// Create zipper objects
const numberZipperObj = createCatObject(zipper, 'numberZipper');
numberZipperCategory.addObject(numberZipperObj);

const stringZipperObj = createCatObject(
  createListZipper(['a', 'b', 'c', 'd', 'e'], 2),
  'stringZipper'
);
stringZipperCategory.addObject(stringZipperObj);

// Create a zipper functor
const numberToStringZipperFunctor = new ZipperFunctor(
  numberZipperCategory,
  stringZipperCategory,
  // Map from number to string
  num => String(num),
  // Map from string to number
  str => Number(str)
);

// Map a zipper object
const mappedZipper = numberToStringZipperFunctor.mapObject(numberZipperObj);
console.log('\nMapped zipper focus:', mappedZipper.value.focus);
console.log('Mapped zipper left elements:', mappedZipper.value.context.left);
console.log('Mapped zipper right elements:', mappedZipper.value.context.right);

// Demonstrate zipper operations on the mapped zipper
const mappedLeft = mappedZipper.value.left();
console.log('Mapped zipper after moving left, focus is on:', mappedLeft.focus);

const mappedUpdated = mappedZipper.value.update('X');
console.log('Mapped zipper after updating focus to X:', mappedUpdated.focus);

const mappedReconstructed = mappedUpdated.reconstruct();
console.log('Mapped zipper reconstructed:', mappedReconstructed);
