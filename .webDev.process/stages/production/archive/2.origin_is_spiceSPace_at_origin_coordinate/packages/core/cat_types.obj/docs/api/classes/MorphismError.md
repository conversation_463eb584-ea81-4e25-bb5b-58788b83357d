[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / MorphismError

# Class: MorphismError

Defined in: [atomic/errors/morphism.ts:12](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/morphism.ts#L12)

Represents an error that occurs in a morphism

## Extends

- `Error`

## Constructors

### new MorphismError()

> **new MorphismError**(`message`): [`MorphismError`](MorphismError.md)

Defined in: [atomic/errors/morphism.ts:17](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/morphism.ts#L17)

Creates a new morphism error

#### Parameters

##### message

`string`

Error message

#### Returns

[`MorphismError`](MorphismError.md)

#### Overrides

`Error.constructor`
