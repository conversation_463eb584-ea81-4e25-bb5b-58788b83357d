/**
 * Example of using the cat_types system to model a monad
 */

import { ConcreteCategory } from '../src/atomic/cat.ts';
import { createCatObject } from '../src/atomic/catObject.ts';
import { BaseMorphism } from '../src/atomic/morphism.ts';
import { BaseFunctor } from '../src/atomic/functor.ts';
import { BaseMonad } from '../src/atomic/monad.ts';

// Create a simple category
const simpleCategory = new ConcreteCategory();

// Create some objects
const objA = createCatObject(5, 'A');
const objB = createCatObject(10, 'B');
const objC = createCatObject(15, 'C');

// Add objects to the category
simpleCategory.addObject(objA);
simpleCategory.addObject(objB);
simpleCategory.addObject(objC);

// Create morphisms
const doubleValue = new BaseMorphism(
  objA,
  objB,
  (x: number) => x * 2
);

const addFive = new BaseMorphism(
  objA,
  objC,
  (x: number) => x + 5
);

// Add morphisms to the category
simpleCategory.addMorphism(doubleValue);
simpleCategory.addMorphism(addFive);

// Create a "Maybe" monad (similar to Option/Maybe in functional programming)
// First, create the endofunctor

// The Maybe functor wraps values in an object with a "hasValue" flag
type Maybe<T> = { hasValue: boolean; value: T | null };

const maybeObjectMap = (obj: any) => {
  return createCatObject(
    { hasValue: true, value: obj.value } as Maybe<any>,
    `Maybe(${obj.id.name})`
  );
};

const maybeMorphismMap = (morphism: any) => {
  return new BaseMorphism(
    maybeObjectMap(morphism.source),
    maybeObjectMap(morphism.target),
    (maybeX: Maybe<any>) => {
      if (!maybeX.hasValue) {
        return { hasValue: false, value: null };
      }
      try {
        const result = morphism.apply(maybeX.value);
        return { hasValue: true, value: result };
      } catch (error) {
        return { hasValue: false, value: null };
      }
    }
  );
};

const maybeFunctor = new BaseFunctor(
  simpleCategory,
  simpleCategory,
  maybeObjectMap,
  maybeMorphismMap
);

// Now create the unit and join natural transformations for the Maybe monad

const maybeUnit = (obj: any) => {
  return createCatObject(
    { hasValue: true, value: obj.value } as Maybe<any>,
    `Unit(${obj.id.name})`
  );
};

const maybeJoin = (obj: any) => {
  const maybeValue = obj.value as Maybe<Maybe<any>>;
  if (!maybeValue.hasValue || !maybeValue.value?.hasValue) {
    return createCatObject(
      { hasValue: false, value: null } as Maybe<any>,
      `Join(${obj.id.name})`
    );
  }
  return createCatObject(
    { hasValue: true, value: maybeValue.value.value } as Maybe<any>,
    `Join(${obj.id.name})`
  );
};

// Create the Maybe monad
const maybeMonad = new BaseMonad(
  maybeFunctor,
  simpleCategory,
  maybeUnit,
  maybeJoin
);

// Usage example
function demonstrateMonad() {
  console.log('Maybe Monad Example:');

  // Create some values
  const value = createCatObject(5, 'value');
  const nothing = createCatObject({ hasValue: false, value: null } as Maybe<number>, 'nothing');

  // Apply unit to lift a value into the monad
  const maybeValue = maybeMonad.unit(value);
  console.log('unit(5):', maybeValue.value);

  // Define some functions that work with Maybe values
  const safeDivide = (divisor: number) => (x: number) => {
    if (divisor === 0) {
      return createCatObject({ hasValue: false, value: null } as Maybe<number>, 'divByZero');
    }
    return createCatObject({ hasValue: true, value: x / divisor } as Maybe<number>, `div(${x}/${divisor})`);
  };

  const safeAdd = (addend: number) => (x: number) => {
    return createCatObject({ hasValue: true, value: x + addend } as Maybe<number>, `add(${x}+${addend})`);
  };

  // Use bind to chain operations
  console.log('Chaining operations with bind:');

  // Successful chain: 5 / 2 + 3 = 5.5
  const result1 = maybeMonad.bind(maybeValue, safeDivide(2));
  console.log('5 / 2:', result1.value);

  // This would be the full chain, but our bind implementation is complex
  // const result2 = maybeMonad.bind(result1, safeAdd(3));
  // console.log('(5 / 2) + 3:', result2.value);

  // Failed operation: 5 / 0 = Nothing
  const result3 = maybeMonad.bind(maybeValue, safeDivide(0));
  console.log('5 / 0:', result3.value);

  // Demonstrate join
  const nestedMaybe = createCatObject(
    { hasValue: true, value: { hasValue: true, value: 42 } } as Maybe<Maybe<number>>,
    'nestedMaybe'
  );

  const flattened = maybeMonad.join(nestedMaybe);
  console.log('join(Just(Just(42))):', flattened.value);

  const nestedNothing = createCatObject(
    { hasValue: true, value: { hasValue: false, value: null } } as Maybe<Maybe<number>>,
    'nestedNothing'
  );

  const flattenedNothing = maybeMonad.join(nestedNothing);
  console.log('join(Just(Nothing)):', flattenedNothing.value);
}

// Export for use in other examples
export {
  simpleCategory,
  objA, objB, objC,
  doubleValue, addFive,
  maybeMonad,
  demonstrateMonad
};
