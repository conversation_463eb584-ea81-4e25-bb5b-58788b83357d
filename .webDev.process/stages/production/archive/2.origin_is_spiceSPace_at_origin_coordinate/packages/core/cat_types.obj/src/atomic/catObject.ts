/**
 * Category Object implementation
 *
 * This file implements the CatObject type, which represents an object in a category.
 */

import { CatId, createCatId } from './catId';

// @CatObject
/**
 * Represents an object in a category
 * @typeParam T - Type of the value contained in the object
 */
interface CatObject<T = any> {
  /**
   * Unique identifier for the object
   */
  readonly id: CatId;

  /**
   * Value contained in the object
   */
  readonly value: T;
}

// @createCatObject
/**
 * Creates a new category object
 * @param value - Value to be contained in the object
 * @param name - Optional name for debugging
 * @returns A new CatObject instance
 */
function createCatObject<T>(value: T, name?: string): CatObject<T> {
  return {
    id: createCatId(name),
    value
  };
}

export { CatObject, createCatObject };
