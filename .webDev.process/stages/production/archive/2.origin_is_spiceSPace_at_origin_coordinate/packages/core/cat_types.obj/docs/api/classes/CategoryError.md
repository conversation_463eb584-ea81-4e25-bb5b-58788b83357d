[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / CategoryError

# Class: CategoryError

Defined in: [atomic/errors/cat.ts:12](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/cat.ts#L12)

Represents an error that occurs in a category

## Extends

- `Error`

## Constructors

### new CategoryError()

> **new CategoryError**(`message`): [`CategoryError`](CategoryError.md)

Defined in: [atomic/errors/cat.ts:17](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/errors/cat.ts#L17)

Creates a new category error

#### Parameters

##### message

`string`

Error message

#### Returns

[`CategoryError`](CategoryError.md)

#### Overrides

`Error.constructor`
