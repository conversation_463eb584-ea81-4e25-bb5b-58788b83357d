/**
 * @module Functors/Monad
 */
/**
 * ## Monads
 *
 * A monad is an endofunctor with additional structure (unit and join operations).
 *
 * ### Web Development Applications:
 * - **Promises**: Handle asynchronous operations
 * - **Optional/Maybe**: Handle potentially missing values
 * - **State Management**: Encapsulate and transform application state
 *
 * ### Example:
 * ```typescript
 * // Maybe monad
 * interface Maybe<T> {
 *   map: <U>(f: (value: T) => U) => Maybe<U>;
 *   flatMap: <U>(f: (value: T) => Maybe<U>) => Maybe<U>;
 *   getOrElse: (defaultValue: T) => T;
 * }
 *
 * // Creating a Maybe
 * const just = <T>(value: T): Maybe<T> => ({
 *   map: <U>(f: (value: T) => U) => just(f(value)),
 *   flatMap: <U>(f: (value: T) => Maybe<U>) => f(value),
 *   getOrElse: () => value
 * });
 *
 * const nothing = <T>(): Maybe<T> => ({
 *   map: () => nothing<any>(),
 *   flatMap: () => nothing<any>(),
 *   getOrElse: (defaultValue: T) => defaultValue
 * });
 * ```
 *
 * ### Monad Laws:
 * 1. Left Identity: `return a >>= f ≡ f a`
 * 2. Right Identity: `m >>= return ≡ m`
 * 3. Associativity: `(m >>= f) >>= g ≡ m >>= (\x -> f x >>= g)`
 */
/**
 * @module Functors/Monad/Tests
 */
/**
 * ## Monad Functor Tests
 *
 * Test specification for the Monad functor implementation.
 *
 * ### Interface Tests:
 * - Verify `unit` (return) creates a monad from a value
 * - Verify `bind` (>>=) applies a function to a monadic value
 * - Verify `join` flattens nested monads
 *
 * ### Laws Validation:
 * - Test Left Identity: `unit(a).bind(f) = f(a)`
 * - Test Right Identity: `m.bind(unit) = m`
 * - Test Associativity: `m.bind(f).bind(g) = m.bind(x => f(x).bind(g))`
 *
 * ### Functor Tests:
 * - Test mapping between monad types
 * - Test preservation of monad behavior after mapping
 * - Test functor laws (identity, composition)
 *
 * ### Utility Tests:
 * - Test monad creation from values
 * - Test monad composition
 * - Test error handling and propagation
 *
 * ### Specific Monad Tests:
 * - Test Maybe monad with present and absent values
 * - Test Either monad with success and failure cases
 * - Test Promise monad with resolved and rejected states
 */ 
//# sourceMappingURL=monad.d.ts.map