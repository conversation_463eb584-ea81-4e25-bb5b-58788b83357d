import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
    test: {
        environment: 'node',
        include: ['**/*.test.ts', '*.test.ts'], // Add *.test.ts to catch files in the root
        globals: false,
        root: __dirname,
        isolate: true
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, './'),
            '@errors': resolve(__dirname, './atomic/errors'),
            '@error': resolve(__dirname, '../error')
        }
    }
});