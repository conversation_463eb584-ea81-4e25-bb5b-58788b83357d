---
title: "API Reference"
description: "Complete API reference for the cat_types package"
---

# API Reference

This is the complete API reference for the `cat_types` package. It provides detailed information about all classes, interfaces, and functions in the package.

## How to Use This Reference

The API reference is organized into the following sections:

- **Classes**: Implementation classes like `BaseMorphism`, `BaseFunctor`, etc.
- **Interfaces**: Type definitions like `Category`, `Functor`, `Monad`, etc.
- **Functions**: Utility functions like `createCatObject`, `prop`, etc.

Each entry includes:
- Type signatures
- Parameter descriptions
- Return value descriptions
- Examples (where applicable)

## Core Components

### Categories

Categories are the fundamental structure in category theory. They consist of objects and morphisms between those objects.

- [Category](./interfaces/Category.md) - Interface for categories
- [ConcreteCategory](./classes/ConcreteCategory.md) - Implementation of the Category interface

### Objects

Objects are the nodes in a category. They represent data types or values.

- [CatObject](./interfaces/CatObject.md) - Interface for category objects
- [createCatObject](./functions/createCatObject.md) - Function to create category objects

### Morphisms

Morphisms are the arrows in a category. They represent transformations between objects.

- [Morphism](./interfaces/Morphism.md) - Interface for morphisms
- [BaseMorphism](./classes/BaseMorphism.md) - Implementation of the Morphism interface

### Functors

Functors map between categories, preserving structure.

- [Functor](./interfaces/Functor.md) - Interface for functors
- [BaseFunctor](./classes/BaseFunctor.md) - Implementation of the Functor interface

### Monads

Monads are a special kind of functor with additional structure.

- [Monad](./interfaces/Monad.md) - Interface for monads
- [BaseMonad](./classes/BaseMonad.md) - Implementation of the Monad interface

### Lenses

Lenses focus on parts of data structures, allowing you to view and update them.

- [Lens](./interfaces/Lens.md) - Interface for lenses
- [createLens](./functions/createLens.md) - Function to create lenses
- [prop](./functions/prop.md) - Function to create a lens for an object property
- [composeLens](./functions/composeLens.md) - Function to compose lenses
- [LensFunctor](./classes/LensFunctor.md) - Functor for mapping between lens categories

### Zippers

Zippers provide a way to navigate and edit tree-like data structures.

- [Zipper](./interfaces/Zipper.md) - Interface for zippers
- [ZipperContext](./interfaces/ZipperContext.md) - Context for zippers
- [createListZipper](./functions/createListZipper.md) - Function to create a zipper for lists
- [createTreeZipper](./functions/createTreeZipper.md) - Function to create a zipper for trees
- [ZipperFunctor](./classes/ZipperFunctor.md) - Functor for mapping between zipper categories

### Errors

Error classes for different types of errors.

- [CategoryError](./classes/CategoryError.md) - Errors related to categories
- [MorphismError](./classes/MorphismError.md) - Errors related to morphisms
- [FunctorError](./classes/FunctorError.md) - Errors related to functors
- [MonadError](./classes/MonadError.md) - Errors related to monads

## Common Usage Patterns

### Creating and Using Categories

```typescript
import { ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create a category
const category = new ConcreteCategory();

// Create objects
const stringObj = createCatObject('', 'String');
const numberObj = createCatObject(0, 'Number');

// Add objects to the category
category.addObject(stringObj);
category.addObject(numberObj);

// Create a morphism
const stringToNumber = new BaseMorphism(
  stringObj,
  numberObj,
  (s: string) => parseFloat(s)
);

// Add the morphism to the category
category.addMorphism(stringToNumber);

// Use the morphism
const result = stringToNumber.apply('42'); // 42
```

### Working with Lenses

```typescript
import { createLens, prop, composeLens } from '@future/cat_types';

// Create lenses
const userLens = prop('user');
const nameLens = prop('name');
const userNameLens = composeLens(userLens, nameLens);

// Use lenses
const state = { user: { name: 'John', age: 30 } };
const name = userNameLens.get(state); // 'John'
const newState = userNameLens.set('Jane', state);
// { user: { name: 'Jane', age: 30 } }
```

### Creating and Using Functors

```typescript
import { BaseFunctor, ConcreteCategory, createCatObject, BaseMorphism } from '@future/cat_types';

// Create source and target categories
const sourceCategory = new ConcreteCategory();
const targetCategory = new ConcreteCategory();

// Create a functor
const functor = new BaseFunctor(
  sourceCategory,
  targetCategory,
  // Object mapping function
  (obj) => {
    // Map objects from source to target
    return targetObj;
  },
  // Morphism mapping function
  (morphism) => {
    // Map morphisms from source to target
    return targetMorphism;
  }
);

// Map an object
const mappedObj = functor.mapObject(sourceObj);

// Map a morphism
const mappedMorphism = functor.mapMorphism(sourceMorphism);
```

### Creating and Using Monads

```typescript
import { BaseMonad, BaseFunctor, ConcreteCategory, createCatObject } from '@future/cat_types';

// Create a monad
const monad = new BaseMonad(
  functor,
  category,
  // Unit function (lifts a value into the monad)
  (obj) => {
    // Lift the value into the monad
    return monadicObj;
  },
  // Join function (flattens nested monadic values)
  (obj) => {
    // Flatten the nested monadic value
    return flattenedObj;
  }
);

// Lift a value into the monad
const monadicValue = monad.unit(value);

// Flatten a nested monadic value
const flattenedValue = monad.join(nestedValue);

// Bind a function to a monadic value
const result = monad.bind(monadicValue, fn);
```

## Further Reading

For more detailed information on how to use these components, check out:

- [Quick Start Guide](../usage/quick-start.md)
- [Practical Web Dev Guide](../usage/practical-web-dev-guide.md)
- [Category Theory Concepts](../usage/category-theory-concepts.md)
- [Examples](../examples/index.md)
