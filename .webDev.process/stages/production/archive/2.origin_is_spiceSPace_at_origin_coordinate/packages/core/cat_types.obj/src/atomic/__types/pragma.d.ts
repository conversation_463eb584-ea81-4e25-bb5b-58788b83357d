/**
 * Type declarations for the pragma system
 * 
 * This file provides type declarations for the implicit dependencies that are
 * available through the pragma system.
 */

// Declare global symbols that are available through the pragma system
declare global {
  // CatId related
  const createCatId: (name?: string) => CatId;
  interface CatId {
    readonly id: symbol;
    readonly name?: string;
  }
  
  // CatObject related
  const createCatObject: <T>(value: T, name?: string) => CatObject<T>;
  interface CatObject<T = any> {
    readonly id: CatId;
    readonly value: T;
  }
  
  // Morphism related
  interface Morphism<O extends CatObject> {
    source: O;
    target: O;
    apply(input: any): any;
  }
  class BaseMorphism<O extends CatObject> implements Morphism<O> {
    constructor(source: O, target: O, transform: (input: any) => any);
    source: O;
    target: O;
    apply(input: any): any;
  }
  
  // Category related
  interface Category<O extends CatObject, M extends Morphism<O>> {
    id(obj: O): M;
    compose(f: M, g: M): M;
    validateLaws(): boolean;
    addObject(obj: O): void;
    addMorphism(morphism: M): void;
    getObjects(): Set<O>;
    getMorphisms(): Map<string, M>;
  }
  class ConcreteCategory<O extends CatObject = CatObject, M extends Morphism<O> = Morphism<O>> implements Category<O, M> {
    constructor();
    id(obj: O): M;
    compose(f: M, g: M): M;
    validateLaws(): boolean;
    addObject(obj: O): void;
    addMorphism(morphism: M): void;
    getObjects(): Set<O>;
    getMorphisms(): Map<string, M>;
  }
  
  // Functor related
  interface Functor<S extends CatObject, SM extends Morphism<S>, T extends CatObject, TM extends Morphism<T>> {
    readonly source: Category<S, SM>;
    readonly target: Category<T, TM>;
    mapObject(obj: S): T;
    mapMorphism(morphism: SM): TM;
    validateLaws(): boolean;
  }
  class BaseFunctor<S extends CatObject, SM extends Morphism<S>, T extends CatObject, TM extends Morphism<T>> implements Functor<S, SM, T, TM> {
    constructor(source: Category<S, SM>, target: Category<T, TM>, objectMap: (obj: S) => T, morphismMap: (morphism: SM) => TM);
    readonly source: Category<S, SM>;
    readonly target: Category<T, TM>;
    mapObject(obj: S): T;
    mapMorphism(morphism: SM): TM;
    validateLaws(): boolean;
  }
  
  // Monad related
  interface Monad<C extends CatObject, CM extends Morphism<C>> {
    readonly functor: Functor<C, CM, C, CM>;
    readonly category: Category<C, CM>;
    unit(obj: C): C;
    join(obj: C): C;
    bind(obj: C, f: (value: any) => C): C;
    validateLaws(): boolean;
  }
  class BaseMonad<C extends CatObject, CM extends Morphism<C>> implements Monad<C, CM> {
    constructor(functor: Functor<C, CM, C, CM>, category: Category<C, CM>, unit: (obj: C) => C, join: (obj: C) => C);
    readonly functor: Functor<C, CM, C, CM>;
    readonly category: Category<C, CM>;
    unit(obj: C): C;
    join(obj: C): C;
    bind(obj: C, f: (value: any) => C): C;
    validateLaws(): boolean;
  }
}

// Export an empty object to make this a module
export {};
