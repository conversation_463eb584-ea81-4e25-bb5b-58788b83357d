/**
 * Functor error implementation
 *
 * This file implements the FunctorError type, which represents an error that
 * occurs in a functor.
 */

// @FunctorError
/**
 * Represents an error that occurs in a functor
 */
export class FunctorError extends Error {
  /**
   * Creates a new functor error
   * @param message - Error message
   */
  constructor(message: string) {
    super(message);
    this.name = 'FunctorError';
  }
}
