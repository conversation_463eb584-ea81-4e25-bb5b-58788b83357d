[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / composeLens

# Function: composeLens()

> **composeLens**\<`S`, `A`, `B`\>(`outer`, `inner`): [`Lens`](../interfaces/Lens.md)\<`S`, `B`\>

Defined in: [functors/lens.fr.ts:111](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/lens.fr.ts#L111)

Compose two lenses to create a lens that focuses deeper

## Type Parameters

• **S**

• **A**

• **B**

## Parameters

### outer

[`Lens`](../interfaces/Lens.md)\<`S`, `A`\>

The outer lens focusing on a structure

### inner

[`Lens`](../interfaces/Lens.md)\<`A`, `B`\>

The inner lens focusing on a part of the outer focus

## Returns

[`Lens`](../interfaces/Lens.md)\<`S`, `B`\>

A composed lens that focuses on the inner part through the outer structure
