import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
export default defineConfig({
    test: {
        environment: 'node',
        include: ['**/*.test.ts', '*.test.ts'],
        globals: false,
        root: __dirname,
        isolate: true
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, './src')
        }
    }
});
//# sourceMappingURL=vitest.config.js.map