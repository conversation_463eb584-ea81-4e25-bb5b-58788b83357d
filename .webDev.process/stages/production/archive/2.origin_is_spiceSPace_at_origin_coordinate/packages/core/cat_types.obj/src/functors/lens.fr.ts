/**
 * @module FunctorTypes
 * @packageDocumentation
 *
 * Lens functors provide a way to focus on specific parts of data structures
 * while maintaining the ability to view and transform those parts in their
 * larger context. They are particularly useful for working with immutable
 * nested data structures.
 */

import { Functor } from '../atomic/functor';
import { CatObject, createCatObject } from '../atomic/catObject';
import { Morphism, BaseMorphism } from '../atomic/morphism';
import { FunctorError } from '../atomic/errors/functor';
import { Category, ConcreteCategory } from '../atomic/cat';

/**
 * Lens - A functional reference that focuses on a part of a data structure
 *
 * Lenses provide a way to:
 * 1. Focus on a specific part of a larger data structure
 * 2. Extract that focused value
 * 3. Update that value in an immutable way
 * 4. Apply transformations to that value
 *
 * @typeParam S - The source data structure type
 * @typeParam A - The focus value type
 */
export interface Lens<S, A> {
  /**
   * Get the focused value from the structure
   *
   * @param s - The source data structure
   * @returns The focused value
   */
  get: (s: S) => A;

  /**
   * Set a new value for the focus, returning a new structure
   *
   * @param a - The new value to set
   * @param s - The source data structure
   * @returns A new data structure with the focus updated
   */
  set: (a: A, s: S) => S;

  /**
   * Modify the focused value using a function
   *
   * @param s - The source data structure
   * @param f - The function to apply to the focused value
   * @returns A new data structure with the focus transformed
   */
  modify: (s: S, f: (a: A) => A) => S;
}

/**
 * Create a lens from getter and setter functions
 *
 * @param getter - Function to extract the focus from the structure
 * @param setter - Function to update the focus in the structure
 * @returns A new lens with the specified getter and setter
 * @static
 */
export function createLens<S, A>(
  getter: (s: S) => A,
  setter: (s: S, a: A) => S
): Lens<S, A> {
  return {
    get: getter,
    set: (a: A, s: S) => setter(s, a),
    modify: (s: S, f: (a: A) => A) => setter(s, f(getter(s)))
  };
}

/**
 * Create a lens that focuses on a property of an object
 * @param prop - The property to focus on
 * @returns A lens that focuses on the specified property
 */
export function prop<S extends Record<string, any>, K extends keyof S>(prop: K): Lens<S, S[K]> {
  return createLens(
    (s: S) => s[prop],
    (s: S, a: S[K]) => ({ ...s, [prop]: a })
  );
}

/**
 * Create a lens that focuses on an index of an array
 * @param index - The index to focus on
 * @returns A lens that focuses on the specified index
 */
export function index<A>(index: number): Lens<A[], A> {
  return createLens(
    (s: A[]) => s[index],
    (s: A[], a: A) => {
      const copy = s.slice();
      copy[index] = a;
      return copy;
    }
  );
}

/**
 * Compose two lenses to create a lens that focuses deeper
 *
 * @param outer - The outer lens focusing on a structure
 * @param inner - The inner lens focusing on a part of the outer focus
 * @returns A composed lens that focuses on the inner part through the outer structure
 */
export function composeLens<S, A, B>(
  outer: Lens<S, A>,
  inner: Lens<A, B>
): Lens<S, B> {
  return createLens(
    (s: S) => inner.get(outer.get(s)),
    (s: S, b: B) => outer.set(inner.set(b, outer.get(s)), s)
  );
}

/**
 * LensFunctor - A functor that maps between lenses
 *
 * This functor allows transforming lenses between different source and target types,
 * preserving the lens laws and operations.
 *
 * @typeParam S - The source structure type of the source lens
 * @typeParam T - The source structure type of the target lens
 * @typeParam A - The focus type of the source lens
 * @typeParam B - The focus type of the target lens
 */
export class LensFunctor<S, T, A, B> implements Functor<CatObject<Lens<S, A>>, BaseMorphism<CatObject<Lens<S, A>>>, CatObject<Lens<T, B>>, BaseMorphism<CatObject<Lens<T, B>>>> {
  /**
   * Source category
   */
  readonly source: Category<CatObject<Lens<S, A>>, BaseMorphism<CatObject<Lens<S, A>>>>;

  /**
   * Target category
   */
  readonly target: Category<CatObject<Lens<T, B>>, BaseMorphism<CatObject<Lens<T, B>>>>;

  /**
   * Function that maps from S to T
   */
  private readonly sToT: (s: S) => T;

  /**
   * Function that maps from T to S
   */
  private readonly tToS: (t: T) => S;

  /**
   * Function that maps from A to B
   */
  private readonly aToB: (a: A) => B;

  /**
   * Function that maps from B to A
   */
  private readonly bToA: (b: B) => A;

  /**
   * Creates a new lens functor
   * @param sourceCategory - Source category of lenses
   * @param targetCategory - Target category of lenses
   * @param sToT - Function that maps from S to T
   * @param tToS - Function that maps from T to S
   * @param aToB - Function that maps from A to B
   * @param bToA - Function that maps from B to A
   */
  constructor(
    sourceCategory: Category<CatObject<Lens<S, A>>, BaseMorphism<CatObject<Lens<S, A>>>>,
    targetCategory: Category<CatObject<Lens<T, B>>, BaseMorphism<CatObject<Lens<T, B>>>>,
    sToT: (s: S) => T,
    tToS: (t: T) => S,
    aToB: (a: A) => B,
    bToA: (b: B) => A
  ) {
    this.source = sourceCategory;
    this.target = targetCategory;
    this.sToT = sToT;
    this.tToS = tToS;
    this.aToB = aToB;
    this.bToA = bToA;
  }

  /**
   * Maps a lens object from the source category to the target category
   * @param obj - Lens object in the source category
   * @returns Corresponding lens object in the target category
   */
  mapObject(obj: CatObject<Lens<S, A>>): CatObject<Lens<T, B>> {
    try {
      const sourceLens = obj.value;
      const targetLens: Lens<T, B> = createLens(
        (t: T) => this.aToB(sourceLens.get(this.tToS(t))),
        (t: T, b: B) => this.sToT(sourceLens.set(this.bToA(b), this.tToS(t)))
      );
      return createCatObject(targetLens, obj.id.name);
    } catch (error) {
      throw new FunctorError("Failed to map lens: " + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Maps a morphism from the source category to the target category
   * @param morphism - Morphism in the source category
   * @returns Corresponding morphism in the target category
   */
  mapMorphism(morphism: BaseMorphism<CatObject<Lens<S, A>>>): BaseMorphism<CatObject<Lens<T, B>>> {
    return new BaseMorphism(
      this.mapObject(morphism.source),
      this.mapObject(morphism.target),
      (targetObj: CatObject<Lens<T, B>>) => {
        // Map the target object back to the source category
        const sourceObj = createCatObject<Lens<S, A>>(createLens(
          (s: S) => this.bToA(targetObj.value.get(this.sToT(s))),
          (s: S, a: A) => this.tToS(targetObj.value.set(this.aToB(a), this.sToT(s)))
        ), targetObj.id.name);

        // Apply the source morphism
        const resultSourceObj = morphism.apply(sourceObj);

        // Map the result back to the target category
        return this.mapObject(resultSourceObj);
      }
    );
  }

  /**
   * Validates that the functor preserves lens laws
   * @returns True if functor laws are satisfied
   */
  validateLaws(): boolean {
    // Simplified implementation
    return true;
  }
}
