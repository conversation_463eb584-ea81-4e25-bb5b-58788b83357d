{"$schema": "https://typedoc.org/schema.json", "entryPoints": ["./src/index.ts"], "out": "./docs/api", "name": "Cat Types API Reference", "includeVersion": true, "excludePrivate": true, "excludeProtected": false, "excludeExternals": true, "readme": "none", "plugin": ["typedoc-plugin-markdown"], "theme": "markdown", "categorizeByGroup": true, "categoryOrder": ["Category Theory", "Categories", "Objects", "Morphisms", "Functors", "Monads", "*"]}