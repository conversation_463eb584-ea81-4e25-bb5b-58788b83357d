[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / Zipper

# Interface: Zipper\<T\>

Defined in: [functors/zipper.fr.ts:29](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L29)

Zipper - A structure that allows navigation and local editing
with a focus point and surrounding context

Zippers provide a way to:
1. Focus on a specific element in a data structure
2. Navigate to adjacent elements (up, down, left, right)
3. Modify the focused element
4. Reconstruct the entire data structure with modifications

## Type Parameters

• **T**

The type of elements in the structure

## Properties

### context

> **context**: [`ZipperContext`](ZipperContext.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:38](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L38)

The context (path) to the focused element

***

### focus

> **focus**: `T`

Defined in: [functors/zipper.fr.ts:33](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L33)

The currently focused element

## Methods

### down()

> **down**(): `null` \| [`Zipper`](Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:50](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L50)

Move the focus down in the structure

#### Returns

`null` \| [`Zipper`](Zipper.md)\<`T`\>

A new zipper with the focus moved down, or null if at the bottom

***

### left()

> **left**(): `null` \| [`Zipper`](Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:56](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L56)

Move the focus left in the structure

#### Returns

`null` \| [`Zipper`](Zipper.md)\<`T`\>

A new zipper with the focus moved left, or null if at the leftmost

***

### reconstruct()

> **reconstruct**(): `T`

Defined in: [functors/zipper.fr.ts:75](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L75)

Reconstruct the entire data structure with any modifications

#### Returns

`T`

The reconstructed data structure

***

### right()

> **right**(): `null` \| [`Zipper`](Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:62](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L62)

Move the focus right in the structure

#### Returns

`null` \| [`Zipper`](Zipper.md)\<`T`\>

A new zipper with the focus moved right, or null if at the rightmost

***

### up()

> **up**(): `null` \| [`Zipper`](Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:44](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L44)

Move the focus up in the structure

#### Returns

`null` \| [`Zipper`](Zipper.md)\<`T`\>

A new zipper with the focus moved up, or null if at the top

***

### update()

> **update**(`newFocus`): [`Zipper`](Zipper.md)\<`T`\>

Defined in: [functors/zipper.fr.ts:69](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L69)

Update the focused element

#### Parameters

##### newFocus

`T`

The new value for the focused element

#### Returns

[`Zipper`](Zipper.md)\<`T`\>

A new zipper with the focus updated
