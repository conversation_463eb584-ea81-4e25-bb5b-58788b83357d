---
title: "Maybe Monad Example"
description: "A practical example of using the Maybe monad for handling nullable values"
---

# Maybe Monad Example

This example demonstrates how to create and use a Maybe monad for handling nullable values in a type-safe way.

## The Maybe Monad

The Maybe monad is a design pattern that represents computations which might fail or return a null value. It has two cases:
- `Nothing`: Represents a failed computation or null value
- `Just a`: Represents a successful computation with result `a`

Using the Maybe monad allows you to chain operations that might fail without having to check for null values at each step.

## Implementation

```typescript
import { 
  ConcreteCategory, 
  createCatObject, 
  BaseMorphism, 
  BaseFunctor, 
  BaseMonad 
} from '@future/cat_types';

// Define the Maybe type
type Maybe<T> = { tag: 'Nothing' } | { tag: 'Just', value: T };

// Create a category for the Maybe monad
const maybeCategory = new ConcreteCategory<Maybe<any>, BaseMorphism<Maybe<any>>>();

// Create the Nothing and Just objects
const nothing = createCatObject<Maybe<any>>({ tag: 'Nothing' }, 'Nothing');
const just = createCatObject<Maybe<any>>({ tag: 'Just', value: null }, 'Just');

// Add objects to the category
maybeCategory.addObject(nothing);
maybeCategory.addObject(just);

// Create the Maybe functor
const maybeFunctor = new BaseFunctor(
  maybeCategory,
  maybeCategory,
  // Object mapping function
  (obj) => {
    const maybeValue = obj.value as Maybe<any>;
    if (maybeValue.tag === 'Nothing') return nothing;
    return createCatObject<Maybe<any>>({ tag: 'Just', value: maybeValue.value }, 'Just');
  },
  // Morphism mapping function
  (morphism) => {
    return new BaseMorphism(
      morphism.source,
      morphism.target,
      (x: Maybe<any>) => {
        if (x.tag === 'Nothing') return { tag: 'Nothing' };
        return { tag: 'Just', value: morphism.apply({ tag: 'Just', value: x.value }).value };
      }
    );
  }
);

// Create the Maybe monad
const maybeMonad = new BaseMonad(
  maybeFunctor,
  maybeCategory,
  // Unit function (lifts a value into the monad)
  (obj) => {
    return createCatObject<Maybe<any>>({ tag: 'Just', value: obj.value }, 'Just');
  },
  // Join function (flattens nested monadic values)
  (obj) => {
    const maybeValue = obj.value as Maybe<any>;
    if (maybeValue.tag === 'Nothing') return nothing;
    const innerValue = maybeValue.value as Maybe<any>;
    if (innerValue.tag === 'Nothing') return nothing;
    return createCatObject<Maybe<any>>({ tag: 'Just', value: innerValue.value }, 'Just');
  }
);

// Helper functions for working with the Maybe monad
function just<T>(value: T): Maybe<T> {
  return { tag: 'Just', value };
}

function nothing<T>(): Maybe<T> {
  return { tag: 'Nothing' };
}

function fromNullable<T>(value: T | null | undefined): Maybe<T> {
  if (value === null || value === undefined) return nothing<T>();
  return just(value);
}

function map<A, B>(f: (a: A) => B, ma: Maybe<A>): Maybe<B> {
  if (ma.tag === 'Nothing') return nothing<B>();
  return just(f(ma.value));
}

function flatMap<A, B>(f: (a: A) => Maybe<B>, ma: Maybe<A>): Maybe<B> {
  if (ma.tag === 'Nothing') return nothing<B>();
  return f(ma.value);
}

// Example usage
function safeDivide(a: number, b: number): Maybe<number> {
  if (b === 0) return nothing<number>();
  return just(a / b);
}

function safeRoot(a: number): Maybe<number> {
  if (a < 0) return nothing<number>();
  return just(Math.sqrt(a));
}

// Chain operations that might fail
function calculateComplexFormula(a: number, b: number, c: number): Maybe<number> {
  return flatMap(
    x => flatMap(
      y => safeRoot(y),
      safeDivide(x, c)
    ),
    safeDivide(a, b)
  );
}

// Test the complex formula
console.log(calculateComplexFormula(10, 2, 5)); // Just 1
console.log(calculateComplexFormula(10, 0, 5)); // Nothing
console.log(calculateComplexFormula(10, 2, 0)); // Nothing
console.log(calculateComplexFormula(-10, 2, 5)); // Nothing
```

## Explanation

1. We define a `Maybe<T>` type with two variants: `Nothing` and `Just`.
2. We create a category for the Maybe monad with the `Nothing` and `Just` objects.
3. We create a functor that maps between Maybe values, preserving the structure.
4. We create a monad with `unit` and `join` operations:
   - `unit` lifts a value into the monad by wrapping it in a `Just`.
   - `join` flattens nested Maybe values.
5. We define helper functions for working with the Maybe monad:
   - `just` creates a `Just` value.
   - `nothing` creates a `Nothing` value.
   - `fromNullable` converts a potentially null value to a Maybe.
   - `map` applies a function to a Maybe value if it's a `Just`.
   - `flatMap` applies a function that returns a Maybe to a Maybe value.
6. We define functions that might fail and return Maybe values:
   - `safeDivide` returns `Nothing` if dividing by zero.
   - `safeRoot` returns `Nothing` if taking the square root of a negative number.
7. We chain these operations together in a complex formula, handling all potential failures gracefully.

## Benefits of the Maybe Monad

Using the Maybe monad provides several benefits:

1. **Type Safety**: The type system ensures that you handle both the `Just` and `Nothing` cases.
2. **No Null Checks**: You don't need to check for null values at each step of a computation.
3. **Composability**: You can easily compose operations that might fail.
4. **Readability**: The code clearly expresses the intent of handling potential failures.

## Next Steps

Try implementing other monads like the Either monad for error handling or the List monad for computations with multiple results.
