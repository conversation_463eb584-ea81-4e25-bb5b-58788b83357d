[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / Morphism

# Interface: Morphism\<O\>

Defined in: [atomic/morphism.ts:15](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L15)

Represents a morphism (arrow) between objects in a category

## Type Parameters

• **O** *extends* [`CatObject`](CatObject.md)

Type of objects in the category

## Properties

### source

> **source**: `O`

Defined in: [atomic/morphism.ts:19](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L19)

Source object of the morphism

***

### target

> **target**: `O`

Defined in: [atomic/morphism.ts:24](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L24)

Target object of the morphism

## Methods

### apply()

> **apply**(`input`): `any`

Defined in: [atomic/morphism.ts:31](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/atomic/morphism.ts#L31)

Applies the morphism to an input

#### Parameters

##### input

`any`

Input to apply the morphism to

#### Returns

`any`

Result of applying the morphism
