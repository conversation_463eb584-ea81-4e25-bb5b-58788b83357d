/**
 * Monad error implementation
 *
 * This file implements the MonadError type, which represents an error that
 * occurs in a monad.
 */

// @MonadError
/**
 * Represents an error that occurs in a monad
 */
export class MonadError extends Error {
  /**
   * Creates a new monad error
   * @param message - Error message
   */
  constructor(message: string) {
    super(message);
    this.name = 'MonadError';
  }
}
