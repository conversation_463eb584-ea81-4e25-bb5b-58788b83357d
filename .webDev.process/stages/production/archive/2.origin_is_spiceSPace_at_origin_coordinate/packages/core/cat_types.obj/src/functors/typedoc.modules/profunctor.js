"use strict";
/**
 * @module Functors/Profunctor
 */
/**
 * ## Profunctors
 *
 * A profunctor is contravariant in its first argument and covariant in its second.
 *
 * ### Web Development Applications:
 * - **React Components**: Contravariant in props, covariant in rendered output
 * - **Event Handlers**: Contravariant in event type, covariant in result
 * - **API Endpoints**: Contravariant in request, covariant in response
 *
 * ### Example:
 * ```typescript
 * // Function as a profunctor
 * interface Fn<A, B> {
 *   run: (a: A) => B;
 *   contramap: <C>(f: (c: C) => A) => Fn<C, B>;
 *   map: <C>(f: (b: B) => C) => Fn<A, C>;
 *   dimap: <C, D>(f: (c: C) => A, g: (b: B) => D) => Fn<C, D>;
 * }
 *
 * // Create a function profunctor
 * const createFn = <A, B>(run: (a: A) => B): Fn<A, B> => ({
 *   run,
 *   contramap: <C>(f: (c: C) => A) => createFn<C, B>(c => run(f(c))),
 *   map: <C>(f: (b: B) => C) => createFn<A, C>(a => f(run(a))),
 *   dimap: <C, D>(f: (c: C) => A, g: (b: B) => D) => createFn<C, D>(c => g(run(f(c))))
 * });
 * ```
 *
 * ### Profunctor Laws:
 * 1. Identity: `F.dimap(id, id) = id`
 * 2. Composition: `F.dimap(f, g).dimap(h, i) = F.dimap(x => f(h(x)), y => i(g(y)))`
 */
/**
 * @module Functors/Profunctor/Tests
 */
/**
 * ## Profunctor Tests
 *
 * Test specification for the Profunctor implementation.
 *
 * ### Interface Tests:
 * - Verify `contramap` correctly transforms input types
 * - Verify `map` correctly transforms output types
 * - Verify `dimap` correctly transforms both input and output types
 *
 * ### Laws Validation:
 * - Test Identity Law: `F.dimap(id, id) = id`
 * - Test Composition Law: `F.dimap(f, g).dimap(h, i) = F.dimap(x => f(h(x)), y => i(g(y)))`
 *
 * ### Functor Tests:
 * - Test mapping between profunctor types
 * - Test preservation of profunctor behavior after mapping
 * - Test error handling for invalid mappings
 *
 * ### Utility Tests:
 * - Test profunctor creation
 * - Test composition with other functors
 * - Test with complex transformation functions
 *
 * ### Specific Profunctor Tests:
 * - Test Function profunctor with various input/output types
 * - Test React component as a profunctor
 * - Test API endpoint as a profunctor
 */ 
//# sourceMappingURL=profunctor.js.map