/**
 * @module Functors/Contravariant
 */
/**
 * ## Contravariant Functors
 *
 * A contravariant functor reverses the direction of morphisms.
 *
 * ### Web Development Applications:
 * - **Event Handlers**: Transform event handlers in the opposite direction of UI components
 * - **Validation Functions**: Compose validation rules in reverse
 * - **Comparison Functions**: Transform comparison logic
 *
 * ### Example:
 * ```typescript
 * // Contravariant functor for validation
 * interface Predicate<A> {
 *   test: (a: A) => boolean;
 *   contramap: <B>(f: (b: B) => A) => Predicate<B>;
 * }
 *
 * // Create a predicate
 * const createPredicate = <A>(test: (a: A) => boolean): Predicate<A> => ({
 *   test,
 *   contramap: <B>(f: (b: B) => A) => createPredicate<B>(b => test(f(b)))
 * });
 *
 * // Email validator
 * const isValidEmail = createPredicate<string>(email => email.includes('@'));
 *
 * // User validator (derived from email validator)
 * const hasValidEmail = isValidEmail.contramap<User>(user => user.email);
 * ```
 *
 * ### Contravariant Functor Laws:
 * 1. Identity: `F.contramap(id) = id`
 * 2. Composition: `F.contramap(f).contramap(g) = F.contramap(x => f(g(x)))`
 */
/**
 * @module Functors/Contravariant/Tests
 */
/**
 * ## Contravariant Functor Tests
 *
 * Test specification for the Contravariant functor implementation.
 *
 * ### Interface Tests:
 * - Verify `contramap` correctly transforms input types
 * - Verify original behavior is preserved after transformation
 * - Verify chained transformations work correctly
 *
 * ### Laws Validation:
 * - Test Identity Law: `F.contramap(id) = id`
 * - Test Composition Law: `F.contramap(f).contramap(g) = F.contramap(x => f(g(x)))`
 *
 * ### Functor Tests:
 * - Test mapping between contravariant functor types
 * - Test preservation of contravariant behavior after mapping
 * - Test error handling for invalid mappings
 *
 * ### Utility Tests:
 * - Test contravariant functor creation
 * - Test composition with other functors
 * - Test with complex transformation functions
 *
 * ### Specific Contravariant Tests:
 * - Test Predicate contravariant functor
 * - Test Comparison contravariant functor
 * - Test Validator contravariant functor
 */ 
//# sourceMappingURL=contravariant.d.ts.map