"use strict";
/**
 * @module FunctorTypes
 * @description A comprehensive guide to functor types in category theory and their applications in web development
 */
/**
 * # Functors in Category Theory
 *
 * Functors are mappings between categories that preserve structure. They consist of:
 * - A mapping of objects from one category to another
 * - A mapping of morphisms that preserves composition and identity
 *
 * ## Common Functor Types
 *
 * This guide covers the most important functor types and their practical applications.
 */
/**
 * ## Endofunctors
 *
 * An endofunctor is a functor from a category to itself.
 *
 * ### Web Development Applications:
 * - **Higher-Order Components (HOCs)**: Transform React components while preserving their interface
 * - **Reducers**: Transform application state while maintaining its structure
 * - **Middleware**: Transform request/response objects in a web server
 *
 * ### Example:
 * ```typescript
 * // A simple HOC as an endofunctor
 * const withLogging = (Component) => (props) => {
 *   console.log(`Rendering ${Component.displayName} with props:`, props);
 *   return <Component {...props} />;
 * };
 * ```
 */
/**
 * ## Natural Transformations
 *
 * A natural transformation is a mapping between functors that respects the structure of both functors.
 *
 * ### Web Development Applications:
 * - **Theme Switching**: Transform all UI components from one theme to another
 * - **Responsive Design**: Transform layouts based on screen size
 * - **Internationalization**: Transform text content across an application
 *
 * ### Example:
 * ```typescript
 * // A natural transformation between theme functors
 * const lightToDark = {
 *   button: (LightButton) => DarkButton,
 *   card: (LightCard) => DarkCard,
 *   // Transforms each component type consistently
 * };
 * ```
 */
/**
 * ## Monads
 *
 * A monad is an endofunctor with additional structure (unit and join operations).
 *
 * ### Web Development Applications:
 * - **Promises**: Handle asynchronous operations
 * - **Optional/Maybe**: Handle potentially missing values
 * - **State Management**: Encapsulate and transform application state
 *
 * ### Example:
 * ```typescript
 * // Promise as a monad
 * const unit = (x) => Promise.resolve(x);
 * const join = (promiseOfPromise) => promiseOfPromise.then(p => p);
 * const bind = (promise, fn) => promise.then(fn);
 * ```
 */
/**
 * ## Adjoint Functors
 *
 * Adjoint functors are pairs of functors that are "almost" inverses of each other.
 *
 * ### Web Development Applications:
 * - **Form Handling**: Form creation and validation
 * - **API Communication**: Serialization and deserialization
 * - **Authentication**: Login and session validation
 *
 * ### Example:
 * ```typescript
 * // Serialization and deserialization as adjoint functors
 * const serialize = (data) => JSON.stringify(data);
 * const deserialize = (json) => JSON.parse(json);
 * ```
 */
/**
 * ## Contravariant Functors
 *
 * A contravariant functor reverses the direction of morphisms.
 *
 * ### Web Development Applications:
 * - **Event Handlers**: Transform event handlers in the opposite direction of UI components
 * - **Validation Functions**: Compose validation rules in reverse
 * - **Comparison Functions**: Transform comparison logic
 *
 * ### Example:
 * ```typescript
 * // Contravariant functor for validation
 * const contramap = (validator, transform) =>
 *   (value) => validator(transform(value));
 *
 * const validateEmail = (email) => email.includes('@');
 * const getUserEmail = (user) => user.email;
 *
 * // Creates a user validator from an email validator
 * const validateUserEmail = contramap(validateEmail, getUserEmail);
 * ```
 */
/**
 * ## Profunctors
 *
 * A profunctor is contravariant in its first argument and covariant in its second.
 *
 * ### Web Development Applications:
 * - **React Components**: Contravariant in props, covariant in rendered output
 * - **Event Handlers**: Contravariant in event type, covariant in result
 * - **API Endpoints**: Contravariant in request, covariant in response
 *
 * ### Example:
 * ```typescript
 * // React component as a profunctor
 * type Component<P, R> = (props: P) => R;
 *
 * // Contravariant in props (P), covariant in result (R)
 * const dimap = <A, B, C, D>(
 *   f: (a: A) => B,
 *   g: (c: C) => D,
 *   component: Component<B, C>
 * ): Component<A, D> =>
 *   (props) => g(component(f(props)));
 * ```
 */
/**
 * ## Bifunctors
 *
 * A bifunctor is covariant in two arguments.
 *
 * ### Web Development Applications:
 * - **Pair Types**: Transform both elements of a pair
 * - **Either/Result Types**: Transform both success and error cases
 * - **Form Fields**: Transform both field value and validation state
 *
 * ### Example:
 * ```typescript
 * // Either type as a bifunctor
 * type Either<L, R> = { tag: 'left'; value: L } | { tag: 'right'; value: R };
 *
 * const bimap = <A, B, C, D>(
 *   f: (a: A) => B,
 *   g: (c: C) => D,
 *   either: Either<A, C>
 * ): Either<B, D> =>
 *   either.tag === 'left'
 *     ? { tag: 'left', value: f(either.value) }
 *     : { tag: 'right', value: g(either.value) };
 * ```
 */
/**
 * ## Lenses (Functional References)
 *
 * Lenses combine functors to provide a way to focus on a part of a data structure.
 *
 * ### Web Development Applications:
 * - **Form Field Management**: Focus on specific form fields
 * - **State Management**: Update nested state immutably
 * - **Data Transformation**: Transform specific parts of complex objects
 *
 * ### Example:
 * ```typescript
 * // Simple lens
 * interface Lens<S, A> {
 *   get: (s: S) => A;
 *   set: (a: A, s: S) => S;
 * }
 *
 * // Lens for a user's email
 * const emailLens: Lens<User, string> = {
 *   get: (user) => user.email,
 *   set: (email, user) => ({ ...user, email })
 * };
 * ```
 */
/**
 * ## Practical Applications in Web Development
 *
 * Understanding functors provides powerful abstractions for common web development patterns:
 *
 * 1. **Component Composition**: Use functors to compose UI components in a type-safe way
 * 2. **State Management**: Model state transitions as endofunctors
 * 3. **Form Handling**: Use bifunctors and profunctors for form validation
 * 4. **API Integration**: Use monads for handling asynchronous API calls
 * 5. **Error Handling**: Use bifunctors for consistent error management
 *
 * These categorical abstractions help create more maintainable, composable, and robust web applications.
 */ 
//# sourceMappingURL=functors.js.map