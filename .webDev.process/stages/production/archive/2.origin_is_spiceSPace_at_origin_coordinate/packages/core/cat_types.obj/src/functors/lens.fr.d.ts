/**
 * @module FunctorTypes
 * @packageDocumentation
 *
 * Lens functors provide a way to focus on specific parts of data structures
 * while maintaining the ability to view and transform those parts in their
 * larger context. They are particularly useful for working with immutable
 * nested data structures.
 */
import { Functor } from '../functor.type';
import { Morphism } from '../morphism.type';
/**
 * Lens - A functional reference that focuses on a part of a data structure
 *
 * Lenses provide a way to:
 * 1. Focus on a specific part of a larger data structure
 * 2. Extract that focused value
 * 3. Update that value in an immutable way
 * 4. Apply transformations to that value
 *
 * @typeParam S - The source data structure type
 * @typeParam A - The focus value type
 */
export interface Lens<S, A> {
    /**
     * Get the focused value from the structure
     *
     * @param s - The source data structure
     * @returns The focused value
     */
    get: (s: S) => A;
    /**
     * Set a new value for the focus, returning a new structure
     *
     * @param a - The new value to set
     * @param s - The source data structure
     * @returns A new data structure with the focus updated
     */
    set: (a: A, s: S) => S;
    /**
     * Modify the focused value using a function
     *
     * @param s - The source data structure
     * @param f - The function to apply to the focused value
     * @returns A new data structure with the focus transformed
     */
    modify: (s: S, f: (a: A) => A) => S;
}
/**
 * LensFunctor - A functor that maps between lenses
 *
 * This functor allows transforming lenses between different source and target types,
 * preserving the lens laws and operations.
 *
 * @typeParam S - The source structure type of the source lens
 * @typeParam T - The source structure type of the target lens
 * @typeParam A - The focus type of the source lens
 * @typeParam B - The focus type of the target lens
 */
export declare class LensFunctor<S, T, A, B> implements Functor<Lens<S, A>, Lens<T, B>> {
    private readonly objectMap;
    private readonly morphismMap;
    /**
     * Creates a new lens functor
     *
     * @param objectMap - Function to map from source structure to target structure
     * @param morphismMap - Function to map from source lens to target lens
     */
    constructor(objectMap: (s: S) => T, morphismMap: (lens: Lens<S, A>) => Lens<T, B>);
    /**
     * Maps a source lens to a target lens
     *
     * @param lens - The source lens to map
     * @returns A new lens targeting the mapped structure and focus
     * @throws {FunctorError} If mapping fails
     */
    mapObject(lens: Lens<S, A>): Lens<T, B>;
    /**
     * Maps a morphism between lenses
     *
     * @param morphism - The source morphism to map
     * @returns A new morphism between target lenses
     */
    mapMorphism(morphism: Morphism<Lens<S, A>>): Morphism<Lens<T, B>>;
    /**
     * Validates that the functor preserves lens laws
     *
     * The lens laws are:
     * 1. get(set(a, s)) = a (GetSet)
     * 2. set(get(s), s) = s (SetGet)
     * 3. set(a', set(a, s)) = set(a', s) (SetSet)
     *
     * @returns True if the functor preserves lens laws
     * @throws {Error} Method not implemented
     */
    validateLaws(): boolean;
    /**
     * Create a lens from getter and setter functions
     *
     * @param getter - Function to extract the focus from the structure
     * @param setter - Function to update the focus in the structure
     * @returns A new lens with the specified getter and setter
     * @static
     */
    static from<S, A>(getter: (s: S) => A, setter: (s: S, a: A) => S): Lens<S, A>;
    /**
     * Compose two lenses to create a lens that focuses deeper
     *
     * @param outer - The outer lens focusing on a structure
     * @param inner - The inner lens focusing on a part of the outer focus
     * @returns A composed lens that focuses on the inner part through the outer structure
     * @static
     */
    static compose<S, A, B>(outer: Lens<S, A>, inner: Lens<A, B>): Lens<S, B>;
}
//# sourceMappingURL=lens.fr.d.ts.map