[**Cat Types API Reference v0.1.0**](../README.md)

***

[Cat Types API Reference](../README.md) / createListZipper

# Function: createListZipper()

> **createListZipper**\<`T`\>(`list`, `index`): [`Zipper`](../interfaces/Zipper.md)\<`T`\> \| `null`

Defined in: [functors/zipper.fr.ts:105](https://github.com/shustermandmitry/spicetime-architecture/blob/aabdbd68bf4f26cc2419fac6f578015c180fced8/packages/utils/origin.rfr/cat_types.obj/src/functors/zipper.fr.ts#L105)

Create a zipper for a list

## Type Parameters

• **T**

## Parameters

### list

`T`[]

The list to create a zipper for

### index

`number` = `0`

The index to focus on (default: 0)

## Returns

[`Zipper`](../interfaces/Zipper.md)\<`T`\> \| `null`

A zipper focused on the specified element
