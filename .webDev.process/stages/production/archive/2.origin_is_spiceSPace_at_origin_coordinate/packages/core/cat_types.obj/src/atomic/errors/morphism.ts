/**
 * Morphism error implementation
 *
 * This file implements the MorphismError type, which represents an error that
 * occurs in a morphism.
 */

// @MorphismError
/**
 * Represents an error that occurs in a morphism
 */
export class MorphismError extends Error {
  /**
   * Creates a new morphism error
   * @param message - Error message
   */
  constructor(message: string) {
    super(message);
    this.name = 'MorphismError';
  }
}
