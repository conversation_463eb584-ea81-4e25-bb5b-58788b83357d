# Comprehensive Branch Naming Convention: Mapping Organizational Structure to Spacetime Pragmas

## Core Philosophy

Our branch naming convention is designed to map directly to:
1. Organizational structure
2. Decision authority
3. Development processes
4. Spacetime pragma syntax

This creates a unified system where repository structure reflects both team organization and conceptual relationships.

## Branch Name Structure

```
[optional-prefix@@][path/component@pragma1@pragma2/...]
```

### Components

1. **Optional Prefix**: Categorizes the branch's broad purpose using `@@` delimiter (e.g., `feature@@`)
2. **Path Components**: Reflect the project's structure
3. **Pragmas**: Semantic modifiers attached to path components using `@`

## Why `@@` for Prefixes

The double-at (`@@`) delimiter for prefixes:
1. Clearly distinguishes prefixes from path components
2. Allows safe use in TypeScript scripts and other contexts
3. Maintains consistency with our pragma syntax approach
4. Prevents ambiguity with single `@` used for pragmas

## Spacetime Pragma Syntax Mapping

Our branch naming directly maps to our spacetime pragma syntax, where:

- **Path components** represent nodes in spacetime
- **Pragmas** represent semantic properties of those nodes
- **Hierarchical structure** represents containment and relationship

## Pragma Types

### @meta
Indicates meta-information about a component.
```
utils@meta → .utils (meta-information about utils)
```

### @id
Denotes a specific instance, version, or stage.
```
release@id → A specific release stage
```

### @team
Specifies team ownership.
```
auth@team:frontend → The frontend team owns the auth component
```

### @owner
Specifies individual ownership/responsibility.
```
config@owner:lead → The lead is responsible for config
```

### @stage
Indicates development stage.
```
feature@stage:alpha → Feature in alpha stage
```

## Organizational Structure Mapping

### Team Hierarchy
```
core@team:platform/auth@team:identity/oauth
```
**Meaning**: The platform team owns the core, the identity team (a sub-team) owns auth, and specifically the oauth component.

### Decision Authority
```
api@owner:architect/endpoints@team:backend
```
**Meaning**: The architect has final decision authority on the API, while the backend team implements endpoints.

## Process Stage Representation

```
feature@@payment@stage:design
feature@@payment@stage:implementation
feature@@payment@stage:testing
feature@@payment@stage:release
```
**Meaning**: The payment feature progressing through different stages of development.

## Common Branch Prefix Patterns (Optional)

While pragmas handle most semantic needs, traditional prefixes with `@@` can be used for broad categorization:

- `feature@@` - New functionality
- `bugfix@@` - Bug fixes
- `hotfix@@` - Urgent fixes for production
- `release@@` - Release preparation
- `refactor@@` - Code improvements without functional changes
- `docs@@` - Documentation updates

## Examples in Practice

### Feature Development with Team Ownership
```
feature@@dashboard@team:frontend/widgets@owner:alice
```
**Meaning**: A feature branch for dashboard widgets owned by the frontend team, with Alice as the specific owner.

### Component with Multiple Pragmas
```
api/auth@stage:beta@team:security
```
**Meaning**: The auth API component in beta stage, owned by the security team.

### Release Branch with Version
```
release@@core@id:v2.1
```
**Meaning**: Release branch for version 2.1 of the core.

### Meta-Information Branch
```
docs@@architecture@meta@team:leads
```
**Meaning**: Meta-documentation about architecture, managed by the leads team.

### Team-Specific Development Branch
```
dev@@team:frontend/components/button
```
**Meaning**: Development branch for the button component, owned by the frontend team.

## Conflict Resolution Strategy

This convention establishes a clear hierarchy of decision authority:

1. **Owner-level decisions**: Changes by designated owners (`@owner`) are authoritative
2. **Team-level decisions**: Team (`@team`) decisions apply within their domain
3. **Stage-based priority**: Later stages (`@stage:release`) override earlier ones (`@stage:alpha`)

When merging branches, conflicts are resolved by respecting this hierarchy, eliminating confusion and reducing merge conflicts.

## Integration with Tooling

Our tooling will:

1. Parse branch names according to this convention
2. Visualize the organizational and semantic relationships
3. Automate branch creation with proper naming
4. Enforce naming conventions through hooks
5. Provide insights into team ownership and responsibility
6. Automate conflict resolution based on authority hierarchy

## Benefits

1. **Organizational Clarity**: Repository structure mirrors team structure
2. **Reduced Conflicts**: Clear authority lines simplify merging
3. **Process Visibility**: Development stages are explicit in branch names
4. **Semantic Richness**: Pragmas provide detailed meaning to path components
5. **Unified Conceptual Model**: Branch naming aligns with our spacetime pragma syntax
6. **Scalability**: Convention scales with organization and project growth
7. **Script-Safe**: The `@@` delimiter ensures compatibility with TypeScript and other contexts

## Conclusion

By adopting this branch naming convention, we create a direct mapping between our organizational structure, development processes, and spacetime pragma syntax. This unified approach provides both flexibility for real-world scenarios and structure for maintaining order as the project grows.