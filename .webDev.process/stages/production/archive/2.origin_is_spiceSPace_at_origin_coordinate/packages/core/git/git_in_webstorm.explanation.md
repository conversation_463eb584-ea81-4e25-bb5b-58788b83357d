# Git Model in WebStorm

## What You're Seeing

In the Git tab navigation, WebStorm displays a mix of:

1. **Branches** - Like `docs` in your case
2. **Paths** - Like `packages/utils/git`
3. **Special notations** - Like `folder@packages` which is likely a branch with a path prefix

## The Confusion

The confusion stems from how WebStorm represents Git's flat branch structure in a hierarchical UI:

- Git branches are actually flat (just names with slashes)
- WebStorm tries to display them hierarchically
- This creates visual ambiguity between paths and branch names

## Your Specific Case

When you see:
- `packages/utils/git/docs` - This means `docs` is a branch, and the rest is just the path
- `folder@packages` - This is likely a branch named `folder` with some path context

## Creating Hierarchical Structure

If you want `docs` in utils to be based on `folder@packages`, you have two options:

1. **Git Branch Approach**: Create a new branch from `folder@packages` and name it something like `utils-docs`
   ```
   git checkout folder@packages
   git checkout -b utils-docs
   ```

2. **Custom View Approach**: As you mentioned, you could use your Git package abstraction to create a custom view that organizes branches logically regardless of Git's flat naming

## Recommendation

Since you're already working on a Git abstraction package, the custom view approach is likely better:

1. Keep Git's native flat branch structure
2. Use your abstraction layer to present a logical hierarchy
3. Map the flat branch names to your preferred hierarchical visualization
4. This avoids fighting against Git's model while giving users a more intuitive interface