# Flexible Branch Structure Representation

## The Problem: Git's Flat vs. Hierarchical Structure

Git's branch model is fundamentally flat - branches are simply named references to commits. However, developers naturally think in hierarchical structures that mirror their project organization. This creates tension between:

1. **Flat Branch Names**: Simple but lacks organizational context
2. **Structured Branch Names**: Provides organization but becomes unwieldy

## Common Scenarios and Pain Points

### Scenario 1: The Empty Folder Dilemma
When a folder contains no files (is "leafless"), developers often represent it as a branch:
- `feature/auth` instead of `feature/auth/main`

But when content is later added:
- Someone adds documentation → Now need `feature/auth/docs`
- Someone adds configuration → Now need `feature/auth/config`

This forces painful branch restructuring and coordination across the team.

### Scenario 2: Inconsistent Naming Conventions
Different team members adopt different conventions:
- Some use `feature-auth-docs`
- Others use `feature/auth/docs`
- Still others use `docs@feature/auth`

This inconsistency makes branch management and navigation difficult.

## Our Solution: Unified Project Spacetime

Our approach accommodates both flat and hierarchical representations through a custom abstraction layer:

### Key Principles

1. **Representation Independence**: The underlying branch name format doesn't matter
2. **Inferred Structure**: We automatically infer hierarchical relationships from:
    - Branch naming patterns
    - File paths within branches
    - Commit relationships
    - Branch metadata
3. **Bidirectional Mapping**: Changes in the visual hierarchy can update branch names and vice versa
4. **Flexible Views**: Users can switch between flat and hierarchical views based on preference

### How It Works

Our system maintains a "project spacetime" - a unified representation of your project's structure across branches and time:

1. **Branch Analysis**: We analyze branch names, commit history, and content to infer relationships
2. **Metadata Enhancement**: We store additional metadata about branch relationships
3. **Smart Visualization**: We present branches in a logical hierarchy regardless of naming
4. **Seamless Transitions**: When structure changes (e.g., a leafless folder gets content), we handle the transition automatically

### Examples

#### Example 1: Mixed Branch Naming
Given these branches:
- `feature/auth`
- `docs/auth`
- `config-auth`

Our system presents a unified view:
```
project/
├── feature/
│   └── auth/
├── docs/
│   └── auth/
└── config/
    └── auth/
```

#### Example 2: Evolving Structure
When a leafless folder gets content:

Before:
- Branch: `feature/auth`

After (automatically handled):
- Branch structure:
    - `feature/auth/main`
    - `feature/auth/docs`

The user experience remains consistent despite the underlying branch restructuring.

## Benefits

1. **Reduced Coordination Overhead**: Teams don't need to agree on a single branch naming convention
2. **Flexible Evolution**: Project structure can evolve naturally without painful migrations
3. **Intuitive Navigation**: Developers can navigate in a way that makes sense to them
4. **Consistent Mental Model**: The project spacetime provides a stable way to think about the project regardless of branch naming

## Conclusion

By creating a unified abstraction layer that accommodates both flat and hierarchical branch representations, we eliminate the pain points of Git's branch model while preserving its flexibility. This approach allows teams to work with Git in a way that aligns with their mental model of the project, regardless of how branches are actually named.

The linkages between branches, commits, and files are leveraged to construct a coherent project spacetime that remains consistent even as the project structure evolves.