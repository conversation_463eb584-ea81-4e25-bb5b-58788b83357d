export declare class ProposalManager {
    /**
     * Create a new proposal for stage transition
     */
    createProposal(options: {
        sourceStage: 'ideation' | 'design' | 'production';
        targetStage: 'ideation' | 'design' | 'production';
        path: string;
        changes: any;
    }): string;
    /**
     * Apply an approved proposal
     */
    applyProposal(proposalId: string): void;
    private getTargetPath;
    private formatChanges;
}
//# sourceMappingURL=proposal_manager.d.ts.map