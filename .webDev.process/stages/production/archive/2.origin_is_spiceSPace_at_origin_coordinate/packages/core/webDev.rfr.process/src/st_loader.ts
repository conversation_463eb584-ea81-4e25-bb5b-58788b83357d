/**
 * Loader for .st files that automatically wraps them in production stage
 */
export function loadStFile(filePath: string): any {
    const content = fs.readFileSync(filePath, 'utf8');

    // Wrap content in production stage function
    const wrappedContent = `
    import { stPragma } from '@spicetime/pragma.rfr';
    
    export const module = {
      @stPragma({ stage: 'production' })
      production(scope) {
        // Original file content
        ${content}
        
        // Return modified scope
        return scope;
      }
    };
    
    // Auto-execute for compatibility
    export default module.production({});
  `;

    // Use a transpiler to handle the decorator syntax
    return transpileAndExecute(wrappedContent);
}