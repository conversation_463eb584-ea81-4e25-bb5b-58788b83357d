import { resolveViteConfig } from './vite-resolver';
import { deepMerge } from '../utils/deep-merge';

// Registry of special resolvers for different config types
const resolvers: Record<string, (base: any, extensions: any[]) => any> = {
  'vite': resolveViteConfig,
  // Add more special resolvers as needed
  // 'webpack': resolveWebpackConfig,
  // 'babel': resolveBabelConfig,
};

/**
 * Resolves configuration based on type, using special resolvers when available
 * or falling back to deep merge
 */
export function resolveConfig(configType: string, baseConfig: any, extensions: any[]): any {
  // Use special resolver if available
  if (resolvers[configType]) {
    return resolvers[configType](baseConfig, extensions);
  }

  // Default resolution using deep merge
  let result = { ...baseConfig };
  for (const extension of extensions) {
    result = deepMerge(result, extension);
  }
  return result;
}