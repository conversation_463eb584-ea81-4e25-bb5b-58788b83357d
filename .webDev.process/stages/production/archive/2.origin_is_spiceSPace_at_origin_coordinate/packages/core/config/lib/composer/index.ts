import { resolveConfig } from '../resolvers';
import { Stage, Config } from '../types';
import { findParentConfig } from '../utils/parent-finder';

/**
 * Composes configuration by merging base, stages, parent, and custom extensions
 * using appropriate resolvers for each config type
 */
export function composeConfig(configType: string, stages: Stage[], path: string, customExtensions?: Config): Config {
  // Import base config object
  const baseConfig = require(`../base/${configType}.js`);

  // Collect stage extensions
  const stageExtensions = stages.map(stage => {
    try {
      return require(`../stages/${stage}/${configType}.js`);
    } catch (error) {
      console.warn(`No ${configType} config found for stage ${stage}`);
      return {};
    }
  });

  // Find parent config if it exists
  const parentConfig = findParentConfig(configType, path);

  // Compose in the right order:
  // 1. Start with base
  // 2. Apply stage extensions
  let composedConfig = resolveConfig(configType, baseConfig, stageExtensions);

  // 3. Apply parent config if it exists (parent overrides stages)
  if (parentConfig) {
    composedConfig = resolveConfig(configType, composedConfig, [parentConfig]);
  }

  // 4. Apply custom extensions (custom overrides everything)
  if (customExtensions) {
    composedConfig = resolveConfig(configType, composedConfig, [customExtensions]);
  }

  return composedConfig;
}