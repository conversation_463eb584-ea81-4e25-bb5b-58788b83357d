import { deepMerge } from '../utils/deep-merge';
import type { Plugin } from 'vite';

/**
 * Special resolver for Vite configurations that handles:
 * 1. Plugin composition (avoiding duplicates)
 * 2. Proper merging of resolve.alias
 * 3. Dependency resolution between configs
 */
export function resolveViteConfig(baseConfig: any, extensions: any[]): any {
  let resultConfig = { ...baseConfig };

  // Process each extension
  for (const extension of extensions) {
    // Special handling for plugins array
    if (extension.plugins) {
      resultConfig.plugins = mergePlugins(resultConfig.plugins || [], extension.plugins);
    }

    // Special handling for resolve.alias
    if (extension.resolve?.alias) {
      resultConfig.resolve = resultConfig.resolve || {};
      resultConfig.resolve.alias = mergeAliases(resultConfig.resolve.alias || {}, extension.resolve.alias);
    }

    // Merge the rest normally
    const extensionWithoutSpecials = { ...extension };
    delete extensionWithoutSpecials.plugins;
    if (extensionWithoutSpecials.resolve?.alias) {
      delete extensionWithoutSpecials.resolve.alias;
      if (Object.keys(extensionWithoutSpecials.resolve).length === 0) {
        delete extensionWithoutSpecials.resolve;
      }
    }

    resultConfig = deepMerge(resultConfig, extensionWithoutSpecials);
  }

  return resultConfig;
}

/**
 * Merges plugin arrays, avoiding duplicates by plugin name
 */
function mergePlugins(basePlugins: Plugin[], extensionPlugins: Plugin[]): Plugin[] {
  const pluginMap = new Map<string, Plugin>();

  // Add base plugins to map
  for (const plugin of basePlugins) {
    if (plugin && typeof plugin === 'object' && 'name' in plugin) {
      pluginMap.set(plugin.name, plugin);
    } else {
      // For plugins without name, just keep them
      pluginMap.set(`anonymous_${Math.random().toString(36).substring(2, 9)}`, plugin);
    }
  }

  // Add/override with extension plugins
  for (const plugin of extensionPlugins) {
    if (plugin && typeof plugin === 'object' && 'name' in plugin) {
      pluginMap.set(plugin.name, plugin);
    } else {
      pluginMap.set(`anonymous_${Math.random().toString(36).substring(2, 9)}`, plugin);
    }
  }

  return Array.from(pluginMap.values());
}

/**
 * Merges resolve.alias objects, handling both object and array formats
 */
function mergeAliases(baseAlias: any, extensionAlias: any): any {
  // Handle array format
  if (Array.isArray(baseAlias) || Array.isArray(extensionAlias)) {
    const baseAliasArray = Array.isArray(baseAlias) ? baseAlias : objectToAliasArray(baseAlias);
    const extensionAliasArray = Array.isArray(extensionAlias) ? extensionAlias : objectToAliasArray(extensionAlias);

    // Merge by find key
    const result = [...baseAliasArray];
    for (const extAlias of extensionAliasArray) {
      const existingIndex = result.findIndex(item => item.find === extAlias.find);
      if (existingIndex >= 0) {
        result[existingIndex] = extAlias;
      } else {
        result.push(extAlias);
      }
    }
    return result;
  }

  // Handle object format
  return { ...baseAlias, ...extensionAlias };
}

/**
 * Converts alias object to array format
 */
function objectToAliasArray(aliasObj: Record<string, string>): Array<{find: string, replacement: string}> {
  return Object.entries(aliasObj).map(([find, replacement]) => ({
    find,
    replacement
  }));
}