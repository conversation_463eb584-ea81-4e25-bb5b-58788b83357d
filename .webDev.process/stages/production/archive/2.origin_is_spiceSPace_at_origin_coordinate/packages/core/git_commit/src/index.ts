
/**
 * @module git_commit
 * @category Git
 * @packageDocumentation
 *
 * Git commit utilities with AI assistance and temporal awareness
 *
 * This module provides a set of utilities for creating structured git commits
 * with AI-assisted message generation, temporal references, analyzing staged changes,
 * and organizing commits based on file structure and temporal proximity.
 *
 * The package integrates with Augment AI to generate meaningful commit messages
 * based on the changes in your repository, following the Conventional Commits specification.
 *
 * @example
 * ```typescript
 * import {
 *   analyzeFileChanges,
 *   organizeCommits,
 *   doCommits,
 *   generateCommitSuggestions
 * } from '@local/git_commit';
 *
 * // Get AI-generated commit suggestions
 * const suggestions = generateCommitSuggestions();
 *
 * // Or manually analyze staged changes with temporal metadata
 * const changes = analyzeFileChanges();
 *
 * // Organize into logical commit groups
 * const commitPlans = organizeCommits(changes);
 *
 * // Execute the commits
 * const results = doCommits(commitPlans);
 * ```
 *
 * @remarks
 * This package is designed to work with Git repositories and requires Git to be installed.
 * It uses the Augment AI service for generating commit suggestions, which requires internet access.
 */

import {
  createCommit,
  getStagedFiles,
  addTemporalReference,
  getCommitHistory,
  parseCommitMessage
} from './git_operations';

import {
  analyzeFileChanges,
  organizeCommits,
  doCommits
} from './commit_analyzer';

import {
  generateCommitSuggestions,
  executeCommits,
  curateCommitSuggestions
} from './augment_integration';

import type {
  CommitOptions,
  TemporalReferenceOptions,
  FileChange,
  CommitPlan,
  CommitInfo
} from './types/git_operations.type';

import type {
  CommitSuggestionOutput
} from './augment_integration';

import {
  GitCommitError,
  GitRepoError,
  CommitError,
  TemporalAnalysisError,
  AIIntegrationError
} from './errors';

// Export all functionality
export {
  // Core git operations
  createCommit,
  getStagedFiles,
  addTemporalReference,
  getCommitHistory,
  parseCommitMessage,

  // Commit planning
  analyzeFileChanges,
  organizeCommits,
  doCommits,

  // Augment integration
  generateCommitSuggestions,
  executeCommits,
  curateCommitSuggestions,

  // Error types
  GitCommitError,
  GitRepoError,
  CommitError,
  TemporalAnalysisError,
  AIIntegrationError
};

export type {
  CommitOptions,
  TemporalReferenceOptions,
  FileChange,
  CommitPlan,
  CommitInfo,
  CommitSuggestionOutput
};

/**
 * API schema describing the functionality for process integration
 * This schema can be used by WebDev process and other tools to understand
 * and integrate with the git_commit module
 */
export const apiSchema = {
  name: 'git_commit',
  description: 'Git commit utilities with temporal awareness',
  functions: [
    {
      name: 'analyzeFileChanges',
      description: 'Analyze staged files with temporal metadata',
      fn: analyzeFileChanges,
      parameters: []
    },
    {
      name: 'organizeCommits',
      description: 'Organize file changes into logical commit groups',
      fn: organizeCommits,
      parameters: [
        { name: 'changes', type: 'FileChange[]', description: 'Array of file changes with temporal metadata' }
      ]
    },
    {
      name: 'generateCommitSuggestions',
      description: 'Generate commit suggestions for Augment integration',
      fn: generateCommitSuggestions,
      parameters: []
    },
    {
      name: 'executeCommits',
      description: 'Execute approved commit plans',
      fn: executeCommits,
      parameters: [
        { name: 'approvedCommits', type: 'CommitSuggestion[]', description: 'Array of approved commit suggestions' }
      ]
    },
    {
      name: 'curateCommitSuggestions',
      description: 'Update commit suggestions based on user curation',
      fn: curateCommitSuggestions,
      parameters: [
        { name: 'suggestions', type: 'CommitSuggestion[]', description: 'Original commit suggestions' },
        { name: 'curationType', type: 'string', description: 'Type of curation operation' },
        { name: 'params', type: 'any', description: 'Parameters for the curation operation' }
      ]
    },
    {
      name: 'createCommit',
      description: 'Create a git commit with enhanced formatting',
      fn: createCommit,
      parameters: [
        { name: 'options', type: 'CommitOptions', description: 'Commit options' }
      ]
    },
    {
      name: 'getStagedFiles',
      description: 'List staged files',
      fn: getStagedFiles,
      parameters: []
    },
    {
      name: 'getCommitHistory',
      description: 'Get commit history',
      fn: getCommitHistory,
      parameters: [
        { name: 'filePath', type: 'string', description: 'Path to file (optional)' },
        { name: 'limit', type: 'number', description: 'Maximum number of commits', default: 10 }
      ]
    }
  ]
};