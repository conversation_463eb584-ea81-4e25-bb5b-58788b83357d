/**
 * @module git_commit/augment_launcher.type
 * @category Types
 * @packageDocumentation
 *
 * Type definitions for Augment launcher with git commit context
 */

import type { CommitSuggestion } from '../types/augment_integration.type';

/**
 * Context data structure for Augment chat
 */
export interface AugmentContext {
  /** Type of context */
  type: string;
  /** Serialized commit suggestions */
  suggestions: string;
  /** Timestamp of context creation */
  timestamp: string;
  /** Optional metadata */
  metadata?: Record<string, any>;
}

/**
 * Options for the Augment launcher
 */
export interface LauncherOptions {
  /** Whether to run in dry-run mode (don't launch Augment) */
  dryRun?: boolean;
  /** Template to use for Augment chat */
  template?: string;
  /** Whether to output debug information */
  debug?: boolean;
  /** Whether to include file diffs in suggestions */
  includeDiffs?: boolean;
  /** Maximum number of suggestions to generate */
  maxSuggestions?: number;
}

/**
 * Result of the launcher operation
 */
export interface LauncherResult {
  /** Whether the operation was successful */
  success: boolean;
  /** Generated commit suggestions */
  suggestions: CommitSuggestion[];
  /** Error message if operation failed */
  error?: string;
  /** Whether Augment was launched */
  augmentLaunched: boolean;
}