/**
 * Environment detector for the adaptive linguistic system
 */

/**
 * Memory capabilities
 */
export interface MemoryCapabilities {
  /** Total available memory in bytes */
  totalMemory: number;
  
  /** Maximum array buffer size */
  maxArrayBufferSize: number;
  
  /** Whether shared array buffers are supported */
  supportsSharedArrayBuffer: boolean;
}

/**
 * Storage capabilities
 */
export interface StorageCapabilities {
  /** Whether local storage is available */
  hasLocalStorage: boolean;
  
  /** Whether indexed DB is available */
  hasIndexedDB: boolean;
  
  /** Whether file system access is available */
  hasFileSystemAccess: boolean;
  
  /** Available storage space in bytes */
  availableStorageSpace: number;
  
  /** Whether persistent storage is available */
  supportsPersistentStorage: boolean;
}

/**
 * Processing capabilities
 */
export interface ProcessingCapabilities {
  /** Number of available CPU cores */
  cpuCores: number;
  
  /** Whether web workers are supported */
  supportsWebWorkers: boolean;
  
  /** Whether shared workers are supported */
  supportsSharedWorkers: boolean;
  
  /** Whether service workers are supported */
  supportsServiceWorkers: boolean;
  
  /** Whether WebAssembly is supported */
  supportsWebAssembly: boolean;
  
  /** Whether SIMD is supported */
  supportsSIMD: boolean;
}

/**
 * Network capabilities
 */
export interface NetworkCapabilities {
  /** Whether the device is online */
  isOnline: boolean;
  
  /** Network connection type */
  connectionType: 'unknown' | 'ethernet' | 'wifi' | 'cellular' | 'none';
  
  /** Effective network type */
  effectiveType: 'unknown' | 'slow-2g' | '2g' | '3g' | '4g';
  
  /** Whether the connection is metered */
  isMetered: boolean;
  
  /** Whether the connection saves data */
  saveData: boolean;
  
  /** Maximum downlink speed in Mbps */
  downlinkMax: number;
}

/**
 * Platform information
 */
export interface Platform {
  /** Platform type */
  type: 'browser' | 'node' | 'deno' | 'react-native' | 'electron' | 'unknown';
  
  /** Operating system */
  os: 'windows' | 'macos' | 'linux' | 'android' | 'ios' | 'unknown';
  
  /** Device type */
  device: 'desktop' | 'tablet' | 'mobile' | 'server' | 'unknown';
  
  /** Browser name (if applicable) */
  browser?: string;
  
  /** Browser version (if applicable) */
  browserVersion?: string;
  
  /** Node.js version (if applicable) */
  nodeVersion?: string;
}

/**
 * Environment capabilities
 */
export interface EnvironmentCapabilities {
  /** Memory capabilities */
  memory: MemoryCapabilities;
  
  /** Storage capabilities */
  storage: StorageCapabilities;
  
  /** Processing capabilities */
  processing: ProcessingCapabilities;
  
  /** Network capabilities */
  network: NetworkCapabilities;
  
  /** Platform information */
  platform: Platform;
}

/**
 * Environment tier
 */
export type EnvironmentTier = 'minimal' | 'standard' | 'advanced';

/**
 * Environment detector
 */
export class EnvironmentDetector {
  /**
   * Detect environment capabilities
   */
  detect(): EnvironmentCapabilities {
    return {
      memory: this.detectMemoryCapabilities(),
      storage: this.detectStorageCapabilities(),
      processing: this.detectProcessingCapabilities(),
      network: this.detectNetworkCapabilities(),
      platform: this.detectPlatform()
    };
  }

  /**
   * Determine the environment tier based on capabilities
   */
  determineTier(capabilities: EnvironmentCapabilities): EnvironmentTier {
    // Check if the environment is minimal
    if (this.isMinimalEnvironment(capabilities)) {
      return 'minimal';
    }
    
    // Check if the environment is advanced
    if (this.isAdvancedEnvironment(capabilities)) {
      return 'advanced';
    }
    
    // Default to standard
    return 'standard';
  }

  /**
   * Detect memory capabilities
   */
  private detectMemoryCapabilities(): MemoryCapabilities {
    // Default values for environments where detection is not possible
    const defaultCapabilities: MemoryCapabilities = {
      totalMemory: 512 * 1024 * 1024, // 512 MB
      maxArrayBufferSize: 100 * 1024 * 1024, // 100 MB
      supportsSharedArrayBuffer: false
    };
    
    try {
      // Browser environment
      if (typeof window !== 'undefined') {
        const performance = window.performance as any;
        
        // Check for memory info
        const memory = performance?.memory;
        
        return {
          totalMemory: memory?.jsHeapSizeLimit || defaultCapabilities.totalMemory,
          maxArrayBufferSize: memory?.jsHeapSizeLimit / 4 || defaultCapabilities.maxArrayBufferSize,
          supportsSharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined'
        };
      }
      
      // Node.js environment
      if (typeof process !== 'undefined' && process.versions?.node) {
        return {
          totalMemory: (process as any).memoryUsage?.().heapTotal || defaultCapabilities.totalMemory,
          maxArrayBufferSize: 2 * 1024 * 1024 * 1024, // 2 GB
          supportsSharedArrayBuffer: true
        };
      }
      
      return defaultCapabilities;
    } catch (error) {
      console.warn('Error detecting memory capabilities:', error);
      return defaultCapabilities;
    }
  }

  /**
   * Detect storage capabilities
   */
  private detectStorageCapabilities(): StorageCapabilities {
    // Default values for environments where detection is not possible
    const defaultCapabilities: StorageCapabilities = {
      hasLocalStorage: false,
      hasIndexedDB: false,
      hasFileSystemAccess: false,
      availableStorageSpace: 0,
      supportsPersistentStorage: false
    };
    
    try {
      // Browser environment
      if (typeof window !== 'undefined') {
        const hasLocalStorage = (() => {
          try {
            const testKey = '__test__';
            localStorage.setItem(testKey, testKey);
            localStorage.removeItem(testKey);
            return true;
          } catch (e) {
            return false;
          }
        })();
        
        const hasIndexedDB = typeof indexedDB !== 'undefined';
        
        const hasFileSystemAccess = typeof (window as any).showOpenFilePicker === 'function';
        
        // Check for storage manager
        const storageManager = (navigator as any).storage;
        
        return {
          hasLocalStorage,
          hasIndexedDB,
          hasFileSystemAccess,
          availableStorageSpace: storageManager?.estimate?.().quota || defaultCapabilities.availableStorageSpace,
          supportsPersistentStorage: typeof storageManager?.persist === 'function'
        };
      }
      
      // Node.js environment
      if (typeof process !== 'undefined' && process.versions?.node) {
        return {
          hasLocalStorage: false,
          hasIndexedDB: false,
          hasFileSystemAccess: true,
          availableStorageSpace: Number.MAX_SAFE_INTEGER, // Assume unlimited
          supportsPersistentStorage: true
        };
      }
      
      return defaultCapabilities;
    } catch (error) {
      console.warn('Error detecting storage capabilities:', error);
      return defaultCapabilities;
    }
  }

  /**
   * Detect processing capabilities
   */
  private detectProcessingCapabilities(): ProcessingCapabilities {
    // Default values for environments where detection is not possible
    const defaultCapabilities: ProcessingCapabilities = {
      cpuCores: 1,
      supportsWebWorkers: false,
      supportsSharedWorkers: false,
      supportsServiceWorkers: false,
      supportsWebAssembly: false,
      supportsSIMD: false
    };
    
    try {
      // Browser environment
      if (typeof window !== 'undefined') {
        return {
          cpuCores: navigator.hardwareConcurrency || defaultCapabilities.cpuCores,
          supportsWebWorkers: typeof Worker !== 'undefined',
          supportsSharedWorkers: typeof SharedWorker !== 'undefined',
          supportsServiceWorkers: 'serviceWorker' in navigator,
          supportsWebAssembly: typeof WebAssembly !== 'undefined',
          supportsSIMD: typeof WebAssembly !== 'undefined' && typeof (WebAssembly as any).validate === 'function'
        };
      }
      
      // Node.js environment
      if (typeof process !== 'undefined' && process.versions?.node) {
        const os = require('os');
        
        return {
          cpuCores: os.cpus().length,
          supportsWebWorkers: false,
          supportsSharedWorkers: false,
          supportsServiceWorkers: false,
          supportsWebAssembly: typeof WebAssembly !== 'undefined',
          supportsSIMD: typeof WebAssembly !== 'undefined' && typeof (WebAssembly as any).validate === 'function'
        };
      }
      
      return defaultCapabilities;
    } catch (error) {
      console.warn('Error detecting processing capabilities:', error);
      return defaultCapabilities;
    }
  }

  /**
   * Detect network capabilities
   */
  private detectNetworkCapabilities(): NetworkCapabilities {
    // Default values for environments where detection is not possible
    const defaultCapabilities: NetworkCapabilities = {
      isOnline: true,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      isMetered: false,
      saveData: false,
      downlinkMax: 10
    };
    
    try {
      // Browser environment
      if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
        const connection = (navigator as any).connection;
        
        return {
          isOnline: navigator.onLine,
          connectionType: connection?.type || defaultCapabilities.connectionType,
          effectiveType: connection?.effectiveType || defaultCapabilities.effectiveType,
          isMetered: connection?.metered || defaultCapabilities.isMetered,
          saveData: connection?.saveData || defaultCapabilities.saveData,
          downlinkMax: connection?.downlinkMax || defaultCapabilities.downlinkMax
        };
      }
      
      // Node.js environment
      if (typeof process !== 'undefined' && process.versions?.node) {
        return {
          isOnline: true, // Assume online
          connectionType: 'ethernet',
          effectiveType: '4g',
          isMetered: false,
          saveData: false,
          downlinkMax: 100
        };
      }
      
      return defaultCapabilities;
    } catch (error) {
      console.warn('Error detecting network capabilities:', error);
      return defaultCapabilities;
    }
  }

  /**
   * Detect platform
   */
  private detectPlatform(): Platform {
    // Default values for environments where detection is not possible
    const defaultPlatform: Platform = {
      type: 'unknown',
      os: 'unknown',
      device: 'unknown'
    };
    
    try {
      // Browser environment
      if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
        const userAgent = navigator.userAgent;
        
        // Detect browser
        let browser: string | undefined;
        let browserVersion: string | undefined;
        
        if (userAgent.indexOf('Chrome') !== -1) {
          browser = 'Chrome';
          browserVersion = userAgent.match(/Chrome\/(\d+\.\d+)/)![1];
        } else if (userAgent.indexOf('Firefox') !== -1) {
          browser = 'Firefox';
          browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/)![1];
        } else if (userAgent.indexOf('Safari') !== -1) {
          browser = 'Safari';
          browserVersion = userAgent.match(/Version\/(\d+\.\d+)/)![1];
        } else if (userAgent.indexOf('Edge') !== -1) {
          browser = 'Edge';
          browserVersion = userAgent.match(/Edge\/(\d+\.\d+)/)![1];
        }
        
        // Detect OS
        let os: Platform['os'] = 'unknown';
        
        if (userAgent.indexOf('Windows') !== -1) {
          os = 'windows';
        } else if (userAgent.indexOf('Mac') !== -1) {
          os = 'macos';
        } else if (userAgent.indexOf('Linux') !== -1) {
          os = 'linux';
        } else if (userAgent.indexOf('Android') !== -1) {
          os = 'android';
        } else if (userAgent.indexOf('iOS') !== -1 || userAgent.indexOf('iPhone') !== -1 || userAgent.indexOf('iPad') !== -1) {
          os = 'ios';
        }
        
        // Detect device
        let device: Platform['device'] = 'unknown';
        
        if (userAgent.indexOf('Mobile') !== -1 || userAgent.indexOf('iPhone') !== -1) {
          device = 'mobile';
        } else if (userAgent.indexOf('iPad') !== -1 || userAgent.indexOf('Tablet') !== -1) {
          device = 'tablet';
        } else {
          device = 'desktop';
        }
        
        // Check for Electron
        const isElectron = userAgent.indexOf('Electron') !== -1;
        
        return {
          type: isElectron ? 'electron' : 'browser',
          os,
          device,
          browser,
          browserVersion
        };
      }
      
      // Node.js environment
      if (typeof process !== 'undefined' && process.versions?.node) {
        const os = require('os');
        
        let nodeOs: Platform['os'] = 'unknown';
        
        if (os.platform() === 'win32') {
          nodeOs = 'windows';
        } else if (os.platform() === 'darwin') {
          nodeOs = 'macos';
        } else if (os.platform() === 'linux') {
          nodeOs = 'linux';
        }
        
        return {
          type: 'node',
          os: nodeOs,
          device: 'server',
          nodeVersion: process.versions.node
        };
      }
      
      return defaultPlatform;
    } catch (error) {
      console.warn('Error detecting platform:', error);
      return defaultPlatform;
    }
  }

  /**
   * Check if the environment is minimal
   */
  private isMinimalEnvironment(capabilities: EnvironmentCapabilities): boolean {
    // Check memory constraints
    const hasLimitedMemory = capabilities.memory.totalMemory < 1024 * 1024 * 1024; // Less than 1 GB
    
    // Check processing constraints
    const hasLimitedProcessing = capabilities.processing.cpuCores <= 2;
    
    // Check device constraints
    const isMobileDevice = capabilities.platform.device === 'mobile';
    
    // Check network constraints
    const hasLimitedNetwork = capabilities.network.effectiveType === 'slow-2g' || 
                             capabilities.network.effectiveType === '2g' ||
                             capabilities.network.saveData;
    
    // An environment is minimal if it has limited memory or processing power,
    // is a mobile device, or has a limited network connection
    return hasLimitedMemory || hasLimitedProcessing || isMobileDevice || hasLimitedNetwork;
  }

  /**
   * Check if the environment is advanced
   */
  private isAdvancedEnvironment(capabilities: EnvironmentCapabilities): boolean {
    // Check memory capabilities
    const hasLargeMemory = capabilities.memory.totalMemory >= 8 * 1024 * 1024 * 1024; // 8 GB or more
    
    // Check processing capabilities
    const hasMultipleCores = capabilities.processing.cpuCores >= 4;
    
    // Check platform
    const isServer = capabilities.platform.device === 'server';
    
    // Check for advanced features
    const hasAdvancedFeatures = capabilities.processing.supportsWebAssembly &&
                               capabilities.processing.supportsSIMD &&
                               capabilities.memory.supportsSharedArrayBuffer;
    
    // An environment is advanced if it has large memory, multiple cores,
    // is a server, or has advanced features
    return hasLargeMemory && hasMultipleCores && (isServer || hasAdvancedFeatures);
  }
}
