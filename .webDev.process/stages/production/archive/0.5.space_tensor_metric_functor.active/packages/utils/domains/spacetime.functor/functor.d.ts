import { Functor } from '../atomic/functor.type';
import { CatId } from '../atomic/cat.type';
/**
 * SpaceTimeCategory - Category representing spacetime
 */
export interface SpaceTimeCategory {
    identity: string;
    objects: Map<string, SpaceTimePoint>;
    morphisms: Map<string, SpaceTimePath>;
}
/**
 * SpaceTimePoint - A point in spacetime (event)
 */
export interface SpaceTimePoint {
    coordinates: [number, number, number, number];
    properties: Map<string, any>;
}
/**
 * SpaceTimePath - A path between spacetime points
 */
export interface SpaceTimePath {
    source: SpaceTimePoint;
    target: SpaceTimePoint;
    isTimelike: boolean;
    isSpacelike: boolean;
    isLightlike: boolean;
}
/**
 * ObserverFunctor - Maps between reference frames
 */
export declare class ObserverFunctor implements Functor<SpaceTimeCategory, SpaceTimeCategory> {
    readonly source: CatId<SpaceTimeCategory>;
    readonly target: CatId<SpaceTimeCategory>;
    private readonly velocityVector;
    readonly id: CatId<Functor<SpaceTimeCategory, SpaceTimeCategory>>;
    constructor(source: CatId<SpaceTimeCategory>, target: CatId<SpaceTimeCategory>, velocityVector: [number, number, number], name?: string);
    /**
     * Maps a spacetime category to another reference frame
     */
    mapObject(category: SpaceTimeCategory): SpaceTimeCategory;
    /**
     * Apply Lorentz transformation to a spacetime point
     */
    private transformPoint;
    validate(): boolean;
}
/**
 * TemporalAsymmetryFunctor - Captures the asymmetry of time
 */
export declare class TemporalAsymmetryFunctor implements Functor<SpaceTimeCategory, any> {
    readonly source: CatId<SpaceTimeCategory>;
    readonly target: CatId<any>;
    readonly id: CatId<Functor<SpaceTimeCategory, any>>;
    constructor(source: CatId<SpaceTimeCategory>, target: CatId<any>, name?: string);
    /**
     * Maps spacetime to a partially ordered set representing causal structure
     */
    mapObject(category: SpaceTimeCategory): any;
    validate(): boolean;
}
//# sourceMappingURL=functor.d.ts.map