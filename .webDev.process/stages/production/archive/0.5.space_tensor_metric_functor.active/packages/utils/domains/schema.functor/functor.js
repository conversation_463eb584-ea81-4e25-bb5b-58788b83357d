import { createCatId } from '../atomic/cat.type';
/**
 * ResolverFunctor - Maps between resolver categories
 */
export class ResolverFunctor {
    source;
    target;
    mapResolver;
    id;
    constructor(source, target, mapResolver, name) {
        this.source = source;
        this.target = target;
        this.mapResolver = mapResolver;
        this.id = createCatId(name || `ResolverFunctor_${source.name || 'source'}_to_${target.name || 'target'}`);
    }
    /**
     * Maps a resolver category to another resolver category
     */
    mapObject(category) {
        // Create a new category with mapped resolvers
        const mappedResolvers = new Map();
        category.objects.resolvers.forEach((resolver, key) => {
            mappedResolvers.set(key, this.mapResolver(resolver));
        });
        return {
            identity: `${this.target.name}_${category.identity}`,
            objects: {
                types: category.objects.types,
                resolvers: mappedResolvers
            },
            morphisms: {
                compose: category.morphisms.compose,
                map: category.morphisms.map
            }
        };
    }
    validate() {
        // Validation logic would go here
        return true;
    }
}
//# sourceMappingURL=functor.js.map