"use strict";
/**
 * Calculates the infinitesimal spacetime interval between two nearby events
 *
 * @param event - The reference spacetime event
 * @param deltaEvent - The small displacement from the reference event
 * @returns The spacetime interval ds²
 */
calculateInterval(event, SpaceTimeEvent, deltaEvent, SpaceTimeEvent);
number;
{
    const metric = this.mapSpaceTimeEvent(event);
    // Calculate ds² = g_μν dx^μ dx^ν
    let interval = 0;
    // Time component: g00 * dt²
    interval += (metric.get('g00') || -1) * deltaEvent.t * deltaEvent.t;
    // Space components: g11 * dx² + g22 * dy² + g33 * dz²
    interval += (metric.get('g11') || 1) * deltaEvent.x * deltaEvent.x;
    interval += (metric.get('g22') || 1) * deltaEvent.y * deltaEvent.y;
    interval += (metric.get('g33') || 1) * deltaEvent.z * deltaEvent.z;
    return interval;
}
/**
 * Determines if there exists a causal connection between two events
 *
 * @param event1 - The first spacetime event
 * @param event2 - The second spacetime event
 * @returns True if event2 is in the future light cone of event1
 */
isCausal(event1, SpaceTimeEvent, event2, SpaceTimeEvent);
boolean;
{
    // For simplicity, using Minkowski metric for causal check
    const dt = event2.t - event1.t;
    const dx = event2.x - event1.x;
    const dy = event2.y - event1.y;
    const dz = event2.z - event1.z;
    // In future light cone if dt > 0 and ds² <= 0
    // (using -+++ signature)
    const ds2 = -dt * dt + dx * dx + dy * dy + dz * dz;
    return dt > 0 && ds2 <= 0;
}
//# sourceMappingURL=2.distance_between_events.functor.js.map