import { Functor } from '../atomic/functor.type';
import { CatId } from '../atomic/cat.type';
/**
 * ResolverCategory - Category of resolvers
 */
export interface ResolverCategory<T = any> {
    identity: string;
    objects: {
        types: Map<string, SchemaType>;
        resolvers: Map<string, Resolver<T>>;
    };
    morphisms: {
        compose: <A, B, C>(f: Resolver<A, B>, g: Resolver<B, C>) => Resolver<A, C>;
        map: <A, B>(resolver: Resolver<A, B>, name: string) => Resolver<A, B>;
    };
}
/**
 * Resolver - Maps between data types
 */
export interface Resolver<S = any, T = any> {
    resolve: (source: S) => Promise<T> | T;
    compose: <R>(next: Resolver<T, R>) => Resolver<S, R>;
}
/**
 * SchemaType - Represents a type in the schema
 */
export interface SchemaType {
    name: string;
    fields: Map<string, SchemaField>;
}
/**
 * Schem<PERSON><PERSON>ield - Represents a field in a schema type
 */
export interface SchemaField {
    name: string;
    type: string;
    resolve?: Resolver<any, any>;
}
/**
 * ResolverFunctor - Maps between resolver categories
 */
export declare class ResolverFunctor<S = 'categorical', T = 'physical'> implements Functor<ResolverCategory<S>, ResolverCategory<T>> {
    readonly source: CatId<ResolverCategory<S>>;
    readonly target: CatId<ResolverCategory<T>>;
    private readonly mapResolver;
    readonly id: CatId<Functor<ResolverCategory<S>, ResolverCategory<T>>>;
    constructor(source: CatId<ResolverCategory<S>>, target: CatId<ResolverCategory<T>>, mapResolver: <A, B>(resolver: Resolver<A, B>) => Resolver<A, B>, name?: string);
    /**
     * Maps a resolver category to another resolver category
     */
    mapObject(category: ResolverCategory<S>): ResolverCategory<T>;
    validate(): boolean;
}
//# sourceMappingURL=functor.d.ts.map